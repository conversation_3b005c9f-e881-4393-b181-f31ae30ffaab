import { ConfigurationService } from '@/services/ConfigurationService';
import * as vscode from 'vscode';

describe('ConfigurationService', () => {
  let configService: ConfigurationService;
  let mockConfig: any;

  beforeEach(() => {
    mockConfig = {
      get: jest.fn(),
      update: jest.fn(),
    };

    (vscode.workspace.getConfiguration as jest.Mock).mockReturnValue(mockConfig);
    (vscode.workspace.onDidChangeConfiguration as jest.Mock).mockReturnValue({
      dispose: jest.fn(),
    });

    configService = new ConfigurationService();
  });

  afterEach(() => {
    configService.dispose();
  });

  describe('get', () => {
    it('should return configuration value', () => {
      mockConfig.get.mockReturnValue('test-value');

      const result = configService.get('test.key');

      expect(result).toBe('test-value');
      expect(vscode.workspace.getConfiguration).toHaveBeenCalledWith('lazycode');
      expect(mockConfig.get).toHaveBeenCalledWith('test.key');
    });

    it('should return default value when config is undefined', () => {
      mockConfig.get.mockReturnValue(undefined);

      const result = configService.get('test.key', 'default-value');

      expect(result).toBe('default-value');
    });

    it('should return undefined when no default provided', () => {
      mockConfig.get.mockReturnValue(undefined);

      const result = configService.get('test.key');

      expect(result).toBeUndefined();
    });
  });

  describe('set', () => {
    it('should update configuration value', async () => {
      mockConfig.update.mockResolvedValue(undefined);

      await configService.set('test.key', 'new-value');

      expect(mockConfig.update).toHaveBeenCalledWith(
        'test.key',
        'new-value',
        vscode.ConfigurationTarget.Global
      );
    });

    it('should use specified target', async () => {
      mockConfig.update.mockResolvedValue(undefined);

      await configService.set('test.key', 'new-value', vscode.ConfigurationTarget.Workspace);

      expect(mockConfig.update).toHaveBeenCalledWith(
        'test.key',
        'new-value',
        vscode.ConfigurationTarget.Workspace
      );
    });

    it('should throw error when update fails', async () => {
      const error = new Error('Update failed');
      mockConfig.update.mockRejectedValue(error);

      await expect(configService.set('test.key', 'new-value')).rejects.toThrow('Update failed');
    });
  });

  describe('getCompletionsConfig', () => {
    it('should return completions configuration', () => {
      mockConfig.get
        .mockReturnValueOnce(true) // enableAutomaticCompletions
        .mockReturnValueOnce(false); // enableQuickSuggestions

      const result = configService.getCompletionsConfig();

      expect(result).toEqual({
        enableAutomaticCompletions: true,
        enableQuickSuggestions: false,
      });
    });

    it('should use default values when config is undefined', () => {
      mockConfig.get.mockReturnValue(undefined);

      const result = configService.getCompletionsConfig();

      expect(result).toEqual({
        enableAutomaticCompletions: true,
        enableQuickSuggestions: true,
      });
    });
  });

  describe('getChatConfig', () => {
    it('should return chat configuration', () => {
      mockConfig.get.mockReturnValue(false);

      const result = configService.getChatConfig();

      expect(result).toEqual({
        enableEmptyFileHint: false,
      });
    });
  });

  describe('getAdvancedConfig', () => {
    it('should return advanced configuration', () => {
      mockConfig.get.mockReturnValueOnce('test-token').mockReturnValueOnce('https://api.test.com');

      const result = configService.getAdvancedConfig();

      expect(result).toEqual({
        apiToken: 'test-token',
        apiUrl: 'https://api.test.com',
      });
    });
  });

  describe('setCompletionsConfig', () => {
    it('should update completions configuration', async () => {
      mockConfig.update.mockResolvedValue(undefined);

      await configService.setCompletionsConfig({
        enableAutomaticCompletions: false,
        enableQuickSuggestions: true,
      });

      expect(mockConfig.update).toHaveBeenCalledTimes(2);
      expect(mockConfig.update).toHaveBeenCalledWith(
        'completions.enableAutomaticCompletions',
        false,
        vscode.ConfigurationTarget.Global
      );
      expect(mockConfig.update).toHaveBeenCalledWith(
        'completions.enableQuickSuggestions',
        true,
        vscode.ConfigurationTarget.Global
      );
    });

    it('should only update provided values', async () => {
      mockConfig.update.mockResolvedValue(undefined);

      await configService.setCompletionsConfig({
        enableAutomaticCompletions: false,
      });

      expect(mockConfig.update).toHaveBeenCalledTimes(1);
      expect(mockConfig.update).toHaveBeenCalledWith(
        'completions.enableAutomaticCompletions',
        false,
        vscode.ConfigurationTarget.Global
      );
    });
  });

  describe('validateConfiguration', () => {
    it('should return valid when configuration is correct', () => {
      // Mock the getFullConfiguration method calls
      mockConfig.get
        .mockReturnValueOnce(true) // enableAutomaticCompletions
        .mockReturnValueOnce(true) // enableQuickSuggestions
        .mockReturnValueOnce(true) // enableEmptyFileHint
        .mockReturnValueOnce('test-token') // apiToken
        .mockReturnValueOnce('https://api.test.com'); // apiUrl

      const result = configService.validateConfiguration();

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should return invalid when both token and URL are missing', () => {
      mockConfig.get
        .mockReturnValueOnce(true) // enableAutomaticCompletions
        .mockReturnValueOnce(true) // enableQuickSuggestions
        .mockReturnValueOnce(true) // enableEmptyFileHint
        .mockReturnValueOnce('') // apiToken
        .mockReturnValueOnce(''); // apiUrl

      const result = configService.validateConfiguration();

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Either API token or API URL must be configured');
    });

    it('should return invalid when URL is malformed', () => {
      mockConfig.get
        .mockReturnValueOnce(true) // enableAutomaticCompletions
        .mockReturnValueOnce(true) // enableQuickSuggestions
        .mockReturnValueOnce(true) // enableEmptyFileHint
        .mockReturnValueOnce('test-token') // apiToken
        .mockReturnValueOnce('invalid-url'); // apiUrl

      const result = configService.validateConfiguration();

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('API URL must be a valid URL');
    });
  });

  describe('onDidChange', () => {
    it('should register configuration change listener', () => {
      const callback = jest.fn();
      const mockDisposable = { dispose: jest.fn() };
      (vscode.workspace.onDidChangeConfiguration as jest.Mock).mockReturnValue(mockDisposable);

      const disposable = configService.onDidChange(callback);

      expect(vscode.workspace.onDidChangeConfiguration).toHaveBeenCalled();
      expect(disposable).toBe(mockDisposable);
    });
  });
});
