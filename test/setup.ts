/**
 * Jest setup file for LazyCode extension tests
 */

// Mock VSCode API
const vscode = {
  window: {
    showInformationMessage: jest.fn(),
    showWarningMessage: jest.fn(),
    showErrorMessage: jest.fn(),
    showInputBox: jest.fn(),
    showQuickPick: jest.fn(),
    showSaveDialog: jest.fn(),
    createOutputChannel: jest.fn(() => ({
      appendLine: jest.fn(),
      show: jest.fn(),
      dispose: jest.fn(),
    })),
    withProgress: jest.fn(),
    activeTextEditor: undefined,
    onDidChangeActiveTextEditor: jest.fn(),
  },
  workspace: {
    getConfiguration: jest.fn(() => ({
      get: jest.fn(),
      update: jest.fn(),
    })),
    onDidChangeConfiguration: jest.fn(),
    onDidChangeTextDocument: jest.fn(),
    onDidChangeWorkspaceFolders: jest.fn(),
    getWorkspaceFolder: jest.fn(),
    workspaceFolders: [],
    openTextDocument: jest.fn(),
    applyEdit: jest.fn(),
    fs: {
      writeFile: jest.fn(),
    },
  },
  commands: {
    registerCommand: jest.fn(),
    executeCommand: jest.fn(),
  },
  env: {
    clipboard: {
      writeText: jest.fn(),
    },
    machineId: 'test-machine-id',
  },
  extensions: {
    getExtension: jest.fn(),
  },
  ConfigurationTarget: {
    Global: 1,
    Workspace: 2,
    WorkspaceFolder: 3,
  },
  ProgressLocation: {
    Notification: 15,
  },
  Uri: {
    file: jest.fn((path: string) => ({ fsPath: path })),
  },
  Position: jest.fn(),
  Range: jest.fn(),
  Selection: jest.fn(),
  WorkspaceEdit: jest.fn(() => ({
    createFile: jest.fn(),
    insert: jest.fn(),
    replace: jest.fn(),
  })),
  Disposable: jest.fn(() => ({
    dispose: jest.fn(),
  })),
};

// Mock the vscode module
jest.mock('vscode', () => vscode, { virtual: true });

// Set up global test environment
global.console = {
  ...console,
  // Suppress console output during tests unless explicitly needed
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Set test environment
process.env.NODE_ENV = 'test';

// Global test utilities
(global as any).createMockContext = () => ({
  editor: {
    document: {
      fileName: '/test/file.ts',
      languageId: 'typescript',
      getText: jest.fn(() => 'test code'),
      uri: { fsPath: '/test/file.ts' },
    },
    selection: {
      isEmpty: false,
      start: { line: 0, character: 0 },
      end: { line: 0, character: 9 },
    },
  },
  selection: {
    isEmpty: false,
    start: { line: 0, character: 0 },
    end: { line: 0, character: 9 },
  },
  document: {
    fileName: '/test/file.ts',
    languageId: 'typescript',
    getText: jest.fn(() => 'test code'),
  },
  workspaceFolder: {
    uri: { fsPath: '/test/workspace' },
    name: 'test-workspace',
    index: 0,
  },
});

// Clean up after each test
afterEach(() => {
  jest.clearAllMocks();
});
