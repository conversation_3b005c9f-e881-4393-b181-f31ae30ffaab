{"name": "lazycode", "displayName": "LazyCode", "description": "AI-powered coding assistant - A reverse-engineered implementation", "version": "0.1.0", "publisher": "lazycode", "private": true, "license": "MIT", "engines": {"vscode": "^1.82.0", "node": ">= 18.15.0"}, "categories": ["AI", "Cha<PERSON>", "Programming Languages", "Snippets"], "keywords": ["ai", "assistant", "chat", "completion", "copilot"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "icon": "media/icon.png", "galleryBanner": {"color": "#1e1e1e", "theme": "dark"}, "contributes": {"configuration": [{"title": "LazyCode", "properties": {"lazycode.completions.enableAutomaticCompletions": {"type": "boolean", "default": true, "description": "Enable automatic inline code completions"}, "lazycode.completions.enableQuickSuggestions": {"type": "boolean", "default": true, "description": "Add LazyCode to the IntelliSense pop-up suggestions"}, "lazycode.chat.enableEmptyFileHint": {"type": "boolean", "default": true, "description": "Display a hint to use LazyCode Chat when an empty file is open"}, "lazycode.advanced.apiToken": {"type": "string", "default": "", "description": "API token for LazyCode access"}, "lazycode.advanced.apiUrl": {"type": "string", "default": "", "description": "Custom API URL for LazyCode service"}}}], "commands": [{"command": "lazycode.chat.focus", "title": "Open LazyCode Chat", "category": "LazyCode", "icon": "$(comment-discussion)"}, {"command": "lazycode.chat.newChat", "title": "Start New Chat", "category": "LazyCode"}, {"command": "lazycode.code.explain", "title": "Explain Code", "category": "LazyCode"}, {"command": "lazycode.code.fix", "title": "Fix Code", "category": "LazyCode"}, {"command": "lazycode.code.test", "title": "Generate Tests", "category": "LazyCode"}, {"command": "lazycode.code.document", "title": "Generate Documentation", "category": "LazyCode"}, {"command": "lazycode.settings.open", "title": "Open Settings", "category": "LazyCode", "icon": "$(gear)"}], "keybindings": [{"command": "lazycode.chat.focus", "key": "ctrl+l", "mac": "cmd+l"}, {"command": "lazycode.code.explain", "key": "ctrl+shift+e", "mac": "cmd+shift+e", "when": "editorHasSelection"}], "viewsContainers": {"activitybar": [{"id": "lazycode-chat", "title": "LazyCode", "icon": "media/activitybar.svg"}]}, "views": {"lazycode-chat": [{"id": "lazycode-chat", "name": "Cha<PERSON>", "type": "webview"}]}, "menus": {"editor/context": [{"submenu": "lazycode.context-submenu", "group": "0_lazycode"}], "view/title": [{"command": "lazycode.settings.open", "when": "view == lazycode-chat", "group": "navigation"}]}, "submenus": [{"id": "lazycode.context-submenu", "label": "LazyCode"}]}, "scripts": {"build": "npm run build:extension", "build:extension": "esbuild ./src/extension.ts --bundle --outfile=out/extension.js --external:vscode --format=cjs --platform=node --sourcemap", "watch": "npm run watch:extension", "watch:extension": "npm run build:extension -- --watch", "test": "npm run test:extension", "test:extension": "jest", "lint": "eslint src --ext ts", "lint:fix": "eslint src --ext ts --fix", "package": "vsce package", "vscode:prepublish": "npm run build"}, "devDependencies": {"@types/vscode": "^1.82.0", "@types/node": "^18.15.0", "@types/jest": "^29.5.0", "@typescript-eslint/eslint-plugin": "^7.0.0", "@typescript-eslint/parser": "^7.0.0", "esbuild": "^0.19.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "ts-jest": "^29.1.0", "typescript": "^5.1.0", "@vscode/test-electron": "^2.3.0", "@vscode/vsce": "^2.22.0"}, "dependencies": {"axios": "^1.5.0"}}