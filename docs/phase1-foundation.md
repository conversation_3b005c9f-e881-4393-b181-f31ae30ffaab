# 阶段1: 项目基础设施搭建

## 概述

本阶段的目标是建立LazyCode项目的基础架构，包括项目结构、开发环境、构建系统和核心架构设计。

## 任务清单

### 1.1 项目初始化

#### 1.1.1 创建项目结构
```
lazycode/
├── src/                    # 主要源代码
│   ├── extension.ts        # 插件入口点
│   ├── commands/           # 命令处理器
│   ├── providers/          # VSCode提供器
│   ├── services/           # 业务服务
│   ├── utils/              # 工具函数
│   └── types/              # 类型定义
├── webviews/               # Webview前端代码
│   ├── src/
│   │   ├── components/     # Vue组件
│   │   ├── stores/         # 状态管理
│   │   ├── utils/          # 前端工具
│   │   └── types/          # 前端类型
│   ├── public/             # 静态资源
│   └── dist/               # 构建输出
├── test/                   # 测试代码
│   ├── unit/               # 单元测试
│   ├── integration/        # 集成测试
│   └── e2e/                # 端到端测试
├── docs/                   # 文档
├── media/                  # 媒体资源
├── out/                    # 编译输出
└── package.json            # 项目配置
```

#### 1.1.2 配置package.json
- [ ] 设置基本信息 (name, version, description)
- [ ] 配置VSCode引擎版本要求
- [ ] 设置分类和关键词
- [ ] 配置激活事件
- [ ] 设置主入口文件
- [ ] 添加开发依赖

#### 1.1.3 TypeScript配置
- [ ] 创建tsconfig.json (后端)
- [ ] 创建tsconfig.webview.json (前端)
- [ ] 配置编译选项和路径映射
- [ ] 设置严格模式和类型检查

#### 1.1.4 构建工具配置
- [ ] 配置esbuild用于后端构建
- [ ] 配置Vite用于前端构建
- [ ] 设置开发和生产环境构建
- [ ] 配置热重载和监听模式

### 1.2 开发环境设置

#### 1.2.1 代码规范工具
- [ ] 配置ESLint规则
- [ ] 配置Prettier格式化
- [ ] 设置pre-commit钩子
- [ ] 配置VSCode工作区设置

#### 1.2.2 测试环境
- [ ] 配置Jest测试框架
- [ ] 设置VSCode测试运行器
- [ ] 创建测试工具和模拟对象
- [ ] 配置测试覆盖率报告

#### 1.2.3 调试配置
- [ ] 配置VSCode调试设置
- [ ] 设置断点和日志
- [ ] 配置扩展开发主机
- [ ] 设置热重载调试

### 1.3 核心架构设计

#### 1.3.1 插件生命周期管理
```typescript
// src/extension.ts
export async function activate(context: vscode.ExtensionContext) {
    // 初始化核心服务
    // 注册命令和提供器
    // 设置事件监听
}

export function deactivate() {
    // 清理资源
    // 保存状态
}
```

#### 1.3.2 服务容器设计
```typescript
// src/services/ServiceContainer.ts
export class ServiceContainer {
    private services = new Map<string, any>();
    
    register<T>(name: string, service: T): void
    get<T>(name: string): T
    dispose(): void
}
```

#### 1.3.3 事件系统设计
```typescript
// src/utils/EventEmitter.ts
export class EventEmitter<T = any> {
    private listeners = new Map<string, Function[]>();
    
    on(event: string, listener: Function): void
    emit(event: string, data?: T): void
    off(event: string, listener: Function): void
}
```

#### 1.3.4 配置管理系统
```typescript
// src/services/ConfigurationService.ts
export class ConfigurationService {
    get<T>(key: string, defaultValue?: T): T
    set(key: string, value: any): Promise<void>
    onDidChange(callback: (e: ConfigurationChangeEvent) => void): void
}
```

#### 1.3.5 日志系统设计
```typescript
// src/utils/Logger.ts
export class Logger {
    static debug(message: string, ...args: any[]): void
    static info(message: string, ...args: any[]): void
    static warn(message: string, ...args: any[]): void
    static error(message: string, error?: Error): void
}
```

### 1.4 Webview基础框架

#### 1.4.1 Webview管理器
```typescript
// src/webview/WebviewManager.ts
export class WebviewManager {
    private panels = new Map<string, vscode.WebviewPanel>();
    
    createPanel(viewType: string, title: string): WebviewPanel
    getPanel(viewType: string): WebviewPanel | undefined
    disposePanel(viewType: string): void
}
```

#### 1.4.2 消息通信系统
```typescript
// src/webview/MessageHandler.ts
export interface Message {
    type: string;
    payload?: any;
    id?: string;
}

export class MessageHandler {
    send(message: Message): void
    on(type: string, handler: (payload: any) => any): void
    request(type: string, payload?: any): Promise<any>
}
```

#### 1.4.3 前端状态管理
```typescript
// webviews/src/stores/index.ts
export const useAppStore = defineStore('app', {
    state: () => ({
        theme: 'dark',
        isLoading: false,
        error: null
    }),
    
    actions: {
        setTheme(theme: string) { this.theme = theme },
        setLoading(loading: boolean) { this.isLoading = loading },
        setError(error: string | null) { this.error = error }
    }
})
```

## 测试计划

### 1.1 基础功能测试
- [ ] 插件能够在VSCode中正常加载
- [ ] 扩展激活和停用正常工作
- [ ] 基础命令注册成功
- [ ] 配置读取和保存功能正常

### 1.2 开发环境测试
- [ ] 热重载功能正常工作
- [ ] 调试断点能够正常命中
- [ ] 测试用例能够正常运行
- [ ] 代码格式化和检查正常

### 1.3 架构测试
- [ ] 服务容器注册和获取正常
- [ ] 事件系统发布和订阅正常
- [ ] 消息通信双向正常
- [ ] 错误处理机制有效

## 验收标准

### 功能验收
1. 插件能够在VSCode中正常安装和激活
2. 基础命令能够正确注册和执行
3. 配置系统能够正常读取和保存设置
4. Webview能够正常显示和交互
5. 开发环境支持热重载和调试

### 代码质量验收
1. TypeScript编译无错误和警告
2. ESLint检查通过，无代码规范问题
3. 测试覆盖率达到80%以上
4. 所有公共API都有完整的类型定义
5. 代码结构清晰，模块职责明确

### 性能验收
1. 插件激活时间 < 1秒
2. 内存使用 < 50MB (空闲状态)
3. Webview首次加载时间 < 2秒
4. 命令响应时间 < 100ms
5. 热重载时间 < 3秒

## 交付物

1. **完整的项目结构**: 包含所有必要的目录和配置文件
2. **基础代码框架**: 核心服务和架构的基础实现
3. **开发环境**: 完整配置的开发、测试和调试环境
4. **文档**: 架构设计文档和开发指南
5. **测试用例**: 基础功能的单元测试和集成测试

## 下一阶段准备

完成本阶段后，为阶段2做好以下准备：
1. 确认所有基础架构稳定可靠
2. 验证开发环境和工具链正常工作
3. 完善文档和代码注释
4. 准备UI组件库的设计规范
5. 规划Webview界面的详细设计

这个阶段是整个项目的基础，必须确保质量和稳定性，为后续开发奠定坚实的基础。
