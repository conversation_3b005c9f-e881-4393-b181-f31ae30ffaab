# LazyCode 项目文档

本文档包含LazyCode项目的详细技术文档和开发指南。

## 项目概述

LazyCode是对Augment VSCode插件的逆向开发复刻项目。目标是通过分析origin目录中编译混淆后的代码，重新实现一个功能完整的AI编程助手VSCode插件。

## 原始插件分析

### 基本信息
- **插件名称**: Augment
- **版本**: 0.441.1
- **发布者**: Augment
- **描述**: AI编程助手，提供代码补全、聊天、自动修复等功能
- **支持的VSCode版本**: ^1.82.0
- **Node.js版本要求**: >= 18.15.0

### 核心功能模块

#### 1. 主要功能
- **AI聊天**: 与AI助手进行对话，获取编程帮助
- **代码补全**: 自动代码补全和建议
- **Next Edit**: 智能代码编辑建议
- **自动修复**: 代码错误自动修复
- **代码解释**: 解释选中的代码
- **测试生成**: 自动生成测试代码
- **文档生成**: 自动生成代码文档
- **远程代理**: 支持远程开发环境

#### 2. UI组件结构
- **主面板**: 聊天界面和工作区上下文管理
- **设置面板**: 插件配置和偏好设置
- **历史面板**: 聊天历史记录
- **Next Edit面板**: 代码编辑建议
- **差异视图**: 代码变更对比
- **自动修复面板**: 错误修复建议
- **规则编辑器**: 自定义规则管理
- **记忆编辑器**: AI记忆管理

#### 3. 技术架构
- **前端框架**: 基于Webview的现代前端框架（可能是Vue/React）
- **编辑器集成**: Monaco Editor集成
- **状态管理**: 全局状态管理系统
- **通信机制**: VSCode Extension API + Webview消息传递
- **网络层**: HTTP/WebSocket API调用
- **数据存储**: 本地存储 + 工作区存储

### 命令系统

插件注册了大量命令，主要分类：
1. **聊天相关**: 焦点切换、新建聊天等
2. **代码操作**: 解释、修复、测试、文档生成
3. **Next Edit**: 接受/拒绝建议、导航等
4. **设置管理**: 配置面板、快捷键设置
5. **账户管理**: 登录、登出、账户管理
6. **远程代理**: SSH配置、远程环境管理

### 配置系统

支持丰富的配置选项：
- **代码补全设置**: 自动补全开关、语言过滤等
- **Next Edit设置**: 后台建议、自动应用等
- **高级设置**: API令牌、服务器URL、集成配置
- **实验性功能**: 用户指南、MCP服务器等

### 视图容器和面板

- **活动栏容器**: Augment主入口
- **面板容器**: Next Edit功能面板
- **Webview视图**: 聊天界面和Next Edit界面

### 快捷键绑定

提供了完整的快捷键系统：
- **Ctrl+L / Cmd+L**: 打开Augment面板
- **Ctrl+I / Cmd+I**: 代码指令
- **Ctrl+Enter / Cmd+Enter**: 接受建议
- **Ctrl+Shift+A / Cmd+Shift+A**: 显示Augment命令
- 以及大量Next Edit相关的快捷键

### 菜单集成

- **编辑器右键菜单**: 发送到Augment子菜单
- **编辑器标题栏**: Next Edit操作按钮
- **命令面板**: 所有命令的集成
- **SCM标题**: Git提交消息生成

这个分析为我们的逆向开发提供了清晰的功能蓝图和技术方向。