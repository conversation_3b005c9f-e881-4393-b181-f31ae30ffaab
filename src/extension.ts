import * as vscode from 'vscode';
import { logger, LogLevel } from '@/utils/Logger';
import { serviceContainer, SERVICE_NAMES } from '@/services/ServiceContainer';
import { ConfigurationService } from '@/services/ConfigurationService';
import { registerCommands } from '@/commands';

/**
 * Extension activation function
 */
export async function activate(context: vscode.ExtensionContext): Promise<void> {
  try {
    logger.info('LazyCode extension is activating...');

    // Set log level based on environment
    if (process.env.NODE_ENV === 'development') {
      logger.setLogLevel(LogLevel.DEBUG);
    }

    // Initialize core services
    await initializeServices(context);

    // Register commands
    await registerCommandHandlers(context);

    // Register providers
    await registerProviders(context);

    // Setup event listeners
    setupEventListeners(context);

    // Show welcome message for first-time users
    await showWelcomeMessage(context);

    logger.info('LazyCode extension activated successfully');
  } catch (error) {
    logger.error('Failed to activate LazyCode extension', error as Error);

    // Show error message to user
    void vscode.window
      .showErrorMessage(
        'Failed to activate LazyCode extension. Please check the output panel for details.',
        'Show Output'
      )
      .then(selection => {
        if (selection === 'Show Output') {
          logger.show();
        }
      });

    throw error;
  }
}

/**
 * Extension deactivation function
 */
export function deactivate(): void {
  try {
    logger.info('LazyCode extension is deactivating...');

    // Dispose all services
    serviceContainer.dispose();

    // Dispose logger
    logger.dispose();

    logger.info('LazyCode extension deactivated successfully');
  } catch (error) {
    console.error('Error during extension deactivation:', error);
  }
}

/**
 * Initialize core services
 */
async function initializeServices(context: vscode.ExtensionContext): Promise<void> {
  logger.debug('Initializing services...');

  // Register configuration service
  serviceContainer.registerInstance(SERVICE_NAMES.CONFIGURATION, new ConfigurationService());

  // TODO: Register other services as they are implemented
  // - WebviewManager
  // - ApiClient
  // - ChatService
  // - CompletionService

  logger.debug('Services initialized');
}

/**
 * Register command handlers
 */
async function registerCommandHandlers(context: vscode.ExtensionContext): Promise<void> {
  logger.debug('Registering commands...');

  // Import and register all commands
  await registerCommands(context);

  logger.debug('Commands registered');
}

/**
 * Register language providers
 */
async function registerProviders(context: vscode.ExtensionContext): Promise<void> {
  logger.debug('Registering providers...');

  // TODO: Register providers as they are implemented
  // - CompletionProvider
  // - HoverProvider
  // - CodeActionProvider

  logger.debug('Providers registered');
}

/**
 * Setup global event listeners
 */
function setupEventListeners(context: vscode.ExtensionContext): void {
  logger.debug('Setting up event listeners...');

  // Listen for active editor changes
  context.subscriptions.push(
    vscode.window.onDidChangeActiveTextEditor(editor => {
      if (editor) {
        logger.debug(`Active editor changed: ${editor.document.fileName}`);
      }
    })
  );

  // Listen for text document changes
  context.subscriptions.push(
    vscode.workspace.onDidChangeTextDocument(event => {
      logger.debug(`Document changed: ${event.document.fileName}`);
    })
  );

  // Listen for workspace folder changes
  context.subscriptions.push(
    vscode.workspace.onDidChangeWorkspaceFolders(event => {
      logger.debug('Workspace folders changed', {
        added: event.added.length,
        removed: event.removed.length,
      });
    })
  );

  // Listen for configuration changes
  context.subscriptions.push(
    vscode.workspace.onDidChangeConfiguration(event => {
      if (event.affectsConfiguration('lazycode')) {
        logger.debug('LazyCode configuration changed');
      }
    })
  );

  logger.debug('Event listeners set up');
}

/**
 * Show welcome message for first-time users
 */
async function showWelcomeMessage(context: vscode.ExtensionContext): Promise<void> {
  const hasShownWelcome = context.globalState.get<boolean>('hasShownWelcome', false);

  if (!hasShownWelcome) {
    const selection = await vscode.window.showInformationMessage(
      'Welcome to LazyCode! Your AI-powered coding assistant is ready to help.',
      'Open Chat',
      'Settings',
      "Don't show again"
    );

    switch (selection) {
      case 'Open Chat':
        await vscode.commands.executeCommand('lazycode.chat.focus');
        break;
      case 'Settings':
        await vscode.commands.executeCommand('lazycode.settings.open');
        break;
      case "Don't show again":
        await context.globalState.update('hasShownWelcome', true);
        break;
    }

    if (selection !== "Don't show again") {
      await context.globalState.update('hasShownWelcome', true);
    }
  }
}

/**
 * Get extension version
 */
export function getExtensionVersion(): string {
  const extension = vscode.extensions.getExtension('lazycode.lazycode');
  return extension?.packageJSON?.version ?? 'unknown';
}

/**
 * Check if extension is in development mode
 */
export function isDevelopmentMode(): boolean {
  return process.env.NODE_ENV === 'development' || vscode.env.machineId === 'someValue'; // VSCode development host
}
