import * as vscode from 'vscode';
import { IEventEmitter } from '@/types';

export class EventEmitter<T = any> implements IEventEmitter<T> {
  private listeners = new Map<string, Set<(data: T) => void>>();
  private disposables: vscode.Disposable[] = [];

  public on(event: string, listener: (data: T) => void): vscode.Disposable {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }

    const eventListeners = this.listeners.get(event)!;
    eventListeners.add(listener);

    const disposable = new vscode.Disposable(() => {
      eventListeners.delete(listener);
      if (eventListeners.size === 0) {
        this.listeners.delete(event);
      }
    });

    this.disposables.push(disposable);
    return disposable;
  }

  public emit(event: string, data?: T): void {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      // Create a copy of listeners to avoid issues if listeners are modified during iteration
      const listenersArray = Array.from(eventListeners);
      for (const listener of listenersArray) {
        try {
          listener(data as T);
        } catch (error) {
          // eslint-disable-next-line no-console
          console.error(`Error in event listener for event '${event}':`, error);
        }
      }
    }
  }

  public once(event: string, listener: (data: T) => void): vscode.Disposable {
    const onceListener = (data: T): void => {
      listener(data);
      disposable.dispose();
    };

    const disposable = this.on(event, onceListener);
    return disposable;
  }

  public removeAllListeners(event?: string): void {
    if (event) {
      this.listeners.delete(event);
    } else {
      this.listeners.clear();
    }
  }

  public listenerCount(event: string): number {
    const eventListeners = this.listeners.get(event);
    return eventListeners ? eventListeners.size : 0;
  }

  public eventNames(): string[] {
    return Array.from(this.listeners.keys());
  }

  public dispose(): void {
    // Dispose all registered disposables
    this.disposables.forEach(disposable => disposable.dispose());
    this.disposables = [];

    // Clear all listeners
    this.listeners.clear();
  }
}

// Typed event emitter for specific event types
export class TypedEventEmitter<TEventMap extends Record<string, any>> {
  private emitter = new EventEmitter();

  public on<K extends keyof TEventMap>(
    event: K,
    listener: (data: TEventMap[K]) => void
  ): vscode.Disposable {
    return this.emitter.on(event as string, listener);
  }

  public emit<K extends keyof TEventMap>(event: K, data: TEventMap[K]): void {
    this.emitter.emit(event as string, data);
  }

  public once<K extends keyof TEventMap>(
    event: K,
    listener: (data: TEventMap[K]) => void
  ): vscode.Disposable {
    return this.emitter.once(event as string, listener);
  }

  public removeAllListeners<K extends keyof TEventMap>(event?: K): void {
    this.emitter.removeAllListeners(event as string);
  }

  public listenerCount<K extends keyof TEventMap>(event: K): number {
    return this.emitter.listenerCount(event as string);
  }

  public dispose(): void {
    this.emitter.dispose();
  }
}
