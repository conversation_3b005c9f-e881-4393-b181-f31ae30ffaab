import * as vscode from 'vscode';
import { ILogger } from '@/types';

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
}

export class Logger implements ILogger {
  private static instance: Logger;
  private outputChannel: vscode.OutputChannel;
  private logLevel: LogLevel = LogLevel.INFO;

  private constructor() {
    this.outputChannel = vscode.window.createOutputChannel('LazyCode');
  }

  public static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  public setLogLevel(level: LogLevel): void {
    this.logLevel = level;
  }

  public debug(message: string, ...args: any[]): void {
    if (this.logLevel <= LogLevel.DEBUG) {
      this.log('DEBUG', message, ...args);
    }
  }

  public info(message: string, ...args: any[]): void {
    if (this.logLevel <= LogLevel.INFO) {
      this.log('INFO', message, ...args);
    }
  }

  public warn(message: string, ...args: any[]): void {
    if (this.logLevel <= LogLevel.WARN) {
      this.log('WARN', message, ...args);
    }
  }

  public error(message: string, error?: Error): void {
    if (this.logLevel <= LogLevel.ERROR) {
      this.log('ERROR', message, error);
      if (error) {
        this.log('ERROR', `Stack trace: ${error.stack}`);
      }
    }
  }

  private log(level: string, message: string, ...args: any[]): void {
    const timestamp = new Date().toISOString();
    const formattedMessage = `[${timestamp}] [${level}] ${message}`;
    
    // Log to output channel
    this.outputChannel.appendLine(formattedMessage);
    
    // Log additional arguments if any
    if (args.length > 0) {
      args.forEach(arg => {
        if (arg instanceof Error) {
          this.outputChannel.appendLine(`  Error: ${arg.message}`);
          if (arg.stack) {
            this.outputChannel.appendLine(`  Stack: ${arg.stack}`);
          }
        } else if (typeof arg === 'object') {
          this.outputChannel.appendLine(`  ${JSON.stringify(arg, null, 2)}`);
        } else {
          this.outputChannel.appendLine(`  ${String(arg)}`);
        }
      });
    }

    // Also log to console in development
    if (process.env.NODE_ENV === 'development') {
      switch (level) {
        case 'DEBUG':
          console.debug(formattedMessage, ...args);
          break;
        case 'INFO':
          console.info(formattedMessage, ...args);
          break;
        case 'WARN':
          console.warn(formattedMessage, ...args);
          break;
        case 'ERROR':
          console.error(formattedMessage, ...args);
          break;
      }
    }
  }

  public show(): void {
    this.outputChannel.show();
  }

  public dispose(): void {
    this.outputChannel.dispose();
  }
}

// Export singleton instance
export const logger = Logger.getInstance();
