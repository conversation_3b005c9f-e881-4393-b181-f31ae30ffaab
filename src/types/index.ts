/**
 * Core types for LazyCode extension
 */

import * as vscode from 'vscode';

// Configuration types
export interface LazyCodeConfiguration {
  completions: {
    enableAutomaticCompletions: boolean;
    enableQuickSuggestions: boolean;
  };
  chat: {
    enableEmptyFileHint: boolean;
  };
  advanced: {
    apiToken: string;
    apiUrl: string;
  };
}

// Message types for webview communication
export interface WebviewMessage {
  type: string;
  payload?: any;
  id?: string;
}

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: number;
  metadata?: {
    tokens?: number;
    model?: string;
    error?: string;
  };
}

export interface ChatSession {
  id: string;
  title: string;
  messages: ChatMessage[];
  createdAt: number;
  updatedAt: number;
}

// Service interfaces
export interface ILogger {
  debug(message: string, ...args: any[]): void;
  info(message: string, ...args: any[]): void;
  warn(message: string, ...args: any[]): void;
  error(message: string, error?: Error): void;
}

export interface IConfigurationService {
  get<T>(key: string, defaultValue?: T): T;
  set(key: string, value: any): Promise<void>;
  onDidChange(callback: (e: vscode.ConfigurationChangeEvent) => void): vscode.Disposable;
}

export interface IEventEmitter<T = any> {
  on(event: string, listener: (data: T) => void): vscode.Disposable;
  emit(event: string, data?: T): void;
  dispose(): void;
}

export interface IWebviewManager {
  createPanel(viewType: string, title: string, options?: WebviewOptions): WebviewPanel;
  getPanel(viewType: string): WebviewPanel | undefined;
  disposePanel(viewType: string): void;
  dispose(): void;
}

export interface WebviewOptions {
  enableScripts?: boolean;
  retainContextWhenHidden?: boolean;
  localResourceRoots?: vscode.Uri[];
}

export interface WebviewPanel {
  panel: vscode.WebviewPanel;
  postMessage(message: WebviewMessage): Promise<boolean>;
  onDidReceiveMessage(handler: (message: WebviewMessage) => void): vscode.Disposable;
  dispose(): void;
}

// API types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface CompletionRequest {
  prompt: string;
  context?: {
    language: string;
    filePath: string;
    selection?: string;
    surroundingCode?: string;
  };
  options?: {
    maxTokens?: number;
    temperature?: number;
    model?: string;
  };
}

export interface CompletionResponse {
  completion: string;
  confidence: number;
  metadata?: {
    model: string;
    tokens: number;
    latency: number;
  };
}

export interface ChatRequest {
  message: string;
  sessionId?: string;
  context?: {
    selectedCode?: string;
    filePath?: string;
    language?: string;
    workspaceInfo?: WorkspaceInfo;
  };
  options?: {
    stream?: boolean;
    model?: string;
  };
}

export interface ChatResponse {
  message: string;
  sessionId: string;
  metadata?: {
    model: string;
    tokens: number;
    latency: number;
  };
}

export interface WorkspaceInfo {
  rootPath: string;
  files: string[];
  languages: string[];
  dependencies?: Record<string, string>;
}

// Command types
export interface CommandContext {
  editor?: vscode.TextEditor;
  selection?: vscode.Selection;
  document?: vscode.TextDocument;
  workspaceFolder?: vscode.WorkspaceFolder;
}

export interface CommandHandler {
  (context: CommandContext, ...args: any[]): Promise<void> | void;
}

// Provider types
export interface CompletionProvider extends vscode.InlineCompletionItemProvider {
  // Additional methods if needed
}

export interface HoverProvider extends vscode.HoverProvider {
  // Additional methods if needed
}

// Error types
export class LazyCodeError extends Error {
  constructor(
    message: string,
    public code?: string,
    public statusCode?: number,
    public cause?: Error
  ) {
    super(message);
    this.name = 'LazyCodeError';
  }
}

// Utility types
export type Disposable = vscode.Disposable;
export type ExtensionContext = vscode.ExtensionContext;

// Re-export commonly used VSCode types
export {
  vscode,
  type TextDocument,
  type TextEditor,
  type Selection,
  type Range,
  type Position,
  type WorkspaceFolder,
  type ConfigurationChangeEvent,
} from 'vscode';
