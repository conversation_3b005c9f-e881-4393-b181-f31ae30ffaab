import * as vscode from 'vscode';
import { logger } from '@/utils/Logger';

export type ServiceFactory<T> = () => T | Promise<T>;
export type ServiceInstance<T> = T | Promise<T>;

export interface ServiceDefinition<T = any> {
  factory: ServiceFactory<T>;
  singleton: boolean;
  instance?: ServiceInstance<T>;
}

export class ServiceContainer {
  private static instance: ServiceContainer;
  private services = new Map<string, ServiceDefinition>();
  private disposables: vscode.Disposable[] = [];

  private constructor() {}

  public static getInstance(): ServiceContainer {
    if (!ServiceContainer.instance) {
      ServiceContainer.instance = new ServiceContainer();
    }
    return ServiceContainer.instance;
  }

  /**
   * Register a service with the container
   */
  public register<T>(
    name: string,
    factory: ServiceFactory<T>,
    options: { singleton?: boolean } = {}
  ): void {
    const { singleton = true } = options;

    if (this.services.has(name)) {
      logger.warn(`Service '${name}' is already registered. Overwriting.`);
    }

    this.services.set(name, {
      factory,
      singleton,
    });

    logger.debug(`Service '${name}' registered (singleton: ${singleton})`);
  }

  /**
   * Register a service instance directly
   */
  public registerInstance<T>(name: string, instance: T): void {
    this.services.set(name, {
      factory: () => instance,
      singleton: true,
      instance,
    });

    logger.debug(`Service instance '${name}' registered`);
  }

  /**
   * Get a service from the container
   */
  public async get<T>(name: string): Promise<T> {
    const serviceDefinition = this.services.get(name);
    
    if (!serviceDefinition) {
      throw new Error(`Service '${name}' is not registered`);
    }

    // If it's a singleton and we have an instance, return it
    if (serviceDefinition.singleton && serviceDefinition.instance) {
      return await serviceDefinition.instance;
    }

    // Create new instance
    try {
      const instance = await serviceDefinition.factory();
      
      // Store instance if it's a singleton
      if (serviceDefinition.singleton) {
        serviceDefinition.instance = instance;
      }

      logger.debug(`Service '${name}' instantiated`);
      return instance;
    } catch (error) {
      logger.error(`Failed to instantiate service '${name}'`, error as Error);
      throw error;
    }
  }

  /**
   * Get a service synchronously (only works if the service is already instantiated)
   */
  public getSync<T>(name: string): T {
    const serviceDefinition = this.services.get(name);
    
    if (!serviceDefinition) {
      throw new Error(`Service '${name}' is not registered`);
    }

    if (!serviceDefinition.instance) {
      throw new Error(`Service '${name}' is not yet instantiated. Use get() instead.`);
    }

    if (serviceDefinition.instance instanceof Promise) {
      throw new Error(`Service '${name}' is still being instantiated. Use get() instead.`);
    }

    return serviceDefinition.instance;
  }

  /**
   * Check if a service is registered
   */
  public has(name: string): boolean {
    return this.services.has(name);
  }

  /**
   * Check if a service is instantiated
   */
  public isInstantiated(name: string): boolean {
    const serviceDefinition = this.services.get(name);
    return serviceDefinition?.instance !== undefined;
  }

  /**
   * Unregister a service
   */
  public unregister(name: string): void {
    const serviceDefinition = this.services.get(name);
    
    if (serviceDefinition?.instance) {
      // Try to dispose the instance if it has a dispose method
      const instance = serviceDefinition.instance;
      if (instance && typeof instance === 'object' && 'dispose' in instance) {
        try {
          (instance as any).dispose();
        } catch (error) {
          logger.warn(`Error disposing service '${name}'`, error);
        }
      }
    }

    this.services.delete(name);
    logger.debug(`Service '${name}' unregistered`);
  }

  /**
   * Get all registered service names
   */
  public getServiceNames(): string[] {
    return Array.from(this.services.keys());
  }

  /**
   * Clear all services
   */
  public clear(): void {
    // Dispose all services that have a dispose method
    for (const [name, serviceDefinition] of this.services) {
      if (serviceDefinition.instance) {
        const instance = serviceDefinition.instance;
        if (instance && typeof instance === 'object' && 'dispose' in instance) {
          try {
            (instance as any).dispose();
          } catch (error) {
            logger.warn(`Error disposing service '${name}'`, error);
          }
        }
      }
    }

    this.services.clear();
    logger.debug('All services cleared');
  }

  /**
   * Dispose the service container
   */
  public dispose(): void {
    this.clear();
    this.disposables.forEach(disposable => disposable.dispose());
    this.disposables = [];
  }
}

// Export singleton instance
export const serviceContainer = ServiceContainer.getInstance();

// Service name constants
export const SERVICE_NAMES = {
  CONFIGURATION: 'configuration',
  WEBVIEW_MANAGER: 'webviewManager',
  API_CLIENT: 'apiClient',
  CHAT_SERVICE: 'chatService',
  COMPLETION_SERVICE: 'completionService',
} as const;
