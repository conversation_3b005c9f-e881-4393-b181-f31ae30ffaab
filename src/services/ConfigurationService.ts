import * as vscode from 'vscode';
import { IConfigurationService, LazyCodeConfiguration } from '@/types';
import { logger } from '@/utils/Logger';

export class ConfigurationService implements IConfigurationService {
  private static readonly CONFIGURATION_SECTION = 'lazycode';
  private disposables: vscode.Disposable[] = [];

  constructor() {
    // Listen for configuration changes
    this.disposables.push(
      vscode.workspace.onDidChangeConfiguration(e => {
        if (e.affectsConfiguration(ConfigurationService.CONFIGURATION_SECTION)) {
          logger.debug('Configuration changed');
        }
      })
    );
  }

  public get<T>(key: string, defaultValue?: T): T {
    const config = vscode.workspace.getConfiguration(ConfigurationService.CONFIGURATION_SECTION);
    const value = config.get<T>(key);
    
    if (value === undefined && defaultValue !== undefined) {
      return defaultValue;
    }
    
    return value as T;
  }

  public async set(key: string, value: any, target?: vscode.ConfigurationTarget): Promise<void> {
    try {
      const config = vscode.workspace.getConfiguration(ConfigurationService.CONFIGURATION_SECTION);
      await config.update(key, value, target ?? vscode.ConfigurationTarget.Global);
      logger.debug(`Configuration updated: ${key} = ${JSON.stringify(value)}`);
    } catch (error) {
      logger.error(`Failed to update configuration: ${key}`, error as Error);
      throw error;
    }
  }

  public onDidChange(
    callback: (e: vscode.ConfigurationChangeEvent) => void
  ): vscode.Disposable {
    return vscode.workspace.onDidChangeConfiguration(e => {
      if (e.affectsConfiguration(ConfigurationService.CONFIGURATION_SECTION)) {
        callback(e);
      }
    });
  }

  // Typed getters for specific configuration sections
  public getCompletionsConfig(): LazyCodeConfiguration['completions'] {
    return {
      enableAutomaticCompletions: this.get('completions.enableAutomaticCompletions', true),
      enableQuickSuggestions: this.get('completions.enableQuickSuggestions', true),
    };
  }

  public getChatConfig(): LazyCodeConfiguration['chat'] {
    return {
      enableEmptyFileHint: this.get('chat.enableEmptyFileHint', true),
    };
  }

  public getAdvancedConfig(): LazyCodeConfiguration['advanced'] {
    return {
      apiToken: this.get('advanced.apiToken', ''),
      apiUrl: this.get('advanced.apiUrl', ''),
    };
  }

  public getFullConfiguration(): LazyCodeConfiguration {
    return {
      completions: this.getCompletionsConfig(),
      chat: this.getChatConfig(),
      advanced: this.getAdvancedConfig(),
    };
  }

  // Typed setters for specific configuration sections
  public async setCompletionsConfig(
    config: Partial<LazyCodeConfiguration['completions']>
  ): Promise<void> {
    const promises: Promise<void>[] = [];
    
    if (config.enableAutomaticCompletions !== undefined) {
      promises.push(this.set('completions.enableAutomaticCompletions', config.enableAutomaticCompletions));
    }
    
    if (config.enableQuickSuggestions !== undefined) {
      promises.push(this.set('completions.enableQuickSuggestions', config.enableQuickSuggestions));
    }
    
    await Promise.all(promises);
  }

  public async setChatConfig(
    config: Partial<LazyCodeConfiguration['chat']>
  ): Promise<void> {
    const promises: Promise<void>[] = [];
    
    if (config.enableEmptyFileHint !== undefined) {
      promises.push(this.set('chat.enableEmptyFileHint', config.enableEmptyFileHint));
    }
    
    await Promise.all(promises);
  }

  public async setAdvancedConfig(
    config: Partial<LazyCodeConfiguration['advanced']>
  ): Promise<void> {
    const promises: Promise<void>[] = [];
    
    if (config.apiToken !== undefined) {
      promises.push(this.set('advanced.apiToken', config.apiToken));
    }
    
    if (config.apiUrl !== undefined) {
      promises.push(this.set('advanced.apiUrl', config.apiUrl));
    }
    
    await Promise.all(promises);
  }

  // Validation methods
  public validateConfiguration(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    const config = this.getFullConfiguration();

    // Validate API configuration
    if (!config.advanced.apiToken && !config.advanced.apiUrl) {
      errors.push('Either API token or API URL must be configured');
    }

    if (config.advanced.apiUrl && !this.isValidUrl(config.advanced.apiUrl)) {
      errors.push('API URL must be a valid URL');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  public dispose(): void {
    this.disposables.forEach(disposable => disposable.dispose());
    this.disposables = [];
  }
}
