import * as vscode from 'vscode';
import { logger } from '@/utils/Logger';
import { CommandHandler, CommandContext } from '@/types';

// Import command handlers
import { chatCommands } from './chatCommands';
import { codeCommands } from './codeCommands';
import { settingsCommands } from './settingsCommands';

/**
 * Command registration helper
 */
class CommandRegistry {
  private commands = new Map<string, CommandHandler>();

  public register(commandId: string, handler: CommandHandler): void {
    if (this.commands.has(commandId)) {
      logger.warn(`Command '${commandId}' is already registered. Overwriting.`);
    }
    
    this.commands.set(commandId, handler);
    logger.debug(`Command '${commandId}' registered`);
  }

  public getHandler(commandId: string): CommandHandler | undefined {
    return this.commands.get(commandId);
  }

  public getAllCommands(): string[] {
    return Array.from(this.commands.keys());
  }

  public createVSCodeCommand(commandId: string): (...args: any[]) => Promise<void> {
    return async (...args: any[]) => {
      const handler = this.getHandler(commandId);
      if (!handler) {
        logger.error(`No handler found for command: ${commandId}`);
        return;
      }

      try {
        // Create command context
        const context = this.createCommandContext();
        
        logger.debug(`Executing command: ${commandId}`, { args });
        await handler(context, ...args);
        logger.debug(`Command executed successfully: ${commandId}`);
      } catch (error) {
        logger.error(`Error executing command '${commandId}'`, error as Error);
        
        // Show error to user
        void vscode.window.showErrorMessage(
          `Failed to execute command: ${error instanceof Error ? error.message : 'Unknown error'}`
        );
      }
    };
  }

  private createCommandContext(): CommandContext {
    const editor = vscode.window.activeTextEditor;
    const workspaceFolder = editor?.document 
      ? vscode.workspace.getWorkspaceFolder(editor.document.uri)
      : vscode.workspace.workspaceFolders?.[0];

    return {
      editor,
      selection: editor?.selection,
      document: editor?.document,
      workspaceFolder,
    };
  }
}

const commandRegistry = new CommandRegistry();

/**
 * Register all commands with VSCode
 */
export async function registerCommands(context: vscode.ExtensionContext): Promise<void> {
  logger.debug('Registering commands...');

  // Register command handlers
  registerChatCommands();
  registerCodeCommands();
  registerSettingsCommands();

  // Register all commands with VSCode
  for (const commandId of commandRegistry.getAllCommands()) {
    const disposable = vscode.commands.registerCommand(
      commandId,
      commandRegistry.createVSCodeCommand(commandId)
    );
    context.subscriptions.push(disposable);
  }

  logger.info(`Registered ${commandRegistry.getAllCommands().length} commands`);
}

/**
 * Register chat-related commands
 */
function registerChatCommands(): void {
  // Focus chat panel
  commandRegistry.register('lazycode.chat.focus', chatCommands.focusChat);
  
  // Start new chat
  commandRegistry.register('lazycode.chat.newChat', chatCommands.newChat);
  
  // Clear chat history
  commandRegistry.register('lazycode.chat.clearHistory', chatCommands.clearHistory);
}

/**
 * Register code-related commands
 */
function registerCodeCommands(): void {
  // Explain code
  commandRegistry.register('lazycode.code.explain', codeCommands.explainCode);
  
  // Fix code
  commandRegistry.register('lazycode.code.fix', codeCommands.fixCode);
  
  // Generate tests
  commandRegistry.register('lazycode.code.test', codeCommands.generateTests);
  
  // Generate documentation
  commandRegistry.register('lazycode.code.document', codeCommands.generateDocumentation);
}

/**
 * Register settings-related commands
 */
function registerSettingsCommands(): void {
  // Open settings
  commandRegistry.register('lazycode.settings.open', settingsCommands.openSettings);
  
  // Reset settings
  commandRegistry.register('lazycode.settings.reset', settingsCommands.resetSettings);
}

/**
 * Get command registry instance (for testing)
 */
export function getCommandRegistry(): CommandRegistry {
  return commandRegistry;
}
