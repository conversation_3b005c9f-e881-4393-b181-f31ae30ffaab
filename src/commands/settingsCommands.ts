import * as vscode from 'vscode';
import { CommandContext } from '@/types';
import { logger } from '@/utils/Logger';
import { serviceContainer, SERVICE_NAMES } from '@/services/ServiceContainer';
import { ConfigurationService } from '@/services/ConfigurationService';

export const settingsCommands = {
  /**
   * Open LazyCode settings
   */
  async openSettings(_context: CommandContext): Promise<void> {
    logger.debug('Opening LazyCode settings');

    try {
      // TODO: Implement settings webview panel
      // For now, open VSCode settings filtered to LazyCode
      await vscode.commands.executeCommand('workbench.action.openSettings', 'lazycode');

      // Future implementation:
      // const webviewManager = await serviceContainer.get<IWebviewManager>(SERVICE_NAMES.WEBVIEW_MANAGER);
      // const settingsPanel = webviewManager.getPanel('lazycode-settings');
      // if (settingsPanel) {
      //   settingsPanel.panel.reveal();
      // } else {
      //   await webviewManager.createPanel('lazycode-settings', 'LazyCode Settings');
      // }
    } catch (error) {
      logger.error('Failed to open settings', error as Error);
      throw error;
    }
  },

  /**
   * Reset LazyCode settings to defaults
   */
  async resetSettings(_context: CommandContext): Promise<void> {
    logger.debug('Resetting LazyCode settings');

    try {
      const confirmation = await vscode.window.showWarningMessage(
        'Are you sure you want to reset all LazyCode settings to their default values?',
        { modal: true },
        'Reset Settings'
      );

      if (confirmation === 'Reset Settings') {
        const configService = await serviceContainer.get<ConfigurationService>(
          SERVICE_NAMES.CONFIGURATION
        );

        // Reset all configuration sections
        await configService.setCompletionsConfig({
          enableAutomaticCompletions: true,
          enableQuickSuggestions: true,
        });

        await configService.setChatConfig({
          enableEmptyFileHint: true,
        });

        await configService.setAdvancedConfig({
          apiToken: '',
          apiUrl: '',
        });

        await vscode.window.showInformationMessage('LazyCode settings have been reset to defaults');
        logger.info('Settings reset to defaults');
      }
    } catch (error) {
      logger.error('Failed to reset settings', error as Error);
      throw error;
    }
  },

  /**
   * Configure API settings
   */
  async configureApi(_context: CommandContext): Promise<void> {
    logger.debug('Configuring API settings');

    try {
      const configService = await serviceContainer.get<ConfigurationService>(
        SERVICE_NAMES.CONFIGURATION
      );
      const currentConfig = configService.getAdvancedConfig();

      // Get API URL
      const apiUrl = await vscode.window.showInputBox({
        prompt: 'Enter the API URL for LazyCode service',
        value: currentConfig.apiUrl,
        placeHolder: 'https://api.example.com',
        validateInput: value => {
          if (value && !this.isValidUrl(value)) {
            return 'Please enter a valid URL';
          }
          return undefined;
        },
      });

      if (apiUrl === undefined) {
        return; // User cancelled
      }

      // Get API token
      const apiToken = await vscode.window.showInputBox({
        prompt: 'Enter your API token',
        value: currentConfig.apiToken,
        placeHolder: 'Your API token',
        password: true,
      });

      if (apiToken === undefined) {
        return; // User cancelled
      }

      // Save configuration
      await configService.setAdvancedConfig({
        apiUrl,
        apiToken,
      });

      // Validate configuration
      const validation = configService.validateConfiguration();
      if (validation.isValid) {
        await vscode.window.showInformationMessage('API configuration saved successfully');
      } else {
        await vscode.window.showWarningMessage(
          `Configuration saved with warnings: ${validation.errors.join(', ')}`
        );
      }

      logger.info('API configuration updated');
    } catch (error) {
      logger.error('Failed to configure API settings', error as Error);
      throw error;
    }
  },

  /**
   * Toggle automatic completions
   */
  async toggleAutomaticCompletions(_context: CommandContext): Promise<void> {
    logger.debug('Toggling automatic completions');

    try {
      const configService = await serviceContainer.get<ConfigurationService>(
        SERVICE_NAMES.CONFIGURATION
      );
      const currentConfig = configService.getCompletionsConfig();

      const newValue = !currentConfig.enableAutomaticCompletions;
      await configService.setCompletionsConfig({
        enableAutomaticCompletions: newValue,
      });

      const status = newValue ? 'enabled' : 'disabled';
      await vscode.window.showInformationMessage(`Automatic completions ${status}`);

      logger.info(`Automatic completions ${status}`);
    } catch (error) {
      logger.error('Failed to toggle automatic completions', error as Error);
      throw error;
    }
  },

  /**
   * Show configuration status
   */
  async showConfigurationStatus(context: CommandContext): Promise<void> {
    logger.debug('Showing configuration status');

    try {
      const configService = await serviceContainer.get<ConfigurationService>(
        SERVICE_NAMES.CONFIGURATION
      );
      const config = configService.getFullConfiguration();
      const validation = configService.validateConfiguration();

      const statusItems: vscode.QuickPickItem[] = [
        {
          label: '$(gear) General Settings',
          description: 'View general configuration',
          detail: `Completions: ${config.completions.enableAutomaticCompletions ? 'On' : 'Off'}, Quick Suggestions: ${config.completions.enableQuickSuggestions ? 'On' : 'Off'}`,
        },
        {
          label: '$(comment-discussion) Chat Settings',
          description: 'View chat configuration',
          detail: `Empty File Hint: ${config.chat.enableEmptyFileHint ? 'On' : 'Off'}`,
        },
        {
          label: '$(key) API Settings',
          description: 'View API configuration',
          detail: `URL: ${config.advanced.apiUrl || 'Not set'}, Token: ${config.advanced.apiToken ? 'Set' : 'Not set'}`,
        },
        {
          label: validation.isValid
            ? '$(check) Configuration Valid'
            : '$(warning) Configuration Issues',
          description: validation.isValid
            ? 'All settings are valid'
            : 'Some settings need attention',
          detail: validation.isValid ? 'Ready to use' : validation.errors.join(', '),
        },
      ];

      const selected = await vscode.window.showQuickPick(statusItems, {
        placeHolder: 'LazyCode Configuration Status',
        ignoreFocusOut: true,
      });

      if (selected) {
        // Handle selection if needed
        if (selected.label.includes('API Settings')) {
          await this.configureApi(context);
        } else if (selected.label.includes('General Settings')) {
          await this.openSettings(context);
        }
      }
    } catch (error) {
      logger.error('Failed to show configuration status', error as Error);
      throw error;
    }
  },

  /**
   * Export configuration
   */
  async exportConfiguration(_context: CommandContext): Promise<void> {
    logger.debug('Exporting configuration');

    try {
      const configService = await serviceContainer.get<ConfigurationService>(
        SERVICE_NAMES.CONFIGURATION
      );
      const config = configService.getFullConfiguration();

      // Remove sensitive information
      const exportConfig = {
        ...config,
        advanced: {
          ...config.advanced,
          apiToken: config.advanced.apiToken ? '[REDACTED]' : '',
        },
      };

      const configJson = JSON.stringify(exportConfig, null, 2);

      const action = await vscode.window.showInformationMessage(
        'Configuration exported successfully!',
        'Copy to Clipboard',
        'Save to File'
      );

      if (action === 'Copy to Clipboard') {
        await vscode.env.clipboard.writeText(configJson);
        await vscode.window.showInformationMessage('Configuration copied to clipboard');
      } else if (action === 'Save to File') {
        const uri = await vscode.window.showSaveDialog({
          defaultUri: vscode.Uri.file('lazycode-config.json'),
          filters: {
            'JSON Files': ['json'],
            'All Files': ['*'],
          },
        });

        if (uri) {
          await vscode.workspace.fs.writeFile(uri, Buffer.from(configJson, 'utf8'));
          await vscode.window.showInformationMessage(`Configuration saved to ${uri.fsPath}`);
        }
      }
    } catch (error) {
      logger.error('Failed to export configuration', error as Error);
      throw error;
    }
  },

  /**
   * Helper method to validate URL
   */
  isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  },
};
