{"compilerOptions": {"target": "ES2020", "lib": ["ES2020"], "module": "commonjs", "moduleResolution": "node", "outDir": "out", "rootDir": "src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/types/*": ["src/types/*"], "@/utils/*": ["src/utils/*"], "@/services/*": ["src/services/*"], "@/commands/*": ["src/commands/*"], "@/providers/*": ["src/providers/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "out", "webviews", "test"]}