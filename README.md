# LazyCode

LazyCode is an AI-powered coding assistant for Visual Studio Code, designed to enhance your development experience with intelligent code completion, chat assistance, and automated code analysis.

## Features

### 🤖 AI Chat Assistant
- Interactive chat interface for coding questions
- Context-aware responses based on your current code
- Support for multiple programming languages

### ⚡ Smart Code Completion
- Intelligent inline code suggestions
- Context-aware completions
- Customizable completion settings

### 🔧 Code Analysis & Fixes
- Automatic code explanation
- Smart error detection and fixes
- Code optimization suggestions

### 📝 Documentation & Testing
- Automatic documentation generation
- Test case generation
- Code comment suggestions

### ⚙️ Customizable Settings
- Flexible configuration options
- API integration support
- Personalized preferences

## Installation

1. Open Visual Studio Code
2. Go to Extensions (Ctrl+Shift+X)
3. Search for "LazyCode"
4. Click Install

## Quick Start

1. **Open Chat**: Press `Ctrl+L` (or `Cmd+L` on Mac) to open the LazyCode chat panel
2. **Explain Code**: Select code and press `Ctrl+Shift+E` to get an explanation
3. **Configure Settings**: Use `Ctrl+Shift+P` and search for "LazyCode: Open Settings"

## Commands

| Command | Shortcut | Description |
|---------|----------|-------------|
| LazyCode: Open Chat | `Ctrl+L` | Open the chat interface |
| LazyCode: Explain Code | `Ctrl+Shift+E` | Explain selected code |
| LazyCode: Fix Code | - | Suggest fixes for selected code |
| LazyCode: Generate Tests | - | Generate test cases |
| LazyCode: Generate Documentation | - | Generate documentation |
| LazyCode: Open Settings | - | Open LazyCode settings |

## Configuration

LazyCode can be configured through VS Code settings. Key configuration options include:

### Completions
- `lazycode.completions.enableAutomaticCompletions`: Enable automatic inline completions
- `lazycode.completions.enableQuickSuggestions`: Add LazyCode to IntelliSense suggestions

### Chat
- `lazycode.chat.enableEmptyFileHint`: Show hint to use chat when opening empty files

### Advanced
- `lazycode.advanced.apiToken`: API token for LazyCode service
- `lazycode.advanced.apiUrl`: Custom API URL

## Development

### Prerequisites
- Node.js 18.15.0 or higher
- pnpm package manager
- Visual Studio Code

### Setup
```bash
# Clone the repository
git clone <repository-url>
cd lazycode

# Install dependencies
pnpm install

# Build the extension
pnpm run build

# Run tests
pnpm run test

# Start development mode
pnpm run watch
```

### Project Structure
```
lazycode/
├── src/                    # Extension source code
│   ├── commands/           # Command handlers
│   ├── services/           # Core services
│   ├── utils/              # Utility functions
│   └── types/              # TypeScript definitions
├── webviews/               # Webview frontend code
├── test/                   # Test files
├── docs/                   # Documentation
├── media/                  # Icons and assets
└── out/                    # Compiled output
```

### Testing
```bash
# Run all tests
pnpm run test

# Run extension tests
pnpm run test:extension

# Run webview tests
pnpm run test:webview

# Run with coverage
pnpm run test -- --coverage
```

### Building
```bash
# Development build
pnpm run build

# Production build
pnpm run build:prod

# Package extension
pnpm run package
```

## Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Workflow
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

### Code Style
- Use TypeScript for all new code
- Follow the existing code style
- Run `pnpm run lint` before committing
- Add JSDoc comments for public APIs

## Architecture

LazyCode is built with a modular architecture:

- **Extension Host**: Main extension logic running in Node.js
- **Webviews**: Frontend UI components using modern web technologies
- **Service Container**: Dependency injection for core services
- **Event System**: Decoupled communication between components
- **Configuration**: Centralized settings management

## Roadmap

### Phase 1: Foundation ✅
- [x] Basic project structure
- [x] Core services and architecture
- [x] Command system
- [x] Configuration management

### Phase 2: UI Framework (In Progress)
- [ ] Webview infrastructure
- [ ] Chat interface
- [ ] Settings panel
- [ ] Component library

### Phase 3: Core Features
- [ ] AI chat functionality
- [ ] Code completion
- [ ] Code analysis
- [ ] Documentation generation

### Phase 4: Advanced Features
- [ ] Next Edit suggestions
- [ ] Automated fixes
- [ ] Workspace integration
- [ ] Performance optimization

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

- 📖 [Documentation](docs/)
- 🐛 [Issue Tracker](https://github.com/your-repo/lazycode/issues)
- 💬 [Discussions](https://github.com/your-repo/lazycode/discussions)

## Acknowledgments

This project is inspired by modern AI coding assistants and aims to provide an open, customizable alternative for developers.
