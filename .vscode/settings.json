{"typescript.preferences.includePackageJsonAutoImports": "on", "typescript.suggest.autoImports": true, "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "eslint.validate": ["typescript"], "files.exclude": {"**/node_modules": true, "**/out": true, "**/dist": true, "**/.git": true, "**/.DS_Store": true, "**/coverage": true}, "search.exclude": {"**/node_modules": true, "**/out": true, "**/dist": true, "**/coverage": true}, "typescript.preferences.importModuleSpecifier": "relative"}