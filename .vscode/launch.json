{"version": "0.2.0", "configurations": [{"name": "Run Extension", "type": "extensionHost", "request": "launch", "args": ["--extensionDevelopmentPath=${workspaceFolder}"], "outFiles": ["${workspaceFolder}/out/**/*.js"], "preLaunchTask": "${workspaceFolder}/.vscode/tasks.json:build"}, {"name": "Extension Tests", "type": "extensionHost", "request": "launch", "args": ["--extensionDevelopmentPath=${workspaceFolder}", "--extensionTestsPath=${workspaceFolder}/out/test/suite/index"], "outFiles": ["${workspaceFolder}/out/test/**/*.js"], "preLaunchTask": "${workspaceFolder}/.vscode/tasks.json:build"}, {"name": "Debug Jest Tests", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/.bin/jest", "args": ["--runInBand", "--no-cache"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "env": {"NODE_ENV": "test"}}]}