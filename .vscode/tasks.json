{"version": "2.0.0", "tasks": [{"label": "build", "type": "npm", "script": "build", "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": ["$tsc"]}, {"label": "watch", "type": "npm", "script": "watch", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "isBackground": true, "problemMatcher": ["$tsc-watch"]}, {"label": "test", "type": "npm", "script": "test", "group": {"kind": "test", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}}, {"label": "lint", "type": "npm", "script": "lint", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}}]}