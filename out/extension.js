"use strict";
var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/extension.ts
var extension_exports = {};
__export(extension_exports, {
  activate: () => activate,
  deactivate: () => deactivate,
  getExtensionVersion: () => getExtensionVersion,
  isDevelopmentMode: () => isDevelopmentMode
});
module.exports = __toCommonJS(extension_exports);
var vscode7 = __toESM(require("vscode"));

// src/utils/Logger.ts
var vscode = __toESM(require("vscode"));
var Logger = class _Logger {
  constructor() {
    this.logLevel = 1 /* INFO */;
    this.outputChannel = vscode.window.createOutputChannel("LazyCode");
  }
  static getInstance() {
    if (!_Logger.instance) {
      _Logger.instance = new _Logger();
    }
    return _Logger.instance;
  }
  setLogLevel(level) {
    this.logLevel = level;
  }
  debug(message, ...args) {
    if (this.logLevel <= 0 /* DEBUG */) {
      this.log("DEBUG", message, ...args);
    }
  }
  info(message, ...args) {
    if (this.logLevel <= 1 /* INFO */) {
      this.log("INFO", message, ...args);
    }
  }
  warn(message, ...args) {
    if (this.logLevel <= 2 /* WARN */) {
      this.log("WARN", message, ...args);
    }
  }
  error(message, error) {
    if (this.logLevel <= 3 /* ERROR */) {
      this.log("ERROR", message, error);
      if (error) {
        this.log("ERROR", `Stack trace: ${error.stack}`);
      }
    }
  }
  log(level, message, ...args) {
    const timestamp = (/* @__PURE__ */ new Date()).toISOString();
    const formattedMessage = `[${timestamp}] [${level}] ${message}`;
    this.outputChannel.appendLine(formattedMessage);
    if (args.length > 0) {
      args.forEach((arg) => {
        if (arg instanceof Error) {
          this.outputChannel.appendLine(`  Error: ${arg.message}`);
          if (arg.stack) {
            this.outputChannel.appendLine(`  Stack: ${arg.stack}`);
          }
        } else if (typeof arg === "object") {
          this.outputChannel.appendLine(`  ${JSON.stringify(arg, null, 2)}`);
        } else {
          this.outputChannel.appendLine(`  ${String(arg)}`);
        }
      });
    }
    if (process.env.NODE_ENV === "development") {
      switch (level) {
        case "DEBUG":
          console.debug(formattedMessage, ...args);
          break;
        case "INFO":
          console.info(formattedMessage, ...args);
          break;
        case "WARN":
          console.warn(formattedMessage, ...args);
          break;
        case "ERROR":
          console.error(formattedMessage, ...args);
          break;
      }
    }
  }
  show() {
    this.outputChannel.show();
  }
  dispose() {
    this.outputChannel.dispose();
  }
};
var logger = Logger.getInstance();

// src/services/ServiceContainer.ts
var ServiceContainer = class _ServiceContainer {
  constructor() {
    this.services = /* @__PURE__ */ new Map();
    this.disposables = [];
  }
  static getInstance() {
    if (!_ServiceContainer.instance) {
      _ServiceContainer.instance = new _ServiceContainer();
    }
    return _ServiceContainer.instance;
  }
  /**
   * Register a service with the container
   */
  register(name, factory, options = {}) {
    const { singleton = true } = options;
    if (this.services.has(name)) {
      logger.warn(`Service '${name}' is already registered. Overwriting.`);
    }
    this.services.set(name, {
      factory,
      singleton
    });
    logger.debug(`Service '${name}' registered (singleton: ${singleton})`);
  }
  /**
   * Register a service instance directly
   */
  registerInstance(name, instance) {
    this.services.set(name, {
      factory: () => instance,
      singleton: true,
      instance
    });
    logger.debug(`Service instance '${name}' registered`);
  }
  /**
   * Get a service from the container
   */
  async get(name) {
    const serviceDefinition = this.services.get(name);
    if (!serviceDefinition) {
      throw new Error(`Service '${name}' is not registered`);
    }
    if (serviceDefinition.singleton && serviceDefinition.instance) {
      return await serviceDefinition.instance;
    }
    try {
      const instance = await serviceDefinition.factory();
      if (serviceDefinition.singleton) {
        serviceDefinition.instance = instance;
      }
      logger.debug(`Service '${name}' instantiated`);
      return instance;
    } catch (error) {
      logger.error(`Failed to instantiate service '${name}'`, error);
      throw error;
    }
  }
  /**
   * Get a service synchronously (only works if the service is already instantiated)
   */
  getSync(name) {
    const serviceDefinition = this.services.get(name);
    if (!serviceDefinition) {
      throw new Error(`Service '${name}' is not registered`);
    }
    if (!serviceDefinition.instance) {
      throw new Error(`Service '${name}' is not yet instantiated. Use get() instead.`);
    }
    if (serviceDefinition.instance instanceof Promise) {
      throw new Error(`Service '${name}' is still being instantiated. Use get() instead.`);
    }
    return serviceDefinition.instance;
  }
  /**
   * Check if a service is registered
   */
  has(name) {
    return this.services.has(name);
  }
  /**
   * Check if a service is instantiated
   */
  isInstantiated(name) {
    const serviceDefinition = this.services.get(name);
    return serviceDefinition?.instance !== void 0;
  }
  /**
   * Unregister a service
   */
  unregister(name) {
    const serviceDefinition = this.services.get(name);
    if (serviceDefinition?.instance) {
      const instance = serviceDefinition.instance;
      if (instance && typeof instance === "object" && "dispose" in instance) {
        try {
          instance.dispose();
        } catch (error) {
          logger.warn(`Error disposing service '${name}'`, error);
        }
      }
    }
    this.services.delete(name);
    logger.debug(`Service '${name}' unregistered`);
  }
  /**
   * Get all registered service names
   */
  getServiceNames() {
    return Array.from(this.services.keys());
  }
  /**
   * Clear all services
   */
  clear() {
    for (const [name, serviceDefinition] of this.services) {
      if (serviceDefinition.instance) {
        const instance = serviceDefinition.instance;
        if (instance && typeof instance === "object" && "dispose" in instance) {
          try {
            instance.dispose();
          } catch (error) {
            logger.warn(`Error disposing service '${name}'`, error);
          }
        }
      }
    }
    this.services.clear();
    logger.debug("All services cleared");
  }
  /**
   * Dispose the service container
   */
  dispose() {
    this.clear();
    this.disposables.forEach((disposable) => disposable.dispose());
    this.disposables = [];
  }
};
var serviceContainer = ServiceContainer.getInstance();
var SERVICE_NAMES = {
  CONFIGURATION: "configuration",
  WEBVIEW_MANAGER: "webviewManager",
  API_CLIENT: "apiClient",
  CHAT_SERVICE: "chatService",
  COMPLETION_SERVICE: "completionService"
};

// src/services/ConfigurationService.ts
var vscode2 = __toESM(require("vscode"));
var ConfigurationService = class _ConfigurationService {
  constructor() {
    this.disposables = [];
    this.disposables.push(
      vscode2.workspace.onDidChangeConfiguration((e) => {
        if (e.affectsConfiguration(_ConfigurationService.CONFIGURATION_SECTION)) {
          logger.debug("Configuration changed");
        }
      })
    );
  }
  static {
    this.CONFIGURATION_SECTION = "lazycode";
  }
  get(key, defaultValue) {
    const config = vscode2.workspace.getConfiguration(_ConfigurationService.CONFIGURATION_SECTION);
    const value = config.get(key);
    if (value === void 0 && defaultValue !== void 0) {
      return defaultValue;
    }
    return value;
  }
  async set(key, value, target) {
    try {
      const config = vscode2.workspace.getConfiguration(_ConfigurationService.CONFIGURATION_SECTION);
      await config.update(key, value, target ?? vscode2.ConfigurationTarget.Global);
      logger.debug(`Configuration updated: ${key} = ${JSON.stringify(value)}`);
    } catch (error) {
      logger.error(`Failed to update configuration: ${key}`, error);
      throw error;
    }
  }
  onDidChange(callback) {
    return vscode2.workspace.onDidChangeConfiguration((e) => {
      if (e.affectsConfiguration(_ConfigurationService.CONFIGURATION_SECTION)) {
        callback(e);
      }
    });
  }
  // Typed getters for specific configuration sections
  getCompletionsConfig() {
    return {
      enableAutomaticCompletions: this.get("completions.enableAutomaticCompletions", true),
      enableQuickSuggestions: this.get("completions.enableQuickSuggestions", true)
    };
  }
  getChatConfig() {
    return {
      enableEmptyFileHint: this.get("chat.enableEmptyFileHint", true)
    };
  }
  getAdvancedConfig() {
    return {
      apiToken: this.get("advanced.apiToken", ""),
      apiUrl: this.get("advanced.apiUrl", "")
    };
  }
  getFullConfiguration() {
    return {
      completions: this.getCompletionsConfig(),
      chat: this.getChatConfig(),
      advanced: this.getAdvancedConfig()
    };
  }
  // Typed setters for specific configuration sections
  async setCompletionsConfig(config) {
    const promises = [];
    if (config.enableAutomaticCompletions !== void 0) {
      promises.push(this.set("completions.enableAutomaticCompletions", config.enableAutomaticCompletions));
    }
    if (config.enableQuickSuggestions !== void 0) {
      promises.push(this.set("completions.enableQuickSuggestions", config.enableQuickSuggestions));
    }
    await Promise.all(promises);
  }
  async setChatConfig(config) {
    const promises = [];
    if (config.enableEmptyFileHint !== void 0) {
      promises.push(this.set("chat.enableEmptyFileHint", config.enableEmptyFileHint));
    }
    await Promise.all(promises);
  }
  async setAdvancedConfig(config) {
    const promises = [];
    if (config.apiToken !== void 0) {
      promises.push(this.set("advanced.apiToken", config.apiToken));
    }
    if (config.apiUrl !== void 0) {
      promises.push(this.set("advanced.apiUrl", config.apiUrl));
    }
    await Promise.all(promises);
  }
  // Validation methods
  validateConfiguration() {
    const errors = [];
    const config = this.getFullConfiguration();
    if (!config.advanced.apiToken && !config.advanced.apiUrl) {
      errors.push("Either API token or API URL must be configured");
    }
    if (config.advanced.apiUrl && !this.isValidUrl(config.advanced.apiUrl)) {
      errors.push("API URL must be a valid URL");
    }
    return {
      isValid: errors.length === 0,
      errors
    };
  }
  isValidUrl(url) {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }
  dispose() {
    this.disposables.forEach((disposable) => disposable.dispose());
    this.disposables = [];
  }
};

// src/commands/index.ts
var vscode6 = __toESM(require("vscode"));

// src/commands/chatCommands.ts
var vscode3 = __toESM(require("vscode"));
var chatCommands = {
  /**
   * Focus the chat panel
   */
  async focusChat(_context) {
    logger.debug("Focusing chat panel");
    try {
      await vscode3.window.showInformationMessage(
        "Chat panel will be implemented in the next phase"
      );
    } catch (error) {
      logger.error("Failed to focus chat panel", error);
      throw error;
    }
  },
  /**
   * Start a new chat session
   */
  async newChat(_context) {
    logger.debug("Starting new chat session");
    try {
      await vscode3.window.showInformationMessage(
        "New chat session will be implemented in the next phase"
      );
    } catch (error) {
      logger.error("Failed to start new chat session", error);
      throw error;
    }
  },
  /**
   * Clear chat history
   */
  async clearHistory(_context) {
    logger.debug("Clearing chat history");
    try {
      const confirmation = await vscode3.window.showWarningMessage(
        "Are you sure you want to clear all chat history? This action cannot be undone.",
        { modal: true },
        "Clear History"
      );
      if (confirmation === "Clear History") {
        await vscode3.window.showInformationMessage(
          "Chat history clearing will be implemented in the next phase"
        );
        logger.info("Chat history cleared");
      }
    } catch (error) {
      logger.error("Failed to clear chat history", error);
      throw error;
    }
  },
  /**
   * Send selected code to chat
   */
  async sendCodeToChat(context) {
    logger.debug("Sending selected code to chat");
    try {
      if (!context.editor || !context.selection || context.selection.isEmpty) {
        await vscode3.window.showWarningMessage("Please select some code to send to chat");
        return;
      }
      const selectedText = context.editor.document.getText(context.selection);
      const language = context.editor.document.languageId;
      const _fileName = context.editor.document.fileName;
      await vscode3.window.showInformationMessage(
        `Sending ${selectedText.length} characters of ${language} code to chat`
      );
    } catch (error) {
      logger.error("Failed to send code to chat", error);
      throw error;
    }
  },
  /**
   * Ask question about selected code
   */
  async askAboutCode(context, question) {
    logger.debug("Asking question about selected code");
    try {
      if (!context.editor || !context.selection || context.selection.isEmpty) {
        await vscode3.window.showWarningMessage("Please select some code to ask about");
        return;
      }
      if (!question) {
        question = await vscode3.window.showInputBox({
          prompt: "What would you like to know about this code?",
          placeHolder: "e.g., What does this function do?"
        });
        if (!question) {
          return;
        }
      }
      const _selectedText = context.editor.document.getText(context.selection);
      const language = context.editor.document.languageId;
      const _fileName = context.editor.document.fileName;
      await vscode3.window.showInformationMessage(`Question: "${question}" about ${language} code`);
    } catch (error) {
      logger.error("Failed to ask question about code", error);
      throw error;
    }
  }
};

// src/commands/codeCommands.ts
var vscode4 = __toESM(require("vscode"));
var codeCommands = {
  /**
   * Explain selected code
   */
  async explainCode(context) {
    logger.debug("Explaining selected code");
    try {
      if (!context.editor || !context.selection || context.selection.isEmpty) {
        await vscode4.window.showWarningMessage("Please select some code to explain");
        return;
      }
      const selectedText = context.editor.document.getText(context.selection);
      const language = context.editor.document.languageId;
      const _fileName = context.editor.document.fileName;
      logger.debug(`Explaining ${selectedText.length} characters of ${language} code`);
      await vscode4.window.showInformationMessage(
        `Code explanation will be implemented in the next phase. Selected: ${selectedText.length} characters of ${language} code.`
      );
    } catch (error) {
      logger.error("Failed to explain code", error);
      throw error;
    }
  },
  /**
   * Fix selected code
   */
  async fixCode(context) {
    logger.debug("Fixing selected code");
    try {
      if (!context.editor || !context.selection || context.selection.isEmpty) {
        await vscode4.window.showWarningMessage("Please select some code to fix");
        return;
      }
      const selectedText = context.editor.document.getText(context.selection);
      const language = context.editor.document.languageId;
      const _fileName = context.editor.document.fileName;
      logger.debug(`Fixing ${selectedText.length} characters of ${language} code`);
      await vscode4.window.withProgress(
        {
          location: vscode4.ProgressLocation.Notification,
          title: "Analyzing and fixing code...",
          cancellable: true
        },
        async (progress, token) => {
          progress.report({ increment: 50, message: "Analyzing code..." });
          await new Promise((resolve) => setTimeout(resolve, 1e3));
          if (token.isCancellationRequested) {
            return;
          }
          progress.report({ increment: 50, message: "Generating fix..." });
          await vscode4.window.showInformationMessage(
            `Code fixing will be implemented in the next phase. Selected: ${selectedText.length} characters of ${language} code.`
          );
        }
      );
    } catch (error) {
      logger.error("Failed to fix code", error);
      throw error;
    }
  },
  /**
   * Generate tests for selected code
   */
  async generateTests(context) {
    logger.debug("Generating tests for selected code");
    try {
      if (!context.editor || !context.selection || context.selection.isEmpty) {
        await vscode4.window.showWarningMessage("Please select some code to generate tests for");
        return;
      }
      const selectedText = context.editor.document.getText(context.selection);
      const language = context.editor.document.languageId;
      const _fileName = context.editor.document.fileName;
      logger.debug(`Generating tests for ${selectedText.length} characters of ${language} code`);
      const testFramework = await vscode4.window.showQuickPick(
        [
          { label: "Jest", description: "JavaScript/TypeScript testing framework" },
          { label: "Mocha", description: "JavaScript testing framework" },
          { label: "PyTest", description: "Python testing framework" },
          { label: "JUnit", description: "Java testing framework" },
          { label: "Auto-detect", description: "Let LazyCode choose the best framework" }
        ],
        {
          placeHolder: "Select a testing framework",
          ignoreFocusOut: true
        }
      );
      if (!testFramework) {
        return;
      }
      await vscode4.window.showInformationMessage(
        `Test generation with ${testFramework.label} will be implemented in the next phase.`
      );
    } catch (error) {
      logger.error("Failed to generate tests", error);
      throw error;
    }
  },
  /**
   * Generate documentation for selected code
   */
  async generateDocumentation(context) {
    logger.debug("Generating documentation for selected code");
    try {
      if (!context.editor || !context.selection || context.selection.isEmpty) {
        await vscode4.window.showWarningMessage("Please select some code to document");
        return;
      }
      const selectedText = context.editor.document.getText(context.selection);
      const language = context.editor.document.languageId;
      const _fileName = context.editor.document.fileName;
      logger.debug(
        `Generating documentation for ${selectedText.length} characters of ${language} code`
      );
      const docStyle = await vscode4.window.showQuickPick(
        [
          { label: "JSDoc", description: "JavaScript/TypeScript documentation" },
          { label: "Sphinx", description: "Python documentation" },
          { label: "Javadoc", description: "Java documentation" },
          { label: "XML Doc", description: "C# documentation" },
          { label: "Auto-detect", description: "Let LazyCode choose the best style" }
        ],
        {
          placeHolder: "Select documentation style",
          ignoreFocusOut: true
        }
      );
      if (!docStyle) {
        return;
      }
      await vscode4.window.showInformationMessage(
        `Documentation generation with ${docStyle.label} will be implemented in the next phase.`
      );
    } catch (error) {
      logger.error("Failed to generate documentation", error);
      throw error;
    }
  },
  /**
   * Helper method to create a test file
   */
  async createTestFile(originalFileName, testCode, language) {
    try {
      const path = require("path");
      const ext = path.extname(originalFileName);
      const baseName = path.basename(originalFileName, ext);
      const dirName = path.dirname(originalFileName);
      let testFileName;
      if (language === "javascript" || language === "typescript") {
        testFileName = path.join(dirName, `${baseName}.test${ext}`);
      } else if (language === "python") {
        testFileName = path.join(dirName, `test_${baseName}.py`);
      } else {
        testFileName = path.join(dirName, `${baseName}_test${ext}`);
      }
      const testUri = vscode4.Uri.file(testFileName);
      const edit = new vscode4.WorkspaceEdit();
      edit.createFile(testUri, { ignoreIfExists: true });
      edit.insert(testUri, new vscode4.Position(0, 0), testCode);
      await vscode4.workspace.applyEdit(edit);
      const document = await vscode4.workspace.openTextDocument(testUri);
      await vscode4.window.showTextDocument(document);
      logger.info(`Test file created: ${testFileName}`);
    } catch (error) {
      logger.error("Failed to create test file", error);
      throw error;
    }
  }
};

// src/commands/settingsCommands.ts
var vscode5 = __toESM(require("vscode"));
var settingsCommands = {
  /**
   * Open LazyCode settings
   */
  async openSettings(_context) {
    logger.debug("Opening LazyCode settings");
    try {
      await vscode5.commands.executeCommand("workbench.action.openSettings", "lazycode");
    } catch (error) {
      logger.error("Failed to open settings", error);
      throw error;
    }
  },
  /**
   * Reset LazyCode settings to defaults
   */
  async resetSettings(_context) {
    logger.debug("Resetting LazyCode settings");
    try {
      const confirmation = await vscode5.window.showWarningMessage(
        "Are you sure you want to reset all LazyCode settings to their default values?",
        { modal: true },
        "Reset Settings"
      );
      if (confirmation === "Reset Settings") {
        const configService = await serviceContainer.get(
          SERVICE_NAMES.CONFIGURATION
        );
        await configService.setCompletionsConfig({
          enableAutomaticCompletions: true,
          enableQuickSuggestions: true
        });
        await configService.setChatConfig({
          enableEmptyFileHint: true
        });
        await configService.setAdvancedConfig({
          apiToken: "",
          apiUrl: ""
        });
        await vscode5.window.showInformationMessage("LazyCode settings have been reset to defaults");
        logger.info("Settings reset to defaults");
      }
    } catch (error) {
      logger.error("Failed to reset settings", error);
      throw error;
    }
  },
  /**
   * Configure API settings
   */
  async configureApi(_context) {
    logger.debug("Configuring API settings");
    try {
      const configService = await serviceContainer.get(
        SERVICE_NAMES.CONFIGURATION
      );
      const currentConfig = configService.getAdvancedConfig();
      const apiUrl = await vscode5.window.showInputBox({
        prompt: "Enter the API URL for LazyCode service",
        value: currentConfig.apiUrl,
        placeHolder: "https://api.example.com",
        validateInput: (value) => {
          if (value && !this.isValidUrl(value)) {
            return "Please enter a valid URL";
          }
          return void 0;
        }
      });
      if (apiUrl === void 0) {
        return;
      }
      const apiToken = await vscode5.window.showInputBox({
        prompt: "Enter your API token",
        value: currentConfig.apiToken,
        placeHolder: "Your API token",
        password: true
      });
      if (apiToken === void 0) {
        return;
      }
      await configService.setAdvancedConfig({
        apiUrl,
        apiToken
      });
      const validation = configService.validateConfiguration();
      if (validation.isValid) {
        await vscode5.window.showInformationMessage("API configuration saved successfully");
      } else {
        await vscode5.window.showWarningMessage(
          `Configuration saved with warnings: ${validation.errors.join(", ")}`
        );
      }
      logger.info("API configuration updated");
    } catch (error) {
      logger.error("Failed to configure API settings", error);
      throw error;
    }
  },
  /**
   * Toggle automatic completions
   */
  async toggleAutomaticCompletions(_context) {
    logger.debug("Toggling automatic completions");
    try {
      const configService = await serviceContainer.get(
        SERVICE_NAMES.CONFIGURATION
      );
      const currentConfig = configService.getCompletionsConfig();
      const newValue = !currentConfig.enableAutomaticCompletions;
      await configService.setCompletionsConfig({
        enableAutomaticCompletions: newValue
      });
      const status = newValue ? "enabled" : "disabled";
      await vscode5.window.showInformationMessage(`Automatic completions ${status}`);
      logger.info(`Automatic completions ${status}`);
    } catch (error) {
      logger.error("Failed to toggle automatic completions", error);
      throw error;
    }
  },
  /**
   * Show configuration status
   */
  async showConfigurationStatus(context) {
    logger.debug("Showing configuration status");
    try {
      const configService = await serviceContainer.get(
        SERVICE_NAMES.CONFIGURATION
      );
      const config = configService.getFullConfiguration();
      const validation = configService.validateConfiguration();
      const statusItems = [
        {
          label: "$(gear) General Settings",
          description: "View general configuration",
          detail: `Completions: ${config.completions.enableAutomaticCompletions ? "On" : "Off"}, Quick Suggestions: ${config.completions.enableQuickSuggestions ? "On" : "Off"}`
        },
        {
          label: "$(comment-discussion) Chat Settings",
          description: "View chat configuration",
          detail: `Empty File Hint: ${config.chat.enableEmptyFileHint ? "On" : "Off"}`
        },
        {
          label: "$(key) API Settings",
          description: "View API configuration",
          detail: `URL: ${config.advanced.apiUrl || "Not set"}, Token: ${config.advanced.apiToken ? "Set" : "Not set"}`
        },
        {
          label: validation.isValid ? "$(check) Configuration Valid" : "$(warning) Configuration Issues",
          description: validation.isValid ? "All settings are valid" : "Some settings need attention",
          detail: validation.isValid ? "Ready to use" : validation.errors.join(", ")
        }
      ];
      const selected = await vscode5.window.showQuickPick(statusItems, {
        placeHolder: "LazyCode Configuration Status",
        ignoreFocusOut: true
      });
      if (selected) {
        if (selected.label.includes("API Settings")) {
          await this.configureApi(context);
        } else if (selected.label.includes("General Settings")) {
          await this.openSettings(context);
        }
      }
    } catch (error) {
      logger.error("Failed to show configuration status", error);
      throw error;
    }
  },
  /**
   * Export configuration
   */
  async exportConfiguration(_context) {
    logger.debug("Exporting configuration");
    try {
      const configService = await serviceContainer.get(
        SERVICE_NAMES.CONFIGURATION
      );
      const config = configService.getFullConfiguration();
      const exportConfig = {
        ...config,
        advanced: {
          ...config.advanced,
          apiToken: config.advanced.apiToken ? "[REDACTED]" : ""
        }
      };
      const configJson = JSON.stringify(exportConfig, null, 2);
      const action = await vscode5.window.showInformationMessage(
        "Configuration exported successfully!",
        "Copy to Clipboard",
        "Save to File"
      );
      if (action === "Copy to Clipboard") {
        await vscode5.env.clipboard.writeText(configJson);
        await vscode5.window.showInformationMessage("Configuration copied to clipboard");
      } else if (action === "Save to File") {
        const uri = await vscode5.window.showSaveDialog({
          defaultUri: vscode5.Uri.file("lazycode-config.json"),
          filters: {
            "JSON Files": ["json"],
            "All Files": ["*"]
          }
        });
        if (uri) {
          await vscode5.workspace.fs.writeFile(uri, Buffer.from(configJson, "utf8"));
          await vscode5.window.showInformationMessage(`Configuration saved to ${uri.fsPath}`);
        }
      }
    } catch (error) {
      logger.error("Failed to export configuration", error);
      throw error;
    }
  },
  /**
   * Helper method to validate URL
   */
  isValidUrl(url) {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }
};

// src/commands/index.ts
var CommandRegistry = class {
  constructor() {
    this.commands = /* @__PURE__ */ new Map();
  }
  register(commandId, handler) {
    if (this.commands.has(commandId)) {
      logger.warn(`Command '${commandId}' is already registered. Overwriting.`);
    }
    this.commands.set(commandId, handler);
    logger.debug(`Command '${commandId}' registered`);
  }
  getHandler(commandId) {
    return this.commands.get(commandId);
  }
  getAllCommands() {
    return Array.from(this.commands.keys());
  }
  createVSCodeCommand(commandId) {
    return async (...args) => {
      const handler = this.getHandler(commandId);
      if (!handler) {
        logger.error(`No handler found for command: ${commandId}`);
        return;
      }
      try {
        const context = this.createCommandContext();
        logger.debug(`Executing command: ${commandId}`, { args });
        await handler(context, ...args);
        logger.debug(`Command executed successfully: ${commandId}`);
      } catch (error) {
        logger.error(`Error executing command '${commandId}'`, error);
        void vscode6.window.showErrorMessage(
          `Failed to execute command: ${error instanceof Error ? error.message : "Unknown error"}`
        );
      }
    };
  }
  createCommandContext() {
    const editor = vscode6.window.activeTextEditor;
    const workspaceFolder = editor?.document ? vscode6.workspace.getWorkspaceFolder(editor.document.uri) : vscode6.workspace.workspaceFolders?.[0];
    return {
      editor,
      selection: editor?.selection,
      document: editor?.document,
      workspaceFolder
    };
  }
};
var commandRegistry = new CommandRegistry();
async function registerCommands(context) {
  logger.debug("Registering commands...");
  registerChatCommands();
  registerCodeCommands();
  registerSettingsCommands();
  for (const commandId of commandRegistry.getAllCommands()) {
    const disposable = vscode6.commands.registerCommand(
      commandId,
      commandRegistry.createVSCodeCommand(commandId)
    );
    context.subscriptions.push(disposable);
  }
  logger.info(`Registered ${commandRegistry.getAllCommands().length} commands`);
}
function registerChatCommands() {
  commandRegistry.register("lazycode.chat.focus", chatCommands.focusChat);
  commandRegistry.register("lazycode.chat.newChat", chatCommands.newChat);
  commandRegistry.register("lazycode.chat.clearHistory", chatCommands.clearHistory);
}
function registerCodeCommands() {
  commandRegistry.register("lazycode.code.explain", codeCommands.explainCode);
  commandRegistry.register("lazycode.code.fix", codeCommands.fixCode);
  commandRegistry.register("lazycode.code.test", codeCommands.generateTests);
  commandRegistry.register("lazycode.code.document", codeCommands.generateDocumentation);
}
function registerSettingsCommands() {
  commandRegistry.register("lazycode.settings.open", settingsCommands.openSettings);
  commandRegistry.register("lazycode.settings.reset", settingsCommands.resetSettings);
}

// src/extension.ts
async function activate(context) {
  try {
    logger.info("LazyCode extension is activating...");
    if (process.env.NODE_ENV === "development") {
      logger.setLogLevel(0 /* DEBUG */);
    }
    await initializeServices(context);
    await registerCommandHandlers(context);
    await registerProviders(context);
    setupEventListeners(context);
    await showWelcomeMessage(context);
    logger.info("LazyCode extension activated successfully");
  } catch (error) {
    logger.error("Failed to activate LazyCode extension", error);
    void vscode7.window.showErrorMessage(
      "Failed to activate LazyCode extension. Please check the output panel for details.",
      "Show Output"
    ).then((selection) => {
      if (selection === "Show Output") {
        logger.show();
      }
    });
    throw error;
  }
}
function deactivate() {
  try {
    logger.info("LazyCode extension is deactivating...");
    serviceContainer.dispose();
    logger.dispose();
    logger.info("LazyCode extension deactivated successfully");
  } catch (error) {
    console.error("Error during extension deactivation:", error);
  }
}
async function initializeServices(_context) {
  logger.debug("Initializing services...");
  serviceContainer.registerInstance(SERVICE_NAMES.CONFIGURATION, new ConfigurationService());
  logger.debug("Services initialized");
}
async function registerCommandHandlers(context) {
  logger.debug("Registering commands...");
  await registerCommands(context);
  logger.debug("Commands registered");
}
async function registerProviders(_context) {
  logger.debug("Registering providers...");
  logger.debug("Providers registered");
}
function setupEventListeners(context) {
  logger.debug("Setting up event listeners...");
  context.subscriptions.push(
    vscode7.window.onDidChangeActiveTextEditor((editor) => {
      if (editor) {
        logger.debug(`Active editor changed: ${editor.document.fileName}`);
      }
    })
  );
  context.subscriptions.push(
    vscode7.workspace.onDidChangeTextDocument((event) => {
      logger.debug(`Document changed: ${event.document.fileName}`);
    })
  );
  context.subscriptions.push(
    vscode7.workspace.onDidChangeWorkspaceFolders((event) => {
      logger.debug("Workspace folders changed", {
        added: event.added.length,
        removed: event.removed.length
      });
    })
  );
  context.subscriptions.push(
    vscode7.workspace.onDidChangeConfiguration((event) => {
      if (event.affectsConfiguration("lazycode")) {
        logger.debug("LazyCode configuration changed");
      }
    })
  );
  logger.debug("Event listeners set up");
}
async function showWelcomeMessage(context) {
  const hasShownWelcome = context.globalState.get("hasShownWelcome", false);
  if (!hasShownWelcome) {
    const selection = await vscode7.window.showInformationMessage(
      "Welcome to LazyCode! Your AI-powered coding assistant is ready to help.",
      "Open Chat",
      "Settings",
      "Don't show again"
    );
    switch (selection) {
      case "Open Chat":
        await vscode7.commands.executeCommand("lazycode.chat.focus");
        break;
      case "Settings":
        await vscode7.commands.executeCommand("lazycode.settings.open");
        break;
      case "Don't show again":
        await context.globalState.update("hasShownWelcome", true);
        break;
    }
    if (selection !== "Don't show again") {
      await context.globalState.update("hasShownWelcome", true);
    }
  }
}
function getExtensionVersion() {
  const extension = vscode7.extensions.getExtension("lazycode.lazycode");
  return extension?.packageJSON?.version ?? "unknown";
}
function isDevelopmentMode() {
  return process.env.NODE_ENV === "development" || vscode7.env.machineId === "someValue";
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  activate,
  deactivate,
  getExtensionVersion,
  isDevelopmentMode
});
//# sourceMappingURL=extension.js.map
