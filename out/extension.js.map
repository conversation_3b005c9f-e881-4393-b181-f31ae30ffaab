{"version": 3, "sources": ["../src/extension.ts", "../src/utils/Logger.ts", "../src/services/ServiceContainer.ts", "../src/services/ConfigurationService.ts", "../src/commands/index.ts", "../src/commands/chatCommands.ts", "../src/commands/codeCommands.ts", "../src/commands/settingsCommands.ts"], "sourcesContent": ["import * as vscode from 'vscode';\nimport { logger, LogLevel } from '@/utils/Logger';\nimport { serviceContainer, SERVICE_NAMES } from '@/services/ServiceContainer';\nimport { ConfigurationService } from '@/services/ConfigurationService';\nimport { registerCommands } from '@/commands';\n\n/**\n * Extension activation function\n */\nexport async function activate(context: vscode.ExtensionContext): Promise<void> {\n  try {\n    logger.info('LazyCode extension is activating...');\n\n    // Set log level based on environment\n    if (process.env.NODE_ENV === 'development') {\n      logger.setLogLevel(LogLevel.DEBUG);\n    }\n\n    // Initialize core services\n    await initializeServices(context);\n\n    // Register commands\n    await registerCommandHandlers(context);\n\n    // Register providers\n    await registerProviders(context);\n\n    // Setup event listeners\n    setupEventListeners(context);\n\n    // Show welcome message for first-time users\n    await showWelcomeMessage(context);\n\n    logger.info('LazyCode extension activated successfully');\n  } catch (error) {\n    logger.error('Failed to activate LazyCode extension', error as Error);\n\n    // Show error message to user\n    void vscode.window\n      .showErrorMessage(\n        'Failed to activate LazyCode extension. Please check the output panel for details.',\n        'Show Output'\n      )\n      .then(selection => {\n        if (selection === 'Show Output') {\n          logger.show();\n        }\n      });\n\n    throw error;\n  }\n}\n\n/**\n * Extension deactivation function\n */\nexport function deactivate(): void {\n  try {\n    logger.info('LazyCode extension is deactivating...');\n\n    // Dispose all services\n    serviceContainer.dispose();\n\n    // Dispose logger\n    logger.dispose();\n\n    logger.info('LazyCode extension deactivated successfully');\n  } catch (error) {\n    console.error('Error during extension deactivation:', error);\n  }\n}\n\n/**\n * Initialize core services\n */\nasync function initializeServices(context: vscode.ExtensionContext): Promise<void> {\n  logger.debug('Initializing services...');\n\n  // Register configuration service\n  serviceContainer.registerInstance(SERVICE_NAMES.CONFIGURATION, new ConfigurationService());\n\n  // TODO: Register other services as they are implemented\n  // - WebviewManager\n  // - ApiClient\n  // - ChatService\n  // - CompletionService\n\n  logger.debug('Services initialized');\n}\n\n/**\n * Register command handlers\n */\nasync function registerCommandHandlers(context: vscode.ExtensionContext): Promise<void> {\n  logger.debug('Registering commands...');\n\n  // Import and register all commands\n  await registerCommands(context);\n\n  logger.debug('Commands registered');\n}\n\n/**\n * Register language providers\n */\nasync function registerProviders(context: vscode.ExtensionContext): Promise<void> {\n  logger.debug('Registering providers...');\n\n  // TODO: Register providers as they are implemented\n  // - CompletionProvider\n  // - HoverProvider\n  // - CodeActionProvider\n\n  logger.debug('Providers registered');\n}\n\n/**\n * Setup global event listeners\n */\nfunction setupEventListeners(context: vscode.ExtensionContext): void {\n  logger.debug('Setting up event listeners...');\n\n  // Listen for active editor changes\n  context.subscriptions.push(\n    vscode.window.onDidChangeActiveTextEditor(editor => {\n      if (editor) {\n        logger.debug(`Active editor changed: ${editor.document.fileName}`);\n      }\n    })\n  );\n\n  // Listen for text document changes\n  context.subscriptions.push(\n    vscode.workspace.onDidChangeTextDocument(event => {\n      logger.debug(`Document changed: ${event.document.fileName}`);\n    })\n  );\n\n  // Listen for workspace folder changes\n  context.subscriptions.push(\n    vscode.workspace.onDidChangeWorkspaceFolders(event => {\n      logger.debug('Workspace folders changed', {\n        added: event.added.length,\n        removed: event.removed.length,\n      });\n    })\n  );\n\n  // Listen for configuration changes\n  context.subscriptions.push(\n    vscode.workspace.onDidChangeConfiguration(event => {\n      if (event.affectsConfiguration('lazycode')) {\n        logger.debug('LazyCode configuration changed');\n      }\n    })\n  );\n\n  logger.debug('Event listeners set up');\n}\n\n/**\n * Show welcome message for first-time users\n */\nasync function showWelcomeMessage(context: vscode.ExtensionContext): Promise<void> {\n  const hasShownWelcome = context.globalState.get<boolean>('hasShownWelcome', false);\n\n  if (!hasShownWelcome) {\n    const selection = await vscode.window.showInformationMessage(\n      'Welcome to LazyCode! Your AI-powered coding assistant is ready to help.',\n      'Open Chat',\n      'Settings',\n      \"Don't show again\"\n    );\n\n    switch (selection) {\n      case 'Open Chat':\n        await vscode.commands.executeCommand('lazycode.chat.focus');\n        break;\n      case 'Settings':\n        await vscode.commands.executeCommand('lazycode.settings.open');\n        break;\n      case \"Don't show again\":\n        await context.globalState.update('hasShownWelcome', true);\n        break;\n    }\n\n    if (selection !== \"Don't show again\") {\n      await context.globalState.update('hasShownWelcome', true);\n    }\n  }\n}\n\n/**\n * Get extension version\n */\nexport function getExtensionVersion(): string {\n  const extension = vscode.extensions.getExtension('lazycode.lazycode');\n  return extension?.packageJSON?.version ?? 'unknown';\n}\n\n/**\n * Check if extension is in development mode\n */\nexport function isDevelopmentMode(): boolean {\n  return process.env.NODE_ENV === 'development' || vscode.env.machineId === 'someValue'; // VSCode development host\n}\n", "import * as vscode from 'vscode';\nimport { ILogger } from '@/types';\n\nexport enum LogLevel {\n  DEBUG = 0,\n  INFO = 1,\n  WARN = 2,\n  ERROR = 3,\n}\n\nexport class Logger implements ILogger {\n  private static instance: Logger;\n  private outputChannel: vscode.OutputChannel;\n  private logLevel: LogLevel = LogLevel.INFO;\n\n  private constructor() {\n    this.outputChannel = vscode.window.createOutputChannel('LazyCode');\n  }\n\n  public static getInstance(): Logger {\n    if (!Logger.instance) {\n      Logger.instance = new Logger();\n    }\n    return Logger.instance;\n  }\n\n  public setLogLevel(level: LogLevel): void {\n    this.logLevel = level;\n  }\n\n  public debug(message: string, ...args: any[]): void {\n    if (this.logLevel <= LogLevel.DEBUG) {\n      this.log('DEBUG', message, ...args);\n    }\n  }\n\n  public info(message: string, ...args: any[]): void {\n    if (this.logLevel <= LogLevel.INFO) {\n      this.log('INFO', message, ...args);\n    }\n  }\n\n  public warn(message: string, ...args: any[]): void {\n    if (this.logLevel <= LogLevel.WARN) {\n      this.log('WARN', message, ...args);\n    }\n  }\n\n  public error(message: string, error?: Error): void {\n    if (this.logLevel <= LogLevel.ERROR) {\n      this.log('ERROR', message, error);\n      if (error) {\n        this.log('ERROR', `Stack trace: ${error.stack}`);\n      }\n    }\n  }\n\n  private log(level: string, message: string, ...args: any[]): void {\n    const timestamp = new Date().toISOString();\n    const formattedMessage = `[${timestamp}] [${level}] ${message}`;\n    \n    // Log to output channel\n    this.outputChannel.appendLine(formattedMessage);\n    \n    // Log additional arguments if any\n    if (args.length > 0) {\n      args.forEach(arg => {\n        if (arg instanceof Error) {\n          this.outputChannel.appendLine(`  Error: ${arg.message}`);\n          if (arg.stack) {\n            this.outputChannel.appendLine(`  Stack: ${arg.stack}`);\n          }\n        } else if (typeof arg === 'object') {\n          this.outputChannel.appendLine(`  ${JSON.stringify(arg, null, 2)}`);\n        } else {\n          this.outputChannel.appendLine(`  ${String(arg)}`);\n        }\n      });\n    }\n\n    // Also log to console in development\n    if (process.env.NODE_ENV === 'development') {\n      switch (level) {\n        case 'DEBUG':\n          console.debug(formattedMessage, ...args);\n          break;\n        case 'INFO':\n          console.info(formattedMessage, ...args);\n          break;\n        case 'WARN':\n          console.warn(formattedMessage, ...args);\n          break;\n        case 'ERROR':\n          console.error(formattedMessage, ...args);\n          break;\n      }\n    }\n  }\n\n  public show(): void {\n    this.outputChannel.show();\n  }\n\n  public dispose(): void {\n    this.outputChannel.dispose();\n  }\n}\n\n// Export singleton instance\nexport const logger = Logger.getInstance();\n", "import * as vscode from 'vscode';\nimport { logger } from '@/utils/Logger';\n\nexport type ServiceFactory<T> = () => T | Promise<T>;\nexport type ServiceInstance<T> = T | Promise<T>;\n\nexport interface ServiceDefinition<T = any> {\n  factory: ServiceFactory<T>;\n  singleton: boolean;\n  instance?: ServiceInstance<T>;\n}\n\nexport class ServiceContainer {\n  private static instance: ServiceContainer;\n  private services = new Map<string, ServiceDefinition>();\n  private disposables: vscode.Disposable[] = [];\n\n  private constructor() {}\n\n  public static getInstance(): ServiceContainer {\n    if (!ServiceContainer.instance) {\n      ServiceContainer.instance = new ServiceContainer();\n    }\n    return ServiceContainer.instance;\n  }\n\n  /**\n   * Register a service with the container\n   */\n  public register<T>(\n    name: string,\n    factory: ServiceFactory<T>,\n    options: { singleton?: boolean } = {}\n  ): void {\n    const { singleton = true } = options;\n\n    if (this.services.has(name)) {\n      logger.warn(`Service '${name}' is already registered. Overwriting.`);\n    }\n\n    this.services.set(name, {\n      factory,\n      singleton,\n    });\n\n    logger.debug(`Service '${name}' registered (singleton: ${singleton})`);\n  }\n\n  /**\n   * Register a service instance directly\n   */\n  public registerInstance<T>(name: string, instance: T): void {\n    this.services.set(name, {\n      factory: () => instance,\n      singleton: true,\n      instance,\n    });\n\n    logger.debug(`Service instance '${name}' registered`);\n  }\n\n  /**\n   * Get a service from the container\n   */\n  public async get<T>(name: string): Promise<T> {\n    const serviceDefinition = this.services.get(name);\n    \n    if (!serviceDefinition) {\n      throw new Error(`Service '${name}' is not registered`);\n    }\n\n    // If it's a singleton and we have an instance, return it\n    if (serviceDefinition.singleton && serviceDefinition.instance) {\n      return await serviceDefinition.instance;\n    }\n\n    // Create new instance\n    try {\n      const instance = await serviceDefinition.factory();\n      \n      // Store instance if it's a singleton\n      if (serviceDefinition.singleton) {\n        serviceDefinition.instance = instance;\n      }\n\n      logger.debug(`Service '${name}' instantiated`);\n      return instance;\n    } catch (error) {\n      logger.error(`Failed to instantiate service '${name}'`, error as Error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get a service synchronously (only works if the service is already instantiated)\n   */\n  public getSync<T>(name: string): T {\n    const serviceDefinition = this.services.get(name);\n    \n    if (!serviceDefinition) {\n      throw new Error(`Service '${name}' is not registered`);\n    }\n\n    if (!serviceDefinition.instance) {\n      throw new Error(`Service '${name}' is not yet instantiated. Use get() instead.`);\n    }\n\n    if (serviceDefinition.instance instanceof Promise) {\n      throw new Error(`Service '${name}' is still being instantiated. Use get() instead.`);\n    }\n\n    return serviceDefinition.instance;\n  }\n\n  /**\n   * Check if a service is registered\n   */\n  public has(name: string): boolean {\n    return this.services.has(name);\n  }\n\n  /**\n   * Check if a service is instantiated\n   */\n  public isInstantiated(name: string): boolean {\n    const serviceDefinition = this.services.get(name);\n    return serviceDefinition?.instance !== undefined;\n  }\n\n  /**\n   * Unregister a service\n   */\n  public unregister(name: string): void {\n    const serviceDefinition = this.services.get(name);\n    \n    if (serviceDefinition?.instance) {\n      // Try to dispose the instance if it has a dispose method\n      const instance = serviceDefinition.instance;\n      if (instance && typeof instance === 'object' && 'dispose' in instance) {\n        try {\n          (instance as any).dispose();\n        } catch (error) {\n          logger.warn(`Error disposing service '${name}'`, error);\n        }\n      }\n    }\n\n    this.services.delete(name);\n    logger.debug(`Service '${name}' unregistered`);\n  }\n\n  /**\n   * Get all registered service names\n   */\n  public getServiceNames(): string[] {\n    return Array.from(this.services.keys());\n  }\n\n  /**\n   * Clear all services\n   */\n  public clear(): void {\n    // Dispose all services that have a dispose method\n    for (const [name, serviceDefinition] of this.services) {\n      if (serviceDefinition.instance) {\n        const instance = serviceDefinition.instance;\n        if (instance && typeof instance === 'object' && 'dispose' in instance) {\n          try {\n            (instance as any).dispose();\n          } catch (error) {\n            logger.warn(`Error disposing service '${name}'`, error);\n          }\n        }\n      }\n    }\n\n    this.services.clear();\n    logger.debug('All services cleared');\n  }\n\n  /**\n   * Dispose the service container\n   */\n  public dispose(): void {\n    this.clear();\n    this.disposables.forEach(disposable => disposable.dispose());\n    this.disposables = [];\n  }\n}\n\n// Export singleton instance\nexport const serviceContainer = ServiceContainer.getInstance();\n\n// Service name constants\nexport const SERVICE_NAMES = {\n  CONFIGURATION: 'configuration',\n  WEBVIEW_MANAGER: 'webviewManager',\n  API_CLIENT: 'apiClient',\n  CHAT_SERVICE: 'chatService',\n  COMPLETION_SERVICE: 'completionService',\n} as const;\n", "import * as vscode from 'vscode';\nimport { IConfigurationService, LazyCodeConfiguration } from '@/types';\nimport { logger } from '@/utils/Logger';\n\nexport class ConfigurationService implements IConfigurationService {\n  private static readonly CONFIGURATION_SECTION = 'lazycode';\n  private disposables: vscode.Disposable[] = [];\n\n  constructor() {\n    // Listen for configuration changes\n    this.disposables.push(\n      vscode.workspace.onDidChangeConfiguration(e => {\n        if (e.affectsConfiguration(ConfigurationService.CONFIGURATION_SECTION)) {\n          logger.debug('Configuration changed');\n        }\n      })\n    );\n  }\n\n  public get<T>(key: string, defaultValue?: T): T {\n    const config = vscode.workspace.getConfiguration(ConfigurationService.CONFIGURATION_SECTION);\n    const value = config.get<T>(key);\n    \n    if (value === undefined && defaultValue !== undefined) {\n      return defaultValue;\n    }\n    \n    return value as T;\n  }\n\n  public async set(key: string, value: any, target?: vscode.ConfigurationTarget): Promise<void> {\n    try {\n      const config = vscode.workspace.getConfiguration(ConfigurationService.CONFIGURATION_SECTION);\n      await config.update(key, value, target ?? vscode.ConfigurationTarget.Global);\n      logger.debug(`Configuration updated: ${key} = ${JSON.stringify(value)}`);\n    } catch (error) {\n      logger.error(`Failed to update configuration: ${key}`, error as Error);\n      throw error;\n    }\n  }\n\n  public onDidChange(\n    callback: (e: vscode.ConfigurationChangeEvent) => void\n  ): vscode.Disposable {\n    return vscode.workspace.onDidChangeConfiguration(e => {\n      if (e.affectsConfiguration(ConfigurationService.CONFIGURATION_SECTION)) {\n        callback(e);\n      }\n    });\n  }\n\n  // Typed getters for specific configuration sections\n  public getCompletionsConfig(): LazyCodeConfiguration['completions'] {\n    return {\n      enableAutomaticCompletions: this.get('completions.enableAutomaticCompletions', true),\n      enableQuickSuggestions: this.get('completions.enableQuickSuggestions', true),\n    };\n  }\n\n  public getChatConfig(): LazyCodeConfiguration['chat'] {\n    return {\n      enableEmptyFileHint: this.get('chat.enableEmptyFileHint', true),\n    };\n  }\n\n  public getAdvancedConfig(): LazyCodeConfiguration['advanced'] {\n    return {\n      apiToken: this.get('advanced.apiToken', ''),\n      apiUrl: this.get('advanced.apiUrl', ''),\n    };\n  }\n\n  public getFullConfiguration(): LazyCodeConfiguration {\n    return {\n      completions: this.getCompletionsConfig(),\n      chat: this.getChatConfig(),\n      advanced: this.getAdvancedConfig(),\n    };\n  }\n\n  // Typed setters for specific configuration sections\n  public async setCompletionsConfig(\n    config: Partial<LazyCodeConfiguration['completions']>\n  ): Promise<void> {\n    const promises: Promise<void>[] = [];\n    \n    if (config.enableAutomaticCompletions !== undefined) {\n      promises.push(this.set('completions.enableAutomaticCompletions', config.enableAutomaticCompletions));\n    }\n    \n    if (config.enableQuickSuggestions !== undefined) {\n      promises.push(this.set('completions.enableQuickSuggestions', config.enableQuickSuggestions));\n    }\n    \n    await Promise.all(promises);\n  }\n\n  public async setChatConfig(\n    config: Partial<LazyCodeConfiguration['chat']>\n  ): Promise<void> {\n    const promises: Promise<void>[] = [];\n    \n    if (config.enableEmptyFileHint !== undefined) {\n      promises.push(this.set('chat.enableEmptyFileHint', config.enableEmptyFileHint));\n    }\n    \n    await Promise.all(promises);\n  }\n\n  public async setAdvancedConfig(\n    config: Partial<LazyCodeConfiguration['advanced']>\n  ): Promise<void> {\n    const promises: Promise<void>[] = [];\n    \n    if (config.apiToken !== undefined) {\n      promises.push(this.set('advanced.apiToken', config.apiToken));\n    }\n    \n    if (config.apiUrl !== undefined) {\n      promises.push(this.set('advanced.apiUrl', config.apiUrl));\n    }\n    \n    await Promise.all(promises);\n  }\n\n  // Validation methods\n  public validateConfiguration(): { isValid: boolean; errors: string[] } {\n    const errors: string[] = [];\n    const config = this.getFullConfiguration();\n\n    // Validate API configuration\n    if (!config.advanced.apiToken && !config.advanced.apiUrl) {\n      errors.push('Either API token or API URL must be configured');\n    }\n\n    if (config.advanced.apiUrl && !this.isValidUrl(config.advanced.apiUrl)) {\n      errors.push('API URL must be a valid URL');\n    }\n\n    return {\n      isValid: errors.length === 0,\n      errors,\n    };\n  }\n\n  private isValidUrl(url: string): boolean {\n    try {\n      new URL(url);\n      return true;\n    } catch {\n      return false;\n    }\n  }\n\n  public dispose(): void {\n    this.disposables.forEach(disposable => disposable.dispose());\n    this.disposables = [];\n  }\n}\n", "import * as vscode from 'vscode';\nimport { logger } from '@/utils/Logger';\nimport { CommandHandler, CommandContext } from '@/types';\n\n// Import command handlers\nimport { chatCommands } from './chatCommands';\nimport { codeCommands } from './codeCommands';\nimport { settingsCommands } from './settingsCommands';\n\n/**\n * Command registration helper\n */\nclass CommandRegistry {\n  private commands = new Map<string, CommandHandler>();\n\n  public register(commandId: string, handler: CommandHandler): void {\n    if (this.commands.has(commandId)) {\n      logger.warn(`Command '${commandId}' is already registered. Overwriting.`);\n    }\n    \n    this.commands.set(commandId, handler);\n    logger.debug(`Command '${commandId}' registered`);\n  }\n\n  public getHandler(commandId: string): CommandHandler | undefined {\n    return this.commands.get(commandId);\n  }\n\n  public getAllCommands(): string[] {\n    return Array.from(this.commands.keys());\n  }\n\n  public createVSCodeCommand(commandId: string): (...args: any[]) => Promise<void> {\n    return async (...args: any[]) => {\n      const handler = this.getHandler(commandId);\n      if (!handler) {\n        logger.error(`No handler found for command: ${commandId}`);\n        return;\n      }\n\n      try {\n        // Create command context\n        const context = this.createCommandContext();\n        \n        logger.debug(`Executing command: ${commandId}`, { args });\n        await handler(context, ...args);\n        logger.debug(`Command executed successfully: ${commandId}`);\n      } catch (error) {\n        logger.error(`Error executing command '${commandId}'`, error as Error);\n        \n        // Show error to user\n        void vscode.window.showErrorMessage(\n          `Failed to execute command: ${error instanceof Error ? error.message : 'Unknown error'}`\n        );\n      }\n    };\n  }\n\n  private createCommandContext(): CommandContext {\n    const editor = vscode.window.activeTextEditor;\n    const workspaceFolder = editor?.document \n      ? vscode.workspace.getWorkspaceFolder(editor.document.uri)\n      : vscode.workspace.workspaceFolders?.[0];\n\n    return {\n      editor,\n      selection: editor?.selection,\n      document: editor?.document,\n      workspaceFolder,\n    };\n  }\n}\n\nconst commandRegistry = new CommandRegistry();\n\n/**\n * Register all commands with VSCode\n */\nexport async function registerCommands(context: vscode.ExtensionContext): Promise<void> {\n  logger.debug('Registering commands...');\n\n  // Register command handlers\n  registerChatCommands();\n  registerCodeCommands();\n  registerSettingsCommands();\n\n  // Register all commands with VSCode\n  for (const commandId of commandRegistry.getAllCommands()) {\n    const disposable = vscode.commands.registerCommand(\n      commandId,\n      commandRegistry.createVSCodeCommand(commandId)\n    );\n    context.subscriptions.push(disposable);\n  }\n\n  logger.info(`Registered ${commandRegistry.getAllCommands().length} commands`);\n}\n\n/**\n * Register chat-related commands\n */\nfunction registerChatCommands(): void {\n  // Focus chat panel\n  commandRegistry.register('lazycode.chat.focus', chatCommands.focusChat);\n  \n  // Start new chat\n  commandRegistry.register('lazycode.chat.newChat', chatCommands.newChat);\n  \n  // Clear chat history\n  commandRegistry.register('lazycode.chat.clearHistory', chatCommands.clearHistory);\n}\n\n/**\n * Register code-related commands\n */\nfunction registerCodeCommands(): void {\n  // Explain code\n  commandRegistry.register('lazycode.code.explain', codeCommands.explainCode);\n  \n  // Fix code\n  commandRegistry.register('lazycode.code.fix', codeCommands.fixCode);\n  \n  // Generate tests\n  commandRegistry.register('lazycode.code.test', codeCommands.generateTests);\n  \n  // Generate documentation\n  commandRegistry.register('lazycode.code.document', codeCommands.generateDocumentation);\n}\n\n/**\n * Register settings-related commands\n */\nfunction registerSettingsCommands(): void {\n  // Open settings\n  commandRegistry.register('lazycode.settings.open', settingsCommands.openSettings);\n  \n  // Reset settings\n  commandRegistry.register('lazycode.settings.reset', settingsCommands.resetSettings);\n}\n\n/**\n * Get command registry instance (for testing)\n */\nexport function getCommandRegistry(): CommandRegistry {\n  return commandRegistry;\n}\n", "import * as vscode from 'vscode';\nimport { CommandContext } from '@/types';\nimport { logger } from '@/utils/Logger';\n\nexport const chatCommands = {\n  /**\n   * Focus the chat panel\n   */\n  async focusChat(context: CommandContext): Promise<void> {\n    logger.debug('Focusing chat panel');\n    \n    try {\n      // TODO: Implement webview panel focusing\n      // For now, just show a placeholder message\n      await vscode.window.showInformationMessage('Chat panel will be implemented in the next phase');\n      \n      // Future implementation:\n      // const webviewManager = await serviceContainer.get<IWebviewManager>(SERVICE_NAMES.WEBVIEW_MANAGER);\n      // const chatPanel = webviewManager.getPanel('lazycode-chat');\n      // if (chatPanel) {\n      //   chatPanel.panel.reveal();\n      // } else {\n      //   await webviewManager.createPanel('lazycode-chat', 'LazyCode Chat');\n      // }\n    } catch (error) {\n      logger.error('Failed to focus chat panel', error as Error);\n      throw error;\n    }\n  },\n\n  /**\n   * Start a new chat session\n   */\n  async newChat(context: CommandContext): Promise<void> {\n    logger.debug('Starting new chat session');\n    \n    try {\n      // TODO: Implement new chat session creation\n      await vscode.window.showInformationMessage('New chat session will be implemented in the next phase');\n      \n      // Future implementation:\n      // const chatService = await serviceContainer.get<IChatService>(SERVICE_NAMES.CHAT_SERVICE);\n      // const newSession = await chatService.createSession();\n      // \n      // // Focus the chat panel and load the new session\n      // await this.focusChat(context);\n      // \n      // // Send message to webview to load new session\n      // const webviewManager = await serviceContainer.get<IWebviewManager>(SERVICE_NAMES.WEBVIEW_MANAGER);\n      // const chatPanel = webviewManager.getPanel('lazycode-chat');\n      // if (chatPanel) {\n      //   await chatPanel.postMessage({\n      //     type: 'loadSession',\n      //     payload: { sessionId: newSession.id }\n      //   });\n      // }\n    } catch (error) {\n      logger.error('Failed to start new chat session', error as Error);\n      throw error;\n    }\n  },\n\n  /**\n   * Clear chat history\n   */\n  async clearHistory(context: CommandContext): Promise<void> {\n    logger.debug('Clearing chat history');\n    \n    try {\n      const confirmation = await vscode.window.showWarningMessage(\n        'Are you sure you want to clear all chat history? This action cannot be undone.',\n        { modal: true },\n        'Clear History'\n      );\n\n      if (confirmation === 'Clear History') {\n        // TODO: Implement chat history clearing\n        await vscode.window.showInformationMessage('Chat history clearing will be implemented in the next phase');\n        \n        // Future implementation:\n        // const chatService = await serviceContainer.get<IChatService>(SERVICE_NAMES.CHAT_SERVICE);\n        // await chatService.clearAllSessions();\n        // \n        // // Notify webview to refresh\n        // const webviewManager = await serviceContainer.get<IWebviewManager>(SERVICE_NAMES.WEBVIEW_MANAGER);\n        // const chatPanel = webviewManager.getPanel('lazycode-chat');\n        // if (chatPanel) {\n        //   await chatPanel.postMessage({\n        //     type: 'historyCleared'\n        //   });\n        // }\n        \n        logger.info('Chat history cleared');\n      }\n    } catch (error) {\n      logger.error('Failed to clear chat history', error as Error);\n      throw error;\n    }\n  },\n\n  /**\n   * Send selected code to chat\n   */\n  async sendCodeToChat(context: CommandContext): Promise<void> {\n    logger.debug('Sending selected code to chat');\n    \n    try {\n      if (!context.editor || !context.selection || context.selection.isEmpty) {\n        await vscode.window.showWarningMessage('Please select some code to send to chat');\n        return;\n      }\n\n      const selectedText = context.editor.document.getText(context.selection);\n      const language = context.editor.document.languageId;\n      const fileName = context.editor.document.fileName;\n\n      // TODO: Implement sending code to chat\n      await vscode.window.showInformationMessage(\n        `Sending ${selectedText.length} characters of ${language} code to chat`\n      );\n      \n      // Future implementation:\n      // const chatService = await serviceContainer.get<IChatService>(SERVICE_NAMES.CHAT_SERVICE);\n      // const message = `Here's some ${language} code from ${fileName}:\\n\\n\\`\\`\\`${language}\\n${selectedText}\\n\\`\\`\\``;\n      // \n      // // Focus chat panel first\n      // await this.focusChat(context);\n      // \n      // // Send the code to chat\n      // const webviewManager = await serviceContainer.get<IWebviewManager>(SERVICE_NAMES.WEBVIEW_MANAGER);\n      // const chatPanel = webviewManager.getPanel('lazycode-chat');\n      // if (chatPanel) {\n      //   await chatPanel.postMessage({\n      //     type: 'addMessage',\n      //     payload: {\n      //       message,\n      //       context: {\n      //         selectedCode: selectedText,\n      //         filePath: fileName,\n      //         language\n      //       }\n      //     }\n      //   });\n      // }\n    } catch (error) {\n      logger.error('Failed to send code to chat', error as Error);\n      throw error;\n    }\n  },\n\n  /**\n   * Ask question about selected code\n   */\n  async askAboutCode(context: CommandContext, question?: string): Promise<void> {\n    logger.debug('Asking question about selected code');\n    \n    try {\n      if (!context.editor || !context.selection || context.selection.isEmpty) {\n        await vscode.window.showWarningMessage('Please select some code to ask about');\n        return;\n      }\n\n      // Get question from user if not provided\n      if (!question) {\n        question = await vscode.window.showInputBox({\n          prompt: 'What would you like to know about this code?',\n          placeHolder: 'e.g., What does this function do?',\n        });\n\n        if (!question) {\n          return; // User cancelled\n        }\n      }\n\n      const selectedText = context.editor.document.getText(context.selection);\n      const language = context.editor.document.languageId;\n      const fileName = context.editor.document.fileName;\n\n      // TODO: Implement asking question about code\n      await vscode.window.showInformationMessage(\n        `Question: \"${question}\" about ${language} code`\n      );\n      \n      // Future implementation:\n      // const chatService = await serviceContainer.get<IChatService>(SERVICE_NAMES.CHAT_SERVICE);\n      // const message = `${question}\\n\\nCode from ${fileName}:\\n\\`\\`\\`${language}\\n${selectedText}\\n\\`\\`\\``;\n      // \n      // // Focus chat panel and send question\n      // await this.focusChat(context);\n      // \n      // const webviewManager = await serviceContainer.get<IWebviewManager>(SERVICE_NAMES.WEBVIEW_MANAGER);\n      // const chatPanel = webviewManager.getPanel('lazycode-chat');\n      // if (chatPanel) {\n      //   await chatPanel.postMessage({\n      //     type: 'sendMessage',\n      //     payload: {\n      //       message,\n      //       context: {\n      //         selectedCode: selectedText,\n      //         filePath: fileName,\n      //         language\n      //       }\n      //     }\n      //   });\n      // }\n    } catch (error) {\n      logger.error('Failed to ask question about code', error as Error);\n      throw error;\n    }\n  },\n};\n", "import * as vscode from 'vscode';\nimport { CommandContext } from '@/types';\nimport { logger } from '@/utils/Logger';\n\nexport const codeCommands = {\n  /**\n   * Explain selected code\n   */\n  async explainCode(context: CommandContext): Promise<void> {\n    logger.debug('Explaining selected code');\n    \n    try {\n      if (!context.editor || !context.selection || context.selection.isEmpty) {\n        await vscode.window.showWarningMessage('Please select some code to explain');\n        return;\n      }\n\n      const selectedText = context.editor.document.getText(context.selection);\n      const language = context.editor.document.languageId;\n      const fileName = context.editor.document.fileName;\n\n      logger.debug(`Explaining ${selectedText.length} characters of ${language} code`);\n\n      // TODO: Implement code explanation\n      await vscode.window.showInformationMessage(\n        `Code explanation will be implemented in the next phase. Selected: ${selectedText.length} characters of ${language} code.`\n      );\n      \n      // Future implementation:\n      // const chatService = await serviceContainer.get<IChatService>(SERVICE_NAMES.CHAT_SERVICE);\n      // const response = await chatService.explainCode({\n      //   code: selectedText,\n      //   language,\n      //   filePath: fileName\n      // });\n      // \n      // // Show explanation in chat panel\n      // await vscode.commands.executeCommand('lazycode.chat.focus');\n      // \n      // const webviewManager = await serviceContainer.get<IWebviewManager>(SERVICE_NAMES.WEBVIEW_MANAGER);\n      // const chatPanel = webviewManager.getPanel('lazycode-chat');\n      // if (chatPanel) {\n      //   await chatPanel.postMessage({\n      //     type: 'showExplanation',\n      //     payload: {\n      //       code: selectedText,\n      //       explanation: response.explanation,\n      //       language,\n      //       filePath: fileName\n      //     }\n      //   });\n      // }\n    } catch (error) {\n      logger.error('Failed to explain code', error as Error);\n      throw error;\n    }\n  },\n\n  /**\n   * Fix selected code\n   */\n  async fixCode(context: CommandContext): Promise<void> {\n    logger.debug('Fixing selected code');\n    \n    try {\n      if (!context.editor || !context.selection || context.selection.isEmpty) {\n        await vscode.window.showWarningMessage('Please select some code to fix');\n        return;\n      }\n\n      const selectedText = context.editor.document.getText(context.selection);\n      const language = context.editor.document.languageId;\n      const fileName = context.editor.document.fileName;\n\n      logger.debug(`Fixing ${selectedText.length} characters of ${language} code`);\n\n      // Show progress indicator\n      await vscode.window.withProgress(\n        {\n          location: vscode.ProgressLocation.Notification,\n          title: 'Analyzing and fixing code...',\n          cancellable: true,\n        },\n        async (progress, token) => {\n          // TODO: Implement code fixing\n          progress.report({ increment: 50, message: 'Analyzing code...' });\n          \n          // Simulate API call delay\n          await new Promise(resolve => setTimeout(resolve, 1000));\n          \n          if (token.isCancellationRequested) {\n            return;\n          }\n          \n          progress.report({ increment: 50, message: 'Generating fix...' });\n          \n          await vscode.window.showInformationMessage(\n            `Code fixing will be implemented in the next phase. Selected: ${selectedText.length} characters of ${language} code.`\n          );\n          \n          // Future implementation:\n          // const chatService = await serviceContainer.get<IChatService>(SERVICE_NAMES.CHAT_SERVICE);\n          // const response = await chatService.fixCode({\n          //   code: selectedText,\n          //   language,\n          //   filePath: fileName\n          // });\n          // \n          // if (response.fixes && response.fixes.length > 0) {\n          //   // Show fixes in a quick pick\n          //   const selectedFix = await vscode.window.showQuickPick(\n          //     response.fixes.map(fix => ({\n          //       label: fix.title,\n          //       description: fix.description,\n          //       detail: fix.code,\n          //       fix\n          //     })),\n          //     {\n          //       placeHolder: 'Select a fix to apply',\n          //       matchOnDescription: true,\n          //       matchOnDetail: true\n          //     }\n          //   );\n          // \n          //   if (selectedFix) {\n          //     // Apply the fix\n          //     const edit = new vscode.WorkspaceEdit();\n          //     edit.replace(context.editor.document.uri, context.selection, selectedFix.fix.code);\n          //     await vscode.workspace.applyEdit(edit);\n          //   }\n          // }\n        }\n      );\n    } catch (error) {\n      logger.error('Failed to fix code', error as Error);\n      throw error;\n    }\n  },\n\n  /**\n   * Generate tests for selected code\n   */\n  async generateTests(context: CommandContext): Promise<void> {\n    logger.debug('Generating tests for selected code');\n    \n    try {\n      if (!context.editor || !context.selection || context.selection.isEmpty) {\n        await vscode.window.showWarningMessage('Please select some code to generate tests for');\n        return;\n      }\n\n      const selectedText = context.editor.document.getText(context.selection);\n      const language = context.editor.document.languageId;\n      const fileName = context.editor.document.fileName;\n\n      logger.debug(`Generating tests for ${selectedText.length} characters of ${language} code`);\n\n      // Ask user for test framework preference\n      const testFramework = await vscode.window.showQuickPick(\n        [\n          { label: 'Jest', description: 'JavaScript/TypeScript testing framework' },\n          { label: 'Mocha', description: 'JavaScript testing framework' },\n          { label: 'PyTest', description: 'Python testing framework' },\n          { label: 'JUnit', description: 'Java testing framework' },\n          { label: 'Auto-detect', description: 'Let LazyCode choose the best framework' },\n        ],\n        {\n          placeHolder: 'Select a testing framework',\n          ignoreFocusOut: true,\n        }\n      );\n\n      if (!testFramework) {\n        return; // User cancelled\n      }\n\n      // TODO: Implement test generation\n      await vscode.window.showInformationMessage(\n        `Test generation with ${testFramework.label} will be implemented in the next phase.`\n      );\n      \n      // Future implementation:\n      // const chatService = await serviceContainer.get<IChatService>(SERVICE_NAMES.CHAT_SERVICE);\n      // const response = await chatService.generateTests({\n      //   code: selectedText,\n      //   language,\n      //   filePath: fileName,\n      //   testFramework: testFramework.label\n      // });\n      // \n      // if (response.tests) {\n      //   // Create a new test file or show the tests\n      //   const action = await vscode.window.showInformationMessage(\n      //     'Tests generated successfully!',\n      //     'Create Test File',\n      //     'Show in Chat',\n      //     'Copy to Clipboard'\n      //   );\n      // \n      //   switch (action) {\n      //     case 'Create Test File':\n      //       await this.createTestFile(fileName, response.tests, language);\n      //       break;\n      //     case 'Show in Chat':\n      //       await vscode.commands.executeCommand('lazycode.chat.focus');\n      //       // Show tests in chat\n      //       break;\n      //     case 'Copy to Clipboard':\n      //       await vscode.env.clipboard.writeText(response.tests);\n      //       await vscode.window.showInformationMessage('Tests copied to clipboard');\n      //       break;\n      //   }\n      // }\n    } catch (error) {\n      logger.error('Failed to generate tests', error as Error);\n      throw error;\n    }\n  },\n\n  /**\n   * Generate documentation for selected code\n   */\n  async generateDocumentation(context: CommandContext): Promise<void> {\n    logger.debug('Generating documentation for selected code');\n    \n    try {\n      if (!context.editor || !context.selection || context.selection.isEmpty) {\n        await vscode.window.showWarningMessage('Please select some code to document');\n        return;\n      }\n\n      const selectedText = context.editor.document.getText(context.selection);\n      const language = context.editor.document.languageId;\n      const fileName = context.editor.document.fileName;\n\n      logger.debug(`Generating documentation for ${selectedText.length} characters of ${language} code`);\n\n      // Ask user for documentation style\n      const docStyle = await vscode.window.showQuickPick(\n        [\n          { label: 'JSDoc', description: 'JavaScript/TypeScript documentation' },\n          { label: 'Sphinx', description: 'Python documentation' },\n          { label: 'Javadoc', description: 'Java documentation' },\n          { label: 'XML Doc', description: 'C# documentation' },\n          { label: 'Auto-detect', description: 'Let LazyCode choose the best style' },\n        ],\n        {\n          placeHolder: 'Select documentation style',\n          ignoreFocusOut: true,\n        }\n      );\n\n      if (!docStyle) {\n        return; // User cancelled\n      }\n\n      // TODO: Implement documentation generation\n      await vscode.window.showInformationMessage(\n        `Documentation generation with ${docStyle.label} will be implemented in the next phase.`\n      );\n      \n      // Future implementation:\n      // const chatService = await serviceContainer.get<IChatService>(SERVICE_NAMES.CHAT_SERVICE);\n      // const response = await chatService.generateDocumentation({\n      //   code: selectedText,\n      //   language,\n      //   filePath: fileName,\n      //   docStyle: docStyle.label\n      // });\n      // \n      // if (response.documentation) {\n      //   // Insert documentation above the selected code\n      //   const edit = new vscode.WorkspaceEdit();\n      //   const insertPosition = new vscode.Position(context.selection.start.line, 0);\n      //   edit.insert(context.editor.document.uri, insertPosition, response.documentation + '\\n');\n      //   await vscode.workspace.applyEdit(edit);\n      // \n      //   await vscode.window.showInformationMessage('Documentation added successfully!');\n      // }\n    } catch (error) {\n      logger.error('Failed to generate documentation', error as Error);\n      throw error;\n    }\n  },\n\n  /**\n   * Helper method to create a test file\n   */\n  async createTestFile(originalFileName: string, testCode: string, language: string): Promise<void> {\n    try {\n      // Generate test file name\n      const path = require('path');\n      const ext = path.extname(originalFileName);\n      const baseName = path.basename(originalFileName, ext);\n      const dirName = path.dirname(originalFileName);\n      \n      let testFileName: string;\n      if (language === 'javascript' || language === 'typescript') {\n        testFileName = path.join(dirName, `${baseName}.test${ext}`);\n      } else if (language === 'python') {\n        testFileName = path.join(dirName, `test_${baseName}.py`);\n      } else {\n        testFileName = path.join(dirName, `${baseName}_test${ext}`);\n      }\n\n      // Create and open the test file\n      const testUri = vscode.Uri.file(testFileName);\n      const edit = new vscode.WorkspaceEdit();\n      edit.createFile(testUri, { ignoreIfExists: true });\n      edit.insert(testUri, new vscode.Position(0, 0), testCode);\n      \n      await vscode.workspace.applyEdit(edit);\n      \n      // Open the test file\n      const document = await vscode.workspace.openTextDocument(testUri);\n      await vscode.window.showTextDocument(document);\n      \n      logger.info(`Test file created: ${testFileName}`);\n    } catch (error) {\n      logger.error('Failed to create test file', error as Error);\n      throw error;\n    }\n  },\n};\n", "import * as vscode from 'vscode';\nimport { CommandContext } from '@/types';\nimport { logger } from '@/utils/Logger';\nimport { serviceContainer, SERVICE_NAMES } from '@/services/ServiceContainer';\nimport { ConfigurationService } from '@/services/ConfigurationService';\n\nexport const settingsCommands = {\n  /**\n   * Open LazyCode settings\n   */\n  async openSettings(context: CommandContext): Promise<void> {\n    logger.debug('Opening LazyCode settings');\n    \n    try {\n      // TODO: Implement settings webview panel\n      // For now, open VSCode settings filtered to LazyCode\n      await vscode.commands.executeCommand('workbench.action.openSettings', 'lazycode');\n      \n      // Future implementation:\n      // const webviewManager = await serviceContainer.get<IWebviewManager>(SERVICE_NAMES.WEBVIEW_MANAGER);\n      // const settingsPanel = webviewManager.getPanel('lazycode-settings');\n      // if (settingsPanel) {\n      //   settingsPanel.panel.reveal();\n      // } else {\n      //   await webviewManager.createPanel('lazycode-settings', 'LazyCode Settings');\n      // }\n    } catch (error) {\n      logger.error('Failed to open settings', error as Error);\n      throw error;\n    }\n  },\n\n  /**\n   * Reset LazyCode settings to defaults\n   */\n  async resetSettings(context: CommandContext): Promise<void> {\n    logger.debug('Resetting LazyCode settings');\n    \n    try {\n      const confirmation = await vscode.window.showWarningMessage(\n        'Are you sure you want to reset all LazyCode settings to their default values?',\n        { modal: true },\n        'Reset Settings'\n      );\n\n      if (confirmation === 'Reset Settings') {\n        const configService = await serviceContainer.get<ConfigurationService>(SERVICE_NAMES.CONFIGURATION);\n        \n        // Reset all configuration sections\n        await configService.setCompletionsConfig({\n          enableAutomaticCompletions: true,\n          enableQuickSuggestions: true,\n        });\n\n        await configService.setChatConfig({\n          enableEmptyFileHint: true,\n        });\n\n        await configService.setAdvancedConfig({\n          apiToken: '',\n          apiUrl: '',\n        });\n\n        await vscode.window.showInformationMessage('LazyCode settings have been reset to defaults');\n        logger.info('Settings reset to defaults');\n      }\n    } catch (error) {\n      logger.error('Failed to reset settings', error as Error);\n      throw error;\n    }\n  },\n\n  /**\n   * Configure API settings\n   */\n  async configureApi(context: CommandContext): Promise<void> {\n    logger.debug('Configuring API settings');\n    \n    try {\n      const configService = await serviceContainer.get<ConfigurationService>(SERVICE_NAMES.CONFIGURATION);\n      const currentConfig = configService.getAdvancedConfig();\n\n      // Get API URL\n      const apiUrl = await vscode.window.showInputBox({\n        prompt: 'Enter the API URL for LazyCode service',\n        value: currentConfig.apiUrl,\n        placeHolder: 'https://api.example.com',\n        validateInput: (value) => {\n          if (value && !this.isValidUrl(value)) {\n            return 'Please enter a valid URL';\n          }\n          return undefined;\n        },\n      });\n\n      if (apiUrl === undefined) {\n        return; // User cancelled\n      }\n\n      // Get API token\n      const apiToken = await vscode.window.showInputBox({\n        prompt: 'Enter your API token',\n        value: currentConfig.apiToken,\n        placeHolder: 'Your API token',\n        password: true,\n      });\n\n      if (apiToken === undefined) {\n        return; // User cancelled\n      }\n\n      // Save configuration\n      await configService.setAdvancedConfig({\n        apiUrl,\n        apiToken,\n      });\n\n      // Validate configuration\n      const validation = configService.validateConfiguration();\n      if (validation.isValid) {\n        await vscode.window.showInformationMessage('API configuration saved successfully');\n      } else {\n        await vscode.window.showWarningMessage(\n          `Configuration saved with warnings: ${validation.errors.join(', ')}`\n        );\n      }\n\n      logger.info('API configuration updated');\n    } catch (error) {\n      logger.error('Failed to configure API settings', error as Error);\n      throw error;\n    }\n  },\n\n  /**\n   * Toggle automatic completions\n   */\n  async toggleAutomaticCompletions(context: CommandContext): Promise<void> {\n    logger.debug('Toggling automatic completions');\n    \n    try {\n      const configService = await serviceContainer.get<ConfigurationService>(SERVICE_NAMES.CONFIGURATION);\n      const currentConfig = configService.getCompletionsConfig();\n      \n      const newValue = !currentConfig.enableAutomaticCompletions;\n      await configService.setCompletionsConfig({\n        enableAutomaticCompletions: newValue,\n      });\n\n      const status = newValue ? 'enabled' : 'disabled';\n      await vscode.window.showInformationMessage(`Automatic completions ${status}`);\n      \n      logger.info(`Automatic completions ${status}`);\n    } catch (error) {\n      logger.error('Failed to toggle automatic completions', error as Error);\n      throw error;\n    }\n  },\n\n  /**\n   * Show configuration status\n   */\n  async showConfigurationStatus(context: CommandContext): Promise<void> {\n    logger.debug('Showing configuration status');\n    \n    try {\n      const configService = await serviceContainer.get<ConfigurationService>(SERVICE_NAMES.CONFIGURATION);\n      const config = configService.getFullConfiguration();\n      const validation = configService.validateConfiguration();\n\n      const statusItems: vscode.QuickPickItem[] = [\n        {\n          label: '$(gear) General Settings',\n          description: 'View general configuration',\n          detail: `Completions: ${config.completions.enableAutomaticCompletions ? 'On' : 'Off'}, Quick Suggestions: ${config.completions.enableQuickSuggestions ? 'On' : 'Off'}`,\n        },\n        {\n          label: '$(comment-discussion) Chat Settings',\n          description: 'View chat configuration',\n          detail: `Empty File Hint: ${config.chat.enableEmptyFileHint ? 'On' : 'Off'}`,\n        },\n        {\n          label: '$(key) API Settings',\n          description: 'View API configuration',\n          detail: `URL: ${config.advanced.apiUrl || 'Not set'}, Token: ${config.advanced.apiToken ? 'Set' : 'Not set'}`,\n        },\n        {\n          label: validation.isValid ? '$(check) Configuration Valid' : '$(warning) Configuration Issues',\n          description: validation.isValid ? 'All settings are valid' : 'Some settings need attention',\n          detail: validation.isValid ? 'Ready to use' : validation.errors.join(', '),\n        },\n      ];\n\n      const selected = await vscode.window.showQuickPick(statusItems, {\n        placeHolder: 'LazyCode Configuration Status',\n        ignoreFocusOut: true,\n      });\n\n      if (selected) {\n        // Handle selection if needed\n        if (selected.label.includes('API Settings')) {\n          await this.configureApi(context);\n        } else if (selected.label.includes('General Settings')) {\n          await this.openSettings(context);\n        }\n      }\n    } catch (error) {\n      logger.error('Failed to show configuration status', error as Error);\n      throw error;\n    }\n  },\n\n  /**\n   * Export configuration\n   */\n  async exportConfiguration(context: CommandContext): Promise<void> {\n    logger.debug('Exporting configuration');\n    \n    try {\n      const configService = await serviceContainer.get<ConfigurationService>(SERVICE_NAMES.CONFIGURATION);\n      const config = configService.getFullConfiguration();\n\n      // Remove sensitive information\n      const exportConfig = {\n        ...config,\n        advanced: {\n          ...config.advanced,\n          apiToken: config.advanced.apiToken ? '[REDACTED]' : '',\n        },\n      };\n\n      const configJson = JSON.stringify(exportConfig, null, 2);\n      \n      const action = await vscode.window.showInformationMessage(\n        'Configuration exported successfully!',\n        'Copy to Clipboard',\n        'Save to File'\n      );\n\n      if (action === 'Copy to Clipboard') {\n        await vscode.env.clipboard.writeText(configJson);\n        await vscode.window.showInformationMessage('Configuration copied to clipboard');\n      } else if (action === 'Save to File') {\n        const uri = await vscode.window.showSaveDialog({\n          defaultUri: vscode.Uri.file('lazycode-config.json'),\n          filters: {\n            'JSON Files': ['json'],\n            'All Files': ['*'],\n          },\n        });\n\n        if (uri) {\n          await vscode.workspace.fs.writeFile(uri, Buffer.from(configJson, 'utf8'));\n          await vscode.window.showInformationMessage(`Configuration saved to ${uri.fsPath}`);\n        }\n      }\n    } catch (error) {\n      logger.error('Failed to export configuration', error as Error);\n      throw error;\n    }\n  },\n\n  /**\n   * Helper method to validate URL\n   */\n  isValidUrl(url: string): boolean {\n    try {\n      new URL(url);\n      return true;\n    } catch {\n      return false;\n    }\n  },\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAAAA,UAAwB;;;ACAxB,aAAwB;AAUjB,IAAM,SAAN,MAAM,QAA0B;AAAA,EAK7B,cAAc;AAFtB,SAAQ,WAAqB;AAG3B,SAAK,gBAAuB,cAAO,oBAAoB,UAAU;AAAA,EACnE;AAAA,EAEA,OAAc,cAAsB;AAClC,QAAI,CAAC,QAAO,UAAU;AACpB,cAAO,WAAW,IAAI,QAAO;AAAA,IAC/B;AACA,WAAO,QAAO;AAAA,EAChB;AAAA,EAEO,YAAY,OAAuB;AACxC,SAAK,WAAW;AAAA,EAClB;AAAA,EAEO,MAAM,YAAoB,MAAmB;AAClD,QAAI,KAAK,YAAY,eAAgB;AACnC,WAAK,IAAI,SAAS,SAAS,GAAG,IAAI;AAAA,IACpC;AAAA,EACF;AAAA,EAEO,KAAK,YAAoB,MAAmB;AACjD,QAAI,KAAK,YAAY,cAAe;AAClC,WAAK,IAAI,QAAQ,SAAS,GAAG,IAAI;AAAA,IACnC;AAAA,EACF;AAAA,EAEO,KAAK,YAAoB,MAAmB;AACjD,QAAI,KAAK,YAAY,cAAe;AAClC,WAAK,IAAI,QAAQ,SAAS,GAAG,IAAI;AAAA,IACnC;AAAA,EACF;AAAA,EAEO,MAAM,SAAiB,OAAqB;AACjD,QAAI,KAAK,YAAY,eAAgB;AACnC,WAAK,IAAI,SAAS,SAAS,KAAK;AAChC,UAAI,OAAO;AACT,aAAK,IAAI,SAAS,gBAAgB,MAAM,KAAK,EAAE;AAAA,MACjD;AAAA,IACF;AAAA,EACF;AAAA,EAEQ,IAAI,OAAe,YAAoB,MAAmB;AAChE,UAAM,aAAY,oBAAI,KAAK,GAAE,YAAY;AACzC,UAAM,mBAAmB,IAAI,SAAS,MAAM,KAAK,KAAK,OAAO;AAG7D,SAAK,cAAc,WAAW,gBAAgB;AAG9C,QAAI,KAAK,SAAS,GAAG;AACnB,WAAK,QAAQ,SAAO;AAClB,YAAI,eAAe,OAAO;AACxB,eAAK,cAAc,WAAW,YAAY,IAAI,OAAO,EAAE;AACvD,cAAI,IAAI,OAAO;AACb,iBAAK,cAAc,WAAW,YAAY,IAAI,KAAK,EAAE;AAAA,UACvD;AAAA,QACF,WAAW,OAAO,QAAQ,UAAU;AAClC,eAAK,cAAc,WAAW,KAAK,KAAK,UAAU,KAAK,MAAM,CAAC,CAAC,EAAE;AAAA,QACnE,OAAO;AACL,eAAK,cAAc,WAAW,KAAK,OAAO,GAAG,CAAC,EAAE;AAAA,QAClD;AAAA,MACF,CAAC;AAAA,IACH;AAGA,QAAI,QAAQ,IAAI,aAAa,eAAe;AAC1C,cAAQ,OAAO;AAAA,QACb,KAAK;AACH,kBAAQ,MAAM,kBAAkB,GAAG,IAAI;AACvC;AAAA,QACF,KAAK;AACH,kBAAQ,KAAK,kBAAkB,GAAG,IAAI;AACtC;AAAA,QACF,KAAK;AACH,kBAAQ,KAAK,kBAAkB,GAAG,IAAI;AACtC;AAAA,QACF,KAAK;AACH,kBAAQ,MAAM,kBAAkB,GAAG,IAAI;AACvC;AAAA,MACJ;AAAA,IACF;AAAA,EACF;AAAA,EAEO,OAAa;AAClB,SAAK,cAAc,KAAK;AAAA,EAC1B;AAAA,EAEO,UAAgB;AACrB,SAAK,cAAc,QAAQ;AAAA,EAC7B;AACF;AAGO,IAAM,SAAS,OAAO,YAAY;;;ACjGlC,IAAM,mBAAN,MAAM,kBAAiB;AAAA,EAKpB,cAAc;AAHtB,SAAQ,WAAW,oBAAI,IAA+B;AACtD,SAAQ,cAAmC,CAAC;AAAA,EAErB;AAAA,EAEvB,OAAc,cAAgC;AAC5C,QAAI,CAAC,kBAAiB,UAAU;AAC9B,wBAAiB,WAAW,IAAI,kBAAiB;AAAA,IACnD;AACA,WAAO,kBAAiB;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA,EAKO,SACL,MACA,SACA,UAAmC,CAAC,GAC9B;AACN,UAAM,EAAE,YAAY,KAAK,IAAI;AAE7B,QAAI,KAAK,SAAS,IAAI,IAAI,GAAG;AAC3B,aAAO,KAAK,YAAY,IAAI,uCAAuC;AAAA,IACrE;AAEA,SAAK,SAAS,IAAI,MAAM;AAAA,MACtB;AAAA,MACA;AAAA,IACF,CAAC;AAED,WAAO,MAAM,YAAY,IAAI,4BAA4B,SAAS,GAAG;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA,EAKO,iBAAoB,MAAc,UAAmB;AAC1D,SAAK,SAAS,IAAI,MAAM;AAAA,MACtB,SAAS,MAAM;AAAA,MACf,WAAW;AAAA,MACX;AAAA,IACF,CAAC;AAED,WAAO,MAAM,qBAAqB,IAAI,cAAc;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA,EAKA,MAAa,IAAO,MAA0B;AAC5C,UAAM,oBAAoB,KAAK,SAAS,IAAI,IAAI;AAEhD,QAAI,CAAC,mBAAmB;AACtB,YAAM,IAAI,MAAM,YAAY,IAAI,qBAAqB;AAAA,IACvD;AAGA,QAAI,kBAAkB,aAAa,kBAAkB,UAAU;AAC7D,aAAO,MAAM,kBAAkB;AAAA,IACjC;AAGA,QAAI;AACF,YAAM,WAAW,MAAM,kBAAkB,QAAQ;AAGjD,UAAI,kBAAkB,WAAW;AAC/B,0BAAkB,WAAW;AAAA,MAC/B;AAEA,aAAO,MAAM,YAAY,IAAI,gBAAgB;AAC7C,aAAO;AAAA,IACT,SAAS,OAAO;AACd,aAAO,MAAM,kCAAkC,IAAI,KAAK,KAAc;AACtE,YAAM;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKO,QAAW,MAAiB;AACjC,UAAM,oBAAoB,KAAK,SAAS,IAAI,IAAI;AAEhD,QAAI,CAAC,mBAAmB;AACtB,YAAM,IAAI,MAAM,YAAY,IAAI,qBAAqB;AAAA,IACvD;AAEA,QAAI,CAAC,kBAAkB,UAAU;AAC/B,YAAM,IAAI,MAAM,YAAY,IAAI,+CAA+C;AAAA,IACjF;AAEA,QAAI,kBAAkB,oBAAoB,SAAS;AACjD,YAAM,IAAI,MAAM,YAAY,IAAI,mDAAmD;AAAA,IACrF;AAEA,WAAO,kBAAkB;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA,EAKO,IAAI,MAAuB;AAChC,WAAO,KAAK,SAAS,IAAI,IAAI;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA,EAKO,eAAe,MAAuB;AAC3C,UAAM,oBAAoB,KAAK,SAAS,IAAI,IAAI;AAChD,WAAO,mBAAmB,aAAa;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA,EAKO,WAAW,MAAoB;AACpC,UAAM,oBAAoB,KAAK,SAAS,IAAI,IAAI;AAEhD,QAAI,mBAAmB,UAAU;AAE/B,YAAM,WAAW,kBAAkB;AACnC,UAAI,YAAY,OAAO,aAAa,YAAY,aAAa,UAAU;AACrE,YAAI;AACF,UAAC,SAAiB,QAAQ;AAAA,QAC5B,SAAS,OAAO;AACd,iBAAO,KAAK,4BAA4B,IAAI,KAAK,KAAK;AAAA,QACxD;AAAA,MACF;AAAA,IACF;AAEA,SAAK,SAAS,OAAO,IAAI;AACzB,WAAO,MAAM,YAAY,IAAI,gBAAgB;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA,EAKO,kBAA4B;AACjC,WAAO,MAAM,KAAK,KAAK,SAAS,KAAK,CAAC;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA,EAKO,QAAc;AAEnB,eAAW,CAAC,MAAM,iBAAiB,KAAK,KAAK,UAAU;AACrD,UAAI,kBAAkB,UAAU;AAC9B,cAAM,WAAW,kBAAkB;AACnC,YAAI,YAAY,OAAO,aAAa,YAAY,aAAa,UAAU;AACrE,cAAI;AACF,YAAC,SAAiB,QAAQ;AAAA,UAC5B,SAAS,OAAO;AACd,mBAAO,KAAK,4BAA4B,IAAI,KAAK,KAAK;AAAA,UACxD;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,SAAK,SAAS,MAAM;AACpB,WAAO,MAAM,sBAAsB;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA,EAKO,UAAgB;AACrB,SAAK,MAAM;AACX,SAAK,YAAY,QAAQ,gBAAc,WAAW,QAAQ,CAAC;AAC3D,SAAK,cAAc,CAAC;AAAA,EACtB;AACF;AAGO,IAAM,mBAAmB,iBAAiB,YAAY;AAGtD,IAAM,gBAAgB;AAAA,EAC3B,eAAe;AAAA,EACf,iBAAiB;AAAA,EACjB,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,oBAAoB;AACtB;;;ACxMA,IAAAC,UAAwB;AAIjB,IAAM,uBAAN,MAAM,sBAAsD;AAAA,EAIjE,cAAc;AAFd,SAAQ,cAAmC,CAAC;AAI1C,SAAK,YAAY;AAAA,MACR,kBAAU,yBAAyB,OAAK;AAC7C,YAAI,EAAE,qBAAqB,sBAAqB,qBAAqB,GAAG;AACtE,iBAAO,MAAM,uBAAuB;AAAA,QACtC;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EAZA;AAAA,SAAwB,wBAAwB;AAAA;AAAA,EAczC,IAAO,KAAa,cAAqB;AAC9C,UAAM,SAAgB,kBAAU,iBAAiB,sBAAqB,qBAAqB;AAC3F,UAAM,QAAQ,OAAO,IAAO,GAAG;AAE/B,QAAI,UAAU,UAAa,iBAAiB,QAAW;AACrD,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,EACT;AAAA,EAEA,MAAa,IAAI,KAAa,OAAY,QAAoD;AAC5F,QAAI;AACF,YAAM,SAAgB,kBAAU,iBAAiB,sBAAqB,qBAAqB;AAC3F,YAAM,OAAO,OAAO,KAAK,OAAO,UAAiB,4BAAoB,MAAM;AAC3E,aAAO,MAAM,0BAA0B,GAAG,MAAM,KAAK,UAAU,KAAK,CAAC,EAAE;AAAA,IACzE,SAAS,OAAO;AACd,aAAO,MAAM,mCAAmC,GAAG,IAAI,KAAc;AACrE,YAAM;AAAA,IACR;AAAA,EACF;AAAA,EAEO,YACL,UACmB;AACnB,WAAc,kBAAU,yBAAyB,OAAK;AACpD,UAAI,EAAE,qBAAqB,sBAAqB,qBAAqB,GAAG;AACtE,iBAAS,CAAC;AAAA,MACZ;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA,EAGO,uBAA6D;AAClE,WAAO;AAAA,MACL,4BAA4B,KAAK,IAAI,0CAA0C,IAAI;AAAA,MACnF,wBAAwB,KAAK,IAAI,sCAAsC,IAAI;AAAA,IAC7E;AAAA,EACF;AAAA,EAEO,gBAA+C;AACpD,WAAO;AAAA,MACL,qBAAqB,KAAK,IAAI,4BAA4B,IAAI;AAAA,IAChE;AAAA,EACF;AAAA,EAEO,oBAAuD;AAC5D,WAAO;AAAA,MACL,UAAU,KAAK,IAAI,qBAAqB,EAAE;AAAA,MAC1C,QAAQ,KAAK,IAAI,mBAAmB,EAAE;AAAA,IACxC;AAAA,EACF;AAAA,EAEO,uBAA8C;AACnD,WAAO;AAAA,MACL,aAAa,KAAK,qBAAqB;AAAA,MACvC,MAAM,KAAK,cAAc;AAAA,MACzB,UAAU,KAAK,kBAAkB;AAAA,IACnC;AAAA,EACF;AAAA;AAAA,EAGA,MAAa,qBACX,QACe;AACf,UAAM,WAA4B,CAAC;AAEnC,QAAI,OAAO,+BAA+B,QAAW;AACnD,eAAS,KAAK,KAAK,IAAI,0CAA0C,OAAO,0BAA0B,CAAC;AAAA,IACrG;AAEA,QAAI,OAAO,2BAA2B,QAAW;AAC/C,eAAS,KAAK,KAAK,IAAI,sCAAsC,OAAO,sBAAsB,CAAC;AAAA,IAC7F;AAEA,UAAM,QAAQ,IAAI,QAAQ;AAAA,EAC5B;AAAA,EAEA,MAAa,cACX,QACe;AACf,UAAM,WAA4B,CAAC;AAEnC,QAAI,OAAO,wBAAwB,QAAW;AAC5C,eAAS,KAAK,KAAK,IAAI,4BAA4B,OAAO,mBAAmB,CAAC;AAAA,IAChF;AAEA,UAAM,QAAQ,IAAI,QAAQ;AAAA,EAC5B;AAAA,EAEA,MAAa,kBACX,QACe;AACf,UAAM,WAA4B,CAAC;AAEnC,QAAI,OAAO,aAAa,QAAW;AACjC,eAAS,KAAK,KAAK,IAAI,qBAAqB,OAAO,QAAQ,CAAC;AAAA,IAC9D;AAEA,QAAI,OAAO,WAAW,QAAW;AAC/B,eAAS,KAAK,KAAK,IAAI,mBAAmB,OAAO,MAAM,CAAC;AAAA,IAC1D;AAEA,UAAM,QAAQ,IAAI,QAAQ;AAAA,EAC5B;AAAA;AAAA,EAGO,wBAAgE;AACrE,UAAM,SAAmB,CAAC;AAC1B,UAAM,SAAS,KAAK,qBAAqB;AAGzC,QAAI,CAAC,OAAO,SAAS,YAAY,CAAC,OAAO,SAAS,QAAQ;AACxD,aAAO,KAAK,gDAAgD;AAAA,IAC9D;AAEA,QAAI,OAAO,SAAS,UAAU,CAAC,KAAK,WAAW,OAAO,SAAS,MAAM,GAAG;AACtE,aAAO,KAAK,6BAA6B;AAAA,IAC3C;AAEA,WAAO;AAAA,MACL,SAAS,OAAO,WAAW;AAAA,MAC3B;AAAA,IACF;AAAA,EACF;AAAA,EAEQ,WAAW,KAAsB;AACvC,QAAI;AACF,UAAI,IAAI,GAAG;AACX,aAAO;AAAA,IACT,QAAQ;AACN,aAAO;AAAA,IACT;AAAA,EACF;AAAA,EAEO,UAAgB;AACrB,SAAK,YAAY,QAAQ,gBAAc,WAAW,QAAQ,CAAC;AAC3D,SAAK,cAAc,CAAC;AAAA,EACtB;AACF;;;AC9JA,IAAAC,UAAwB;;;ACAxB,IAAAC,UAAwB;AAIjB,IAAM,eAAe;AAAA;AAAA;AAAA;AAAA,EAI1B,MAAM,UAAU,SAAwC;AACtD,WAAO,MAAM,qBAAqB;AAElC,QAAI;AAGF,YAAa,eAAO,uBAAuB,kDAAkD;AAAA,IAU/F,SAAS,OAAO;AACd,aAAO,MAAM,8BAA8B,KAAc;AACzD,YAAM;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,QAAQ,SAAwC;AACpD,WAAO,MAAM,2BAA2B;AAExC,QAAI;AAEF,YAAa,eAAO,uBAAuB,wDAAwD;AAAA,IAkBrG,SAAS,OAAO;AACd,aAAO,MAAM,oCAAoC,KAAc;AAC/D,YAAM;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,aAAa,SAAwC;AACzD,WAAO,MAAM,uBAAuB;AAEpC,QAAI;AACF,YAAM,eAAe,MAAa,eAAO;AAAA,QACvC;AAAA,QACA,EAAE,OAAO,KAAK;AAAA,QACd;AAAA,MACF;AAEA,UAAI,iBAAiB,iBAAiB;AAEpC,cAAa,eAAO,uBAAuB,6DAA6D;AAexG,eAAO,KAAK,sBAAsB;AAAA,MACpC;AAAA,IACF,SAAS,OAAO;AACd,aAAO,MAAM,gCAAgC,KAAc;AAC3D,YAAM;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,eAAe,SAAwC;AAC3D,WAAO,MAAM,+BAA+B;AAE5C,QAAI;AACF,UAAI,CAAC,QAAQ,UAAU,CAAC,QAAQ,aAAa,QAAQ,UAAU,SAAS;AACtE,cAAa,eAAO,mBAAmB,yCAAyC;AAChF;AAAA,MACF;AAEA,YAAM,eAAe,QAAQ,OAAO,SAAS,QAAQ,QAAQ,SAAS;AACtE,YAAM,WAAW,QAAQ,OAAO,SAAS;AACzC,YAAM,WAAW,QAAQ,OAAO,SAAS;AAGzC,YAAa,eAAO;AAAA,QAClB,WAAW,aAAa,MAAM,kBAAkB,QAAQ;AAAA,MAC1D;AAAA,IAyBF,SAAS,OAAO;AACd,aAAO,MAAM,+BAA+B,KAAc;AAC1D,YAAM;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,aAAa,SAAyB,UAAkC;AAC5E,WAAO,MAAM,qCAAqC;AAElD,QAAI;AACF,UAAI,CAAC,QAAQ,UAAU,CAAC,QAAQ,aAAa,QAAQ,UAAU,SAAS;AACtE,cAAa,eAAO,mBAAmB,sCAAsC;AAC7E;AAAA,MACF;AAGA,UAAI,CAAC,UAAU;AACb,mBAAW,MAAa,eAAO,aAAa;AAAA,UAC1C,QAAQ;AAAA,UACR,aAAa;AAAA,QACf,CAAC;AAED,YAAI,CAAC,UAAU;AACb;AAAA,QACF;AAAA,MACF;AAEA,YAAM,eAAe,QAAQ,OAAO,SAAS,QAAQ,QAAQ,SAAS;AACtE,YAAM,WAAW,QAAQ,OAAO,SAAS;AACzC,YAAM,WAAW,QAAQ,OAAO,SAAS;AAGzC,YAAa,eAAO;AAAA,QAClB,cAAc,QAAQ,WAAW,QAAQ;AAAA,MAC3C;AAAA,IAwBF,SAAS,OAAO;AACd,aAAO,MAAM,qCAAqC,KAAc;AAChE,YAAM;AAAA,IACR;AAAA,EACF;AACF;;;AClNA,IAAAC,UAAwB;AAIjB,IAAM,eAAe;AAAA;AAAA;AAAA;AAAA,EAI1B,MAAM,YAAY,SAAwC;AACxD,WAAO,MAAM,0BAA0B;AAEvC,QAAI;AACF,UAAI,CAAC,QAAQ,UAAU,CAAC,QAAQ,aAAa,QAAQ,UAAU,SAAS;AACtE,cAAa,eAAO,mBAAmB,oCAAoC;AAC3E;AAAA,MACF;AAEA,YAAM,eAAe,QAAQ,OAAO,SAAS,QAAQ,QAAQ,SAAS;AACtE,YAAM,WAAW,QAAQ,OAAO,SAAS;AACzC,YAAM,WAAW,QAAQ,OAAO,SAAS;AAEzC,aAAO,MAAM,cAAc,aAAa,MAAM,kBAAkB,QAAQ,OAAO;AAG/E,YAAa,eAAO;AAAA,QAClB,qEAAqE,aAAa,MAAM,kBAAkB,QAAQ;AAAA,MACpH;AAAA,IA0BF,SAAS,OAAO;AACd,aAAO,MAAM,0BAA0B,KAAc;AACrD,YAAM;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,QAAQ,SAAwC;AACpD,WAAO,MAAM,sBAAsB;AAEnC,QAAI;AACF,UAAI,CAAC,QAAQ,UAAU,CAAC,QAAQ,aAAa,QAAQ,UAAU,SAAS;AACtE,cAAa,eAAO,mBAAmB,gCAAgC;AACvE;AAAA,MACF;AAEA,YAAM,eAAe,QAAQ,OAAO,SAAS,QAAQ,QAAQ,SAAS;AACtE,YAAM,WAAW,QAAQ,OAAO,SAAS;AACzC,YAAM,WAAW,QAAQ,OAAO,SAAS;AAEzC,aAAO,MAAM,UAAU,aAAa,MAAM,kBAAkB,QAAQ,OAAO;AAG3E,YAAa,eAAO;AAAA,QAClB;AAAA,UACE,UAAiB,yBAAiB;AAAA,UAClC,OAAO;AAAA,UACP,aAAa;AAAA,QACf;AAAA,QACA,OAAO,UAAU,UAAU;AAEzB,mBAAS,OAAO,EAAE,WAAW,IAAI,SAAS,oBAAoB,CAAC;AAG/D,gBAAM,IAAI,QAAQ,aAAW,WAAW,SAAS,GAAI,CAAC;AAEtD,cAAI,MAAM,yBAAyB;AACjC;AAAA,UACF;AAEA,mBAAS,OAAO,EAAE,WAAW,IAAI,SAAS,oBAAoB,CAAC;AAE/D,gBAAa,eAAO;AAAA,YAClB,gEAAgE,aAAa,MAAM,kBAAkB,QAAQ;AAAA,UAC/G;AAAA,QAiCF;AAAA,MACF;AAAA,IACF,SAAS,OAAO;AACd,aAAO,MAAM,sBAAsB,KAAc;AACjD,YAAM;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,cAAc,SAAwC;AAC1D,WAAO,MAAM,oCAAoC;AAEjD,QAAI;AACF,UAAI,CAAC,QAAQ,UAAU,CAAC,QAAQ,aAAa,QAAQ,UAAU,SAAS;AACtE,cAAa,eAAO,mBAAmB,+CAA+C;AACtF;AAAA,MACF;AAEA,YAAM,eAAe,QAAQ,OAAO,SAAS,QAAQ,QAAQ,SAAS;AACtE,YAAM,WAAW,QAAQ,OAAO,SAAS;AACzC,YAAM,WAAW,QAAQ,OAAO,SAAS;AAEzC,aAAO,MAAM,wBAAwB,aAAa,MAAM,kBAAkB,QAAQ,OAAO;AAGzF,YAAM,gBAAgB,MAAa,eAAO;AAAA,QACxC;AAAA,UACE,EAAE,OAAO,QAAQ,aAAa,0CAA0C;AAAA,UACxE,EAAE,OAAO,SAAS,aAAa,+BAA+B;AAAA,UAC9D,EAAE,OAAO,UAAU,aAAa,2BAA2B;AAAA,UAC3D,EAAE,OAAO,SAAS,aAAa,yBAAyB;AAAA,UACxD,EAAE,OAAO,eAAe,aAAa,yCAAyC;AAAA,QAChF;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,gBAAgB;AAAA,QAClB;AAAA,MACF;AAEA,UAAI,CAAC,eAAe;AAClB;AAAA,MACF;AAGA,YAAa,eAAO;AAAA,QAClB,wBAAwB,cAAc,KAAK;AAAA,MAC7C;AAAA,IAkCF,SAAS,OAAO;AACd,aAAO,MAAM,4BAA4B,KAAc;AACvD,YAAM;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,sBAAsB,SAAwC;AAClE,WAAO,MAAM,4CAA4C;AAEzD,QAAI;AACF,UAAI,CAAC,QAAQ,UAAU,CAAC,QAAQ,aAAa,QAAQ,UAAU,SAAS;AACtE,cAAa,eAAO,mBAAmB,qCAAqC;AAC5E;AAAA,MACF;AAEA,YAAM,eAAe,QAAQ,OAAO,SAAS,QAAQ,QAAQ,SAAS;AACtE,YAAM,WAAW,QAAQ,OAAO,SAAS;AACzC,YAAM,WAAW,QAAQ,OAAO,SAAS;AAEzC,aAAO,MAAM,gCAAgC,aAAa,MAAM,kBAAkB,QAAQ,OAAO;AAGjG,YAAM,WAAW,MAAa,eAAO;AAAA,QACnC;AAAA,UACE,EAAE,OAAO,SAAS,aAAa,sCAAsC;AAAA,UACrE,EAAE,OAAO,UAAU,aAAa,uBAAuB;AAAA,UACvD,EAAE,OAAO,WAAW,aAAa,qBAAqB;AAAA,UACtD,EAAE,OAAO,WAAW,aAAa,mBAAmB;AAAA,UACpD,EAAE,OAAO,eAAe,aAAa,qCAAqC;AAAA,QAC5E;AAAA,QACA;AAAA,UACE,aAAa;AAAA,UACb,gBAAgB;AAAA,QAClB;AAAA,MACF;AAEA,UAAI,CAAC,UAAU;AACb;AAAA,MACF;AAGA,YAAa,eAAO;AAAA,QAClB,iCAAiC,SAAS,KAAK;AAAA,MACjD;AAAA,IAoBF,SAAS,OAAO;AACd,aAAO,MAAM,oCAAoC,KAAc;AAC/D,YAAM;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,eAAe,kBAA0B,UAAkB,UAAiC;AAChG,QAAI;AAEF,YAAM,OAAO,QAAQ,MAAM;AAC3B,YAAM,MAAM,KAAK,QAAQ,gBAAgB;AACzC,YAAM,WAAW,KAAK,SAAS,kBAAkB,GAAG;AACpD,YAAM,UAAU,KAAK,QAAQ,gBAAgB;AAE7C,UAAI;AACJ,UAAI,aAAa,gBAAgB,aAAa,cAAc;AAC1D,uBAAe,KAAK,KAAK,SAAS,GAAG,QAAQ,QAAQ,GAAG,EAAE;AAAA,MAC5D,WAAW,aAAa,UAAU;AAChC,uBAAe,KAAK,KAAK,SAAS,QAAQ,QAAQ,KAAK;AAAA,MACzD,OAAO;AACL,uBAAe,KAAK,KAAK,SAAS,GAAG,QAAQ,QAAQ,GAAG,EAAE;AAAA,MAC5D;AAGA,YAAM,UAAiB,YAAI,KAAK,YAAY;AAC5C,YAAM,OAAO,IAAW,sBAAc;AACtC,WAAK,WAAW,SAAS,EAAE,gBAAgB,KAAK,CAAC;AACjD,WAAK,OAAO,SAAS,IAAW,iBAAS,GAAG,CAAC,GAAG,QAAQ;AAExD,YAAa,kBAAU,UAAU,IAAI;AAGrC,YAAM,WAAW,MAAa,kBAAU,iBAAiB,OAAO;AAChE,YAAa,eAAO,iBAAiB,QAAQ;AAE7C,aAAO,KAAK,sBAAsB,YAAY,EAAE;AAAA,IAClD,SAAS,OAAO;AACd,aAAO,MAAM,8BAA8B,KAAc;AACzD,YAAM;AAAA,IACR;AAAA,EACF;AACF;;;ACnUA,IAAAC,UAAwB;AAMjB,IAAM,mBAAmB;AAAA;AAAA;AAAA;AAAA,EAI9B,MAAM,aAAa,SAAwC;AACzD,WAAO,MAAM,2BAA2B;AAExC,QAAI;AAGF,YAAa,iBAAS,eAAe,iCAAiC,UAAU;AAAA,IAUlF,SAAS,OAAO;AACd,aAAO,MAAM,2BAA2B,KAAc;AACtD,YAAM;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,cAAc,SAAwC;AAC1D,WAAO,MAAM,6BAA6B;AAE1C,QAAI;AACF,YAAM,eAAe,MAAa,eAAO;AAAA,QACvC;AAAA,QACA,EAAE,OAAO,KAAK;AAAA,QACd;AAAA,MACF;AAEA,UAAI,iBAAiB,kBAAkB;AACrC,cAAM,gBAAgB,MAAM,iBAAiB,IAA0B,cAAc,aAAa;AAGlG,cAAM,cAAc,qBAAqB;AAAA,UACvC,4BAA4B;AAAA,UAC5B,wBAAwB;AAAA,QAC1B,CAAC;AAED,cAAM,cAAc,cAAc;AAAA,UAChC,qBAAqB;AAAA,QACvB,CAAC;AAED,cAAM,cAAc,kBAAkB;AAAA,UACpC,UAAU;AAAA,UACV,QAAQ;AAAA,QACV,CAAC;AAED,cAAa,eAAO,uBAAuB,+CAA+C;AAC1F,eAAO,KAAK,4BAA4B;AAAA,MAC1C;AAAA,IACF,SAAS,OAAO;AACd,aAAO,MAAM,4BAA4B,KAAc;AACvD,YAAM;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,aAAa,SAAwC;AACzD,WAAO,MAAM,0BAA0B;AAEvC,QAAI;AACF,YAAM,gBAAgB,MAAM,iBAAiB,IAA0B,cAAc,aAAa;AAClG,YAAM,gBAAgB,cAAc,kBAAkB;AAGtD,YAAM,SAAS,MAAa,eAAO,aAAa;AAAA,QAC9C,QAAQ;AAAA,QACR,OAAO,cAAc;AAAA,QACrB,aAAa;AAAA,QACb,eAAe,CAAC,UAAU;AACxB,cAAI,SAAS,CAAC,KAAK,WAAW,KAAK,GAAG;AACpC,mBAAO;AAAA,UACT;AACA,iBAAO;AAAA,QACT;AAAA,MACF,CAAC;AAED,UAAI,WAAW,QAAW;AACxB;AAAA,MACF;AAGA,YAAM,WAAW,MAAa,eAAO,aAAa;AAAA,QAChD,QAAQ;AAAA,QACR,OAAO,cAAc;AAAA,QACrB,aAAa;AAAA,QACb,UAAU;AAAA,MACZ,CAAC;AAED,UAAI,aAAa,QAAW;AAC1B;AAAA,MACF;AAGA,YAAM,cAAc,kBAAkB;AAAA,QACpC;AAAA,QACA;AAAA,MACF,CAAC;AAGD,YAAM,aAAa,cAAc,sBAAsB;AACvD,UAAI,WAAW,SAAS;AACtB,cAAa,eAAO,uBAAuB,sCAAsC;AAAA,MACnF,OAAO;AACL,cAAa,eAAO;AAAA,UAClB,sCAAsC,WAAW,OAAO,KAAK,IAAI,CAAC;AAAA,QACpE;AAAA,MACF;AAEA,aAAO,KAAK,2BAA2B;AAAA,IACzC,SAAS,OAAO;AACd,aAAO,MAAM,oCAAoC,KAAc;AAC/D,YAAM;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,2BAA2B,SAAwC;AACvE,WAAO,MAAM,gCAAgC;AAE7C,QAAI;AACF,YAAM,gBAAgB,MAAM,iBAAiB,IAA0B,cAAc,aAAa;AAClG,YAAM,gBAAgB,cAAc,qBAAqB;AAEzD,YAAM,WAAW,CAAC,cAAc;AAChC,YAAM,cAAc,qBAAqB;AAAA,QACvC,4BAA4B;AAAA,MAC9B,CAAC;AAED,YAAM,SAAS,WAAW,YAAY;AACtC,YAAa,eAAO,uBAAuB,yBAAyB,MAAM,EAAE;AAE5E,aAAO,KAAK,yBAAyB,MAAM,EAAE;AAAA,IAC/C,SAAS,OAAO;AACd,aAAO,MAAM,0CAA0C,KAAc;AACrE,YAAM;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,wBAAwB,SAAwC;AACpE,WAAO,MAAM,8BAA8B;AAE3C,QAAI;AACF,YAAM,gBAAgB,MAAM,iBAAiB,IAA0B,cAAc,aAAa;AAClG,YAAM,SAAS,cAAc,qBAAqB;AAClD,YAAM,aAAa,cAAc,sBAAsB;AAEvD,YAAM,cAAsC;AAAA,QAC1C;AAAA,UACE,OAAO;AAAA,UACP,aAAa;AAAA,UACb,QAAQ,gBAAgB,OAAO,YAAY,6BAA6B,OAAO,KAAK,wBAAwB,OAAO,YAAY,yBAAyB,OAAO,KAAK;AAAA,QACtK;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,aAAa;AAAA,UACb,QAAQ,oBAAoB,OAAO,KAAK,sBAAsB,OAAO,KAAK;AAAA,QAC5E;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,aAAa;AAAA,UACb,QAAQ,QAAQ,OAAO,SAAS,UAAU,SAAS,YAAY,OAAO,SAAS,WAAW,QAAQ,SAAS;AAAA,QAC7G;AAAA,QACA;AAAA,UACE,OAAO,WAAW,UAAU,iCAAiC;AAAA,UAC7D,aAAa,WAAW,UAAU,2BAA2B;AAAA,UAC7D,QAAQ,WAAW,UAAU,iBAAiB,WAAW,OAAO,KAAK,IAAI;AAAA,QAC3E;AAAA,MACF;AAEA,YAAM,WAAW,MAAa,eAAO,cAAc,aAAa;AAAA,QAC9D,aAAa;AAAA,QACb,gBAAgB;AAAA,MAClB,CAAC;AAED,UAAI,UAAU;AAEZ,YAAI,SAAS,MAAM,SAAS,cAAc,GAAG;AAC3C,gBAAM,KAAK,aAAa,OAAO;AAAA,QACjC,WAAW,SAAS,MAAM,SAAS,kBAAkB,GAAG;AACtD,gBAAM,KAAK,aAAa,OAAO;AAAA,QACjC;AAAA,MACF;AAAA,IACF,SAAS,OAAO;AACd,aAAO,MAAM,uCAAuC,KAAc;AAClE,YAAM;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,oBAAoB,SAAwC;AAChE,WAAO,MAAM,yBAAyB;AAEtC,QAAI;AACF,YAAM,gBAAgB,MAAM,iBAAiB,IAA0B,cAAc,aAAa;AAClG,YAAM,SAAS,cAAc,qBAAqB;AAGlD,YAAM,eAAe;AAAA,QACnB,GAAG;AAAA,QACH,UAAU;AAAA,UACR,GAAG,OAAO;AAAA,UACV,UAAU,OAAO,SAAS,WAAW,eAAe;AAAA,QACtD;AAAA,MACF;AAEA,YAAM,aAAa,KAAK,UAAU,cAAc,MAAM,CAAC;AAEvD,YAAM,SAAS,MAAa,eAAO;AAAA,QACjC;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,UAAI,WAAW,qBAAqB;AAClC,cAAa,YAAI,UAAU,UAAU,UAAU;AAC/C,cAAa,eAAO,uBAAuB,mCAAmC;AAAA,MAChF,WAAW,WAAW,gBAAgB;AACpC,cAAM,MAAM,MAAa,eAAO,eAAe;AAAA,UAC7C,YAAmB,YAAI,KAAK,sBAAsB;AAAA,UAClD,SAAS;AAAA,YACP,cAAc,CAAC,MAAM;AAAA,YACrB,aAAa,CAAC,GAAG;AAAA,UACnB;AAAA,QACF,CAAC;AAED,YAAI,KAAK;AACP,gBAAa,kBAAU,GAAG,UAAU,KAAK,OAAO,KAAK,YAAY,MAAM,CAAC;AACxE,gBAAa,eAAO,uBAAuB,0BAA0B,IAAI,MAAM,EAAE;AAAA,QACnF;AAAA,MACF;AAAA,IACF,SAAS,OAAO;AACd,aAAO,MAAM,kCAAkC,KAAc;AAC7D,YAAM;AAAA,IACR;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,KAAsB;AAC/B,QAAI;AACF,UAAI,IAAI,GAAG;AACX,aAAO;AAAA,IACT,QAAQ;AACN,aAAO;AAAA,IACT;AAAA,EACF;AACF;;;AHrQA,IAAM,kBAAN,MAAsB;AAAA,EAAtB;AACE,SAAQ,WAAW,oBAAI,IAA4B;AAAA;AAAA,EAE5C,SAAS,WAAmB,SAA+B;AAChE,QAAI,KAAK,SAAS,IAAI,SAAS,GAAG;AAChC,aAAO,KAAK,YAAY,SAAS,uCAAuC;AAAA,IAC1E;AAEA,SAAK,SAAS,IAAI,WAAW,OAAO;AACpC,WAAO,MAAM,YAAY,SAAS,cAAc;AAAA,EAClD;AAAA,EAEO,WAAW,WAA+C;AAC/D,WAAO,KAAK,SAAS,IAAI,SAAS;AAAA,EACpC;AAAA,EAEO,iBAA2B;AAChC,WAAO,MAAM,KAAK,KAAK,SAAS,KAAK,CAAC;AAAA,EACxC;AAAA,EAEO,oBAAoB,WAAsD;AAC/E,WAAO,UAAU,SAAgB;AAC/B,YAAM,UAAU,KAAK,WAAW,SAAS;AACzC,UAAI,CAAC,SAAS;AACZ,eAAO,MAAM,iCAAiC,SAAS,EAAE;AACzD;AAAA,MACF;AAEA,UAAI;AAEF,cAAM,UAAU,KAAK,qBAAqB;AAE1C,eAAO,MAAM,sBAAsB,SAAS,IAAI,EAAE,KAAK,CAAC;AACxD,cAAM,QAAQ,SAAS,GAAG,IAAI;AAC9B,eAAO,MAAM,kCAAkC,SAAS,EAAE;AAAA,MAC5D,SAAS,OAAO;AACd,eAAO,MAAM,4BAA4B,SAAS,KAAK,KAAc;AAGrE,aAAY,eAAO;AAAA,UACjB,8BAA8B,iBAAiB,QAAQ,MAAM,UAAU,eAAe;AAAA,QACxF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEQ,uBAAuC;AAC7C,UAAM,SAAgB,eAAO;AAC7B,UAAM,kBAAkB,QAAQ,WACrB,kBAAU,mBAAmB,OAAO,SAAS,GAAG,IAChD,kBAAU,mBAAmB,CAAC;AAEzC,WAAO;AAAA,MACL;AAAA,MACA,WAAW,QAAQ;AAAA,MACnB,UAAU,QAAQ;AAAA,MAClB;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAM,kBAAkB,IAAI,gBAAgB;AAK5C,eAAsB,iBAAiB,SAAiD;AACtF,SAAO,MAAM,yBAAyB;AAGtC,uBAAqB;AACrB,uBAAqB;AACrB,2BAAyB;AAGzB,aAAW,aAAa,gBAAgB,eAAe,GAAG;AACxD,UAAM,aAAoB,iBAAS;AAAA,MACjC;AAAA,MACA,gBAAgB,oBAAoB,SAAS;AAAA,IAC/C;AACA,YAAQ,cAAc,KAAK,UAAU;AAAA,EACvC;AAEA,SAAO,KAAK,cAAc,gBAAgB,eAAe,EAAE,MAAM,WAAW;AAC9E;AAKA,SAAS,uBAA6B;AAEpC,kBAAgB,SAAS,uBAAuB,aAAa,SAAS;AAGtE,kBAAgB,SAAS,yBAAyB,aAAa,OAAO;AAGtE,kBAAgB,SAAS,8BAA8B,aAAa,YAAY;AAClF;AAKA,SAAS,uBAA6B;AAEpC,kBAAgB,SAAS,yBAAyB,aAAa,WAAW;AAG1E,kBAAgB,SAAS,qBAAqB,aAAa,OAAO;AAGlE,kBAAgB,SAAS,sBAAsB,aAAa,aAAa;AAGzE,kBAAgB,SAAS,0BAA0B,aAAa,qBAAqB;AACvF;AAKA,SAAS,2BAAiC;AAExC,kBAAgB,SAAS,0BAA0B,iBAAiB,YAAY;AAGhF,kBAAgB,SAAS,2BAA2B,iBAAiB,aAAa;AACpF;;;AJjIA,eAAsB,SAAS,SAAiD;AAC9E,MAAI;AACF,WAAO,KAAK,qCAAqC;AAGjD,QAAI,QAAQ,IAAI,aAAa,eAAe;AAC1C,aAAO,yBAA0B;AAAA,IACnC;AAGA,UAAM,mBAAmB,OAAO;AAGhC,UAAM,wBAAwB,OAAO;AAGrC,UAAM,kBAAkB,OAAO;AAG/B,wBAAoB,OAAO;AAG3B,UAAM,mBAAmB,OAAO;AAEhC,WAAO,KAAK,2CAA2C;AAAA,EACzD,SAAS,OAAO;AACd,WAAO,MAAM,yCAAyC,KAAc;AAGpE,SAAY,eACT;AAAA,MACC;AAAA,MACA;AAAA,IACF,EACC,KAAK,eAAa;AACjB,UAAI,cAAc,eAAe;AAC/B,eAAO,KAAK;AAAA,MACd;AAAA,IACF,CAAC;AAEH,UAAM;AAAA,EACR;AACF;AAKO,SAAS,aAAmB;AACjC,MAAI;AACF,WAAO,KAAK,uCAAuC;AAGnD,qBAAiB,QAAQ;AAGzB,WAAO,QAAQ;AAEf,WAAO,KAAK,6CAA6C;AAAA,EAC3D,SAAS,OAAO;AACd,YAAQ,MAAM,wCAAwC,KAAK;AAAA,EAC7D;AACF;AAKA,eAAe,mBAAmB,SAAiD;AACjF,SAAO,MAAM,0BAA0B;AAGvC,mBAAiB,iBAAiB,cAAc,eAAe,IAAI,qBAAqB,CAAC;AAQzF,SAAO,MAAM,sBAAsB;AACrC;AAKA,eAAe,wBAAwB,SAAiD;AACtF,SAAO,MAAM,yBAAyB;AAGtC,QAAM,iBAAiB,OAAO;AAE9B,SAAO,MAAM,qBAAqB;AACpC;AAKA,eAAe,kBAAkB,SAAiD;AAChF,SAAO,MAAM,0BAA0B;AAOvC,SAAO,MAAM,sBAAsB;AACrC;AAKA,SAAS,oBAAoB,SAAwC;AACnE,SAAO,MAAM,+BAA+B;AAG5C,UAAQ,cAAc;AAAA,IACb,eAAO,4BAA4B,YAAU;AAClD,UAAI,QAAQ;AACV,eAAO,MAAM,0BAA0B,OAAO,SAAS,QAAQ,EAAE;AAAA,MACnE;AAAA,IACF,CAAC;AAAA,EACH;AAGA,UAAQ,cAAc;AAAA,IACb,kBAAU,wBAAwB,WAAS;AAChD,aAAO,MAAM,qBAAqB,MAAM,SAAS,QAAQ,EAAE;AAAA,IAC7D,CAAC;AAAA,EACH;AAGA,UAAQ,cAAc;AAAA,IACb,kBAAU,4BAA4B,WAAS;AACpD,aAAO,MAAM,6BAA6B;AAAA,QACxC,OAAO,MAAM,MAAM;AAAA,QACnB,SAAS,MAAM,QAAQ;AAAA,MACzB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAGA,UAAQ,cAAc;AAAA,IACb,kBAAU,yBAAyB,WAAS;AACjD,UAAI,MAAM,qBAAqB,UAAU,GAAG;AAC1C,eAAO,MAAM,gCAAgC;AAAA,MAC/C;AAAA,IACF,CAAC;AAAA,EACH;AAEA,SAAO,MAAM,wBAAwB;AACvC;AAKA,eAAe,mBAAmB,SAAiD;AACjF,QAAM,kBAAkB,QAAQ,YAAY,IAAa,mBAAmB,KAAK;AAEjF,MAAI,CAAC,iBAAiB;AACpB,UAAM,YAAY,MAAa,eAAO;AAAA,MACpC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAEA,YAAQ,WAAW;AAAA,MACjB,KAAK;AACH,cAAa,iBAAS,eAAe,qBAAqB;AAC1D;AAAA,MACF,KAAK;AACH,cAAa,iBAAS,eAAe,wBAAwB;AAC7D;AAAA,MACF,KAAK;AACH,cAAM,QAAQ,YAAY,OAAO,mBAAmB,IAAI;AACxD;AAAA,IACJ;AAEA,QAAI,cAAc,oBAAoB;AACpC,YAAM,QAAQ,YAAY,OAAO,mBAAmB,IAAI;AAAA,IAC1D;AAAA,EACF;AACF;AAKO,SAAS,sBAA8B;AAC5C,QAAM,YAAmB,mBAAW,aAAa,mBAAmB;AACpE,SAAO,WAAW,aAAa,WAAW;AAC5C;AAKO,SAAS,oBAA6B;AAC3C,SAAO,QAAQ,IAAI,aAAa,iBAAwB,YAAI,cAAc;AAC5E;", "names": ["vscode", "vscode", "vscode", "vscode", "vscode", "vscode"]}