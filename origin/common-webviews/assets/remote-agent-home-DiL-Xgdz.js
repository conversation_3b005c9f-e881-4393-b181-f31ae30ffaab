var Ie=Object.defineProperty;var ke=(i,e,n)=>e in i?Ie(i,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):i[e]=n;var B=(i,e,n)=>ke(i,typeof e!="symbol"?e+"":e,n);import{S as C,i as L,s as F,$ as W,J as R,V as w,T as M,c as h,e as m,W as S,f as I,u as d,t as g,h as p,Z as _,_ as qe,a0 as D,a1 as P,D as O,n as ee,q,r as N,L as j,ac as xe,ad as Ne,P as De,an as le,a2 as We}from"./SpinnerAugment-BUJasFTo.js";import"./design-system-init-BKdwvVur.js";import{s as Ee}from"./index-BkU7QRx6.js";import"./design-system-init-e2yaG07g.js";import{W as y,h as He}from"./BaseButton-ci_067e0.js";import{T as X,M as Pe}from"./TextTooltipAugment-UDQF2J4S.js";import{T as ze,S as Ae,v as Te,a as Ce}from"./trash-mbophkQL.js";import{e as H,u as V,o as Z}from"./each-DUdYBCJG.js";import{R as b}from"./types-CF53Ux0u.js";import{T as Y}from"./Content-CSmc2GUv.js";import{C as Le}from"./CardAugment-DvO45c5p.js";import{I as be}from"./IconButtonAugment-DFy7vWkh.js";import{T as Me}from"./terminal-CwJUqtXN.js";import{A as Fe}from"./augment-logo-CSOE_v2f.js";import"./globals-D0QH3NT1.js";class Oe{constructor(e,n=void 0,t,s){B(this,"subscribers",new Set);this._asyncMsgSender=e,this._state=n,this.validateState=t,this._storeId=s,n&&this.setStateInternal(n)}subscribe(e){return this.subscribers.add(e),e(this),()=>{this.subscribers.delete(e)}}notifySubscribers(){this.subscribers.forEach(e=>e(this))}get state(){return this._state}get storeId(){return this._storeId}shouldAcceptMessage(e,n){return e.id===this.storeId&&this.validateState(n)}update(e){const n=e(this._state);n!==void 0&&this.setStateInternal(n)}setState(e){this.setStateInternal(e)}async setStateInternal(e){JSON.stringify(this._state)!==JSON.stringify(e)&&(this._state=e,await this._asyncMsgSender.send({type:y.updateSharedWebviewState,data:e,id:this.storeId}))}async fetchStateFromExtension(){const e=await this._asyncMsgSender.send({type:y.getSharedWebviewState,id:this.storeId,data:{}});e.type===y.getSharedWebviewStateResponse&&this.shouldAcceptMessage(e,e.data)&&(this._state=e.data,this.notifySubscribers())}handleMessageFromExtension(e){const n=e.data.type===y.asyncWrapper,t=n?e.data.baseMsg.type:e.data.type,s=n?e.data.baseMsg:e.data;switch(t){case y.updateSharedWebviewState:case y.getSharedWebviewStateResponse:return!!this.shouldAcceptMessage(s,s.data)&&(this._state=s.data,this.notifySubscribers(),!0);default:return!1}}}class J{constructor(e){B(this,"_asyncMsgSender");this._asyncMsgSender=e}async sshToRemoteAgent(e){const n=await this._asyncMsgSender.send({type:y.remoteAgentSshRequest,data:{agentId:e}},1e4);return!!n.data.success||(console.error("Failed to connect to remote agent:",n.data.error),!1)}async deleteRemoteAgent(e){return(await this._asyncMsgSender.send({type:y.deleteRemoteAgentRequest,data:{agentId:e}},1e4)).data.success}async showRemoteAgentHomePanel(){await this._asyncMsgSender.send({type:y.showRemoteAgentHomePanel})}async closeRemoteAgentHomePanel(){await this._asyncMsgSender.send({type:y.closeRemoteAgentHomePanel})}async getRemoteAgentNotificationEnabled(e){return(await this._asyncMsgSender.send({type:y.getRemoteAgentNotificationEnabledRequest,data:{agentIds:e}})).data}async setRemoteAgentNotificationEnabled(e,n){await this._asyncMsgSender.send({type:y.setRemoteAgentNotificationEnabled,data:{agentId:e,enabled:n}})}async deleteRemoteAgentNotificationEnabled(e){await this._asyncMsgSender.send({type:y.deleteRemoteAgentNotificationEnabled,data:{agentId:e}})}async notifyRemoteAgentReady(e){await this._asyncMsgSender.send({type:y.remoteAgentNotifyReady,data:{agentId:e}})}async showRemoteAgentDiffPanel(e){await this._asyncMsgSender.send({type:y.showRemoteAgentDiffPanel,data:e})}async closeRemoteAgentDiffPanel(){await this._asyncMsgSender.send({type:y.closeRemoteAgentDiffPanel})}async getRemoteAgentChatHistory(e,n,t=1e4){return await this._asyncMsgSender.send({type:y.getRemoteAgentChatHistoryRequest,data:{agentId:e,lastProcessedSequenceId:n}},t)}async sendRemoteAgentChatRequest(e,n,t=1e4){await this._asyncMsgSender.send({type:y.remoteAgentChatRequest,data:{agentId:e,requestDetails:n}},t)}async interruptRemoteAgent(e,n=1e4){return await this._asyncMsgSender.send({type:y.remoteAgentInterruptRequest,data:{agentId:e}},n)}async createRemoteAgent(e,n,t,s,a=1e4){return await this._asyncMsgSender.send({type:y.createRemoteAgentRequest,data:{prompt:e,workspaceSetup:n,setupScript:t,isSetupScriptAgent:s}},a)}async getRemoteAgentOverviews(e=1e4){return await this._asyncMsgSender.send({type:y.getRemoteAgentOverviewsRequest},e)}async listSetupScripts(e=5e3){return await this._asyncMsgSender.send({type:y.listSetupScriptsRequest},e)}async saveSetupScript(e,n,t,s=5e3){return await this._asyncMsgSender.send({type:y.saveSetupScriptRequest,data:{name:e,content:n,location:t}},s)}async deleteSetupScript(e,n,t=5e3){return await this._asyncMsgSender.send({type:y.deleteSetupScriptRequest,data:{name:e,location:n}},t)}async renameSetupScript(e,n,t,s=5e3){return await this._asyncMsgSender.send({type:y.renameSetupScriptRequest,data:{oldName:e,newName:n,location:t}},s)}async getRemoteAgentWorkspaceLogs(e,n=1e4){return await this._asyncMsgSender.send({type:y.remoteAgentWorkspaceLogsRequest,data:{agentId:e}},n)}async saveLastRemoteAgentSetup(e,n,t){return await this._asyncMsgSender.send({type:y.saveLastRemoteAgentSetupRequest,data:{lastRemoteAgentGitRepoUrl:e,lastRemoteAgentGitBranch:n,lastRemoteAgentSetupScript:t}})}async getLastRemoteAgentSetup(){return await this._asyncMsgSender.send({type:y.getLastRemoteAgentSetupRequest})}async openDiffInBuffer(e,n,t){return await this._asyncMsgSender.send({type:y.openDiffInBuffer,data:{oldContents:e,newContents:n,filePath:t}})}}B(J,"key","remoteAgentsClient");function ue(i){try{const e=new Date(i);if(isNaN(e.getTime()))return"Unknown time";const n=new Date().getTime()-e.getTime(),t=Math.floor(n/1e3),s=Math.floor(t/60),a=Math.floor(s/60),o=Math.floor(a/24);return t<60?`${t}s ago`:s<60?`${s}m ago`:a<24?`${a}h ago`:o<30?`${o}d ago`:e.toLocaleDateString()}catch(e){return console.error("Error formatting date:",e),"Unknown time"}}function Ge(i){let e,n=i[0]?"Running in the cloud":"Running locally";return{c(){e=D(n)},m(t,s){m(t,e,s)},p(t,s){1&s&&n!==(n=t[0]?"Running in the cloud":"Running locally")&&P(e,n)},d(t){t&&p(e)}}}function Ue(i){let e;return{c(){e=D("Unknown time")},m(n,t){m(n,e,t)},p:ee,d(n){n&&p(e)}}}function Be(i){let e;return{c(){e=D(i[3])},m(n,t){m(n,e,t)},p(n,t){8&t&&P(e,n[3])},d(n){n&&p(e)}}}function je(i){let e,n,t,s=i[1]===b.agentRunning?"Last updated":"Started";function a(c,r){return c[2]?Be:Ue}let o=a(i),l=o(i);return{c(){e=D(s),n=M(),l.c(),t=O()},m(c,r){m(c,e,r),m(c,n,r),l.m(c,r),m(c,t,r)},p(c,r){2&r&&s!==(s=c[1]===b.agentRunning?"Last updated":"Started")&&P(e,s),o===(o=a(c))&&l?l.p(c,r):(l.d(1),l=o(c),l&&(l.c(),l.m(t.parentNode,t)))},d(c){c&&(p(e),p(n),p(t)),l.d(c)}}}function Je(i){let e,n,t,s,a,o;return n=new W({props:{size:1,color:"secondary",class:"location-text",$$slots:{default:[Ge]},$$scope:{ctx:i}}}),a=new W({props:{size:1,color:"secondary",class:"time-text",$$slots:{default:[je]},$$scope:{ctx:i}}}),{c(){e=R("div"),w(n.$$.fragment),t=M(),s=R("div"),w(a.$$.fragment),h(s,"class","time-container"),h(e,"class","agent-card-footer svelte-1qwlkoj")},m(l,c){m(l,e,c),S(n,e,null),I(e,t),I(e,s),S(a,s,null),o=!0},p(l,[c]){const r={};33&c&&(r.$$scope={dirty:c,ctx:l}),n.$set(r);const u={};46&c&&(u.$$scope={dirty:c,ctx:l}),a.$set(u)},i(l){o||(d(n.$$.fragment,l),d(a.$$.fragment,l),o=!0)},o(l){g(n.$$.fragment,l),g(a.$$.fragment,l),o=!1},d(l){l&&p(e),_(n),_(a)}}}function Ve(i,e,n){let{isRemote:t=!1}=e,{status:s}=e,{timestamp:a}=e,o=ue(a);const l=function(c,r){let u=1e3;const $=new Date(c),v=setInterval(()=>{const k=Math.floor((new Date().getTime()-$.getTime())/1e3/60);k>=1&&(u=6e4),k>=60&&(u=36e5),k>=1440&&(u=864e5),r(ue(c))},u);return()=>clearInterval(v)}(a,c=>{n(3,o=c)});return qe(()=>{l()}),i.$$set=c=>{"isRemote"in c&&n(0,t=c.isRemote),"status"in c&&n(1,s=c.status),"timestamp"in c&&n(2,a=c.timestamp)},[t,s,a,o]}class Ze extends C{constructor(e){super(),L(this,e,Ve,Je,F,{isRemote:0,status:1,timestamp:2})}}function Ke(i){let e;return{c(){e=D(i[1])},m(n,t){m(n,e,t)},p(n,t){2&t&&P(e,n[1])},d(n){n&&p(e)}}}function Qe(i){let e,n,t,s;return n=new W({props:{size:1,weight:"medium",$$slots:{default:[Ke]},$$scope:{ctx:i}}}),{c(){e=R("div"),w(n.$$.fragment),h(e,"class",t="status-badge "+i[0]+" svelte-144c5bo")},m(a,o){m(a,e,o),S(n,e,null),s=!0},p(a,[o]){const l={};18&o&&(l.$$scope={dirty:o,ctx:a}),n.$set(l),(!s||1&o&&t!==(t="status-badge "+a[0]+" svelte-144c5bo"))&&h(e,"class",t)},i(a){s||(d(n.$$.fragment,a),s=!0)},o(a){g(n.$$.fragment,a),s=!1},d(a){a&&p(e),_(n)}}}function Xe(i,e,n){let t,s,{status:a}=e;return i.$$set=o=>{"status"in o&&n(2,a=o.status)},i.$$.update=()=>{4&i.$$.dirty&&n(1,t=function(o){switch(o){case b.agentStarting:return"Starting";case b.agentRunning:return"Running";case b.agentIdle:return"Idle";case b.agentFailed:return"Failed";default:return"Unknown"}}(a)),4&i.$$.dirty&&n(0,s=function(o){switch(o){case b.agentStarting:return"status-starting";case b.agentRunning:return"status-running";case b.agentIdle:return"status-idle";case b.agentFailed:return"status-error";default:return"status-unknown"}}(a))},[s,t,a]}class Ye extends C{constructor(e){super(),L(this,e,Xe,Qe,F,{status:2})}}function et(i){let e;return{c(){e=D(i[0])},m(n,t){m(n,e,t)},p(n,t){1&t&&P(e,n[0])},d(n){n&&p(e)}}}function tt(i){let e,n,t;return n=new W({props:{size:1,color:"secondary",$$slots:{default:[et]},$$scope:{ctx:i}}}),{c(){e=R("div"),w(n.$$.fragment),h(e,"class","task-text-container svelte-1tatwxk")},m(s,a){m(s,e,a),S(n,e,null),t=!0},p(s,a){const o={};9&a&&(o.$$scope={dirty:a,ctx:s}),n.$set(o)},i(s){t||(d(n.$$.fragment,s),t=!0)},o(s){g(n.$$.fragment,s),t=!1},d(s){s&&p(e),_(n)}}}function de(i){let e,n,t;return n=new W({props:{size:1,color:i[1]==="error"?"error":"neutral",$$slots:{default:[nt]},$$scope:{ctx:i}}}),{c(){e=R("div"),w(n.$$.fragment),h(e,"class","task-status-indicator svelte-1tatwxk")},m(s,a){m(s,e,a),S(n,e,null),t=!0},p(s,a){const o={};2&a&&(o.color=s[1]==="error"?"error":"neutral"),10&a&&(o.$$scope={dirty:a,ctx:s}),n.$set(o)},i(s){t||(d(n.$$.fragment,s),t=!0)},o(s){g(n.$$.fragment,s),t=!1},d(s){s&&p(e),_(n)}}}function nt(i){let e,n=i[1]==="error"?"!":i[1]==="warning"?"⚠":"";return{c(){e=D(n)},m(t,s){m(t,e,s)},p(t,s){2&s&&n!==(n=t[1]==="error"?"!":t[1]==="warning"?"⚠":"")&&P(e,n)},d(t){t&&p(e)}}}function st(i){let e,n,t,s,a,o,l;a=new X({props:{content:i[0],triggerOn:[Y.Hover],maxWidth:"400px",$$slots:{default:[tt]},$$scope:{ctx:i}}});let c=(i[1]==="error"||i[1]==="warning")&&de(i);return{c(){e=R("div"),n=R("div"),s=M(),w(a.$$.fragment),o=M(),c&&c.c(),h(n,"class",t="bullet-point "+i[2]+" svelte-1tatwxk"),h(e,"class","task-item svelte-1tatwxk")},m(r,u){m(r,e,u),I(e,n),I(e,s),S(a,e,null),I(e,o),c&&c.m(e,null),l=!0},p(r,[u]){(!l||4&u&&t!==(t="bullet-point "+r[2]+" svelte-1tatwxk"))&&h(n,"class",t);const $={};1&u&&($.content=r[0]),9&u&&($.$$scope={dirty:u,ctx:r}),a.$set($),r[1]==="error"||r[1]==="warning"?c?(c.p(r,u),2&u&&d(c,1)):(c=de(r),c.c(),d(c,1),c.m(e,null)):c&&(q(),g(c,1,1,()=>{c=null}),N())},i(r){l||(d(a.$$.fragment,r),d(c),l=!0)},o(r){g(a.$$.fragment,r),g(c),l=!1},d(r){r&&p(e),_(a),c&&c.d()}}}function at(i,e,n){let t,{text:s}=e,{status:a="info"}=e;return i.$$set=o=>{"text"in o&&n(0,s=o.text),"status"in o&&n(1,a=o.status)},i.$$.update=()=>{2&i.$$.dirty&&n(2,t=function(o){switch(o){case"success":return"task-success";case"warning":return"task-warning";case"error":return"task-error";default:return"task-info"}}(a))},[s,a,t]}class rt extends C{constructor(e){super(),L(this,e,at,st,F,{text:0,status:1})}}function ge(i,e,n){const t=i.slice();return t[14]=e[n],t[16]=n,t}function it(i){let e,n;return e=new W({props:{size:2,weight:"medium",$$slots:{default:[ct]},$$scope:{ctx:i}}}),{c(){w(e.$$.fragment)},m(t,s){S(e,t,s),n=!0},p(t,s){const a={};131073&s&&(a.$$scope={dirty:s,ctx:t}),e.$set(a)},i(t){n||(d(e.$$.fragment,t),n=!0)},o(t){g(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function ot(i){let e,n,t,s,a,o;return t=new Me({}),a=new W({props:{size:2,weight:"medium",$$slots:{default:[lt]},$$scope:{ctx:i}}}),{c(){e=R("div"),n=R("div"),w(t.$$.fragment),s=M(),w(a.$$.fragment),h(n,"class","setup-script-badge svelte-1mctlb0"),h(e,"class","setup-script-title-container svelte-1mctlb0")},m(l,c){m(l,e,c),I(e,n),S(t,n,null),I(e,s),S(a,e,null),o=!0},p(l,c){const r={};131072&c&&(r.$$scope={dirty:c,ctx:l}),a.$set(r)},i(l){o||(d(t.$$.fragment,l),d(a.$$.fragment,l),o=!0)},o(l){g(t.$$.fragment,l),g(a.$$.fragment,l),o=!1},d(l){l&&p(e),_(t),_(a)}}}function ct(i){let e,n=i[0].session_summary+"";return{c(){e=D(n)},m(t,s){m(t,e,s)},p(t,s){1&s&&n!==(n=t[0].session_summary+"")&&P(e,n)},d(t){t&&p(e)}}}function lt(i){let e;return{c(){e=R("span"),e.textContent="Generate a setup script",h(e,"class","setup-script-title svelte-1mctlb0")},m(n,t){m(n,e,t)},p:ee,d(n){n&&p(e)}}}function $e(i){let e,n,t=[],s=new Map,a=H(i[5].slice(0,3));const o=l=>l[16];for(let l=0;l<a.length;l+=1){let c=ge(i,a,l),r=o(c);s.set(r,t[l]=me(r,c))}return{c(){e=R("div");for(let l=0;l<t.length;l+=1)t[l].c();h(e,"class","tasks-list svelte-1mctlb0")},m(l,c){m(l,e,c);for(let r=0;r<t.length;r+=1)t[r]&&t[r].m(e,null);n=!0},p(l,c){32&c&&(a=H(l[5].slice(0,3)),q(),t=V(t,c,o,1,l,a,s,e,Z,me,null,ge),N())},i(l){if(!n){for(let c=0;c<a.length;c+=1)d(t[c]);n=!0}},o(l){for(let c=0;c<t.length;c+=1)g(t[c]);n=!1},d(l){l&&p(e);for(let c=0;c<t.length;c+=1)t[c].d()}}}function me(i,e){let n,t,s;return t=new rt({props:{text:e[14],status:"success"}}),{key:i,first:null,c(){n=O(),w(t.$$.fragment),this.first=n},m(a,o){m(a,n,o),S(t,a,o),s=!0},p(a,o){e=a;const l={};32&o&&(l.text=e[14]),t.$set(l)},i(a){s||(d(t.$$.fragment,a),s=!0)},o(a){g(t.$$.fragment,a),s=!1},d(a){a&&p(n),_(t,a)}}}function ut(i){let e,n;return e=new Me({}),{c(){w(e.$$.fragment)},m(t,s){S(e,t,s),n=!0},i(t){n||(d(e.$$.fragment,t),n=!0)},o(t){g(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function dt(i){let e,n;return e=new be({props:{disabled:!i[3],variant:"ghost",color:"neutral",size:1,title:i[3]?"SSH to agent":"SSH to agent (agent must be running or idle)",$$slots:{default:[ut]},$$scope:{ctx:i}}}),e.$on("click",i[8]),{c(){w(e.$$.fragment)},m(t,s){S(e,t,s),n=!0},p(t,s){const a={};8&s&&(a.disabled=!t[3]),8&s&&(a.title=t[3]?"SSH to agent":"SSH to agent (agent must be running or idle)"),131072&s&&(a.$$scope={dirty:s,ctx:t}),e.$set(a)},i(t){n||(d(e.$$.fragment,t),n=!0)},o(t){g(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function gt(i){let e,n;return e=new ze({}),{c(){w(e.$$.fragment)},m(t,s){S(e,t,s),n=!0},i(t){n||(d(e.$$.fragment,t),n=!0)},o(t){g(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function $t(i){let e,n;return e=new be({props:{variant:"ghost",color:"neutral",size:1,title:"Delete agent",$$slots:{default:[gt]},$$scope:{ctx:i}}}),e.$on("click",i[9]),{c(){w(e.$$.fragment)},m(t,s){S(e,t,s),n=!0},p(t,s){const a={};131072&s&&(a.$$scope={dirty:s,ctx:t}),e.$set(a)},i(t){n||(d(e.$$.fragment,t),n=!0)},o(t){g(e.$$.fragment,t),n=!1},d(t){_(e,t)}}}function mt(i){let e,n,t,s,a,o,l,c,r,u,$,v,k,se,z,K,T,G;const ae=[ot,it],E=[];function re(f,x){return f[0].is_setup_script_agent?0:1}t=re(i),s=E[t]=ae[t](i),c=new Ye({props:{status:i[0].status}});let A=i[5].length>0&&$e(i);return k=new X({props:{content:"SSH to agent",minWidth:"75px",triggerOn:[Y.Hover],side:"top",$$slots:{default:[dt]},$$scope:{ctx:i}}}),z=new X({props:{content:"Delete agent",minWidth:"75px",triggerOn:[Y.Hover],side:"top",$$slots:{default:[$t]},$$scope:{ctx:i}}}),T=new Ze({props:{isRemote:i[4],status:i[0].status,timestamp:i[0].updated_at||i[0].started_at}}),{c(){e=R("div"),n=R("div"),s.c(),o=M(),l=R("div"),w(c.$$.fragment),r=M(),u=R("div"),A&&A.c(),$=M(),v=R("div"),w(k.$$.fragment),se=M(),w(z.$$.fragment),K=M(),w(T.$$.fragment),h(n,"class","session-summary-container svelte-1mctlb0"),h(n,"title",a=i[0].is_setup_script_agent?"Generate a setup script":i[0].session_summary),h(l,"class","card-info svelte-1mctlb0"),h(e,"class","card-header svelte-1mctlb0"),h(u,"class","card-content svelte-1mctlb0"),h(v,"class","card-actions svelte-1mctlb0")},m(f,x){m(f,e,x),I(e,n),E[t].m(n,null),I(e,o),I(e,l),S(c,l,null),m(f,r,x),m(f,u,x),A&&A.m(u,null),m(f,$,x),m(f,v,x),S(k,v,null),I(v,se),S(z,v,null),m(f,K,x),S(T,f,x),G=!0},p(f,x){let Q=t;t=re(f),t===Q?E[t].p(f,x):(q(),g(E[Q],1,1,()=>{E[Q]=null}),N(),s=E[t],s?s.p(f,x):(s=E[t]=ae[t](f),s.c()),d(s,1),s.m(n,null)),(!G||1&x&&a!==(a=f[0].is_setup_script_agent?"Generate a setup script":f[0].session_summary))&&h(n,"title",a);const ie={};1&x&&(ie.status=f[0].status),c.$set(ie),f[5].length>0?A?(A.p(f,x),32&x&&d(A,1)):(A=$e(f),A.c(),d(A,1),A.m(u,null)):A&&(q(),g(A,1,1,()=>{A=null}),N());const oe={};131080&x&&(oe.$$scope={dirty:x,ctx:f}),k.$set(oe);const ce={};131073&x&&(ce.$$scope={dirty:x,ctx:f}),z.$set(ce);const U={};16&x&&(U.isRemote=f[4]),1&x&&(U.status=f[0].status),1&x&&(U.timestamp=f[0].updated_at||f[0].started_at),T.$set(U)},i(f){G||(d(s),d(c.$$.fragment,f),d(A),d(k.$$.fragment,f),d(z.$$.fragment,f),d(T.$$.fragment,f),G=!0)},o(f){g(s),g(c.$$.fragment,f),g(A),g(k.$$.fragment,f),g(z.$$.fragment,f),g(T.$$.fragment,f),G=!1},d(f){f&&(p(e),p(r),p(u),p($),p(v),p(K)),E[t].d(),_(c),A&&A.d(),_(k),_(z),_(T,f)}}}function pt(i){let e,n,t;return n=new Le({props:{variant:"surface",size:2,interactive:!0,class:"agent-card",$$slots:{default:[mt]},$$scope:{ctx:i}}}),n.$on("click",i[10]),n.$on("keydown",i[11]),{c(){e=R("div"),w(n.$$.fragment),h(e,"class","card-wrapper svelte-1mctlb0"),j(e,"selected-card",i[1]),j(e,"setup-script-card",i[0].is_setup_script_agent)},m(s,a){m(s,e,a),S(n,e,null),t=!0},p(s,[a]){const o={};131129&a&&(o.$$scope={dirty:a,ctx:s}),n.$set(o),(!t||2&a)&&j(e,"selected-card",s[1]),(!t||1&a)&&j(e,"setup-script-card",s[0].is_setup_script_agent)},i(s){t||(d(n.$$.fragment,s),t=!0)},o(s){g(n.$$.fragment,s),t=!1},d(s){s&&p(e),_(n)}}}function ft(i,e,n){let t,s,a,{agent:o}=e,{selected:l=!1}=e,{onSelect:c}=e;const r=xe(J.key);async function u(v){await r.deleteRemoteAgent(v)}function $(){s&&(async v=>{await r.sshToRemoteAgent(v.remote_agent_id)})(o)}return i.$$set=v=>{"agent"in v&&n(0,o=v.agent),"selected"in v&&n(1,l=v.selected),"onSelect"in v&&n(2,c=v.onSelect)},i.$$.update=()=>{1&i.$$.dirty&&n(5,t=o.turn_summaries||[]),1&i.$$.dirty&&n(3,s=o.status===b.agentRunning||o.status===b.agentIdle)},n(4,a=!0),[o,l,c,s,!0,t,u,$,v=>{v.stopPropagation(),$()},v=>{v.stopPropagation(),u(o.remote_agent_id)},()=>c(o.remote_agent_id),v=>v.key==="Enter"&&c(o.remote_agent_id)]}class te extends C{constructor(e){super(),L(this,e,ft,pt,F,{agent:0,selected:1,onSelect:2})}}function ht(i){let e;return{c(){e=D(i[0])},m(n,t){m(n,e,t)},p(n,t){1&t&&P(e,n[0])},d(n){n&&p(e)}}}function yt(i){let e,n,t;return n=new W({props:{size:2,color:"secondary",$$slots:{default:[ht]},$$scope:{ctx:i}}}),{c(){e=R("div"),w(n.$$.fragment),h(e,"class","section-header svelte-1tegnqi")},m(s,a){m(s,e,a),S(n,e,null),t=!0},p(s,[a]){const o={};3&a&&(o.$$scope={dirty:a,ctx:s}),n.$set(o)},i(s){t||(d(n.$$.fragment,s),t=!0)},o(s){g(n.$$.fragment,s),t=!1},d(s){s&&p(e),_(n)}}}function wt(i,e,n){let{title:t}=e;return i.$$set=s=>{"title"in s&&n(0,t=s.title)},[t]}class ne extends C{constructor(e){super(),L(this,e,wt,yt,F,{title:0})}}function pe(i,e,n){const t=i.slice();return t[7]=e[n],t[9]=n,t}function fe(i,e,n){const t=i.slice();return t[7]=e[n],t[9]=n,t}function he(i,e,n){const t=i.slice();return t[7]=e[n],t[9]=n,t}function St(i){let e,n,t,s,a=i[3].length>0&&ye(i),o=i[2].length>0&&Se(i),l=i[1].length>0&&ve(i);return{c(){a&&a.c(),e=M(),o&&o.c(),n=M(),l&&l.c(),t=O()},m(c,r){a&&a.m(c,r),m(c,e,r),o&&o.m(c,r),m(c,n,r),l&&l.m(c,r),m(c,t,r),s=!0},p(c,r){c[3].length>0?a?(a.p(c,r),8&r&&d(a,1)):(a=ye(c),a.c(),d(a,1),a.m(e.parentNode,e)):a&&(q(),g(a,1,1,()=>{a=null}),N()),c[2].length>0?o?(o.p(c,r),4&r&&d(o,1)):(o=Se(c),o.c(),d(o,1),o.m(n.parentNode,n)):o&&(q(),g(o,1,1,()=>{o=null}),N()),c[1].length>0?l?(l.p(c,r),2&r&&d(l,1)):(l=ve(c),l.c(),d(l,1),l.m(t.parentNode,t)):l&&(q(),g(l,1,1,()=>{l=null}),N())},i(c){s||(d(a),d(o),d(l),s=!0)},o(c){g(a),g(o),g(l),s=!1},d(c){c&&(p(e),p(n),p(t)),a&&a.d(c),o&&o.d(c),l&&l.d(c)}}}function _t(i){let e,n,t;return n=new W({props:{size:3,color:"secondary",$$slots:{default:[vt]},$$scope:{ctx:i}}}),{c(){e=R("div"),w(n.$$.fragment),h(e,"class","empty-state svelte-1rqt2ni")},m(s,a){m(s,e,a),S(n,e,null),t=!0},p(s,a){const o={};4096&a&&(o.$$scope={dirty:a,ctx:s}),n.$set(o)},i(s){t||(d(n.$$.fragment,s),t=!0)},o(s){g(n.$$.fragment,s),t=!1},d(s){s&&p(e),_(n)}}}function ye(i){let e,n,t,s,a=[],o=new Map;e=new ne({props:{title:"Ready to review"}});let l=H(i[3]);const c=r=>r[7].remote_agent_id+r[9];for(let r=0;r<l.length;r+=1){let u=he(i,l,r),$=c(u);o.set($,a[r]=we($,u))}return{c(){w(e.$$.fragment),n=M(),t=R("div");for(let r=0;r<a.length;r+=1)a[r].c();h(t,"class","agent-grid svelte-1rqt2ni")},m(r,u){S(e,r,u),m(r,n,u),m(r,t,u);for(let $=0;$<a.length;$+=1)a[$]&&a[$].m(t,null);s=!0},p(r,u){41&u&&(l=H(r[3]),q(),a=V(a,u,c,1,r,l,o,t,Z,we,null,he),N())},i(r){if(!s){d(e.$$.fragment,r);for(let u=0;u<l.length;u+=1)d(a[u]);s=!0}},o(r){g(e.$$.fragment,r);for(let u=0;u<a.length;u+=1)g(a[u]);s=!1},d(r){r&&(p(n),p(t)),_(e,r);for(let u=0;u<a.length;u+=1)a[u].d()}}}function we(i,e){var a;let n,t,s;return t=new te({props:{agent:e[7],selected:e[7].remote_agent_id===((a=e[0].state)==null?void 0:a.selectedAgentId),onSelect:e[5]}}),{key:i,first:null,c(){n=O(),w(t.$$.fragment),this.first=n},m(o,l){m(o,n,l),S(t,o,l),s=!0},p(o,l){var r;e=o;const c={};8&l&&(c.agent=e[7]),9&l&&(c.selected=e[7].remote_agent_id===((r=e[0].state)==null?void 0:r.selectedAgentId)),t.$set(c)},i(o){s||(d(t.$$.fragment,o),s=!0)},o(o){g(t.$$.fragment,o),s=!1},d(o){o&&p(n),_(t,o)}}}function Se(i){let e,n,t,s,a=[],o=new Map;e=new ne({props:{title:"Running agents"}});let l=H(i[2]);const c=r=>r[7].remote_agent_id+r[9];for(let r=0;r<l.length;r+=1){let u=fe(i,l,r),$=c(u);o.set($,a[r]=_e($,u))}return{c(){w(e.$$.fragment),n=M(),t=R("div");for(let r=0;r<a.length;r+=1)a[r].c();h(t,"class","agent-grid svelte-1rqt2ni")},m(r,u){S(e,r,u),m(r,n,u),m(r,t,u);for(let $=0;$<a.length;$+=1)a[$]&&a[$].m(t,null);s=!0},p(r,u){37&u&&(l=H(r[2]),q(),a=V(a,u,c,1,r,l,o,t,Z,_e,null,fe),N())},i(r){if(!s){d(e.$$.fragment,r);for(let u=0;u<l.length;u+=1)d(a[u]);s=!0}},o(r){g(e.$$.fragment,r);for(let u=0;u<a.length;u+=1)g(a[u]);s=!1},d(r){r&&(p(n),p(t)),_(e,r);for(let u=0;u<a.length;u+=1)a[u].d()}}}function _e(i,e){var a;let n,t,s;return t=new te({props:{agent:e[7],selected:e[7].remote_agent_id===((a=e[0].state)==null?void 0:a.selectedAgentId),onSelect:e[5]}}),{key:i,first:null,c(){n=O(),w(t.$$.fragment),this.first=n},m(o,l){m(o,n,l),S(t,o,l),s=!0},p(o,l){var r;e=o;const c={};4&l&&(c.agent=e[7]),5&l&&(c.selected=e[7].remote_agent_id===((r=e[0].state)==null?void 0:r.selectedAgentId)),t.$set(c)},i(o){s||(d(t.$$.fragment,o),s=!0)},o(o){g(t.$$.fragment,o),s=!1},d(o){o&&p(n),_(t,o)}}}function ve(i){let e,n,t,s,a=[],o=new Map;e=new ne({props:{title:"Failed agents"}});let l=H(i[1]);const c=r=>r[7].remote_agent_id+r[9];for(let r=0;r<l.length;r+=1){let u=pe(i,l,r),$=c(u);o.set($,a[r]=Re($,u))}return{c(){w(e.$$.fragment),n=M(),t=R("div");for(let r=0;r<a.length;r+=1)a[r].c();h(t,"class","agent-grid svelte-1rqt2ni")},m(r,u){S(e,r,u),m(r,n,u),m(r,t,u);for(let $=0;$<a.length;$+=1)a[$]&&a[$].m(t,null);s=!0},p(r,u){35&u&&(l=H(r[1]),q(),a=V(a,u,c,1,r,l,o,t,Z,Re,null,pe),N())},i(r){if(!s){d(e.$$.fragment,r);for(let u=0;u<l.length;u+=1)d(a[u]);s=!0}},o(r){g(e.$$.fragment,r);for(let u=0;u<a.length;u+=1)g(a[u]);s=!1},d(r){r&&(p(n),p(t)),_(e,r);for(let u=0;u<a.length;u+=1)a[u].d()}}}function Re(i,e){var a;let n,t,s;return t=new te({props:{agent:e[7],selected:e[7].remote_agent_id===((a=e[0].state)==null?void 0:a.selectedAgentId),onSelect:e[5]}}),{key:i,first:null,c(){n=O(),w(t.$$.fragment),this.first=n},m(o,l){m(o,n,l),S(t,o,l),s=!0},p(o,l){var r;e=o;const c={};2&l&&(c.agent=e[7]),3&l&&(c.selected=e[7].remote_agent_id===((r=e[0].state)==null?void 0:r.selectedAgentId)),t.$set(c)},i(o){s||(d(t.$$.fragment,o),s=!0)},o(o){g(t.$$.fragment,o),s=!1},d(o){o&&p(n),_(t,o)}}}function vt(i){let e;return{c(){e=D("No agents available")},m(n,t){m(n,e,t)},d(n){n&&p(e)}}}function Rt(i){let e,n,t,s;const a=[_t,St],o=[];function l(c,r){var u;return((u=c[0].state)==null?void 0:u.agentOverviews.length)===0?0:1}return n=l(i),t=o[n]=a[n](i),{c(){e=R("div"),t.c(),h(e,"class","agent-list svelte-1rqt2ni")},m(c,r){m(c,e,r),o[n].m(e,null),s=!0},p(c,[r]){let u=n;n=l(c),n===u?o[n].p(c,r):(q(),g(o[u],1,1,()=>{o[u]=null}),N(),t=o[n],t?t.p(c,r):(t=o[n]=a[n](c),t.c()),d(t,1),t.m(e,null))},i(c){s||(d(t),s=!0)},o(c){g(t),s=!1},d(c){c&&p(e),o[n].d()}}}function xt(i,e,n){let t,s,a,o,l;const c=xe(Ae);return Ne(i,c,r=>n(0,l=r)),i.$$.update=()=>{var r;1&i.$$.dirty&&n(6,t=Ee(((r=l.state)==null?void 0:r.agentOverviews)||[])),64&i.$$.dirty&&n(3,s=t.filter(u=>u.status===b.agentIdle)),64&i.$$.dirty&&n(2,a=t.filter(u=>u.status===b.agentRunning||u.status===b.agentStarting)),64&i.$$.dirty&&n(1,o=t.filter(u=>u.status!==b.agentRunning&&u.status!==b.agentStarting&&u.status!==b.agentIdle))},[l,o,a,s,c,function(r){c.update(u=>{if(u)return{...u,selectedAgentId:r}})},t]}class At extends C{constructor(e){super(),L(this,e,xt,Rt,F,{})}}function bt(i){let e,n,t,s,a,o,l,c,r,u;return s=new Fe({}),l=new At({}),{c(){e=R("div"),n=R("h1"),t=R("span"),w(s.$$.fragment),a=D(`
    Remote Agents`),o=M(),w(l.$$.fragment),h(t,"class","l-main__title-logo svelte-1941nw6"),h(n,"class","l-main__title svelte-1941nw6"),h(e,"class","l-main svelte-1941nw6")},m($,v){m($,e,v),I(e,n),I(n,t),S(s,t,null),I(n,a),I(e,o),S(l,e,null),c=!0,r||(u=De(window,"message",i[0].onMessageFromExtension),r=!0)},p:ee,i($){c||(d(s.$$.fragment,$),d(l.$$.fragment,$),c=!0)},o($){g(s.$$.fragment,$),g(l.$$.fragment,$),c=!1},d($){$&&p(e),_(s),_(l),r=!1,u()}}}function Mt(i){const e=new Pe(He),n=new Oe(e,void 0,Te,Ce);e.registerConsumer(n),le(Ae,n);const t=new J(e);return le(J.key,t),We(()=>(n.fetchStateFromExtension().then(()=>{n.update(s=>{if(s)return{...s,activeWebviews:[...s.activeWebviews,"home"]}})}),()=>{e.dispose()})),[e]}new class extends C{constructor(i){super(),L(this,i,Mt,bt,F,{})}}({target:document.getElementById("app")});
