import{S as h,i as p,s as y,J as b,a0 as v,c as o,a7 as f,a8 as g,e as w,f as L,a1 as N,n as u,h as j}from"./SpinnerAugment-BUJasFTo.js";function k(t){let a,i,s;return{c(){a=b("span"),i=v(t[1]),o(a,"class",s=f(`material-symbols-outlined ${t[0]}`)+" svelte-htlsjs"),g(a,"font-variation-settings","'FILL' "+t[3]+", 'wght' "+t[4]+", 'GRAD' "+t[5]),o(a,"title",t[2])},m(e,l){w(e,a,l),L(a,i)},p(e,[l]){2&l&&N(i,e[1]),1&l&&s!==(s=f(`material-symbols-outlined ${e[0]}`)+" svelte-htlsjs")&&o(a,"class",s),56&l&&g(a,"font-variation-settings","'FILL' "+e[3]+", 'wght' "+e[4]+", 'GRAD' "+e[5]),4&l&&o(a,"title",e[2])},i:u,o:u,d(e){e&&j(a)}}}function x(t,a,i){let s,e,l,{class:m=""}=a,{iconName:$=""}=a,{fill:r=!1}=a,{grade:c="normal"}=a,{title:d}=a;return t.$$set=n=>{"class"in n&&i(0,m=n.class),"iconName"in n&&i(1,$=n.iconName),"fill"in n&&i(6,r=n.fill),"grade"in n&&i(7,c=n.grade),"title"in n&&i(2,d=n.title)},t.$$.update=()=>{if(64&t.$$.dirty&&i(3,s=r?"1":"0"),64&t.$$.dirty&&i(4,e=r?"700":"400"),128&t.$$.dirty)switch(c){case"low":i(5,l="-25");break;case"normal":i(5,l="0");break;case"high":i(5,l="200")}},[m,$,d,s,e,l,r,c]}class D extends h{constructor(a){super(),p(this,a,x,k,y,{class:0,iconName:1,fill:6,grade:7,title:2})}}export{D as M};
