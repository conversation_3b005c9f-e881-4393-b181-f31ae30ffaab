var l1=Object.defineProperty;var a1=(e,s,n)=>s in e?l1(e,s,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[s]=n;var O=(e,s,n)=>a1(e,typeof s!="symbol"?s+"":s,n);import{S as H,i as _,s as y,b as z,c as r,e as w,f as L,n as v,h as f,$ as Q,F as A,a as T,J as B,V as s1,K as G,L as b,W as e1,g as J,u as $,t as m,Z as o1,E as V,a6 as n1,j as M,I as E,T as S,q as k,r as I,M as P,N as F,O as N,an as c1,H as t1,w as i1,x as r1,y as u1,d as W,z as d1}from"./SpinnerAugment-BUJasFTo.js";function p1(e){let s,n;return{c(){s=z("svg"),n=z("path"),r(n,"fill-rule","evenodd"),r(n,"clip-rule","evenodd"),r(n,"d","M1.84998 7.49998C1.84998 4.66458 4.05979 1.84998 7.49998 1.84998C10.2783 1.84998 11.6515 3.9064 12.2367 5H10.5C10.2239 5 10 5.22386 10 5.5C10 5.77614 10.2239 6 10.5 6H13.5C13.7761 6 14 5.77614 14 5.5V2.5C14 2.22386 13.7761 2 13.5 2C13.2239 2 13 2.22386 13 2.5V4.31318C12.2955 3.07126 10.6659 0.849976 7.49998 0.849976C3.43716 0.849976 0.849976 4.18537 0.849976 7.49998C0.849976 10.8146 3.43716 14.15 7.49998 14.15C9.44382 14.15 11.0622 13.3808 12.2145 12.2084C12.8315 11.5806 13.3133 10.839 13.6418 10.0407C13.7469 9.78536 13.6251 9.49315 13.3698 9.38806C13.1144 9.28296 12.8222 9.40478 12.7171 9.66014C12.4363 10.3425 12.0251 10.9745 11.5013 11.5074C10.5295 12.4963 9.16504 13.15 7.49998 13.15C4.05979 13.15 1.84998 10.3354 1.84998 7.49998Z"),r(n,"fill","currentColor"),r(s,"width","15"),r(s,"height","15"),r(s,"viewBox","0 0 15 15"),r(s,"fill","none"),r(s,"xmlns","http://www.w3.org/2000/svg")},m(a,o){w(a,s,o),L(s,n)},p:v,i:v,o:v,d(a){a&&f(s)}}}class N1 extends H{constructor(s){super(),_(this,s,null,p1,y,{})}}const h1=e=>({}),R=e=>({});function U(e){let s,n;const a=e[8].icon,o=E(a,e,e[9],R);return{c(){s=B("div"),o&&o.c(),r(s,"class","c-callout-icon svelte-8as1i4")},m(l,c){w(l,s,c),o&&o.m(s,null),n=!0},p(l,c){o&&o.p&&(!n||512&c)&&P(o,a,l,l[9],n?N(a,l[9],c,h1):F(l[9]),R)},i(l){n||($(o,l),n=!0)},o(l){m(o,l),n=!1},d(l){l&&f(s),o&&o.d(l)}}}function C1(e){let s,n,a,o=e[7].icon&&U(e);const l=e[8].default,c=E(l,e,e[9],null);return{c(){o&&o.c(),s=S(),n=B("div"),c&&c.c(),r(n,"class","c-callout-body")},m(t,u){o&&o.m(t,u),w(t,s,u),w(t,n,u),c&&c.m(n,null),a=!0},p(t,u){t[7].icon?o?(o.p(t,u),128&u&&$(o,1)):(o=U(t),o.c(),$(o,1),o.m(s.parentNode,s)):o&&(k(),m(o,1,1,()=>{o=null}),I()),c&&c.p&&(!a||512&u)&&P(c,l,t,t[9],a?N(l,t[9],u,null):F(t[9]),null)},i(t){a||($(o),$(c,t),a=!0)},o(t){m(o),m(c,t),a=!1},d(t){t&&(f(s),f(n)),o&&o.d(t),c&&c.d(t)}}}function g1(e){let s,n,a,o;n=new Q({props:{size:e[6],$$slots:{default:[C1]},$$scope:{ctx:e}}});let l=[A(e[0]),{class:a=`c-callout c-callout--${e[0]} c-callout--${e[1]} c-callout--size-${e[2]} ${e[5]}`},e[4]],c={};for(let t=0;t<l.length;t+=1)c=T(c,l[t]);return{c(){s=B("div"),s1(n.$$.fragment),G(s,c),b(s,"c-callout--highContrast",e[3]),b(s,"svelte-8as1i4",!0)},m(t,u){w(t,s,u),e1(n,s,null),o=!0},p(t,[u]){const C={};640&u&&(C.$$scope={dirty:u,ctx:t}),n.$set(C),G(s,c=J(l,[1&u&&A(t[0]),(!o||39&u&&a!==(a=`c-callout c-callout--${t[0]} c-callout--${t[1]} c-callout--size-${t[2]} ${t[5]}`))&&{class:a},16&u&&t[4]])),b(s,"c-callout--highContrast",t[3]),b(s,"svelte-8as1i4",!0)},i(t){o||($(n.$$.fragment,t),o=!0)},o(t){m(n.$$.fragment,t),o=!1},d(t){t&&f(s),o1(n)}}}function $1(e,s,n){let a,o;const l=["color","variant","size","highContrast"];let c=V(s,l),{$$slots:t={},$$scope:u}=s;const C=n1(t);let{color:p="info"}=s,{variant:h="soft"}=s,{size:g=2}=s,{highContrast:x=!1}=s;const i=g;return e.$$set=d=>{s=T(T({},s),M(d)),n(10,c=V(s,l)),"color"in d&&n(0,p=d.color),"variant"in d&&n(1,h=d.variant),"size"in d&&n(2,g=d.size),"highContrast"in d&&n(3,x=d.highContrast),"$$scope"in d&&n(9,u=d.$$scope)},e.$$.update=()=>{n(5,{class:a,...o}=c,a,(n(4,o),n(10,c)))},[p,h,g,x,o,a,i,C,t,u]}class S1 extends H{constructor(s){super(),_(this,s,$1,g1,y,{color:0,variant:1,size:2,highContrast:3})}}function m1(e){let s,n;return{c(){s=z("svg"),n=z("path"),r(n,"fill-rule","evenodd"),r(n,"clip-rule","evenodd"),r(n,"d","M11.7816 4.03157C12.0062 3.80702 12.0062 3.44295 11.7816 3.2184C11.5571 2.99385 11.193 2.99385 10.9685 3.2184L7.50005 6.68682L4.03164 3.2184C3.80708 2.99385 3.44301 2.99385 3.21846 3.2184C2.99391 3.44295 2.99391 3.80702 3.21846 4.03157L6.68688 7.49999L3.21846 10.9684C2.99391 11.193 2.99391 11.557 3.21846 11.7816C3.44301 12.0061 3.80708 12.0061 4.03164 11.7816L7.50005 8.31316L10.9685 11.7816C11.193 12.0061 11.5571 12.0061 11.7816 11.7816C12.0062 11.557 12.0062 11.193 11.7816 10.9684L8.31322 7.49999L11.7816 4.03157Z"),r(n,"fill","currentColor"),r(s,"width","15"),r(s,"height","15"),r(s,"viewBox","0 0 15 15"),r(s,"fill","none"),r(s,"xmlns","http://www.w3.org/2000/svg")},m(a,o){w(a,s,o),L(s,n)},p:v,i:v,o:v,d(a){a&&f(s)}}}class A1 extends H{constructor(s){super(),_(this,s,null,m1,y,{})}}function f1(e){let s,n;return{c(){s=z("svg"),n=z("path"),r(n,"fill-rule","evenodd"),r(n,"clip-rule","evenodd"),r(n,"d","M7.49933 0.25C3.49635 0.25 0.25 3.49593 0.25 7.50024C0.25 10.703 2.32715 13.4206 5.2081 14.3797C5.57084 14.446 5.70302 14.2222 5.70302 14.0299C5.70302 13.8576 5.69679 13.4019 5.69323 12.797C3.67661 13.235 3.25112 11.825 3.25112 11.825C2.92132 10.9874 2.44599 10.7644 2.44599 10.7644C1.78773 10.3149 2.49584 10.3238 2.49584 10.3238C3.22353 10.375 3.60629 11.0711 3.60629 11.0711C4.25298 12.1788 5.30335 11.8588 5.71638 11.6732C5.78225 11.205 5.96962 10.8854 6.17658 10.7043C4.56675 10.5209 2.87415 9.89918 2.87415 7.12104C2.87415 6.32925 3.15677 5.68257 3.62053 5.17563C3.54576 4.99226 3.29697 4.25521 3.69174 3.25691C3.69174 3.25691 4.30015 3.06196 5.68522 3.99973C6.26337 3.83906 6.8838 3.75895 7.50022 3.75583C8.1162 3.75895 8.73619 3.83906 9.31523 3.99973C10.6994 3.06196 11.3069 3.25691 11.3069 3.25691C11.7026 4.25521 11.4538 4.99226 11.3795 5.17563C11.8441 5.68257 12.1245 6.32925 12.1245 7.12104C12.1245 9.9063 10.4292 10.5192 8.81452 10.6985C9.07444 10.9224 9.30633 11.3648 9.30633 12.0413C9.30633 13.0102 9.29742 13.7922 9.29742 14.0299C9.29742 14.2239 9.42828 14.4496 9.79591 14.3788C12.6746 13.4179 14.75 10.7025 14.75 7.50024C14.75 3.49593 11.5036 0.25 7.49933 0.25Z"),r(n,"fill","currentColor"),r(s,"width","15"),r(s,"height","15"),r(s,"viewBox","0 0 15 15"),r(s,"fill","none"),r(s,"xmlns","http://www.w3.org/2000/svg")},m(a,o){w(a,s,o),L(s,n)},p:v,i:v,o:v,d(a){a&&f(s)}}}class G1 extends H{constructor(s){super(),_(this,s,null,f1,y,{})}}var v1=(e=>(e.readFile="read-file",e.saveFile="save-file",e.editFile="edit-file",e.clarify="clarify",e.onboardingSubAgent="onboarding-sub-agent",e.launchProcess="launch-process",e.killProcess="kill-process",e.readProcess="read-process",e.writeProcess="write-process",e.listProcesses="list-processes",e.waitProcess="wait-process",e.openBrowser="open-browser",e.strReplaceEditor="str-replace-editor",e.remember="remember",e.diagnostics="diagnostics",e.setupScript="setup-script",e.readTerminal="read-terminal",e))(v1||{}),w1=(e=>(e.remoteToolHost="remoteToolHost",e.localToolHost="localToolHost",e.sidecarToolHost="sidecarToolHost",e.mcpHost="mcpHost",e))(w1||{}),b1=(e=>(e[e.ContentText=0]="ContentText",e[e.ContentImage=1]="ContentImage",e))(b1||{}),x1=(e=>(e[e.Unknown=0]="Unknown",e[e.WebSearch=1]="WebSearch",e[e.GitHubApi=8]="GitHubApi",e[e.Linear=12]="Linear",e[e.Jira=13]="Jira",e[e.Confluence=14]="Confluence",e[e.Notion=15]="Notion",e[e.Supabase=16]="Supabase",e[e.Glean=17]="Glean",e))(x1||{});class Z{constructor(s){this._opts=s}get color(){return this._opts.color}get size(){return this._opts.size??1}get variant(){return this._opts.variant}}O(Z,"CONTEXT_KEY","augment-badge");const z1=e=>({}),X=e=>({}),T1=e=>({}),Y=e=>({}),B1=e=>({}),j=e=>({});function q(e){let s,n;const a=e[6].leftButtons,o=E(a,e,e[7],Y);return{c(){s=B("div"),o&&o.c(),r(s,"class","c-badge__left-buttons svelte-s5ltdx")},m(l,c){w(l,s,c),o&&o.m(s,null),n=!0},p(l,c){o&&o.p&&(!n||128&c)&&P(o,a,l,l[7],n?N(a,l[7],c,T1):F(l[7]),Y)},i(l){n||($(o,l),n=!0)},o(l){m(o,l),n=!1},d(l){l&&f(s),o&&o.d(l)}}}function L1(e){let s,n;const a=e[6].default,o=E(a,e,e[7],null);return{c(){s=B("div"),o&&o.c(),r(s,"class","c-badge-body svelte-s5ltdx")},m(l,c){w(l,s,c),o&&o.m(s,null),n=!0},p(l,c){o&&o.p&&(!n||128&c)&&P(o,a,l,l[7],n?N(a,l[7],c,null):F(l[7]),null)},i(l){n||($(o,l),n=!0)},o(l){m(o,l),n=!1},d(l){l&&f(s),o&&o.d(l)}}}function D(e){let s,n;const a=e[6].rightButtons,o=E(a,e,e[7],X);return{c(){s=B("div"),o&&o.c(),r(s,"class","c-badge__right-buttons svelte-s5ltdx")},m(l,c){w(l,s,c),o&&o.m(s,null),n=!0},p(l,c){o&&o.p&&(!n||128&c)&&P(o,a,l,l[7],n?N(a,l[7],c,z1):F(l[7]),X)},i(l){n||($(o,l),n=!0)},o(l){m(o,l),n=!1},d(l){l&&f(s),o&&o.d(l)}}}function H1(e){let s,n,a,o,l,c,t;const u=e[6].chaser,C=E(u,e,e[7],j);let p=e[5].leftButtons&&q(e);o=new Q({props:{size:e[4],weight:"medium",$$slots:{default:[L1]},$$scope:{ctx:e}}});let h=e[5].rightButtons&&D(e),g=[A(e[0]),{class:c=`c-badge c-badge--${e[0]} c-badge--${e[1]} c-badge--size-${e[2]}`}],x={};for(let i=0;i<g.length;i+=1)x=T(x,g[i]);return{c(){s=B("div"),C&&C.c(),n=S(),p&&p.c(),a=S(),s1(o.$$.fragment),l=S(),h&&h.c(),G(s,x),b(s,"c-badge--highContrast",e[3]),b(s,"svelte-s5ltdx",!0)},m(i,d){w(i,s,d),C&&C.m(s,null),L(s,n),p&&p.m(s,null),L(s,a),e1(o,s,null),L(s,l),h&&h.m(s,null),t=!0},p(i,[d]){C&&C.p&&(!t||128&d)&&P(C,u,i,i[7],t?N(u,i[7],d,B1):F(i[7]),j),i[5].leftButtons?p?(p.p(i,d),32&d&&$(p,1)):(p=q(i),p.c(),$(p,1),p.m(s,a)):p&&(k(),m(p,1,1,()=>{p=null}),I());const K={};128&d&&(K.$$scope={dirty:d,ctx:i}),o.$set(K),i[5].rightButtons?h?(h.p(i,d),32&d&&$(h,1)):(h=D(i),h.c(),$(h,1),h.m(s,null)):h&&(k(),m(h,1,1,()=>{h=null}),I()),G(s,x=J(g,[1&d&&A(i[0]),(!t||7&d&&c!==(c=`c-badge c-badge--${i[0]} c-badge--${i[1]} c-badge--size-${i[2]}`))&&{class:c}])),b(s,"c-badge--highContrast",i[3]),b(s,"svelte-s5ltdx",!0)},i(i){t||($(C,i),$(p),$(o.$$.fragment,i),$(h),t=!0)},o(i){m(C,i),m(p),m(o.$$.fragment,i),m(h),t=!1},d(i){i&&f(s),C&&C.d(i),p&&p.d(),o1(o),h&&h.d()}}}function _1(e,s,n){let{$$slots:a={},$$scope:o}=s;const l=n1(a);let{color:c="accent"}=s,{variant:t="soft"}=s,{size:u=1}=s,{highContrast:C=!1}=s;const p=u===3?2:1,h=new Z({color:c,size:u,variant:t});return c1(Z.CONTEXT_KEY,h),e.$$set=g=>{"color"in g&&n(0,c=g.color),"variant"in g&&n(1,t=g.variant),"size"in g&&n(2,u=g.size),"highContrast"in g&&n(3,C=g.highContrast),"$$scope"in g&&n(7,o=g.$$scope)},[c,t,u,C,p,l,a,o]}class M1 extends H{constructor(s){super(),_(this,s,_1,H1,y,{color:0,variant:1,size:2,highContrast:3})}}function y1(e){let s,n,a=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},e[0]],o={};for(let l=0;l<a.length;l+=1)o=T(o,a[l]);return{c(){s=z("svg"),n=new t1(!0),this.h()},l(l){s=i1(l,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var c=r1(s);n=u1(c,!0),c.forEach(f),this.h()},h(){n.a=null,W(s,o)},m(l,c){d1(l,s,c),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M256 48a208 208 0 1 1 0 416 208 208 0 1 1 0-416m0 464a256 256 0 1 0 0-512 256 256 0 1 0 0 512m113-303c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-111 111-47-47c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9l64 64c9.4 9.4 24.6 9.4 33.9 0z"/>',s)},p(l,[c]){W(s,o=J(a,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&c&&l[0]]))},i:v,o:v,d(l){l&&f(s)}}}function E1(e,s,n){return e.$$set=a=>{n(0,s=T(T({},s),M(a)))},[s=M(s)]}class k1 extends H{constructor(s){super(),_(this,s,E1,y1,y,{})}}export{Z as B,S1 as C,G1 as G,v1 as L,N1 as R,w1 as T,M1 as a,x1 as b,A1 as c,k1 as d,b1 as e};
