import{S as Lt,i as Rt,s as kt,J as y,c as m,e as j,f as d,q as et,t as v,r as nt,u as f,h as b,ac as zt,as as Zt,a2 as qt,V as L,W as R,Z as k,Q as Ct,R as yt,Y as _t,a0 as N,T as E,a1 as ft,n as ct,al as Ae,$ as Y,P as lt,A as Nt,a3 as Dt,D as Gt,L as jt,a as xt,b as st,H as Vt,w as Wt,x as Xt,y as Yt,d as Ft,z as Jt,g as Kt,j as Ut,I as Se,M as ze,N as Ee,O as Fe,a6 as Ie,G as se,at as oe,ad as ce,_ as Ze,aa as Ue,ab as Tt}from"./SpinnerAugment-BUJasFTo.js";import{B as Pt}from"./ButtonAugment-DbAwCSeR.js";import{C as It,R as Be}from"./circle-check-D3m08yO6.js";import{E as qe}from"./exclamation-triangle-uzqmF3G7.js";import{T as Ve}from"./trash-mbophkQL.js";import{f as Ht}from"./index-DlpZFSR-.js";import{e as Bt,u as Qt,o as te}from"./each-DUdYBCJG.js";import{G as Pe,C as Ne,B as We,W as Xe,A as Me,a as Ye,b as Ot,g as Je}from"./main-panel-C8Qm-2QZ.js";import{d as ie,T as le}from"./Content-CSmc2GUv.js";import{C as De,T as Te,D as ee}from"./index-yg8vr2DA.js";import{n as ae,R as He}from"./open-in-new-window-eiueNVFd.js";import"./diff-operations-DcwKj7d6.js";import{T as Et}from"./TextTooltipAugment-UDQF2J4S.js";import{T as Oe}from"./terminal-CwJUqtXN.js";import{P as Ke}from"./pen-to-square-DxHNIIBu.js";import{I as ne}from"./IconButtonAugment-DFy7vWkh.js";import"./BaseButton-ci_067e0.js";import"./design-system-init-BKdwvVur.js";import"./file-base64-RhZyEMB8.js";import"./layer-group-DiHphAz9.js";import"./types-CF53Ux0u.js";import"./lodash-BHrlUNHT.js";import"./ellipsis-CRdQranZ.js";import"./Keybindings-CJ37aOb-.js";import"./CardAugment-DvO45c5p.js";import"./augment-logo-CSOE_v2f.js";import"./diff-utils-BYhHYFY1.js";import"./toggleHighContrast-CwIv4U26.js";import"./preload-helper-Dv6uf1Os.js";import"./index-B-fP3g4F.js";import"./isObjectLike-DZvo29T1.js";import"./folder-opened-C1X7jSw2.js";import"./file-paths-BcSg4gks.js";import"./globals-D0QH3NT1.js";import"./await_block-MKx3qG42.js";import"./keypress-DD1aQVr0.js";import"./chevron-down-BPcCn3Z6.js";import"./MaterialIcon-d9y4vLnQ.js";import"./types-e72Yl75f.js";import"./autofix-state-d-ymFdyn.js";import"./VSCodeCodicon-CzBgPB9u.js";import"./chat-flags-model-pSBfdnEi.js";import"./BaseTextInput-BYcZ2XaJ.js";function ue(s){const t=s.match(/github\.com\/([^/]+)\/([^/]+?)(?:\.git|\/|$)/);if(t)return{owner:t[1],name:t[2]}}function $e(s){return s.replace(/^origin\//,"")}function me(s,t,e){const n=s.slice();return n[59]=t[e],n}function pe(s,t,e){const n=s.slice();return n[62]=t[e],n}function Qe(s){let t,e,n;return e=new It({props:{color:"warning",variant:"soft",size:2,$$slots:{default:[rn]},$$scope:{ctx:s}}}),{c(){t=y("div"),L(e.$$.fragment),m(t,"class","c-commit-ref-selector__error svelte-jv14ji")},m(r,o){j(r,t,o),R(e,t,null),n=!0},p(r,o){const i={};2049&o[0]|8&o[2]&&(i.$$scope={dirty:o,ctx:r}),e.$set(i)},i(r){n||(f(e.$$.fragment,r),n=!0)},o(r){v(e.$$.fragment,r),n=!1},d(r){r&&b(t),k(e)}}}function tn(s){let t,e,n,r,o;function i(l){s[44](l)}let c={type:"dropdown",title:s[15],noItemsLabel:"No branches found",onDropdownOpenChange:s[22],$$slots:{"dropdown-content":[Fn],iconLeft:[cn]},$$scope:{ctx:s}};return s[12]!==void 0&&(c.requestClose=s[12]),n=new Ne({props:c}),Ct.push(()=>yt(n,"requestClose",i)),{c(){t=y("div"),e=y("div"),L(n.$$.fragment),m(e,"class","c-commit-ref-selector__branch-line svelte-jv14ji"),m(t,"class","c-commit-ref-selector__branch-line-container svelte-jv14ji")},m(l,a){j(l,t,a),d(t,e),R(n,e,null),o=!0},p(l,a){const u={};32768&a[0]&&(u.title=l[15]),26621&a[0]|8&a[2]&&(u.$$scope={dirty:a,ctx:l}),!r&&4096&a[0]&&(r=!0,u.requestClose=l[12],_t(()=>r=!1)),n.$set(u)},i(l){o||(f(n.$$.fragment,l),o=!0)},o(l){v(n.$$.fragment,l),o=!1},d(l){l&&b(t),k(n)}}}function en(s){let t;return{c(){t=N("Reload available repos and branches")},m(e,n){j(e,t,n)},d(e){e&&b(t)}}}function nn(s){let t,e,n;return e=new Be({}),{c(){t=y("span"),L(e.$$.fragment),m(t,"slot","iconLeft"),m(t,"class","svelte-jv14ji")},m(r,o){j(r,t,o),R(e,t,null),n=!0},p:ct,i(r){n||(f(e.$$.fragment,r),n=!0)},o(r){v(e.$$.fragment,r),n=!1},d(r){r&&b(t),k(e)}}}function rn(s){let t,e,n,r,o,i;return o=new Pt({props:{variant:"ghost",color:"warning",size:1,loading:s[0],class:"c-commit-ref-selector__fetch-button",$$slots:{iconLeft:[nn],default:[en]},$$scope:{ctx:s}}}),o.$on("click",s[17]),{c(){t=y("div"),e=y("div"),n=N(s[11]),r=E(),L(o.$$.fragment),m(e,"class","c-commit-ref-selector__error-message svelte-jv14ji"),m(t,"class","c-commit-ref-selector__error-content svelte-jv14ji")},m(c,l){j(c,t,l),d(t,e),d(e,n),d(t,r),R(o,t,null),i=!0},p(c,l){(!i||2048&l[0])&&ft(n,c[11]);const a={};1&l[0]&&(a.loading=c[0]),8&l[2]&&(a.$$scope={dirty:l,ctx:c}),o.$set(a)},i(c){i||(f(o.$$.fragment,c),i=!0)},o(c){v(o.$$.fragment,c),i=!1},d(c){c&&b(t),k(o)}}}function sn(s){let t,e;return t=new We({}),{c(){L(t.$$.fragment)},m(n,r){R(t,n,r),e=!0},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){v(t.$$.fragment,n),e=!1},d(n){k(t,n)}}}function on(s){let t,e;return t=new Ae({props:{size:1,useCurrentColor:!0}}),{c(){L(t.$$.fragment)},m(n,r){R(t,n,r),e=!0},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){v(t.$$.fragment,n),e=!1},d(n){k(t,n)}}}function cn(s){let t,e,n,r;const o=[on,sn],i=[];function c(l,a){return l[0]?0:1}return e=c(s),n=i[e]=o[e](s),{c(){t=y("div"),n.c(),m(t,"slot","iconLeft"),m(t,"class","c-commit-ref-selector__icon svelte-jv14ji")},m(l,a){j(l,t,a),i[e].m(t,null),r=!0},p(l,a){let u=e;e=c(l),e!==u&&(et(),v(i[u],1,1,()=>{i[u]=null}),nt(),n=i[e],n||(n=i[e]=o[e](l),n.c()),f(n,1),n.m(t,null))},i(l){r||(f(n),r=!0)},o(l){v(n),r=!1},d(l){l&&b(t),i[e].d()}}}function ln(s){let t,e,n,r,o,i,c,l,a,u,w,h;n=new Y({props:{size:2,$$slots:{default:[un]},$$scope:{ctx:s}}}),c=new Y({props:{size:2,$$slots:{default:[$n]},$$scope:{ctx:s}}});const g=[fn,pn,mn],$=[];function p(C,x){return C[6].length>0?0:C[0]?1:2}return u=p(s),w=$[u]=g[u](s),{c(){t=y("div"),e=y("div"),L(n.$$.fragment),r=E(),o=y("div"),i=y("div"),L(c.$$.fragment),l=E(),a=y("div"),w.c(),m(e,"class","c-commit-ref-selector__section-label svelte-jv14ji"),m(i,"class","c-commit-ref-selector__fixed-content svelte-jv14ji"),m(a,"class","c-commit-ref-selector__scrollable-content svelte-jv14ji"),m(o,"class","c-commit-ref-selector__section-content svelte-jv14ji"),m(t,"class","c-commit-ref-selector__section svelte-jv14ji")},m(C,x){j(C,t,x),d(t,e),R(n,e,null),d(t,r),d(t,o),d(o,i),R(c,i,null),d(o,l),d(o,a),$[u].m(a,null),h=!0},p(C,x){const P={};8&x[2]&&(P.$$scope={dirty:x,ctx:C}),n.$set(P);const U={};768&x[0]|8&x[2]&&(U.$$scope={dirty:x,ctx:C}),c.$set(U);let B=u;u=p(C),u===B?$[u].p(C,x):(et(),v($[B],1,1,()=>{$[B]=null}),nt(),w=$[u],w?w.p(C,x):(w=$[u]=g[u](C),w.c()),f(w,1),w.m(a,null))},i(C){h||(f(n.$$.fragment,C),f(c.$$.fragment,C),f(w),h=!0)},o(C){v(n.$$.fragment,C),v(c.$$.fragment,C),v(w),h=!1},d(C){C&&b(t),k(n),k(c),$[u].d()}}}function an(s){let t,e,n,r,o,i,c,l,a,u,w,h,g;return n=new Y({props:{size:2,$$slots:{default:[wn]},$$scope:{ctx:s}}}),c=new Y({props:{size:2,$$slots:{default:[Cn]},$$scope:{ctx:s}}}),u=new De({}),{c(){t=y("button"),e=y("div"),L(n.$$.fragment),r=E(),o=y("div"),i=y("div"),L(c.$$.fragment),l=E(),a=y("div"),L(u.$$.fragment),m(e,"class","c-commit-ref-selector__section-label svelte-jv14ji"),m(a,"class","c-commit-ref-selector__section-icon svelte-jv14ji"),m(i,"class","c-commit-ref-selector__selected-input selected-input-disabled svelte-jv14ji"),m(o,"class","c-commit-ref-selector__section-content svelte-jv14ji"),m(t,"type","button"),m(t,"class","c-commit-ref-selector__section c-commit-ref-selector__section-top svelte-jv14ji")},m($,p){j($,t,p),d(t,e),R(n,e,null),d(t,r),d(t,o),d(o,i),R(c,i,null),d(i,l),d(i,a),R(u,a,null),w=!0,h||(g=[lt(t,"click",s[30]),lt(t,"keydown",s[31])],h=!0)},p($,p){const C={};8&p[2]&&(C.$$scope={dirty:p,ctx:$}),n.$set(C);const x={};16384&p[0]|8&p[2]&&(x.$$scope={dirty:p,ctx:$}),c.$set(x)},i($){w||(f(n.$$.fragment,$),f(c.$$.fragment,$),f(u.$$.fragment,$),w=!0)},o($){v(n.$$.fragment,$),v(c.$$.fragment,$),v(u.$$.fragment,$),w=!1},d($){$&&b(t),k(n),k(c),k(u),h=!1,Nt(g)}}}function un(s){let t;return{c(){t=N("Repo")},m(e,n){j(e,t,n)},d(e){e&&b(t)}}}function $n(s){let t,e,n;return{c(){t=y("input"),m(t,"class","c-commit-ref-selector__selected-input svelte-jv14ji"),m(t,"placeholder","Search repos")},m(r,o){j(r,t,o),Dt(t,s[9]),e||(n=[lt(t,"input",s[32]),lt(t,"focus",s[33]),lt(t,"blur",s[34]),lt(t,"input",s[35])],e=!0)},p(r,o){512&o[0]&&t.value!==r[9]&&Dt(t,r[9])},d(r){r&&b(t),e=!1,Nt(n)}}}function mn(s){let t,e,n;return e=new Y({props:{size:1,color:"neutral",$$slots:{default:[dn]},$$scope:{ctx:s}}}),{c(){t=y("div"),L(e.$$.fragment),m(t,"class","c-commit-ref-selector__item c-commit-ref-selector__item--disabled svelte-jv14ji")},m(r,o){j(r,t,o),R(e,t,null),n=!0},p(r,o){const i={};8&o[2]&&(i.$$scope={dirty:o,ctx:r}),e.$set(i)},i(r){n||(f(e.$$.fragment,r),n=!0)},o(r){v(e.$$.fragment,r),n=!1},d(r){r&&b(t),k(e)}}}function pn(s){let t,e,n;return e=new Y({props:{size:1,color:"neutral",$$slots:{default:[gn]},$$scope:{ctx:s}}}),{c(){t=y("div"),L(e.$$.fragment),m(t,"class","c-commit-ref-selector__item c-commit-ref-selector__item--disabled svelte-jv14ji")},m(r,o){j(r,t,o),R(e,t,null),n=!0},p(r,o){const i={};8&o[2]&&(i.$$scope={dirty:o,ctx:r}),e.$set(i)},i(r){n||(f(e.$$.fragment,r),n=!0)},o(r){v(e.$$.fragment,r),n=!1},d(r){r&&b(t),k(e)}}}function fn(s){let t,e,n=[],r=new Map,o=Bt(s[6]);const i=c=>`${c[62].owner}/${c[62].name}`;for(let c=0;c<o.length;c+=1){let l=pe(s,o,c),a=i(l);r.set(a,n[c]=de(a,l))}return{c(){for(let c=0;c<n.length;c+=1)n[c].c();t=Gt()},m(c,l){for(let a=0;a<n.length;a+=1)n[a]&&n[a].m(c,l);j(c,t,l),e=!0},p(c,l){2097220&l[0]&&(o=Bt(c[6]),et(),n=Qt(n,l,i,1,c,o,r,t.parentNode,te,de,t,pe),nt())},i(c){if(!e){for(let l=0;l<o.length;l+=1)f(n[l]);e=!0}},o(c){for(let l=0;l<n.length;l+=1)v(n[l]);e=!1},d(c){c&&b(t);for(let l=0;l<n.length;l+=1)n[l].d(c)}}}function dn(s){let t;return{c(){t=N("No repos found")},m(e,n){j(e,t,n)},d(e){e&&b(t)}}}function gn(s){let t;return{c(){t=N("Loading...")},m(e,n){j(e,t,n)},d(e){e&&b(t)}}}function vn(s){let t,e=s[62].name+"";return{c(){t=N(e)},m(n,r){j(n,t,r)},p(n,r){64&r[0]&&e!==(e=n[62].name+"")&&ft(t,e)},d(n){n&&b(t)}}}function fe(s){let t,e;return t=new Y({props:{size:1,color:"neutral",class:"c-commit-ref-selector__item-date",$$slots:{default:[hn]},$$scope:{ctx:s}}}),{c(){L(t.$$.fragment)},m(n,r){R(t,n,r),e=!0},p(n,r){const o={};64&r[0]|8&r[2]&&(o.$$scope={dirty:r,ctx:n}),t.$set(o)},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){v(t.$$.fragment,n),e=!1},d(n){k(t,n)}}}function hn(s){let t,e=ae(s[62].updated_at)+"";return{c(){t=N(e)},m(n,r){j(n,t,r)},p(n,r){64&r[0]&&e!==(e=ae(n[62].updated_at)+"")&&ft(t,e)},d(n){n&&b(t)}}}function de(s,t){let e,n,r,o,i,c,l,a;r=new Y({props:{size:2,color:"neutral",class:"c-commit-ref-selector__item-name",$$slots:{default:[vn]},$$scope:{ctx:t}}});let u=t[62].updated_at&&fe(t);function w(){return t[36](t[62])}return{key:s,first:null,c(){var h;e=y("button"),n=y("div"),L(r.$$.fragment),o=E(),u&&u.c(),i=E(),m(n,"class","c-commit-ref-selector__item-content svelte-jv14ji"),m(e,"type","button"),m(e,"class","c-commit-ref-selector__item svelte-jv14ji"),jt(e,"c-commit-ref-selector__item--active",((h=t[2])==null?void 0:h.name)===t[62].name),this.first=e},m(h,g){j(h,e,g),d(e,n),R(r,n,null),d(n,o),u&&u.m(n,null),d(e,i),c=!0,l||(a=lt(e,"click",w),l=!0)},p(h,g){var p;t=h;const $={};64&g[0]|8&g[2]&&($.$$scope={dirty:g,ctx:t}),r.$set($),t[62].updated_at?u?(u.p(t,g),64&g[0]&&f(u,1)):(u=fe(t),u.c(),f(u,1),u.m(n,null)):u&&(et(),v(u,1,1,()=>{u=null}),nt()),(!c||68&g[0])&&jt(e,"c-commit-ref-selector__item--active",((p=t[2])==null?void 0:p.name)===t[62].name)},i(h){c||(f(r.$$.fragment,h),f(u),c=!0)},o(h){v(r.$$.fragment,h),v(u),c=!1},d(h){h&&b(e),k(r),u&&u.d(),l=!1,a()}}}function wn(s){let t;return{c(){t=N("Repo")},m(e,n){j(e,t,n)},d(e){e&&b(t)}}}function Cn(s){let t;return{c(){t=N(s[14])},m(e,n){j(e,t,n)},p(e,n){16384&n[0]&&ft(t,e[14])},d(e){e&&b(t)}}}function yn(s){let t,e,n,r,o,i,c,l,a,u,w,h;n=new Y({props:{size:2,$$slots:{default:[xn]},$$scope:{ctx:s}}}),c=new Y({props:{size:2,$$slots:{default:[bn]},$$scope:{ctx:s}}});const g=[Rn,Ln,jn],$=[];function p(C,x){return C[7].length>0?0:C[0]?1:2}return u=p(s),w=$[u]=g[u](s),{c(){t=y("div"),e=y("div"),L(n.$$.fragment),r=E(),o=y("div"),i=y("div"),L(c.$$.fragment),l=E(),a=y("div"),w.c(),m(e,"class","c-commit-ref-selector__section-label svelte-jv14ji"),m(i,"class","c-commit-ref-selector__fixed-content svelte-jv14ji"),m(a,"class","c-commit-ref-selector__scrollable-content svelte-jv14ji"),m(o,"class","c-commit-ref-selector__section-content svelte-jv14ji"),m(t,"class","c-commit-ref-selector__section svelte-jv14ji")},m(C,x){j(C,t,x),d(t,e),R(n,e,null),d(t,r),d(t,o),d(o,i),R(c,i,null),d(o,l),d(o,a),$[u].m(a,null),h=!0},p(C,x){const P={};8&x[2]&&(P.$$scope={dirty:x,ctx:C}),n.$set(P);const U={};1040&x[0]|8&x[2]&&(U.$$scope={dirty:x,ctx:C}),c.$set(U);let B=u;u=p(C),u===B?$[u].p(C,x):(et(),v($[B],1,1,()=>{$[B]=null}),nt(),w=$[u],w?w.p(C,x):(w=$[u]=g[u](C),w.c()),f(w,1),w.m(a,null))},i(C){h||(f(n.$$.fragment,C),f(c.$$.fragment,C),f(w),h=!0)},o(C){v(n.$$.fragment,C),v(c.$$.fragment,C),v(w),h=!1},d(C){C&&b(t),k(n),k(c),$[u].d()}}}function _n(s){let t,e,n,r,o,i,c,l,a,u,w,h,g;return n=new Y({props:{size:2,$$slots:{default:[zn]},$$scope:{ctx:s}}}),c=new Y({props:{size:2,$$slots:{default:[En]},$$scope:{ctx:s}}}),u=new De({}),{c(){t=y("button"),e=y("div"),L(n.$$.fragment),r=E(),o=y("div"),i=y("div"),L(c.$$.fragment),l=E(),a=y("div"),L(u.$$.fragment),m(e,"class","c-commit-ref-selector__section-label svelte-jv14ji"),m(a,"class","c-commit-ref-selector__section-icon svelte-jv14ji"),m(i,"class","c-commit-ref-selector__selected-input selected-input-disabled svelte-jv14ji"),m(o,"class","c-commit-ref-selector__section-content svelte-jv14ji"),m(t,"type","button"),m(t,"class","c-commit-ref-selector__section c-commit-ref-selector__section-bottom svelte-jv14ji")},m($,p){j($,t,p),d(t,e),R(n,e,null),d(t,r),d(t,o),d(o,i),R(c,i,null),d(i,l),d(i,a),R(u,a,null),w=!0,h||(g=[lt(t,"click",s[37]),lt(t,"keydown",s[38])],h=!0)},p($,p){const C={};8&p[2]&&(C.$$scope={dirty:p,ctx:$}),n.$set(C);const x={};8192&p[0]|8&p[2]&&(x.$$scope={dirty:p,ctx:$}),c.$set(x)},i($){w||(f(n.$$.fragment,$),f(c.$$.fragment,$),f(u.$$.fragment,$),w=!0)},o($){v(n.$$.fragment,$),v(c.$$.fragment,$),v(u.$$.fragment,$),w=!1},d($){$&&b(t),k(n),k(c),k(u),h=!1,Nt(g)}}}function xn(s){let t;return{c(){t=N("Branch")},m(e,n){j(e,t,n)},d(e){e&&b(t)}}}function bn(s){let t,e,n;return{c(){t=y("input"),m(t,"class","c-commit-ref-selector__selected-input svelte-jv14ji"),m(t,"placeholder","Search branches")},m(r,o){j(r,t,o),Dt(t,s[10]),e||(n=[lt(t,"input",s[39]),lt(t,"focus",s[40]),lt(t,"blur",s[41]),lt(t,"input",s[42])],e=!0)},p(r,o){1024&o[0]&&t.value!==r[10]&&Dt(t,r[10])},d(r){r&&b(t),e=!1,Nt(n)}}}function jn(s){let t,e,n;return e=new Y({props:{size:1,color:"neutral",$$slots:{default:[kn]},$$scope:{ctx:s}}}),{c(){t=y("div"),L(e.$$.fragment),m(t,"class","c-commit-ref-selector__item c-commit-ref-selector__item--disabled svelte-jv14ji")},m(r,o){j(r,t,o),R(e,t,null),n=!0},p(r,o){const i={};8&o[2]&&(i.$$scope={dirty:o,ctx:r}),e.$set(i)},i(r){n||(f(e.$$.fragment,r),n=!0)},o(r){v(e.$$.fragment,r),n=!1},d(r){r&&b(t),k(e)}}}function Ln(s){let t,e,n;return e=new Y({props:{size:1,color:"neutral",$$slots:{default:[An]},$$scope:{ctx:s}}}),{c(){t=y("div"),L(e.$$.fragment),m(t,"class","c-commit-ref-selector__item c-commit-ref-selector__item--disabled svelte-jv14ji")},m(r,o){j(r,t,o),R(e,t,null),n=!0},p(r,o){const i={};8&o[2]&&(i.$$scope={dirty:o,ctx:r}),e.$set(i)},i(r){n||(f(e.$$.fragment,r),n=!0)},o(r){v(e.$$.fragment,r),n=!1},d(r){r&&b(t),k(e)}}}function Rn(s){let t,e,n=[],r=new Map,o=Bt(s[7]);const i=c=>{var l;return`${(l=c[2])==null?void 0:l.name}/${c[59].name}`};for(let c=0;c<o.length;c+=1){let l=me(s,o,c),a=i(l);r.set(a,n[c]=ge(a,l))}return{c(){for(let c=0;c<n.length;c+=1)n[c].c();t=Gt()},m(c,l){for(let a=0;a<n.length;a+=1)n[a]&&n[a].m(c,l);j(c,t,l),e=!0},p(c,l){1048716&l[0]&&(o=Bt(c[7]),et(),n=Qt(n,l,i,1,c,o,r,t.parentNode,te,ge,t,me),nt())},i(c){if(!e){for(let l=0;l<o.length;l+=1)f(n[l]);e=!0}},o(c){for(let l=0;l<n.length;l+=1)v(n[l]);e=!1},d(c){c&&b(t);for(let l=0;l<n.length;l+=1)n[l].d(c)}}}function kn(s){let t;return{c(){t=N("No branches found")},m(e,n){j(e,t,n)},d(e){e&&b(t)}}}function An(s){let t;return{c(){t=N("Loading...")},m(e,n){j(e,t,n)},d(e){e&&b(t)}}}function Sn(s){let t,e=s[59].name.replace("origin/","")+"";return{c(){t=N(e)},m(n,r){j(n,t,r)},p(n,r){128&r[0]&&e!==(e=n[59].name.replace("origin/","")+"")&&ft(t,e)},d(n){n&&b(t)}}}function ge(s,t){let e,n,r,o,i,c,l;function a(){return t[43](t[59])}return r=new Y({props:{size:2,color:"neutral",class:"c-commit-ref-selector__item-name",$$slots:{default:[Sn]},$$scope:{ctx:t}}}),{key:s,first:null,c(){var u;e=y("button"),n=y("div"),L(r.$$.fragment),o=E(),m(n,"class","c-commit-ref-selector__item-content svelte-jv14ji"),m(e,"type","button"),m(e,"class","c-commit-ref-selector__item svelte-jv14ji"),jt(e,"c-commit-ref-selector__item--active",((u=t[3])==null?void 0:u.name)===t[59].name),this.first=e},m(u,w){j(u,e,w),d(e,n),R(r,n,null),d(e,o),i=!0,c||(l=lt(e,"click",a),c=!0)},p(u,w){var g;t=u;const h={};128&w[0]|8&w[2]&&(h.$$scope={dirty:w,ctx:t}),r.$set(h),(!i||136&w[0])&&jt(e,"c-commit-ref-selector__item--active",((g=t[3])==null?void 0:g.name)===t[59].name)},i(u){i||(f(r.$$.fragment,u),i=!0)},o(u){v(r.$$.fragment,u),i=!1},d(u){u&&b(e),k(r),c=!1,l()}}}function zn(s){let t;return{c(){t=N("Branch")},m(e,n){j(e,t,n)},d(e){e&&b(t)}}}function En(s){let t;return{c(){t=N(s[13])},m(e,n){j(e,t,n)},p(e,n){8192&n[0]&&ft(t,e[13])},d(e){e&&b(t)}}}function Fn(s){let t,e,n,r,o,i,c,l,a,u;const w=[an,ln],h=[];function g(x,P){return x[5]!=="repo"?0:1}n=g(s),r=h[n]=w[n](s);const $=[_n,yn],p=[];function C(x,P){return x[5]!=="branch"?0:1}return l=C(s),a=p[l]=$[l](s),{c(){t=y("div"),e=y("div"),r.c(),o=E(),i=y("div"),c=E(),a.c(),m(i,"class","c-commit-ref-selector__separator svelte-jv14ji"),m(e,"class","c-commit-ref-selector__dropdown-container svelte-jv14ji"),m(t,"slot","dropdown-content"),m(t,"class","svelte-jv14ji")},m(x,P){j(x,t,P),d(t,e),h[n].m(e,null),d(e,o),d(e,i),d(e,c),p[l].m(e,null),u=!0},p(x,P){let U=n;n=g(x),n===U?h[n].p(x,P):(et(),v(h[U],1,1,()=>{h[U]=null}),nt(),r=h[n],r?r.p(x,P):(r=h[n]=w[n](x),r.c()),f(r,1),r.m(e,o));let B=l;l=C(x),l===B?p[l].p(x,P):(et(),v(p[B],1,1,()=>{p[B]=null}),nt(),a=p[l],a?a.p(x,P):(a=p[l]=$[l](x),a.c()),f(a,1),a.m(e,null))},i(x){u||(f(r),f(a),u=!0)},o(x){v(r),v(a),u=!1},d(x){x&&b(t),h[n].d(),p[l].d()}}}function Un(s){let t,e,n,r,o;const i=[tn,Qe],c=[];function l(a,u){return a[1]?a[1]?1:-1:0}return~(n=l(s))&&(r=c[n]=i[n](s)),{c(){t=y("div"),e=y("div"),r&&r.c(),m(e,"class","c-commit-ref-selector__content svelte-jv14ji"),m(t,"class","c-commit-ref-selector svelte-jv14ji")},m(a,u){j(a,t,u),d(t,e),~n&&c[n].m(e,null),o=!0},p(a,u){let w=n;n=l(a),n===w?~n&&c[n].p(a,u):(r&&(et(),v(c[w],1,1,()=>{c[w]=null}),nt()),~n?(r=c[n],r?r.p(a,u):(r=c[n]=i[n](a),r.c()),f(r,1),r.m(e,null)):r=null)},i(a){o||(f(r),o=!0)},o(a){v(r),o=!1},d(a){a&&b(t),~n&&c[n].d()}}}function Bn(s,t,e){let n,r,o,i;const c=zt(Pe.key),l=Zt();let a,u,{hasError:w=!1}=t,{isLoading:h=!1}=t,{lastUsedBranchName:g=null}=t,{lastUsedRepoUrl:$=null}=t,p="branch",C="",x=[],P=x,U=[],B=U,M=!1,W="",z=!1,Z="",G="",I=()=>{};const ot={noRemoteBranches:"No remote branches found. Remote agents require remote branches to work properly. Please push your current branch to remote.",failedToFetchBranches:"Failed to fetch branches. Please try again.",failedToParseRemoteUrl:"Failed to parse remote URL in your local git repo. Please check your remote URL and try again.",failedToFetchFromRemote:"Failed to fetch from remote. Please try again."};function q(S){e(5,p=S),e(4,z=!1),e(8,M=!1),A((u==null?void 0:u.name)??""),T((a==null?void 0:a.name)??""),S==="branch"?J():Q()}async function X(){const{repos:S,error:K,isDevDeploy:vt}=await c.listUserRepos();if(vt)return void await async function(){console.warn("Fetching branches from local git environment."),C=await c.getRemoteUrl(),e(0,h=!0);const H=ue(C);if(!H)return F(ot.failedToParseRemoteUrl),void e(0,h=!1);e(2,a={name:H.name,owner:H.owner,html_url:C}),x=[a],e(6,P=x);const mt=function(wt){const At=wt.find(pt=>pt.isCurrentBranch),St=wt.find(pt=>pt.isDefault),Mt=!!At&&(At==null?void 0:At.name)===(St==null?void 0:St.name.replace("origin/",""));return wt.filter(pt=>(!Mt||!pt.isDefault)&&(!pt.isCurrentBranch||!pt.isRemote)&&!!pt.isRemote&&pt.isRemote)}((await c.listBranches()).branches),ht=mt.find(wt=>wt.isDefault);e(3,u={name:ht!=null&&ht.name?$e(ht.name):mt[0].name,commit:{sha:"",url:""},protected:!1}),U=mt.map(wt=>({name:$e(wt.name),commit:{sha:"",url:""},protected:!1})),e(7,B=U),_(),w||V(),e(0,h=!1)}();if(K)return F(K),void e(0,h=!1);if(x=S,e(6,P=x),!await c.isGitRepository())return e(0,h=!1),void q("repo");if(!a&&$){const H=x.find(mt=>mt.html_url===$);H&&e(2,a=H)}C=await c.getRemoteUrl();const at=ue(C);if(!at)return e(0,h=!1),void q("repo");const{owner:gt,name:bt}=at,D=x.find(H=>H.name===bt&&H.owner===gt);if(D&&!a)e(2,a=D);else{if(D||!bt||!gt)return q("repo"),void e(0,h=!1);{const H={name:bt,owner:gt,html_url:C};try{const{repo:mt,error:ht}=await c.getGithubRepo(H);ht?(console.warn("Failed to fetch GitHub repo details:",ht),e(2,a=x[0])):(e(2,a=mt),x=[a,...x])}catch(mt){console.error("Error fetching GitHub repo:",mt),e(2,a=x[0])}}}q("branch")}async function tt(){e(0,h=!0),O();try{if(!await c.isGithubAuthenticated())return F("Please authenticate with GitHub to use this feature."),void e(0,h=!1);await X(),await async function(){if(!a)return void q("repo");const S=await c.listRepoBranches(a);if(S.error)return F(`Failed to fetch branches for the repo ${a.owner}/${a.name}. Please make sure you have access to this repo on GitHub.`),void e(0,h=!1);S.isDevDeploy||(U=S.branches,e(7,B=U),u||(g?e(3,u=U.find(K=>K.name===g)):a.default_branch?e(3,u=U.find(K=>K.name===(a==null?void 0:a.default_branch))):e(3,u=U[0])))}(),_(),w||V()}catch(S){console.error("Error fetching git data:",S),F(ot.failedToFetchBranches)}finally{e(0,h=!1)}}async function rt(){e(0,h=!0);try{await tt()}catch(S){console.error("Error fetching and syncing branches:",S),F("Failed to fetch from remote. Please try again.")}finally{e(0,h=!1)}}function _(){h||U.length!==0?O():F(ot.noRemoteBranches)}function F(S){e(1,w=!0),e(11,G=S)}function O(){e(1,w=!1),e(11,G="")}async function J(S=""){const K=S||Z;e(1,w=!1);try{e(7,B=z?(vt=K,U.filter(at=>at.name.startsWith(vt.toLowerCase()))):U),_()}catch(at){console.error("Error fetching branches:",at),e(7,B=[]),F(ot.failedToFetchBranches)}var vt}async function Q(S=""){const K=S||W;e(1,w=!1);try{e(6,P=M?(vt=K,x.filter(at=>at.name.startsWith(vt.toLowerCase()))):x)}catch(at){console.error("Error fetching repos:",at),e(6,P=[]),F(ot.failedToFetchFromRemote)}var vt}async function $t(S){e(3,u=S),V(),I()}async function it(S){e(2,a=S),e(3,u=void 0),U=[],e(7,B=[]),await rt(),q("branch"),V()}function V(){if(!(a!=null&&a.html_url)||!u)return;const S={github_commit_ref:{repository_url:a.html_url,git_ref:u.name}};l("commitRefChange",{commitRef:S,selectedBranch:u})}qt(async()=>{await rt()});const A=S=>{e(10,Z=S)},T=S=>{e(9,W=S)},ut=ie(Q,300,{leading:!1,trailing:!0}),dt=ie(J,300,{leading:!1,trailing:!0});return s.$$set=S=>{"hasError"in S&&e(1,w=S.hasError),"isLoading"in S&&e(0,h=S.isLoading),"lastUsedBranchName"in S&&e(27,g=S.lastUsedBranchName),"lastUsedRepoUrl"in S&&e(28,$=S.lastUsedRepoUrl)},s.$$.update=()=>{9&s.$$.dirty[0]&&e(29,n=()=>h?"...":(u==null?void 0:u.name)||"Select a branch"),536870912&s.$$.dirty[0]&&e(15,r=n()),4&s.$$.dirty[0]&&e(14,o=(a==null?void 0:a.name)??"Loading..."),8&s.$$.dirty[0]&&e(13,i=(u==null?void 0:u.name)??"Loading..."),24&s.$$.dirty[0]&&A(z?"":(u==null?void 0:u.name)??"")},[h,w,a,u,z,p,P,B,M,W,Z,G,I,i,o,r,q,rt,J,Q,$t,it,function(S){S||(e(8,M=!1),e(4,z=!1))},A,T,ut,dt,g,$,n,()=>{p!=="repo"&&q("repo")},S=>{S.key!=="Enter"&&S.key!==" "||p!=="repo"&&q("repo")},function(){W=this.value,e(9,W)},()=>{M||T(""),e(8,M=!0),Q()},()=>{e(8,M=!!W),Q()},()=>ut(),S=>{it(S)},()=>{p!=="branch"&&q("branch")},S=>{S.key!=="Enter"&&S.key!==" "||p!=="branch"&&q("branch")},function(){Z=this.value,e(10,Z)},()=>{z||A(""),e(4,z=!0),J()},()=>{e(4,z=!!Z),J()},()=>dt(),S=>{$t(S)},function(S){I=S,e(12,I)}]}class Pn extends Lt{constructor(t){super(),Rt(this,t,Bn,Un,kt,{hasError:1,isLoading:0,lastUsedBranchName:27,lastUsedRepoUrl:28},null,[-1,-1,-1])}}function Nn(s){let t,e,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},s[0]],r={};for(let o=0;o<n.length;o+=1)r=xt(r,n[o]);return{c(){t=st("svg"),e=new Vt(!0),this.h()},l(o){t=Wt(o,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var i=Xt(t);e=Yt(i,!0),i.forEach(b),this.h()},h(){e.a=null,Ft(t,r)},m(o,i){Jt(o,t,i),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M304 24c0 13.3 10.7 24 24 24h102.1L207 271c-9.4 9.4-9.4 24.6 0 33.9s24.6 9.4 33.9 0l223-223L464 184c0 13.3 10.7 24 24 24s24-10.7 24-24V24c0-13.3-10.7-24-24-24H328c-13.3 0-24 10.7-24 24M72 32C32.2 32 0 64.2 0 104v336c0 39.8 32.2 72 72 72h336c39.8 0 72-32.2 72-72V312c0-13.3-10.7-24-24-24s-24 10.7-24 24v128c0 13.3-10.7 24-24 24H72c-13.3 0-24-10.7-24-24V104c0-13.3 10.7-24 24-24h128c13.3 0 24-10.7 24-24s-10.7-24-24-24z"/>',t)},p(o,[i]){Ft(t,r=Kt(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&i&&o[0]]))},i:ct,o:ct,d(o){o&&b(t)}}}function Mn(s,t,e){return s.$$set=n=>{e(0,t=xt(xt({},t),Ut(n)))},[t=Ut(t)]}class Dn extends Lt{constructor(t){super(),Rt(this,t,Mn,Nn,kt,{})}}function Tn(s){let t,e,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},s[0]],r={};for(let o=0;o<n.length;o+=1)r=xt(r,n[o]);return{c(){t=st("svg"),e=new Vt(!0),this.h()},l(o){t=Wt(o,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var i=Xt(t);e=Yt(i,!0),i.forEach(b),this.h()},h(){e.a=null,Ft(t,r)},m(o,i){Jt(o,t,i),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="m36.4 360.9-23 78.1L1 481.2c-2.5 8.5-.2 17.6 6 23.8s15.3 8.5 23.7 6.1L73 498.6l78.1-23c12.4-3.6 23.7-9.9 33.4-18.4 1.4-1.2 2.7-2.5 4-3.8l304.2-304.1c21.9-21.9 24.6-55.6 8.2-80.5-2.3-3.5-5.1-6.9-8.2-10l-39.4-39.5c-25-25-65.5-25-90.5 0L58.6 323.5c-10.4 10.4-18 23.3-22.2 37.4m46 13.5c1.7-5.6 4.5-10.8 8.4-15.2.6-.6 1.1-1.2 1.7-1.8L321 129l62 62-228.4 228.5c-4.7 4.7-10.6 8.2-17 10.1l-23.4 6.9-54.8 16.1 16.1-54.8z"/>',t)},p(o,[i]){Ft(t,r=Kt(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&i&&o[0]]))},i:ct,o:ct,d(o){o&&b(t)}}}function Hn(s,t,e){return s.$$set=n=>{e(0,t=xt(xt({},t),Ut(n)))},[t=Ut(t)]}class On extends Lt{constructor(t){super(),Rt(this,t,Hn,Tn,kt,{})}}function Gn(s){let t,e,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},s[0]],r={};for(let o=0;o<n.length;o+=1)r=xt(r,n[o]);return{c(){t=st("svg"),e=new Vt(!0),this.h()},l(o){t=Wt(o,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var i=Xt(t);e=Yt(i,!0),i.forEach(b),this.h()},h(){e.a=null,Ft(t,r)},m(o,i){Jt(o,t,i),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M177.1 48h93.7c2.7 0 5.2 1.3 6.7 3.6l19 28.4h-145l19-28.4c1.5-2.2 4-3.6 6.7-3.6zm177.1 32-36.7-55.1C307.1 9.4 289.6 0 270.9 0h-93.8c-18.7 0-36.2 9.4-46.6 24.9L93.8 80H24C10.7 80 0 90.7 0 104s10.7 24 24 24h11.6l24 324.7c2.5 33.4 30.3 59.3 63.8 59.3h201.1c33.5 0 61.3-25.9 63.8-59.3L412.4 128H424c13.3 0 24-10.7 24-24s-10.7-24-24-24h-56.1zm10.1 48-23.8 321.2c-.6 8.4-7.6 14.8-16 14.8H123.4c-8.4 0-15.3-6.5-16-14.8L83.7 128z"/>',t)},p(o,[i]){Ft(t,r=Kt(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},1&i&&o[0]]))},i:ct,o:ct,d(o){o&&b(t)}}}function In(s,t,e){return s.$$set=n=>{e(0,t=xt(xt({},t),Ut(n)))},[t=Ut(t)]}class Zn extends Lt{constructor(t){super(),Rt(this,t,In,Gn,kt,{})}}const qn=s=>({}),ve=s=>({});function he(s){let t,e;const n=s[11].icon,r=Se(n,s,s[10],ve);return{c(){t=y("div"),r&&r.c(),m(t,"class","c-setup-script-selector__icon svelte-118zbly")},m(o,i){j(o,t,i),r&&r.m(t,null),e=!0},p(o,i){r&&r.p&&(!e||1024&i)&&ze(r,n,o,o[10],e?Fe(n,o[10],i,qn):Ee(o[10]),ve)},i(o){e||(f(r,o),e=!0)},o(o){v(r,o),e=!1},d(o){o&&b(t),r&&r.d(o)}}}function Vn(s){let t,e,n,r,o;return{c(){t=y("span"),e=N(s[0]),n=E(),r=y("span"),o=N(s[1]),m(t,"class","c-setup-script-selector__script-name svelte-118zbly"),m(r,"class","c-setup-script-selector__script-path svelte-118zbly")},m(i,c){j(i,t,c),d(t,e),j(i,n,c),j(i,r,c),d(r,o)},p(i,c){1&c&&ft(e,i[0]),2&c&&ft(o,i[1])},i:ct,o:ct,d(i){i&&(b(t),b(n),b(r))}}}function Wn(s){let t,e,n,r,o,i,c,l,a;function u($){s[14]($)}function w($){s[15]($)}let h={size:1,variant:"surface"};s[5]!==void 0&&(h.value=s[5]),s[4]!==void 0&&(h.textInput=s[4]),n=new Te({props:h}),Ct.push(()=>yt(n,"value",u)),Ct.push(()=>yt(n,"textInput",w)),n.$on("keydown",s[7]),n.$on("blur",s[8]);let g=s[6]&&function($){let p;return{c(){p=y("span"),p.textContent=`${$[6]}`,m(p,"class","c-setup-script-selector__extension svelte-118zbly")},m(C,x){j(C,p,x)},p:ct,d(C){C&&b(p)}}}(s);return{c(){t=y("div"),e=y("div"),L(n.$$.fragment),i=E(),g&&g.c(),m(e,"class","c-setup-script-selector__rename-input-container svelte-118zbly"),m(e,"role","presentation"),m(t,"class","c-setup-script-selector__rename-input svelte-118zbly"),m(t,"role","presentation")},m($,p){j($,t,p),d(t,e),R(n,e,null),d(e,i),g&&g.m(e,null),c=!0,l||(a=[lt(t,"click",oe(s[12])),lt(t,"mousedown",oe(s[13]))],l=!0)},p($,p){const C={};!r&&32&p&&(r=!0,C.value=$[5],_t(()=>r=!1)),!o&&16&p&&(o=!0,C.textInput=$[4],_t(()=>o=!1)),n.$set(C),$[6]&&g.p($,p)},i($){c||(f(n.$$.fragment,$),c=!0)},o($){v(n.$$.fragment,$),c=!1},d($){$&&b(t),k(n),g&&g.d(),l=!1,Nt(a)}}}function Xn(s){let t,e,n,r,o,i,c,l,a=s[9].icon&&he(s);const u=[Wn,Vn],w=[];function h(p,C){return p[3]?0:1}r=h(s),o=w[r]=u[r](s);const g=s[11].default,$=Se(g,s,s[10],null);return{c(){t=y("div"),a&&a.c(),e=E(),n=y("div"),o.c(),i=E(),c=y("div"),$&&$.c(),m(n,"class","c-setup-script-selector__script-info svelte-118zbly"),m(c,"class","c-setup-script-selector__script-actions svelte-118zbly"),m(t,"class","c-setup-script-selector__script-item-content svelte-118zbly"),m(t,"role","presentation"),jt(t,"c-setup-script-selector__script-item-content--renaming",s[3]),jt(t,"c-setup-script-selector__script-item-content--is-path",s[2])},m(p,C){j(p,t,C),a&&a.m(t,null),d(t,e),d(t,n),w[r].m(n,null),d(t,i),d(t,c),$&&$.m(c,null),l=!0},p(p,[C]){p[9].icon?a?(a.p(p,C),512&C&&f(a,1)):(a=he(p),a.c(),f(a,1),a.m(t,e)):a&&(et(),v(a,1,1,()=>{a=null}),nt());let x=r;r=h(p),r===x?w[r].p(p,C):(et(),v(w[x],1,1,()=>{w[x]=null}),nt(),o=w[r],o?o.p(p,C):(o=w[r]=u[r](p),o.c()),f(o,1),o.m(n,null)),$&&$.p&&(!l||1024&C)&&ze($,g,p,p[10],l?Fe(g,p[10],C,null):Ee(p[10]),null),(!l||8&C)&&jt(t,"c-setup-script-selector__script-item-content--renaming",p[3]),(!l||4&C)&&jt(t,"c-setup-script-selector__script-item-content--is-path",p[2])},i(p){l||(f(a),f(o),f($,p),l=!0)},o(p){v(a),v(o),v($,p),l=!1},d(p){p&&b(t),a&&a.d(),w[r].d(),$&&$.d(p)}}}function Yn(s,t,e){let{$$slots:n={},$$scope:r}=t;const o=Ie(n);let{name:i}=t,{path:c}=t,{isPath:l=!1}=t,{isRenaming:a=!1}=t;const u=Zt(),{baseName:w,extension:h}=function(p){const C=p.lastIndexOf(".");return C===-1?{baseName:p,extension:""}:{baseName:p.substring(0,C),extension:p.substring(C)}}(i);let g,$=w;return s.$$set=p=>{"name"in p&&e(0,i=p.name),"path"in p&&e(1,c=p.path),"isPath"in p&&e(2,l=p.isPath),"isRenaming"in p&&e(3,a=p.isRenaming),"$$scope"in p&&e(10,r=p.$$scope)},s.$$.update=()=>{24&s.$$.dirty&&a&&g&&setTimeout(()=>{g==null||g.focus(),g==null||g.select()},0)},[i,c,l,a,g,$,h,function(p){if(p.key!=="ArrowLeft"&&p.key!=="ArrowRight"&&p.key!=="ArrowUp"&&p.key!=="ArrowDown")if(p.key==="Enter")if(p.preventDefault(),$.trim()&&$!==w){const C=$.trim()+h;u("rename",{oldName:i,newName:C})}else u("cancelRename");else p.key==="Escape"&&(p.preventDefault(),p.stopPropagation(),u("cancelRename"));else p.stopPropagation()},function(){u("cancelRename")},o,r,n,function(p){se.call(this,s,p)},function(p){se.call(this,s,p)},function(p){$=p,e(5,$)},function(p){g=p,e(4,g)}]}class Ge extends Lt{constructor(t){super(),Rt(this,t,Yn,Xn,kt,{name:0,path:1,isPath:2,isRenaming:3})}}function we(s,t,e){const n=s.slice();return n[42]=t[e],n}function Ce(s){let t,e,n,r,o,i,c,l,a,u;function w(g){s[34](g)}let h={type:"dropdown",title:s[9],onDropdownOpenChange:s[22],$$slots:{"dropdown-content":[ur],iconLeft:[Qn]},$$scope:{ctx:s}};return s[7]!==void 0&&(h.requestClose=s[7]),n=new Ne({props:h}),Ct.push(()=>yt(n,"requestClose",w)),c=new Et({props:{content:"An AI agent will automatically generate a setup script for your project.",$$slots:{default:[fr]},$$scope:{ctx:s}}}),a=new Et({props:{content:"Open a new file for you to write a setup script that you can edit directly.",$$slots:{default:[hr]},$$scope:{ctx:s}}}),{c(){t=y("div"),e=y("div"),L(n.$$.fragment),o=E(),i=y("div"),L(c.$$.fragment),l=E(),L(a.$$.fragment),m(i,"class","c-setup-script-selector__action-buttons svelte-1y2vcsn"),m(e,"class","c-setup-script-selector__script-line svelte-1y2vcsn"),m(t,"class","c-setup-script-selector__script-line-container svelte-1y2vcsn")},m(g,$){j(g,t,$),d(t,e),R(n,e,null),d(e,o),d(e,i),R(c,i,null),d(i,l),R(a,i,null),u=!0},p(g,$){const p={};512&$[0]&&(p.title=g[9]),1342&$[0]|16384&$[1]&&(p.$$scope={dirty:$,ctx:g}),!r&&128&$[0]&&(r=!0,p.requestClose=g[7],_t(()=>r=!1)),n.$set(p);const C={};16384&$[1]&&(C.$$scope={dirty:$,ctx:g}),c.$set(C);const x={};16384&$[1]&&(x.$$scope={dirty:$,ctx:g}),a.$set(x)},i(g){u||(f(n.$$.fragment,g),f(c.$$.fragment,g),f(a.$$.fragment,g),u=!0)},o(g){v(n.$$.fragment,g),v(c.$$.fragment,g),v(a.$$.fragment,g),u=!1},d(g){g&&b(t),k(n),k(c),k(a)}}}function Jn(s){let t,e;return t=new Oe({}),{c(){L(t.$$.fragment)},m(n,r){R(t,n,r),e=!0},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){v(t.$$.fragment,n),e=!1},d(n){k(t,n)}}}function Kn(s){let t,e;return t=new Ae({props:{size:1,useCurrentColor:!0}}),{c(){L(t.$$.fragment)},m(n,r){R(t,n,r),e=!0},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){v(t.$$.fragment,n),e=!1},d(n){k(t,n)}}}function Qn(s){let t,e,n,r;const o=[Kn,Jn],i=[];function c(l,a){return l[1]?0:1}return e=c(s),n=i[e]=o[e](s),{c(){t=y("div"),n.c(),m(t,"slot","iconLeft"),m(t,"class","c-setup-script-selector__icon svelte-1y2vcsn")},m(l,a){j(l,t,a),i[e].m(t,null),r=!0},p(l,a){let u=e;e=c(l),e!==u&&(et(),v(i[u],1,1,()=>{i[u]=null}),nt(),n=i[e],n||(n=i[e]=o[e](l),n.c()),f(n,1),n.m(t,null))},i(l){r||(f(n),r=!0)},o(l){v(n),r=!1},d(l){l&&b(t),i[e].d()}}}function ye(s){let t,e,n;return e=new ee.Item({props:{onSelect:s[28],highlight:s[3]===null,$$slots:{default:[er]},$$scope:{ctx:s}}}),{c(){t=y("div"),L(e.$$.fragment),m(t,"class","c-setup-script-selector__create-option-container svelte-1y2vcsn")},m(r,o){j(r,t,o),R(e,t,null),n=!0},p(r,o){const i={};8&o[0]&&(i.highlight=r[3]===null),16384&o[1]&&(i.$$scope={dirty:o,ctx:r}),e.$set(i)},i(r){n||(f(e.$$.fragment,r),n=!0)},o(r){v(e.$$.fragment,r),n=!1},d(r){r&&b(t),k(e)}}}function tr(s){let t,e;return t=new Oe({props:{slot:"icon"}}),{c(){L(t.$$.fragment)},m(n,r){R(t,n,r),e=!0},p:ct,i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){v(t.$$.fragment,n),e=!1},d(n){k(t,n)}}}function er(s){let t,e;return t=new Ge({props:{name:"Use basic environment",path:"The agent will run in a default environment without custom dependencies.",$$slots:{icon:[tr]},$$scope:{ctx:s}}}),{c(){L(t.$$.fragment)},m(n,r){R(t,n,r),e=!0},p(n,r){const o={};16384&r[1]&&(o.$$scope={dirty:r,ctx:n}),t.$set(o)},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){v(t.$$.fragment,n),e=!1},d(n){k(t,n)}}}function _e(s){let t,e,n,r=[],o=new Map,i=!s[4]&&xe(),c=Bt(s[10]);const l=a=>a[42].path;for(let a=0;a<c.length;a+=1){let u=we(s,c,a),w=l(u);o.set(w,r[a]=be(w,u))}return{c(){i&&i.c(),t=E(),e=y("div");for(let a=0;a<r.length;a+=1)r[a].c();m(e,"class","c-setup-script-selector__scripts-container svelte-1y2vcsn")},m(a,u){i&&i.m(a,u),j(a,t,u),j(a,e,u);for(let w=0;w<r.length;w+=1)r[w]&&r[w].m(e,null);n=!0},p(a,u){a[4]?i&&(et(),v(i,1,1,()=>{i=null}),nt()):i?16&u[0]&&f(i,1):(i=xe(),i.c(),f(i,1),i.m(t.parentNode,t)),3083528&u[0]&&(c=Bt(a[10]),et(),r=Qt(r,u,l,1,a,c,o,e,te,be,null,we),nt())},i(a){if(!n){f(i);for(let u=0;u<c.length;u+=1)f(r[u]);n=!0}},o(a){v(i);for(let u=0;u<r.length;u+=1)v(r[u]);n=!1},d(a){a&&(b(t),b(e)),i&&i.d(a);for(let u=0;u<r.length;u+=1)r[u].d()}}}function xe(s){let t,e;return t=new ee.Separator({}),{c(){L(t.$$.fragment)},m(n,r){R(t,n,r),e=!0},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){v(t.$$.fragment,n),e=!1},d(n){k(t,n)}}}function nr(s){let t,e;return t=new Dn({}),{c(){L(t.$$.fragment)},m(n,r){R(t,n,r),e=!0},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){v(t.$$.fragment,n),e=!1},d(n){k(t,n)}}}function rr(s){let t,e;return t=new ne({props:{size:1,variant:"ghost-block",class:"c-setup-script-selector__action-button",$$slots:{default:[nr]},$$scope:{ctx:s}}}),t.$on("click",function(...n){return s[29](s[42],...n)}),{c(){L(t.$$.fragment)},m(n,r){R(t,n,r),e=!0},p(n,r){s=n;const o={};16384&r[1]&&(o.$$scope={dirty:r,ctx:s}),t.$set(o)},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){v(t.$$.fragment,n),e=!1},d(n){k(t,n)}}}function sr(s){let t,e;return t=new On({}),{c(){L(t.$$.fragment)},m(n,r){R(t,n,r),e=!0},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){v(t.$$.fragment,n),e=!1},d(n){k(t,n)}}}function or(s){let t,e;return t=new ne({props:{size:1,variant:"ghost-block",class:"c-setup-script-selector__action-button",$$slots:{default:[sr]},$$scope:{ctx:s}}}),t.$on("click",function(...n){return s[30](s[42],...n)}),{c(){L(t.$$.fragment)},m(n,r){R(t,n,r),e=!0},p(n,r){s=n;const o={};16384&r[1]&&(o.$$scope={dirty:r,ctx:s}),t.$set(o)},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){v(t.$$.fragment,n),e=!1},d(n){k(t,n)}}}function cr(s){let t,e;return t=new Zn({}),{c(){L(t.$$.fragment)},m(n,r){R(t,n,r),e=!0},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){v(t.$$.fragment,n),e=!1},d(n){k(t,n)}}}function ir(s){let t,e;return t=new ne({props:{size:1,variant:"ghost-block",class:"c-setup-script-selector__action-button",$$slots:{default:[cr]},$$scope:{ctx:s}}}),t.$on("click",function(...n){return s[31](s[42],...n)}),{c(){L(t.$$.fragment)},m(n,r){R(t,n,r),e=!0},p(n,r){s=n;const o={};16384&r[1]&&(o.$$scope={dirty:r,ctx:s}),t.$set(o)},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){v(t.$$.fragment,n),e=!1},d(n){k(t,n)}}}function lr(s){let t,e,n,r,o,i;return t=new Et({props:{content:"Open script in editor",$$slots:{default:[rr]},$$scope:{ctx:s}}}),n=new Et({props:{content:"Rename script",$$slots:{default:[or]},$$scope:{ctx:s}}}),o=new Et({props:{content:"Delete script",$$slots:{default:[ir]},$$scope:{ctx:s}}}),{c(){L(t.$$.fragment),e=E(),L(n.$$.fragment),r=E(),L(o.$$.fragment)},m(c,l){R(t,c,l),j(c,e,l),R(n,c,l),j(c,r,l),R(o,c,l),i=!0},p(c,l){const a={};1024&l[0]|16384&l[1]&&(a.$$scope={dirty:l,ctx:c}),t.$set(a);const u={};1024&l[0]|16384&l[1]&&(u.$$scope={dirty:l,ctx:c}),n.$set(u);const w={};1024&l[0]|16384&l[1]&&(w.$$scope={dirty:l,ctx:c}),o.$set(w)},i(c){i||(f(t.$$.fragment,c),f(n.$$.fragment,c),f(o.$$.fragment,c),i=!0)},o(c){v(t.$$.fragment,c),v(n.$$.fragment,c),v(o.$$.fragment,c),i=!1},d(c){c&&(b(e),b(r)),k(t,c),k(n,c),k(o,c)}}}function ar(s){var r;let t,e,n;return t=new Ge({props:{name:s[42].name,path:s[42].path,isPath:!0,isRenaming:((r=s[8])==null?void 0:r.path)===s[42].path,$$slots:{default:[lr]},$$scope:{ctx:s}}}),t.$on("rename",function(...o){return s[32](s[42],...o)}),t.$on("cancelRename",s[19]),{c(){L(t.$$.fragment),e=E()},m(o,i){R(t,o,i),j(o,e,i),n=!0},p(o,i){var l;s=o;const c={};1024&i[0]&&(c.name=s[42].name),1024&i[0]&&(c.path=s[42].path),1280&i[0]&&(c.isRenaming=((l=s[8])==null?void 0:l.path)===s[42].path),1024&i[0]|16384&i[1]&&(c.$$scope={dirty:i,ctx:s}),t.$set(c)},i(o){n||(f(t.$$.fragment,o),n=!0)},o(o){v(t.$$.fragment,o),n=!1},d(o){o&&b(e),k(t,o)}}}function be(s,t){var i;let e,n,r;function o(){return t[33](t[42])}return n=new ee.Item({props:{onSelect:o,highlight:((i=t[3])==null?void 0:i.path)===t[42].path,$$slots:{default:[ar]},$$scope:{ctx:t}}}),{key:s,first:null,c(){e=Gt(),L(n.$$.fragment),this.first=e},m(c,l){j(c,e,l),R(n,c,l),r=!0},p(c,l){var u;t=c;const a={};1280&l[0]&&(a.onSelect=o),1032&l[0]&&(a.highlight=((u=t[3])==null?void 0:u.path)===t[42].path),1280&l[0]|16384&l[1]&&(a.$$scope={dirty:l,ctx:t}),n.$set(a)},i(c){r||(f(n.$$.fragment,c),r=!0)},o(c){v(n.$$.fragment,c),r=!1},d(c){c&&b(e),k(n,c)}}}function ur(s){let t,e,n,r,o,i,c,l;function a($){s[25]($)}function u($){s[26]($)}let w={placeholder:"Search scripts",size:2};s[4]!==void 0&&(w.value=s[4]),s[5]!==void 0&&(w.textInput=s[5]),n=new Te({props:w}),Ct.push(()=>yt(n,"value",a)),Ct.push(()=>yt(n,"textInput",u)),n.$on("input",s[27]);let h=!s[4]&&ye(s),g=s[2].length>0&&_e(s);return{c(){t=y("div"),e=y("div"),L(n.$$.fragment),i=E(),h&&h.c(),c=E(),g&&g.c(),m(e,"class","c-setup-script-selector__search-field svelte-1y2vcsn"),m(t,"slot","dropdown-content")},m($,p){j($,t,p),d(t,e),R(n,e,null),d(t,i),h&&h.m(t,null),d(t,c),g&&g.m(t,null),l=!0},p($,p){const C={};!r&&16&p[0]&&(r=!0,C.value=$[4],_t(()=>r=!1)),!o&&32&p[0]&&(o=!0,C.textInput=$[5],_t(()=>o=!1)),n.$set(C),$[4]?h&&(et(),v(h,1,1,()=>{h=null}),nt()):h?(h.p($,p),16&p[0]&&f(h,1)):(h=ye($),h.c(),f(h,1),h.m(t,c)),$[2].length>0?g?(g.p($,p),4&p[0]&&f(g,1)):(g=_e($),g.c(),f(g,1),g.m(t,null)):g&&(et(),v(g,1,1,()=>{g=null}),nt())},i($){l||(f(n.$$.fragment,$),f(h),f(g),l=!0)},o($){v(n.$$.fragment,$),v(h),v(g),l=!1},d($){$&&b(t),k(n),h&&h.d(),g&&g.d()}}}function $r(s){let t;return{c(){t=N("Auto-generate a script")},m(e,n){j(e,t,n)},d(e){e&&b(t)}}}function mr(s){let t,e;return t=new Xe({props:{slot:"iconLeft"}}),{c(){L(t.$$.fragment)},m(n,r){R(t,n,r),e=!0},p:ct,i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){v(t.$$.fragment,n),e=!1},d(n){k(t,n)}}}function pr(s){let t,e;return t=new Me({props:{slot:"iconRight"}}),{c(){L(t.$$.fragment)},m(n,r){R(t,n,r),e=!0},p:ct,i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){v(t.$$.fragment,n),e=!1},d(n){k(t,n)}}}function fr(s){let t,e;return t=new Pt({props:{variant:"soft",color:"neutral",size:1,$$slots:{iconRight:[pr],iconLeft:[mr],default:[$r]},$$scope:{ctx:s}}}),t.$on("click",s[12]),{c(){L(t.$$.fragment)},m(n,r){R(t,n,r),e=!0},p(n,r){const o={};16384&r[1]&&(o.$$scope={dirty:r,ctx:n}),t.$set(o)},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){v(t.$$.fragment,n),e=!1},d(n){k(t,n)}}}function dr(s){let t;return{c(){t=N("Write a script by hand")},m(e,n){j(e,t,n)},d(e){e&&b(t)}}}function gr(s){let t,e;return t=new Ke({props:{slot:"iconLeft"}}),{c(){L(t.$$.fragment)},m(n,r){R(t,n,r),e=!0},p:ct,i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){v(t.$$.fragment,n),e=!1},d(n){k(t,n)}}}function vr(s){let t,e;return t=new Me({props:{slot:"iconRight"}}),{c(){L(t.$$.fragment)},m(n,r){R(t,n,r),e=!0},p:ct,i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){v(t.$$.fragment,n),e=!1},d(n){k(t,n)}}}function hr(s){let t,e;return t=new Pt({props:{variant:"soft",color:"neutral",size:1,highlight:!1,$$slots:{iconRight:[vr],iconLeft:[gr],default:[dr]},$$scope:{ctx:s}}}),t.$on("click",s[13]),{c(){L(t.$$.fragment)},m(n,r){R(t,n,r),e=!0},p(n,r){const o={};16384&r[1]&&(o.$$scope={dirty:r,ctx:n}),t.$set(o)},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){v(t.$$.fragment,n),e=!1},d(n){k(t,n)}}}function je(s){let t,e,n;return e=new It({props:{color:"warning",variant:"soft",size:2,$$slots:{default:[yr]},$$scope:{ctx:s}}}),{c(){t=y("div"),L(e.$$.fragment),m(t,"class","c-setup-script-selector__error svelte-1y2vcsn")},m(r,o){j(r,t,o),R(e,t,null),n=!0},p(r,o){const i={};66&o[0]|16384&o[1]&&(i.$$scope={dirty:o,ctx:r}),e.$set(i)},i(r){n||(f(e.$$.fragment,r),n=!0)},o(r){v(e.$$.fragment,r),n=!1},d(r){r&&b(t),k(e)}}}function wr(s){let t;return{c(){t=N("Refresh")},m(e,n){j(e,t,n)},d(e){e&&b(t)}}}function Cr(s){let t,e,n;return e=new Be({}),{c(){t=y("span"),L(e.$$.fragment),m(t,"slot","iconLeft")},m(r,o){j(r,t,o),R(e,t,null),n=!0},p:ct,i(r){n||(f(e.$$.fragment,r),n=!0)},o(r){v(e.$$.fragment,r),n=!1},d(r){r&&b(t),k(e)}}}function yr(s){let t,e,n,r,o,i;return o=new Pt({props:{variant:"ghost",color:"warning",size:1,loading:s[1],$$slots:{iconLeft:[Cr],default:[wr]},$$scope:{ctx:s}}}),o.$on("click",s[15]),{c(){t=y("div"),e=y("div"),n=N(s[6]),r=E(),L(o.$$.fragment),m(e,"class","c-setup-script-selector__error-message svelte-1y2vcsn"),m(t,"class","c-setup-script-selector__error-content svelte-1y2vcsn")},m(c,l){j(c,t,l),d(t,e),d(e,n),d(t,r),R(o,t,null),i=!0},p(c,l){(!i||64&l[0])&&ft(n,c[6]);const a={};2&l[0]&&(a.loading=c[1]),16384&l[1]&&(a.$$scope={dirty:l,ctx:c}),o.$set(a)},i(c){i||(f(o.$$.fragment,c),i=!0)},o(c){v(o.$$.fragment,c),i=!1},d(c){c&&b(t),k(o)}}}function _r(s){let t,e,n,r,o=(!s[0]||s[6]===s[14].noScriptsFound)&&Ce(s),i=s[0]&&s[6]!==s[14].noScriptsFound&&je(s);return{c(){t=y("div"),e=y("div"),o&&o.c(),n=E(),i&&i.c(),m(e,"class","c-setup-script-selector__content svelte-1y2vcsn"),m(t,"class","c-setup-script-selector svelte-1y2vcsn")},m(c,l){j(c,t,l),d(t,e),o&&o.m(e,null),d(e,n),i&&i.m(e,null),r=!0},p(c,l){c[0]&&c[6]!==c[14].noScriptsFound?o&&(et(),v(o,1,1,()=>{o=null}),nt()):o?(o.p(c,l),65&l[0]&&f(o,1)):(o=Ce(c),o.c(),f(o,1),o.m(e,n)),c[0]&&c[6]!==c[14].noScriptsFound?i?(i.p(c,l),65&l[0]&&f(i,1)):(i=je(c),i.c(),f(i,1),i.m(e,null)):i&&(et(),v(i,1,1,()=>{i=null}),nt())},i(c){r||(f(o),f(i),r=!0)},o(c){v(o),v(i),r=!1},d(c){c&&b(t),o&&o.d(),i&&i.d()}}}function xr(s,t,e){var rt;let n,r,o;const i=zt("chatModel").extensionClient,c=_=>{i.openFile({repoRoot:"",pathName:_.path,allowOutOfWorkspace:!0,openLocalUri:_.location==="home"})},l=zt(He.key),a=Zt();let u,{hasError:w=!1}=t,{isLoading:h=!1}=t,{lastUsedScriptPath:g=null}=t,$=[],p=((rt=l.newAgentDraft)==null?void 0:rt.setupScript)??null,C="",x="",P=()=>{},U=null;const B={noScriptsFound:"No setup scripts found. You can create one in ~/.augment/env/, <git root>/.augment/env/, or <workspace root>/.augment/env/.",failedToFetchScripts:"Failed to fetch setup scripts. Please try again."};async function M(){ot();try{if(e(2,$=await l.listSetupScripts()),g&&$.length>0){const _=$.find(F=>F.path===g);_&&(e(3,p=_),tt())}else g===null&&(e(3,p=null),tt());$.length===0?I(B.noScriptsFound):ot()}catch(_){console.error("Error fetching setup scripts:",_),I(B.failedToFetchScripts)}}async function W(_,F){F&&F.stopPropagation();try{const O=await l.deleteSetupScript(_.name,_.location);O.success?((p==null?void 0:p.path)===_.path&&X(null,!1),await M()):(console.error("Failed to delete script:",O.error),I(`Failed to delete script: ${O.error||"Unknown error"}`))}catch(O){console.error("Error deleting script:",O),I(`Error deleting script: ${O instanceof Error?O.message:String(O)}`)}}async function z(_,F){F&&F.stopPropagation(),e(8,U=_)}async function Z(_,F){const{oldName:O,newName:J}=F.detail;try{const Q=await l.renameSetupScript(O,J,_.location);if(Q.success){await M();const $t=$.find(it=>it.path===Q.path);$t&&X($t,!1)}else console.error("Failed to rename script:",Q.error),I(`Failed to rename script: ${Q.error||"Unknown error"}`)}catch(Q){console.error("Error renaming script:",Q),I(`Error renaming script: ${Q instanceof Error?Q.message:String(Q)}`)}finally{G()}}function G(){e(8,U=null)}function I(_){e(0,w=!0),e(6,x=_)}function ot(){e(0,w=!1),e(6,x="")}function q(_=""){const F=_||C;return F?$.filter(O=>O.name.toLowerCase().includes(F.toLowerCase())||O.path.toLowerCase().includes(F.toLowerCase())):$}async function X(_,F=!0){e(3,p=_),tt(),l.saveLastRemoteAgentSetup(null,null,(p==null?void 0:p.path)||null),F&&P()}function tt(){a("setupScriptChange",{script:p})}return qt(async()=>{var _;await M(),g===null?X(null,!1):(_=l.newAgentDraft)!=null&&_.setupScript&&!p&&X(l.newAgentDraft.setupScript)}),s.$$set=_=>{"hasError"in _&&e(0,w=_.hasError),"isLoading"in _&&e(1,h=_.isLoading),"lastUsedScriptPath"in _&&e(23,g=_.lastUsedScriptPath)},s.$$.update=()=>{20&s.$$.dirty[0]&&e(10,n=$.length>0?q(C):[]),10&s.$$.dirty[0]&&e(24,r=()=>h?"...":p?p.isGenerateOption?p.name:p.location==="home"?"~/.augment/env/"+p.name:p.path:"Use basic environment"),16777216&s.$$.dirty[0]&&e(9,o=r())},[w,h,$,p,C,u,x,P,U,o,n,c,async()=>{try{const _=l.newAgentDraft;_&&l.setNewAgentDraft({..._,isSetupScriptAgent:!0});const F=await l.createRemoteAgentFromDraft("SETUP_MODE");return F&&l.setCurrentAgent(F),F}catch(_){console.error("Failed to select setup script generation:",_)}},async()=>{try{const _="setup.sh",F=`#!/bin/bash

# Setup Script for Remote Agent Environment
#
# This script installs dependencies and configures the environment for your project.
# It runs with sudo privileges when needed.
#
# Examples:
# sudo apt-get update && sudo apt-get install -y package-name
# pip install package-name
# npm install -g package-name
# export ENV_VAR=value

# Add your commands below:

`,O=await l.saveSetupScript(_,F,"home");if(O.success&&O.path){await M();const J=$.find(Q=>Q.path===O.path);J&&(X(J,!1),c(J)),P()}else console.error("Failed to create manual setup script:",O.error),I(`Failed to create manual setup script: ${O.error||"Unknown error"}`)}catch(_){console.error("Error creating manual setup script:",_),I(`Error creating manual setup script: ${_ instanceof Error?_.message:String(_)}`)}},B,M,W,z,Z,G,q,X,function(_){_&&u&&(setTimeout(()=>{u==null||u.focus()},0),M())},g,r,function(_){C=_,e(4,C)},function(_){u=_,e(5,u)},()=>q(),()=>X(null,!0),(_,F)=>{F.stopPropagation(),c(_),X(_,!0)},(_,F)=>{F.stopPropagation(),z(_)},(_,F)=>{F.stopPropagation(),W(_)},(_,F)=>Z(_,F),_=>{(U==null?void 0:U.path)!==_.path&&X(_,!0)},function(_){P=_,e(7,P)}]}class br extends Lt{constructor(t){super(),Rt(this,t,xr,_r,kt,{hasError:0,isLoading:1,lastUsedScriptPath:23},null,[-1,-1])}}function jr(s){let t;return{c(){t=N("Hands-free parallel work")},m(e,n){j(e,t,n)},d(e){e&&b(t)}}}function Lr(s){let t;return{c(){t=N("Kick off agents to tackle tasks while you stay focused.")},m(e,n){j(e,t,n)},d(e){e&&b(t)}}}function Rr(s){let t;return{c(){t=N("Clean, isolated environments")},m(e,n){j(e,t,n)},d(e){e&&b(t)}}}function kr(s){let t;return{c(){t=N("Agents run remotely, so your local setup stays untouched.")},m(e,n){j(e,t,n)},d(e){e&&b(t)}}}function Ar(s){let t;return{c(){t=N("Customizable setup")},m(e,n){j(e,t,n)},d(e){e&&b(t)}}}function Sr(s){let t;return{c(){t=N("Match your local environment exactly—tests, servers, everything.")},m(e,n){j(e,t,n)},d(e){e&&b(t)}}}function zr(s){let t;return{c(){t=N("Always-on coding")},m(e,n){j(e,t,n)},d(e){e&&b(t)}}}function Er(s){let t;return{c(){t=N("Agents keep running, even after you shut your laptop.")},m(e,n){j(e,t,n)},d(e){e&&b(t)}}}function Fr(s){let t,e,n,r,o,i,c,l,a,u,w,h,g,$,p,C,x,P,U,B,M,W,z,Z,G,I,ot,q,X,tt,rt,_,F,O,J,Q,$t,it,V,A,T,ut,dt,S,K,vt,at,gt,bt;return c=new Y({props:{size:1,weight:"medium",$$slots:{default:[jr]},$$scope:{ctx:s}}}),u=new Y({props:{size:1,color:"secondary",$$slots:{default:[Lr]},$$scope:{ctx:s}}}),x=new Y({props:{size:1,weight:"medium",$$slots:{default:[Rr]},$$scope:{ctx:s}}}),B=new Y({props:{size:1,color:"secondary",$$slots:{default:[kr]},$$scope:{ctx:s}}}),J=new Y({props:{size:1,weight:"medium",$$slots:{default:[Ar]},$$scope:{ctx:s}}}),it=new Y({props:{size:1,color:"secondary",$$slots:{default:[Sr]},$$scope:{ctx:s}}}),K=new Y({props:{size:1,weight:"medium",$$slots:{default:[zr]},$$scope:{ctx:s}}}),gt=new Y({props:{size:1,color:"secondary",$$slots:{default:[Er]},$$scope:{ctx:s}}}),{c(){t=y("div"),e=y("div"),n=st("svg"),r=st("path"),o=E(),i=y("div"),L(c.$$.fragment),l=E(),a=y("div"),L(u.$$.fragment),w=E(),h=y("div"),g=st("svg"),$=st("path"),p=E(),C=y("div"),L(x.$$.fragment),P=E(),U=y("div"),L(B.$$.fragment),M=E(),W=y("div"),z=st("svg"),Z=st("path"),G=st("g"),I=st("path"),ot=st("path"),q=st("path"),X=st("path"),tt=st("path"),rt=st("path"),_=st("path"),F=E(),O=y("div"),L(J.$$.fragment),Q=E(),$t=y("div"),L(it.$$.fragment),V=E(),A=y("div"),T=st("svg"),ut=st("path"),dt=E(),S=y("div"),L(K.$$.fragment),vt=E(),at=y("div"),L(gt.$$.fragment),m(r,"fill-rule","evenodd"),m(r,"clip-rule","evenodd"),m(r,"d","M15.9693 2.46934C16.11 2.32889 16.3006 2.25 16.4993 2.25C16.6981 2.25 16.8887 2.32889 17.0293 2.46934L21.5293 6.96934C21.6698 7.10997 21.7487 7.30059 21.7487 7.49934C21.7487 7.69809 21.6698 7.88871 21.5293 8.02934L17.0293 12.5293C16.9607 12.603 16.8779 12.6621 16.7859 12.7031C16.6939 12.7441 16.5946 12.7662 16.4939 12.7679C16.3932 12.7697 16.2931 12.7512 16.1997 12.7135C16.1064 12.6757 16.0215 12.6196 15.9503 12.5484C15.8791 12.4772 15.8229 12.3923 15.7852 12.2989C15.7475 12.2055 15.729 12.1055 15.7307 12.0048C15.7325 11.9041 15.7546 11.8048 15.7956 11.7128C15.8366 11.6208 15.8957 11.538 15.9693 11.4693L19.1893 8.24934H7.49934C7.30043 8.24934 7.10966 8.17032 6.96901 8.02967C6.82836 7.88902 6.74934 7.69825 6.74934 7.49934C6.74934 7.30043 6.82836 7.10966 6.96901 6.96901C7.10966 6.82836 7.30043 6.74934 7.49934 6.74934H19.1893L15.9693 3.52934C15.8289 3.38871 15.75 3.19809 15.75 2.99934C15.75 2.80059 15.8289 2.60997 15.9693 2.46934ZM8.02934 11.4693C8.16979 11.61 8.24868 11.8006 8.24868 11.9993C8.24868 12.1981 8.16979 12.3887 8.02934 12.5293L4.80934 15.7493H16.4993C16.6983 15.7493 16.889 15.8284 17.0297 15.969C17.1703 16.1097 17.2493 16.3004 17.2493 16.4993C17.2493 16.6983 17.1703 16.889 17.0297 17.0297C16.889 17.1703 16.6983 17.2493 16.4993 17.2493H4.80934L8.02934 20.4693C8.10303 20.538 8.16213 20.6208 8.20312 20.7128C8.24411 20.8048 8.26616 20.9041 8.26793 21.0048C8.26971 21.1055 8.25118 21.2055 8.21346 21.2989C8.17574 21.3923 8.1196 21.4772 8.04838 21.5484C7.97716 21.6196 7.89233 21.6757 7.79894 21.7135C7.70555 21.7512 7.60552 21.7697 7.50482 21.7679C7.40411 21.7662 7.3048 21.7441 7.2128 21.7031C7.1208 21.6621 7.038 21.603 6.96934 21.5293L2.46934 17.0293C2.32889 16.8887 2.25 16.6981 2.25 16.4993C2.25 16.3006 2.32889 16.11 2.46934 15.9693L6.96934 11.4693C7.10997 11.3289 7.30059 11.25 7.49934 11.25C7.69809 11.25 7.88871 11.3289 8.02934 11.4693Z"),m(r,"fill","currentColor"),m(n,"viewBox","0 0 24 24"),m(n,"fill","none"),m(n,"class","feature-icon svelte-1i4lpix"),m(a,"class","description svelte-1i4lpix"),m(e,"class","feature-item svelte-1i4lpix"),m($,"d","M12.001 16.4994V9.74943M12.001 9.74943L15.001 12.7494M12.001 9.74943L9.00099 12.7494M6.75099 19.4994C5.68042 19.5006 4.64451 19.12 3.82928 18.4261C3.01406 17.7322 2.47294 16.7704 2.3031 15.7134C2.13325 14.6563 2.34581 13.5734 2.90261 12.659C3.4594 11.7446 4.32395 11.0587 5.34099 10.7244C5.07969 9.3856 5.34974 7.99764 6.09397 6.85445C6.8382 5.71127 7.99815 4.90263 9.3282 4.59979C10.6583 4.29694 12.054 4.52367 13.2197 5.23195C14.3855 5.94023 15.2299 7.07446 15.574 8.39443C16.106 8.22141 16.6758 8.20059 17.219 8.33433C17.7622 8.46807 18.2572 8.75104 18.6481 9.15127C19.039 9.55151 19.3101 10.0531 19.431 10.5993C19.5518 11.1455 19.5176 11.7147 19.332 12.2424C20.1506 12.5551 20.834 13.1445 21.2635 13.9084C21.6931 14.6722 21.8417 15.5623 21.6836 16.4243C21.5255 17.2863 21.0707 18.0657 20.398 18.6274C19.7254 19.1891 18.8773 19.4976 18.001 19.4994H6.75099Z"),m($,"stroke","currentColor"),m($,"stroke-width","1.5"),m($,"stroke-linecap","round"),m($,"stroke-linejoin","round"),m(g,"viewBox","0 0 24 24"),m(g,"fill","none"),m(g,"class","feature-icon svelte-1i4lpix"),m(U,"class","description svelte-1i4lpix"),m(h,"class","feature-item svelte-1i4lpix"),m(Z,"d","M24 0V24H0V0H24Z"),m(Z,"fill","white"),m(Z,"fill-opacity","0.01"),m(I,"fill-rule","evenodd"),m(I,"clip-rule","evenodd"),m(I,"d","M13.4371 3.09488C13.417 3.13536 13.3968 3.17584 13.3752 3.21565C13.1103 3.7048 12.6929 4.11541 12.1733 4.39824C12.1415 4.41554 12.1093 4.43237 12.0768 4.4487C11.9746 4.49999 11.9745 4.49999 12.0768 4.55132C12.1093 4.56765 12.1415 4.58447 12.1733 4.60177C12.6929 4.88461 13.1103 5.29522 13.3752 5.78436C13.3968 5.82423 13.417 5.86467 13.4371 5.90513C13.5001 6.03161 13.5001 6.03163 13.5631 5.90513C13.5833 5.86467 13.6034 5.82423 13.625 5.78436C13.89 5.29522 14.3074 4.88461 14.827 4.60177C14.8588 4.58447 14.8909 4.56765 14.9235 4.55132C15.0257 4.49999 15.0256 4.49999 14.9235 4.4487C14.8909 4.43237 14.8588 4.41554 14.827 4.39824C14.3074 4.11541 13.89 3.7048 13.625 3.21565C13.6034 3.17578 13.5833 3.13534 13.5631 3.09488C13.5001 2.96836 13.5001 2.96839 13.4371 3.09488Z"),m(I,"fill","currentColor"),m(ot,"fill-rule","evenodd"),m(ot,"clip-rule","evenodd"),m(ot,"d","M17.9581 8.06325C17.9447 8.09024 17.9312 8.11723 17.9168 8.14377C17.7402 8.46987 17.462 8.7436 17.1155 8.93216C17.0944 8.9437 17.0729 8.95491 17.0512 8.9658C16.9831 8.99999 16.983 8.99999 17.0512 9.03421C17.0729 9.0451 17.0944 9.05631 17.1155 9.06785C17.462 9.25641 17.7402 9.53014 17.9168 9.85624C17.9312 9.88282 17.9447 9.90978 17.9581 9.93676C18.0001 10.0211 18.0001 10.0211 18.0421 9.93676C18.0556 9.90978 18.069 9.88282 18.0834 9.85624C18.26 9.53014 18.5383 9.25641 18.8847 9.06785C18.9059 9.05631 18.9273 9.0451 18.949 9.03421C19.0172 8.99999 19.0171 8.99999 18.949 8.9658C18.9273 8.95491 18.9059 8.9437 18.8847 8.93216C18.5383 8.7436 18.26 8.46987 18.0834 8.14377C18.069 8.11719 18.0556 8.09023 18.0421 8.06325C18.0001 7.97891 18.0001 7.97892 17.9581 8.06325Z"),m(ot,"fill","currentColor"),m(q,"fill-rule","evenodd"),m(q,"clip-rule","evenodd"),m(q,"d","M4.90691 12.6581C4.87709 12.7256 4.8472 12.7931 4.81531 12.8594C4.42339 13.6747 3.80599 14.359 3.03737 14.8304C2.99035 14.8592 2.94277 14.8873 2.89465 14.9145C2.74353 15 2.74337 15 2.89465 15.0855C2.94277 15.1128 2.99035 15.1408 3.03737 15.1696C3.80599 15.641 4.42339 16.3254 4.81531 17.1406C4.84725 17.2071 4.8771 17.2745 4.90691 17.3419C5.0001 17.5527 5.0001 17.5527 5.09329 17.3419C5.1231 17.2745 5.15294 17.2071 5.18489 17.1406C5.57681 16.3254 6.19421 15.641 6.96283 15.1696C7.00985 15.1408 7.05742 15.1128 7.10555 15.0855C7.2568 15 7.25671 15 7.10555 14.9145C7.05742 14.8873 7.00985 14.8592 6.96283 14.8304C6.1942 14.359 5.57681 13.6747 5.18489 12.8594C5.15294 12.793 5.1231 12.7256 5.09329 12.6581C5.0001 12.4473 5.0001 12.4473 4.90691 12.6581Z"),m(q,"fill","currentColor"),m(X,"fill-rule","evenodd"),m(X,"clip-rule","evenodd"),m(X,"d","M13.4371 3.09488C13.417 3.13536 13.3968 3.17584 13.3752 3.21565C13.1103 3.7048 12.6929 4.11541 12.1733 4.39824C12.1415 4.41554 12.1093 4.43237 12.0768 4.4487C11.9746 4.49999 11.9745 4.49999 12.0768 4.55132C12.1093 4.56765 12.1415 4.58447 12.1733 4.60177C12.6929 4.88461 13.1103 5.29522 13.3752 5.78436C13.3968 5.82423 13.417 5.86467 13.4371 5.90513C13.5001 6.03161 13.5001 6.03163 13.5631 5.90513C13.5833 5.86467 13.6034 5.82423 13.625 5.78436C13.89 5.29522 14.3074 4.88461 14.827 4.60177C14.8588 4.58447 14.8909 4.56765 14.9235 4.55132C15.0257 4.49999 15.0256 4.49999 14.9235 4.4487C14.8909 4.43237 14.8588 4.41554 14.827 4.39824C14.3074 4.11541 13.89 3.7048 13.625 3.21565C13.6034 3.17578 13.5833 3.13534 13.5631 3.09488C13.5001 2.96836 13.5001 2.96839 13.4371 3.09488Z"),m(X,"stroke","currentColor"),m(X,"stroke-width","1.5"),m(tt,"fill-rule","evenodd"),m(tt,"clip-rule","evenodd"),m(tt,"d","M17.9581 8.06325C17.9447 8.09024 17.9312 8.11723 17.9168 8.14377C17.7402 8.46987 17.462 8.7436 17.1155 8.93216C17.0944 8.9437 17.0729 8.95491 17.0512 8.9658C16.9831 8.99999 16.983 8.99999 17.0512 9.03421C17.0729 9.0451 17.0944 9.05631 17.1155 9.06785C17.462 9.25641 17.7402 9.53014 17.9168 9.85624C17.9312 9.88282 17.9447 9.90978 17.9581 9.93676C18.0001 10.0211 18.0001 10.0211 18.0421 9.93676C18.0556 9.90978 18.069 9.88282 18.0834 9.85624C18.26 9.53014 18.5383 9.25641 18.8847 9.06785C18.9059 9.05631 18.9273 9.0451 18.949 9.03421C19.0172 8.99999 19.0171 8.99999 18.949 8.9658C18.9273 8.95491 18.9059 8.9437 18.8847 8.93216C18.5383 8.7436 18.26 8.46987 18.0834 8.14377C18.069 8.11719 18.0556 8.09023 18.0421 8.06325C18.0001 7.97891 18.0001 7.97892 17.9581 8.06325Z"),m(tt,"stroke","currentColor"),m(tt,"stroke-width","1.5"),m(rt,"fill-rule","evenodd"),m(rt,"clip-rule","evenodd"),m(rt,"d","M4.90691 12.6581C4.87709 12.7256 4.8472 12.7931 4.81531 12.8594C4.42339 13.6747 3.80599 14.359 3.03737 14.8304C2.99035 14.8592 2.94277 14.8873 2.89465 14.9145C2.74353 15 2.74337 15 2.89465 15.0855C2.94277 15.1128 2.99035 15.1408 3.03737 15.1696C3.80599 15.641 4.42339 16.3254 4.81531 17.1406C4.84725 17.2071 4.8771 17.2745 4.90691 17.3419C5.0001 17.5527 5.0001 17.5527 5.09329 17.3419C5.1231 17.2745 5.15294 17.2071 5.18489 17.1406C5.57681 16.3254 6.19421 15.641 6.96283 15.1696C7.00985 15.1408 7.05742 15.1128 7.10555 15.0855C7.2568 15 7.25671 15 7.10555 14.9145C7.05742 14.8873 7.00985 14.8592 6.96283 14.8304C6.1942 14.359 5.57681 13.6747 5.18489 12.8594C5.15294 12.793 5.1231 12.7256 5.09329 12.6581C5.0001 12.4473 5.0001 12.4473 4.90691 12.6581Z"),m(rt,"stroke","currentColor"),m(rt,"stroke-width","1.5"),m(G,"opacity","0.3"),m(_,"d","M7.75818 10.5867L10.5866 7.7583M17.6577 19.0719L19.0719 17.6577C19.4624 17.2672 19.4624 16.634 19.0719 16.2435L7.75819 4.92976C7.36767 4.53924 6.7345 4.53924 6.34398 4.92976L4.92976 6.34398C4.53924 6.7345 4.53924 7.36767 4.92976 7.75819L16.2435 19.0719C16.634 19.4624 17.2672 19.4624 17.6577 19.0719Z"),m(_,"stroke","currentColor"),m(_,"stroke-width","1.5"),m(z,"viewBox","0 0 24 24"),m(z,"fill","none"),m(z,"class","feature-icon svelte-1i4lpix"),m($t,"class","description svelte-1i4lpix"),m(W,"class","feature-item svelte-1i4lpix"),m(ut,"d","M3.75 13.5L14.25 2.25L12 10.5H20.25L9.75 21.75L12 13.5H3.75Z"),m(ut,"stroke","currentColor"),m(ut,"stroke-width","1.5"),m(ut,"stroke-linecap","round"),m(ut,"stroke-linejoin","round"),m(T,"viewBox","0 0 24 24"),m(T,"fill","none"),m(T,"class","feature-icon svelte-1i4lpix"),m(at,"class","description svelte-1i4lpix"),m(A,"class","feature-item svelte-1i4lpix"),m(t,"class","feature-grid svelte-1i4lpix")},m(D,H){j(D,t,H),d(t,e),d(e,n),d(n,r),d(e,o),d(e,i),R(c,i,null),d(e,l),d(e,a),R(u,a,null),d(t,w),d(t,h),d(h,g),d(g,$),d(h,p),d(h,C),R(x,C,null),d(h,P),d(h,U),R(B,U,null),d(t,M),d(t,W),d(W,z),d(z,Z),d(z,G),d(G,I),d(G,ot),d(G,q),d(G,X),d(G,tt),d(G,rt),d(z,_),d(W,F),d(W,O),R(J,O,null),d(W,Q),d(W,$t),R(it,$t,null),d(t,V),d(t,A),d(A,T),d(T,ut),d(A,dt),d(A,S),R(K,S,null),d(A,vt),d(A,at),R(gt,at,null),bt=!0},p(D,[H]){const mt={};1&H&&(mt.$$scope={dirty:H,ctx:D}),c.$set(mt);const ht={};1&H&&(ht.$$scope={dirty:H,ctx:D}),u.$set(ht);const wt={};1&H&&(wt.$$scope={dirty:H,ctx:D}),x.$set(wt);const At={};1&H&&(At.$$scope={dirty:H,ctx:D}),B.$set(At);const St={};1&H&&(St.$$scope={dirty:H,ctx:D}),J.$set(St);const Mt={};1&H&&(Mt.$$scope={dirty:H,ctx:D}),it.$set(Mt);const pt={};1&H&&(pt.$$scope={dirty:H,ctx:D}),K.$set(pt);const re={};1&H&&(re.$$scope={dirty:H,ctx:D}),gt.$set(re)},i(D){bt||(f(c.$$.fragment,D),f(u.$$.fragment,D),f(x.$$.fragment,D),f(B.$$.fragment,D),f(J.$$.fragment,D),f(it.$$.fragment,D),f(K.$$.fragment,D),f(gt.$$.fragment,D),bt=!0)},o(D){v(c.$$.fragment,D),v(u.$$.fragment,D),v(x.$$.fragment,D),v(B.$$.fragment,D),v(J.$$.fragment,D),v(it.$$.fragment,D),v(K.$$.fragment,D),v(gt.$$.fragment,D),bt=!1},d(D){D&&b(t),k(c),k(u),k(x),k(B),k(J),k(it),k(K),k(gt)}}}class Ur extends Lt{constructor(t){super(),Rt(this,t,null,Fr,kt,{})}}function Br(s){let t;return{c(){t=N("Let's get a remote agent on it")},m(e,n){j(e,t,n)},d(e){e&&b(t)}}}function Le(s){let t,e,n,r;return e=new It({props:{color:"error",variant:"soft",size:2,$$slots:{default:[Pr]},$$scope:{ctx:s}}}),{c(){t=y("div"),L(e.$$.fragment),m(t,"class","error-message svelte-9sa3di")},m(o,i){j(o,t,i),R(e,t,null),r=!0},p(o,i){const c={};268435460&i&&(c.$$scope={dirty:i,ctx:o}),e.$set(c)},i(o){r||(f(e.$$.fragment,o),o&&Ue(()=>{r&&(n||(n=Tt(t,Ht,{y:10},!0)),n.run(1))}),r=!0)},o(o){v(e.$$.fragment,o),o&&(n||(n=Tt(t,Ht,{y:10},!1)),n.run(0)),r=!1},d(o){o&&b(t),k(e),o&&n&&n.end()}}}function Pr(s){let t,e=s[2].remoteAgentCreationError+"";return{c(){t=N(e)},m(n,r){j(n,t,r)},p(n,r){4&r&&e!==(e=n[2].remoteAgentCreationError+"")&&ft(t,e)},d(n){n&&b(t)}}}function Re(s){let t,e,n,r;return e=new It({props:{color:"info",variant:"soft",size:2,$$slots:{icon:[Tr],default:[Dr]},$$scope:{ctx:s}}}),{c(){t=y("div"),L(e.$$.fragment),m(t,"class","error-message svelte-9sa3di")},m(o,i){j(o,t,i),R(e,t,null),r=!0},p(o,i){const c={};268435462&i&&(c.$$scope={dirty:i,ctx:o}),e.$set(c)},i(o){r||(f(e.$$.fragment,o),o&&Ue(()=>{r&&(n||(n=Tt(t,Ht,{y:10},!0)),n.run(1))}),r=!0)},o(o){v(e.$$.fragment,o),o&&(n||(n=Tt(t,Ht,{y:10},!1)),n.run(0)),r=!1},d(o){o&&b(t),k(e),o&&n&&n.end()}}}function ke(s){let t,e;return{c(){t=N(": "),e=N(s[1])},m(n,r){j(n,t,r),j(n,e,r)},p(n,r){2&r&&ft(e,n[1])},d(n){n&&(b(t),b(e))}}}function Nr(s){let t,e,n=s[1]&&ke(s);return{c(){t=N("Delete Oldest Agent"),n&&n.c(),e=Gt()},m(r,o){j(r,t,o),n&&n.m(r,o),j(r,e,o)},p(r,o){r[1]?n?n.p(r,o):(n=ke(r),n.c(),n.m(e.parentNode,e)):n&&(n.d(1),n=null)},d(r){r&&(b(t),b(e)),n&&n.d(r)}}}function Mr(s){let t,e;return t=new Ve({props:{slot:"iconLeft"}}),{c(){L(t.$$.fragment)},m(n,r){R(t,n,r),e=!0},p:ct,i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){v(t.$$.fragment,n),e=!1},d(n){k(t,n)}}}function Dr(s){let t,e,n,r,o,i,c=Ot.replace("%MAX_AGENTS%",s[2].maxRemoteAgents.toString())+"";return o=new Pt({props:{variant:"soft",color:"neutral",size:1,$$slots:{iconLeft:[Mr],default:[Nr]},$$scope:{ctx:s}}}),o.$on("click",s[14]),{c(){t=y("div"),e=y("p"),n=N(c),r=E(),L(o.$$.fragment),m(e,"class","svelte-9sa3di"),m(t,"class","agent-limit-message svelte-9sa3di")},m(l,a){j(l,t,a),d(t,e),d(e,n),d(t,r),R(o,t,null),i=!0},p(l,a){(!i||4&a)&&c!==(c=Ot.replace("%MAX_AGENTS%",l[2].maxRemoteAgents.toString())+"")&&ft(n,c);const u={};268435458&a&&(u.$$scope={dirty:a,ctx:l}),o.$set(u)},i(l){i||(f(o.$$.fragment,l),i=!0)},o(l){v(o.$$.fragment,l),i=!1},d(l){l&&b(t),k(o)}}}function Tr(s){let t,e;return t=new qe({props:{slot:"icon"}}),{c(){L(t.$$.fragment)},m(n,r){R(t,n,r),e=!0},p:ct,i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){v(t.$$.fragment,n),e=!1},d(n){k(t,n)}}}function Hr(s){let t;return{c(){t=N("Create agent")},m(e,n){j(e,t,n)},d(e){e&&b(t)}}}function Or(s){let t,e;return t=new Pt({props:{variant:"solid",color:"accent",size:2,disabled:s[3]||s[8]||s[4]||s[9]||s[0],$$slots:{default:[Hr]},$$scope:{ctx:s}}}),t.$on("click",s[15]),{c(){L(t.$$.fragment)},m(n,r){R(t,n,r),e=!0},p(n,r){const o={};793&r&&(o.disabled=n[3]||n[8]||n[4]||n[9]||n[0]),268435456&r&&(o.$$scope={dirty:r,ctx:n}),t.$set(o)},i(n){e||(f(t.$$.fragment,n),e=!0)},o(n){v(t.$$.fragment,n),e=!1},d(n){k(t,n)}}}function Gr(s){let t,e,n,r,o,i,c,l,a,u,w,h,g,$,p,C,x,P,U,B,M,W,z,Z,G,I,ot,q,X,tt,rt;r=new Y({props:{size:3,weight:"bold",$$slots:{default:[Br]},$$scope:{ctx:s}}}),c=new Ur({});let _=s[2].remoteAgentCreationError&&Le(s);function F(A){s[16](A)}function O(A){s[17](A)}let J={lastUsedRepoUrl:s[5],lastUsedBranchName:s[6]};function Q(A){s[18](A)}function $t(A){s[19](A)}s[3]!==void 0&&(J.hasError=s[3]),s[4]!==void 0&&(J.isLoading=s[4]),$=new Pn({props:J}),Ct.push(()=>yt($,"hasError",F)),Ct.push(()=>yt($,"isLoading",O)),$.$on("commitRefChange",s[12]);let it={lastUsedScriptPath:s[7]};s[8]!==void 0&&(it.hasError=s[8]),s[9]!==void 0&&(it.isLoading=s[9]),M=new br({props:it}),Ct.push(()=>yt(M,"hasError",Q)),Ct.push(()=>yt(M,"isLoading",$t)),M.$on("setupScriptChange",s[13]),I=new Ye({props:{editable:!0,hasToggleModeButton:!1,hasSendButton:!1}});let V=s[0]&&Re(s);return tt=new Et({props:{class:"full-width-button",content:s[3]||s[8]||s[4]||s[9]||s[0]?s[0]?Ot.replace("%MAX_AGENTS%",s[2].maxRemoteAgents.toString()):s[3]?"Please fix repository selection errors":s[8]?"Please fix setup script errors":"Loading...":"",triggerOn:s[3]||s[8]||s[4]||s[9]||s[0]?[le.Hover]:[],$$slots:{default:[Or]},$$scope:{ctx:s}}}),{c(){t=y("div"),e=y("div"),n=y("div"),L(r.$$.fragment),o=E(),i=y("div"),L(c.$$.fragment),l=E(),a=y("div"),_&&_.c(),u=E(),w=y("div"),w.textContent="Start from any repo and branch:",h=E(),g=y("div"),L($.$$.fragment),x=E(),P=y("div"),P.textContent="Select a setup script to prepare the agent's environment:",U=E(),B=y("div"),L(M.$$.fragment),Z=E(),G=y("div"),L(I.$$.fragment),ot=E(),V&&V.c(),q=E(),X=y("div"),L(tt.$$.fragment),m(n,"class","header svelte-9sa3di"),m(i,"class","feature-grid svelte-9sa3di"),m(w,"class","description svelte-9sa3di"),m(g,"class","commit-ref-selector svelte-9sa3di"),m(P,"class","description svelte-9sa3di"),m(B,"class","setup-script svelte-9sa3di"),m(G,"class","chat svelte-9sa3di"),m(X,"class","create-button svelte-9sa3di"),m(a,"class","form-fields"),m(e,"class","content svelte-9sa3di"),m(t,"class","remote-agent-setup svelte-9sa3di")},m(A,T){j(A,t,T),d(t,e),d(e,n),R(r,n,null),d(e,o),d(e,i),R(c,i,null),d(e,l),d(e,a),_&&_.m(a,null),d(a,u),d(a,w),d(a,h),d(a,g),R($,g,null),d(a,x),d(a,P),d(a,U),d(a,B),R(M,B,null),d(a,Z),d(a,G),R(I,G,null),d(a,ot),V&&V.m(a,null),d(a,q),d(a,X),R(tt,X,null),rt=!0},p(A,[T]){const ut={};268435456&T&&(ut.$$scope={dirty:T,ctx:A}),r.$set(ut),A[2].remoteAgentCreationError?_?(_.p(A,T),4&T&&f(_,1)):(_=Le(A),_.c(),f(_,1),_.m(a,u)):_&&(et(),v(_,1,1,()=>{_=null}),nt());const dt={};32&T&&(dt.lastUsedRepoUrl=A[5]),64&T&&(dt.lastUsedBranchName=A[6]),!p&&8&T&&(p=!0,dt.hasError=A[3],_t(()=>p=!1)),!C&&16&T&&(C=!0,dt.isLoading=A[4],_t(()=>C=!1)),$.$set(dt);const S={};128&T&&(S.lastUsedScriptPath=A[7]),!W&&256&T&&(W=!0,S.hasError=A[8],_t(()=>W=!1)),!z&&512&T&&(z=!0,S.isLoading=A[9],_t(()=>z=!1)),M.$set(S),A[0]?V?(V.p(A,T),1&T&&f(V,1)):(V=Re(A),V.c(),f(V,1),V.m(a,q)):V&&(et(),v(V,1,1,()=>{V=null}),nt());const K={};797&T&&(K.content=A[3]||A[8]||A[4]||A[9]||A[0]?A[0]?Ot.replace("%MAX_AGENTS%",A[2].maxRemoteAgents.toString()):A[3]?"Please fix repository selection errors":A[8]?"Please fix setup script errors":"Loading...":""),793&T&&(K.triggerOn=A[3]||A[8]||A[4]||A[9]||A[0]?[le.Hover]:[]),268436249&T&&(K.$$scope={dirty:T,ctx:A}),tt.$set(K)},i(A){rt||(f(r.$$.fragment,A),f(c.$$.fragment,A),f(_),f($.$$.fragment,A),f(M.$$.fragment,A),f(I.$$.fragment,A),f(V),f(tt.$$.fragment,A),rt=!0)},o(A){v(r.$$.fragment,A),v(c.$$.fragment,A),v(_),v($.$$.fragment,A),v(M.$$.fragment,A),v(I.$$.fragment,A),v(V),v(tt.$$.fragment,A),rt=!1},d(A){A&&b(t),k(r),k(c),_&&_.d(),k($),k(M),k(I),V&&V.d(),k(tt)}}}function Ir(s,t,e){let n,r;const o=zt(He.key);ce(s,o,z=>e(2,r=z));const i=zt("chatModel");ce(s,i,z=>e(25,n=z));const c=zt(Pe.key);let l,a,u=null,w=!1,h=!1,g=!1,$=!1,p="",C=null,x=null,P=null;qt(async()=>{try{const z=await o.getLastRemoteAgentSetup();e(5,C=z.lastRemoteAgentGitRepoUrl),e(6,x=z.lastRemoteAgentGitBranch),e(7,P=z.lastRemoteAgentSetupScript)}catch(z){console.error("Failed to load last remote agent setup:",z)}});let U=!1,B=!1,M=null;Ze(()=>{o.setNewAgentDraft(null)});const W=Je(o,n.currentConversationModel,c);return s.$$.update=()=>{if(7&s.$$.dirty&&r.agentOverviews&&(e(0,g=!!r.maxRemoteAgents&&r.agentOverviews.length>=r.maxRemoteAgents),g)){const z=[...r.agentOverviews].sort((Z,G)=>new Date(Z.started_at).getTime()-new Date(G.started_at).getTime())[0];z&&(a=z.remote_agent_id,e(1,p=z.session_summary||""),p.length>30&&e(1,p=p.substring(0,27)+"..."))}},[g,p,r,w,h,C,x,P,U,B,o,i,async function(z){var Z;if(o.setRemoteAgentCreationError(null),u=z.detail.commitRef,l=z.detail.selectedBranch,u&&l){const G={commitRef:u,selectedBranch:l,setupScript:M,isDisabled:w,enableNotification:((Z=o.newAgentDraft)==null?void 0:Z.enableNotification)??!1};o.setNewAgentDraft(G),o.saveLastRemoteAgentSetup(u.github_commit_ref.repository_url,l.name,P)}},function(z){var Z;if(o.setRemoteAgentCreationError(null),M=z.detail.script,o.saveLastRemoteAgentSetup((u==null?void 0:u.github_commit_ref.repository_url)||null,(l==null?void 0:l.name)||null,(M==null?void 0:M.path)||null),u&&l){const G={commitRef:u,selectedBranch:l,setupScript:M,isDisabled:w,enableNotification:((Z=o.newAgentDraft)==null?void 0:Z.enableNotification)??!1};o.setNewAgentDraft(G)}},async function(){if(!$&&a)try{$=!0,await o.deleteAgent(a),e(0,g=r.agentOverviews.length>=r.maxRemoteAgents)}catch(z){console.error("Failed to delete oldest agent:",z)}finally{$=!1}},async function(){try{W()}catch(z){console.error("Failed to create agent:",z)}},function(z){w=z,e(3,w)},function(z){h=z,e(4,h)},function(z){U=z,e(8,U)},function(z){B=z,e(9,B)}]}class Us extends Lt{constructor(t){super(),Rt(this,t,Ir,Gr,kt,{})}}export{Us as default};
