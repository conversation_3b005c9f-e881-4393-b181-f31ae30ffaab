var k=Object.defineProperty;var A=(e,t,s)=>t in e?k(e,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[t]=s;var l=(e,t,s)=>A(e,typeof t!="symbol"?t+"":t,s);import{S as L,i as y,s as F,$ as B,V as W,W as G,u as w,t as b,Z as J,I as R,J as _,T as $,a0 as V,c as r,a7 as E,e as S,f as p,M,N as H,O as q,a1 as z,h as T,b as C,n as I}from"./SpinnerAugment-BUJasFTo.js";import{n as j,g as K,a as Q}from"./file-paths-BcSg4gks.js";class ge{constructor(t=[]){l(this,"_items",[]);l(this,"_focusedItemIdx");l(this,"_subscribers",new Set);l(this,"subscribe",t=>(this._subscribers.add(t),t(this),()=>{this._subscribers.delete(t)}));l(this,"setItems",t=>{this._items=t,this._items.length===0?this.setFocusIdx(void 0):this._focusedItemIdx!==void 0&&this._focusedItemIdx>=this._items.length?this.setFocusIdx(this._items.length-1):this._focusedItemIdx===void 0?this.setFocusIdx(void 0):this.setFocusIdx(this._focusedItemIdx)});l(this,"setFocus",t=>{if(t!==void 0&&t===this.focusedItem)return;const s=t?this._items.indexOf(t):-1;s===-1?this.setFocusIdx(void 0):this.setFocusIdx(s)});l(this,"setFocusIdx",t=>{if(t===this._focusedItemIdx||this._items.length===0)return;if(t===void 0)return this._focusedItemIdx=void 0,void this.notifySubscribers();const s=Math.floor(t/this._items.length)*this._items.length;this._focusedItemIdx=(t-s)%this._items.length,this.notifySubscribers()});l(this,"initFocusIdx",t=>this._focusedItemIdx===void 0&&(this.setFocusIdx(t),!0));l(this,"focusNext",()=>{const t=this.nextIdx();if(t!==void 0)return this.setFocus(this._items[t]),t});l(this,"focusPrev",()=>{const t=this.prevIdx();if(t!==void 0)return this.setFocus(this._items[t]),t});l(this,"prevIdx",(t={})=>{if(this._items.length!==0)return this._focusedItemIdx===void 0?this._items.length-1:t.nowrap&&this._focusedItemIdx===0?0:(this._focusedItemIdx-1+this._items.length)%this._items.length});l(this,"nextIdx",(t={})=>{if(this._items.length!==0)return this._focusedItemIdx===void 0?0:t.nowrap&&this._focusedItemIdx===this._items.length-1?this._items.length-1:(this._focusedItemIdx+1)%this._items.length});l(this,"notifySubscribers",()=>{this._subscribers.forEach(t=>t(this))});this._items=t}get items(){return this._items}get focusedItem(){if(this._focusedItemIdx!==void 0)return this._items[this._focusedItemIdx]}get focusedItemIdx(){return this._focusedItemIdx}}function X(e){return e===void 0?{num_lines:-1,num_chars:-1}:{num_lines:e.split(`
`).length,num_chars:e.length}}class Z{constructor(){l(this,"tracingData",{flags:{},nums:{},string_stats:{},request_ids:{}})}setFlag(t,s=!0){this.tracingData.flags[t]={value:s,timestamp:new Date().toISOString()}}getFlag(t){const s=this.tracingData.flags[t];return s==null?void 0:s.value}setNum(t,s){this.tracingData.nums[t]={value:s,timestamp:new Date().toISOString()}}getNum(t){const s=this.tracingData.nums[t];return s==null?void 0:s.value}setStringStats(t,s){this.tracingData.string_stats[t]={value:X(s),timestamp:new Date().toISOString()}}setRequestId(t,s){this.tracingData.request_ids[t]={value:s,timestamp:new Date().toISOString()}}}var Y=(e=>(e[e.unspecified=0]="unspecified",e[e.classify_and_distill=1]="classify_and_distill",e[e.orientation=2]="orientation",e))(Y||{}),ee=(e=>(e.start="start",e.end="end",e.memoriesRequestId="memoriesRequestId",e.exceptionThrown="exceptionThrown",e.lastUserExchangeRequestId="lastUserExchangeRequestId",e.noMemoryData="noMemoryData",e.agenticTurnHasRememberToolCall="agenticTurnHasRememberToolCall",e.emptyMemory="emptyMemory",e.removeUserExchangeMemoryFailed="removeUserExchangeMemoryFailed",e))(ee||{});class N extends Z{constructor(){super()}static create(){return new N}}var te=(e=>(e.openedAgentConversation="opened-agent-conversation",e.revertCheckpoint="revert-checkpoint",e.agentInterruption="agent-interruption",e.sentUserMessage="sent-user-message",e.rememberToolCall="remember-tool-call",e.openedMemoriesFile="opened-memories-file",e.initialOrientation="initial-orientation",e.classifyAndDistill="classify-and-distill",e.flushMemories="flush-memories",e.vsCodeTerminalShellIntegrationNotAvailable="vs-code-terminal-shell-integration-not-available",e.vsCodeTerminalReadingApproximateOutput="vs-code-terminal-reading-approximate-output",e.vsCodeTerminalTimedOutWaitingForNoopCommand="vs-code-terminal-timed-out-waiting-for-noop-command",e.vsCodeTerminalFailedToUseShellIntegration="vs-code-terminal-failed-to-use-shell-integration",e.vsCodeTerminalLastCommandIsSameAsCurrent="vs-code-terminal-last-command-is-same-as-current",e.vsCodeTerminalPollingDeterminedProcessIsDone="vs-code-terminal-polling-determined-process-is-done",e.vsCodeTerminalFailedToReadOutput="vs-code-terminal-failed-to-read-output",e.vsCodeTerminalBuggyOutput="vs-code-terminal-buggy-output",e.vsCodeTerminalBuggyExecutionEvents="vs-code-terminal-buggy-execution-events",e.vsCodeTerminalUnsupportedVSCodeShell="vs-code-terminal-unsupported-vscode-shell",e.vsCodeTerminalFailedToFindGitBash="vs-code-terminal-failed-to-find-git-bash",e.vsCodeTerminalFailedToFindPowerShell="vs-code-terminal-failed-to-find-powershell",e.vsCodeTerminalNoSupportedShellsFound="vs-code-terminal-no-supported-shells-found",e.vsCodeTerminalSettingsChanged="vs-code-terminal-settings-changed",e.vsCodeTerminalWaitTimeout="vs-code-terminal-wait-timeout",e.vsCodeTerminalErrorLoadingSettings="vs-code-terminal-error-loading-settings",e.vsCodeTerminalErrorCheckingForShellUpdates="vs-code-terminal-error-checking-for-shell-updates",e.vsCodeTerminalErrorCleaningUpTempDir="vs-code-terminal-error-cleaning-up-temp-dir",e.vsCodeTerminalErrorInitializingShells="vs-code-terminal-error-initializing-shells",e.vsCodeTerminalErrorCheckingShellCapability="vs-code-terminal-error-checking-shell-capability",e.vsCodeTerminalErrorCreatingZshEnvironment="vs-code-terminal-error-creating-zsh-environment",e.chatHistoryTruncated="chat-history-truncated",e.enhancedPrompt="enhanced-prompt",e))(te||{}),se=(e=>(e.sentUserMessage="sent-user-message",e))(se||{}),ie=(e=>(e.memoriesRequestId="memoriesRequestId",e.exceptionThrown="exceptionThrown",e.start="start",e.end="end",e.noPendingUserMessage="noPendingUserMessage",e.startSendSilentExchange="startSendSilentExchange",e.sendSilentExchangeRequestId="sendSilentExchangeRequestId",e.sendSilentExchangeResponseStats="sendSilentExchangeResponseStats",e.noRequestId="noRequestId",e.conversationChanged="conversationChanged",e.explanationStats="explanationStats",e.contentStats="contentStats",e.invalidResponse="invalidResponse",e.worthRemembering="worthRemembering",e.lastUserExchangeRequestId="lastUserExchangeRequestId",e.noLastUserExchangeRequestId="noLastUserExchangeRequestId",e))(ie||{});class P extends Z{constructor(){super()}static create(){return new P}}const ne=e=>({}),D=e=>({}),ae=e=>({}),U=e=>({});function O(e){let t,s,i;return{c(){t=_("div"),s=_("div"),i=V(e[3]),r(s,"class","c-filespan__dir-text svelte-sprpft"),r(t,"class","c-filespan__dir svelte-sprpft")},m(a,m){S(a,t,m),p(t,s),p(s,i)},p(a,m){8&m&&z(i,a[3])},d(a){a&&T(t)}}}function re(e){let t,s,i,a,m,x,v,u;const f=e[7].leftIcon,d=R(f,e,e[8],U);let o=!e[2]&&O(e);const c=e[7].rightIcon,h=R(c,e,e[8],D);return{c(){t=_("div"),d&&d.c(),s=$(),i=_("span"),a=V(e[4]),m=$(),o&&o.c(),x=$(),h&&h.c(),r(i,"class","c-filespan__filename svelte-sprpft"),r(t,"class",v=E(`c-filespan ${e[0]}`)+" svelte-sprpft")},m(n,g){S(n,t,g),d&&d.m(t,null),p(t,s),p(t,i),p(i,a),p(t,m),o&&o.m(t,null),p(t,x),h&&h.m(t,null),u=!0},p(n,g){d&&d.p&&(!u||256&g)&&M(d,f,n,n[8],u?q(f,n[8],g,ae):H(n[8]),U),(!u||16&g)&&z(a,n[4]),n[2]?o&&(o.d(1),o=null):o?o.p(n,g):(o=O(n),o.c(),o.m(t,x)),h&&h.p&&(!u||256&g)&&M(h,c,n,n[8],u?q(c,n[8],g,ne):H(n[8]),D),(!u||1&g&&v!==(v=E(`c-filespan ${n[0]}`)+" svelte-sprpft"))&&r(t,"class",v)},i(n){u||(w(d,n),w(h,n),u=!0)},o(n){b(d,n),b(h,n),u=!1},d(n){n&&T(t),d&&d.d(n),o&&o.d(),h&&h.d(n)}}}function oe(e){let t,s;return t=new B({props:{size:e[1],$$slots:{default:[re]},$$scope:{ctx:e}}}),{c(){W(t.$$.fragment)},m(i,a){G(t,i,a),s=!0},p(i,[a]){const m={};2&a&&(m.size=i[1]),285&a&&(m.$$scope={dirty:a,ctx:i}),t.$set(m)},i(i){s||(w(t.$$.fragment,i),s=!0)},o(i){b(t.$$.fragment,i),s=!1},d(i){J(t,i)}}}function le(e,t,s){let i,a,m,{$$slots:x={},$$scope:v}=t,{class:u=""}=t,{filepath:f}=t,{size:d=1}=t,{nopath:o=!1}=t;return e.$$set=c=>{"class"in c&&s(0,u=c.class),"filepath"in c&&s(5,f=c.filepath),"size"in c&&s(1,d=c.size),"nopath"in c&&s(2,o=c.nopath),"$$scope"in c&&s(8,v=c.$$scope)},e.$$.update=()=>{32&e.$$.dirty&&s(6,i=j(f)),64&e.$$.dirty&&s(4,a=K(i)),64&e.$$.dirty&&s(3,m=Q(i))},[u,d,o,m,a,f,i,x,v]}class pe extends L{constructor(t){super(),y(this,t,le,oe,F,{class:0,filepath:5,size:1,nopath:2})}}function de(e){let t,s;return{c(){t=C("svg"),s=C("path"),r(s,"fill-rule","evenodd"),r(s,"clip-rule","evenodd"),r(s,"d","M13.71 4.29L10.71 1.29L10 1H4L3 2V14L4 15H13L14 14V5L13.71 4.29ZM13 14H4V2H9V6H13V14ZM10 5V2L13 5H10Z"),r(s,"fill","currentColor"),r(t,"width","16"),r(t,"height","16"),r(t,"viewBox","0 0 16 16"),r(t,"fill","none"),r(t,"xmlns","http://www.w3.org/2000/svg")},m(i,a){S(i,t,a),p(t,s)},p:I,i:I,o:I,d(i){i&&T(t)}}}class ve extends L{constructor(t){super(),y(this,t,null,de,F,{})}}function ce(e){let t,s;return{c(){t=C("svg"),s=C("path"),r(s,"d","M1.5 14H12.5L12.98 13.63L15.61 6.63L15.13 6H14V3.5L13.5 3H7.70996L6.84998 2.15002L6.5 2H1.5L1 2.5V13.5L1.5 14ZM2 3H6.29004L7.15002 3.84998L7.5 4H13V6H8.5L8.15002 6.15002L7.29004 7H3.5L3.03003 7.33997L2.03003 10.42L2 3ZM12.13 13H2.18994L3.85999 8H7.5L7.84998 7.84998L8.70996 7H14.5L12.13 13Z"),r(s,"fill","currentColor"),r(t,"width","16"),r(t,"height","16"),r(t,"viewBox","0 0 16 16"),r(t,"fill","none"),r(t,"xmlns","http://www.w3.org/2000/svg")},m(i,a){S(i,t,a),p(t,s)},p:I,i:I,o:I,d(i){i&&T(t)}}}class fe extends L{constructor(t){super(),y(this,t,null,ce,F,{})}}export{te as A,P as C,ge as F,Y as R,ie as a,se as b,pe as c,fe as d,ve as e,N as f,ee as g};
