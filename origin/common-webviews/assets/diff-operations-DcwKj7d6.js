var es=Object.defineProperty;var pt=l=>{throw TypeError(l)};var ts=(l,e,t)=>e in l?es(l,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):l[e]=t;var a=(l,e,t)=>ts(l,typeof e!="symbol"?e+"":e,t),nt=(l,e,t)=>e.has(l)||pt("Cannot "+t);var r=(l,e,t)=>(nt(l,e,"read from private field"),t?t.call(l):e.get(l)),v=(l,e,t)=>e.has(l)?pt("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(l):e.set(l,t),m=(l,e,t,s)=>(nt(l,e,"write to private field"),s?s.call(l,t):e.set(l,t),t),u=(l,e,t)=>(nt(l,e,"access private method"),t);var Ye=(l,e,t,s)=>({set _(i){m(l,e,i,t)},get _(){return r(l,e,s)}});import{S as Ot,W as Ce}from"./BaseButton-ci_067e0.js";import{d as ss}from"./Content-CSmc2GUv.js";import{ah as Ie,aj as Be,S as le,i as he,s as ce,a as He,b as L,H as Ht,w as Lt,x as kt,y as Nt,h as k,d as Qe,z as Ut,g as zt,n as x,j as et,J as is,T as ns,c as g,e as Z,f as ve,u as pe,q as bt,t as Ae,r as yt,$ as Bt,V as Wt,W as Gt,Z as jt,a0 as tt,a1 as Vt}from"./SpinnerAugment-BUJasFTo.js";import{g as Y,A as Me,E as P,T as ye,o as vt,p as rt,q as Fe,k as We,S as Ne,C as Ue,r as $t,t as rs,u as at,v as as,w as Ct,x as St,y as os,z as xt,B as ls,D as wt,F as hs,G as ot}from"./open-in-new-window-eiueNVFd.js";import{C as cs,a as H,A as ds,F as us,b as gs}from"./folder-opened-C1X7jSw2.js";import{C as ae,P as $,c as ms,a as re,I as ke,E as _s}from"./file-base64-RhZyEMB8.js";import{S as fs,a as It}from"./types-e72Yl75f.js";import{g as ps,a as bs,b as Tt,i as ys,c as vs}from"./diff-utils-BYhHYFY1.js";function Cs(l){var e;return((e=l.extraData)==null?void 0:e.isAutofix)===!0}function De(l){var e;return((e=l.extraData)==null?void 0:e.isAgentConversation)===!0}var R=(l=>(l[l.active=0]="active",l[l.inactive=1]="inactive",l))(R||{}),Ss=(l=>(l.normal="Normal",l.autofixCommand="AutofixCommand",l.autofixPrompt="AutofixPrompt",l))(Ss||{});const Yt=25e4,li=2e4;class xs{constructor(e){a(this,"_enableEditableHistory",!1);a(this,"_enablePreferenceCollection",!1);a(this,"_enableRetrievalDataCollection",!1);a(this,"_enableDebugFeatures",!1);a(this,"_enableRichTextHistory",!1);a(this,"_modelDisplayNameToId",{});a(this,"_fullFeatured",!0);a(this,"_enableExternalSourcesInChat",!1);a(this,"_smallSyncThreshold",15);a(this,"_bigSyncThreshold",1e3);a(this,"_enableSmartPaste",!1);a(this,"_enableDirectApply",!1);a(this,"_summaryTitles",!1);a(this,"_suggestedEditsAvailable",!1);a(this,"_enableShareService",!1);a(this,"_maxTrackableFileCount",Yt);a(this,"_enableDesignSystemRichTextEditor",!1);a(this,"_enableSources",!1);a(this,"_enableChatMermaidDiagrams",!1);a(this,"_smartPastePrecomputeMode",Ot.visibleHover);a(this,"_useNewThreadsMenu",!1);a(this,"_enableChatMermaidDiagramsMinVersion",!1);a(this,"_enablePromptEnhancer",!1);a(this,"_idleNewSessionNotificationTimeoutMs");a(this,"_idleNewSessionMessageTimeoutMs");a(this,"_enableChatMultimodal",!1);a(this,"_enableAgentMode",!1);a(this,"_enableRichCheckpointInfo",!1);a(this,"_agentMemoriesFilePathName");a(this,"_userTier","unknown");a(this,"_eloModelConfiguration",{highPriorityModels:[],regularBattleModels:[],highPriorityThreshold:.5});a(this,"_truncateChatHistory",!1);a(this,"_enableBackgroundAgents",!1);a(this,"_enableVirtualizedMessageList",!1);a(this,"_customPersonalityPrompts",{});a(this,"_enablePersonalities",!1);a(this,"_memoryClassificationOnFirstToken",!1);a(this,"_isRemoteAgentWindow",!1);a(this,"_remoteAgentId");a(this,"_enableGenerateCommitMessage",!1);a(this,"_modelRegistry",{});a(this,"_enableModelRegistry",!1);a(this,"_subscribers",new Set);a(this,"subscribe",e=>(this._subscribers.add(e),e(this),()=>{this._subscribers.delete(e)}));a(this,"update",e=>{this._enableEditableHistory=e.enableEditableHistory??this._enableEditableHistory,this._enablePreferenceCollection=e.enablePreferenceCollection??this._enablePreferenceCollection,this._enableRetrievalDataCollection=e.enableRetrievalDataCollection??this._enableRetrievalDataCollection,this._enableDebugFeatures=e.enableDebugFeatures??this._enableDebugFeatures,this._enableRichTextHistory=e.enableRichTextHistory??this._enableRichTextHistory,this._modelDisplayNameToId={...e.modelDisplayNameToId},this._fullFeatured=e.fullFeatured??this._fullFeatured,this._enableExternalSourcesInChat=e.enableExternalSourcesInChat??this._enableExternalSourcesInChat,this._smallSyncThreshold=e.smallSyncThreshold??this._smallSyncThreshold,this._bigSyncThreshold=e.bigSyncThreshold??this._bigSyncThreshold,this._enableSmartPaste=e.enableSmartPaste??this._enableSmartPaste,this._enableDirectApply=e.enableDirectApply??this._enableDirectApply,this._summaryTitles=e.summaryTitles??this._summaryTitles,this._suggestedEditsAvailable=e.suggestedEditsAvailable??this._suggestedEditsAvailable,this._enableShareService=e.enableShareService??this._enableShareService,this._maxTrackableFileCount=e.maxTrackableFileCount??this._maxTrackableFileCount,this._enableDesignSystemRichTextEditor=e.enableDesignSystemRichTextEditor??this._enableDesignSystemRichTextEditor,this._enableSources=e.enableSources??this._enableSources,this._enableChatMermaidDiagrams=e.enableChatMermaidDiagrams??this._enableChatMermaidDiagrams,this._smartPastePrecomputeMode=e.smartPastePrecomputeMode??this._smartPastePrecomputeMode,this._useNewThreadsMenu=e.useNewThreadsMenu??this._useNewThreadsMenu,this._enableChatMermaidDiagramsMinVersion=e.enableChatMermaidDiagramsMinVersion??this._enableChatMermaidDiagramsMinVersion,this._enablePromptEnhancer=e.enablePromptEnhancer??this._enablePromptEnhancer,this._idleNewSessionMessageTimeoutMs=e.idleNewSessionMessageTimeoutMs??(e.enableDebugFeatures?this._idleNewSessionMessageTimeoutMs??3e5:this._idleNewSessionMessageTimeoutMs),this._idleNewSessionNotificationTimeoutMs=e.idleNewSessionNotificationTimeoutMs??0,this._enableChatMultimodal=e.enableChatMultimodal??this._enableChatMultimodal,this._enableAgentMode=e.enableAgentMode??this._enableAgentMode,this._enableRichCheckpointInfo=e.enableRichCheckpointInfo??this._enableRichCheckpointInfo,this._agentMemoriesFilePathName=e.agentMemoriesFilePathName??this._agentMemoriesFilePathName,this._userTier=e.userTier??this._userTier,this._eloModelConfiguration=e.eloModelConfiguration??this._eloModelConfiguration,this._truncateChatHistory=e.truncateChatHistory??this._truncateChatHistory,this._enableBackgroundAgents=e.enableBackgroundAgents??this._enableBackgroundAgents,this._enableVirtualizedMessageList=e.enableVirtualizedMessageList??this._enableVirtualizedMessageList,this._customPersonalityPrompts=e.customPersonalityPrompts??this._customPersonalityPrompts,this._enablePersonalities=e.enablePersonalities??this._enablePersonalities,this._memoryClassificationOnFirstToken=e.memoryClassificationOnFirstToken??this._memoryClassificationOnFirstToken,this._isRemoteAgentWindow=e.isRemoteAgentWindow??this._isRemoteAgentWindow,this._remoteAgentId=e.remoteAgentId??this._remoteAgentId,this._enableGenerateCommitMessage=e.enableGenerateCommitMessage??this._enableGenerateCommitMessage,this._modelRegistry=e.modelRegistry??this._modelRegistry,this._enableModelRegistry=e.enableModelRegistry??this._enableModelRegistry,this._subscribers.forEach(t=>t(this))});a(this,"isModelIdValid",e=>e!==void 0&&(Object.values(this._modelDisplayNameToId).includes(e)||Object.values(this._modelRegistry).includes(e??"")));a(this,"getModelDisplayName",e=>{if(e!==void 0)return Object.keys(this._modelDisplayNameToId).find(t=>this._modelDisplayNameToId[t]===e)});e&&this.update(e)}get enableEditableHistory(){return this._fullFeatured&&(this._enableEditableHistory||this._enableDebugFeatures)}get enablePreferenceCollection(){return this._enablePreferenceCollection}get enableRetrievalDataCollection(){return this._enableRetrievalDataCollection}get enableDebugFeatures(){return this._enableDebugFeatures}get enableGenerateCommitMessage(){return this._enableGenerateCommitMessage}get enableRichTextHistory(){return this._enableRichTextHistory}get modelDisplayNameToId(){return this._modelDisplayNameToId}get orderedModelDisplayNames(){return Object.keys(this._modelDisplayNameToId).sort((e,t)=>{const s=e.toLowerCase(),i=t.toLowerCase();return s==="default"&&i!=="default"?-1:i==="default"&&s!=="default"?1:e.localeCompare(t)})}get fullFeatured(){return this._fullFeatured}get enableExternalSourcesInChat(){return this._enableExternalSourcesInChat}get smallSyncThreshold(){return this._smallSyncThreshold}get bigSyncThreshold(){return this._bigSyncThreshold}get enableSmartPaste(){return this._enableDebugFeatures||this._enableSmartPaste}get enableDirectApply(){return this._enableDirectApply||this._enableDebugFeatures}get enableShareService(){return this._enableShareService}get summaryTitles(){return this._summaryTitles}get suggestedEditsAvailable(){return this._suggestedEditsAvailable}get maxTrackableFileCount(){return this._maxTrackableFileCount}get enableSources(){return this._enableDebugFeatures||this._enableSources}get enableChatMermaidDiagrams(){return this._enableDebugFeatures||this._enableChatMermaidDiagrams}get smartPastePrecomputeMode(){return this._smartPastePrecomputeMode}get useNewThreadsMenu(){return this._useNewThreadsMenu}get enableChatMermaidDiagramsMinVersion(){return this._enableChatMermaidDiagramsMinVersion}get enablePromptEnhancer(){return this._enablePromptEnhancer}get enableDesignSystemRichTextEditor(){return this._enableDesignSystemRichTextEditor}get idleNewSessionNotificationTimeoutMs(){return this._idleNewSessionNotificationTimeoutMs??0}get idleNewSessionMessageTimeoutMs(){return this._idleNewSessionMessageTimeoutMs??0}get enableChatMultimodal(){return this._enableChatMultimodal}get enableAgentMode(){return this._enableAgentMode}get enableRichCheckpointInfo(){return this._enableRichCheckpointInfo}get agentMemoriesFilePathName(){return this._agentMemoriesFilePathName}get userTier(){return this._userTier}get eloModelConfiguration(){return this._eloModelConfiguration}get truncateChatHistory(){return this._truncateChatHistory}get enableBackgroundAgents(){return this._enableBackgroundAgents}get enableVirtualizedMessageList(){return this._enableVirtualizedMessageList||this._enableDebugFeatures}get customPersonalityPrompts(){return this._customPersonalityPrompts}get enablePersonalities(){return this._enablePersonalities||this._enableDebugFeatures}get memoryClassificationOnFirstToken(){return this._memoryClassificationOnFirstToken}get isRemoteAgentWindow(){return this._isRemoteAgentWindow}get remoteAgentId(){return this._remoteAgentId}get modelRegistry(){return this._modelRegistry}get enableModelRegistry(){return this._enableModelRegistry}}function ws(l,e,t=1e3){let s=null,i=0;const n=Ie(e),o=()=>{const c=(()=>{const h=Date.now();if(s!==null&&h-i<t)return s;const _=l();return s=_,i=h,_})();n.set(c)};return{subscribe:n.subscribe,resetCache:()=>{s=null,o()},updateStore:o}}var Zt=(l=>(l[l.unset=0]="unset",l[l.positive=1]="positive",l[l.negative=2]="negative",l))(Zt||{});const hi=async(l,e)=>{if(!De(l)||e.chatItemType!==void 0||!(e!=null&&e.request_message))return;const t=cs.create();t.setFlag(H.start);try{await Is(l,e,t)}catch(s){t.setFlag(H.exceptionThrown),console.error("Failed to classify and distill memories",s)}finally{t.setFlag(H.end),l.extensionClient.reportAgentSessionEvent({eventName:ds.classifyAndDistill,conversationId:l.id,eventData:{classifyAndDistillData:t}})}},Is=async(l,e,t)=>{const s=crypto.randomUUID();t.setRequestId(H.memoriesRequestId,s);const i=Be(l).id;t.setFlag(H.startSendSilentExchange);const{responseText:n,requestId:o}=await l.sendSilentExchange({request_message:e.request_message,disableRetrieval:!0,disableHistory:!1,disableSelectedCodeDetails:!0,memoriesInfo:{isClassifyAndDistill:!0}});if(t.setStringStats(H.sendSilentExchangeResponseStats,n),o?t.setRequestId(H.sendSilentExchangeRequestId,o):t.setFlag(H.noRequestId),Be(l).id!==i)return void t.setFlag(H.conversationChanged);let c;try{c=JSON.parse(n)}catch{throw t.setFlag(H.invalidResponse),new Error("Invalid response from classify and distill")}if(typeof c.explanation!="string"||typeof c.content!="string"||typeof c.worthRemembering!="boolean")throw t.setFlag(H.invalidResponse),new Error("Invalid response from classify and distill");t.setStringStats(H.explanationStats,c.explanation),t.setStringStats(H.contentStats,c.content),t.setFlag(H.worthRemembering,c.worthRemembering);const h=c.worthRemembering?c.content:void 0;h&&Ms(l,h,s,t)},ci=l=>{var s;const e=l.chatHistory.at(-1);if(!e||!Y(e))return Me.notRunning;if(!(e.status===P.success||e.status===P.failed||e.status===P.cancelled))return Me.running;const t=(((s=e.structured_output_nodes)==null?void 0:s.filter(i=>i.type===ae.TOOL_USE&&!!i.tool_use))??[]).at(-1);if(!t)return Me.notRunning;switch(l.getToolUseState(e.request_id,t.tool_use.tool_use_id).phase){case ye.runnable:return Me.awaitingUserAction;case ye.cancelled:return Me.notRunning;default:return Me.running}},lt=l=>Y(l)&&!!l.request_message,it=l=>l.chatHistory.findLast(e=>lt(e)),di=l=>{const e=it(l);if(e!=null&&e.structured_output_nodes){const t=e.structured_output_nodes.find(s=>s.type===ae.AGENT_MEMORY);if(t)try{const{memoriesRequestId:s,memory:i}=JSON.parse(t.content);return{memoriesRequestId:s,memory:i}}catch(s){return void console.error("Failed to parse JSON from agent memory node",s)}}},ui=l=>Ts(l,e=>{var t;return!!((t=e.structured_output_nodes)!=null&&t.some(s=>{var i;return s.type===ae.TOOL_USE&&((i=s.tool_use)==null?void 0:i.tool_name)==="remember"}))}).length>0,Ts=(l,e)=>{const t=it(l);return t!=null&&t.request_id?l.historyFrom(t.request_id,!0).filter(s=>Y(s)&&(!e||e(s))):[]},gi=l=>{var s;const e=l.chatHistory.at(-1);if(!(e!=null&&e.request_id)||!Y(e))return!1;const t=((s=e.structured_output_nodes)==null?void 0:s.filter(i=>i.type===ae.TOOL_USE))??[];for(const i of t)if(i.tool_use&&l.getToolUseState(e.request_id,i.tool_use.tool_use_id).phase===ye.runnable)return l.updateToolUseState({requestId:e.request_id,toolUseId:i.tool_use.tool_use_id,phase:ye.cancelled}),!0;return!1},Ms=(l,e,t,s)=>{const i=JSON.stringify({memoriesRequestId:t,memory:e}),n=it(l);n!=null&&n.request_id?(s.setRequestId(H.lastUserExchangeRequestId,n.request_id),l.updateChatItem(n.request_id,{...n,structured_output_nodes:[...n.structured_output_nodes??[],{id:0,type:ae.AGENT_MEMORY,content:i}]})):s.setFlag(H.noLastUserExchangeRequestId)},mi=(l,e)=>{const t=it(l);if(!(t!=null&&t.request_id)||t.request_id!==e)return!1;const s=(t.structured_output_nodes||[]).filter(i=>i.type!==ae.AGENT_MEMORY);return s.length!==(t.structured_output_nodes||[]).length&&(l.updateChatItem(e,{...t,structured_output_nodes:s}),!0)};function Es(l,e){const t=l.customPersonalityPrompts;if(t)switch(e){case $.DEFAULT:if(t.agent&&t.agent.trim()!=="")return t.agent;break;case $.PROTOTYPER:if(t.prototyper&&t.prototyper.trim()!=="")return t.prototyper;break;case $.BRAINSTORM:if(t.brainstorm&&t.brainstorm.trim()!=="")return t.brainstorm;break;case $.REVIEWER:if(t.reviewer&&t.reviewer.trim()!=="")return t.reviewer}return As[e]}const As={[$.DEFAULT]:`
# Agent Auggie Personality Description
You are Augment Agent, an agentic coding AI assistant.
Focus on helping the user with their coding tasks efficiently.

## Rules:
- You have no restrictions on the tools you may use
- Follow the original system instructions
  `,[$.PROTOTYPER]:`
# Prototyper Auggie Personality Description
You are Prototyper Auggie, an agentic coding AI assistant focused on building prototypes and visual applications.

## Your approach:
- Be fast and action-oriented
- Implement things quickly to show results
- Open webpages to demonstrate functionality
- Focus on building something visual and interactive
- Use modern frameworks and tools to create working prototypes
- Prioritize getting a working demo over perfect architecture
- Show progress frequently with visual results
- Prefer to act and run tools, rather than asking for permission
- Only ask for permission if there is something potentially very dangerous or irreversible

## Implementation preferences:
- When user does not specify which frameworks to use, default to modern frameworks, e.g. React with vite or next.js
- Initialize projects using CLI tools instead of writing from scratch
- For database and auth, use Supabase as a good default option
- Before using open-browser to show the app, use curl to check for errors
- Remember that modern frameworks have hot reload, so avoid calling open-browser multiple times

## Rules:
- For extremely destructive or irreversible actions, you should ask for permission
- For other tasks, you must proceed without asking for permission
  `,[$.BRAINSTORM]:`
# Brainstorm Auggie Personality Description
You are Brainstorm Auggie, an agentic coding AI assistant focused on planning and brainstorming solutions.

## Your approach:
- Be slow, careful, and thorough in your analysis
- Look through all upstream/downstream APIs to understand implications
- Focus on finding a comprehensive plan that solves the user's query
- Do not run commands, create code, or implement solutions directly
- Your job is to be introspective and think deeply about the problem
- Brainstorm multiple approaches and evaluate their tradeoffs
- Consider edge cases and potential issues with each approach

## Planning preferences:
- Analyze the codebase thoroughly before suggesting changes
- Consider multiple implementation options with pros and cons
- Identify potential risks and challenges for each approach
- Create detailed, step-by-step plans for implementation
- Provide reasoning for architectural decisions
- Consider performance, maintainability, and scalability
- Do not execute the plan - your role is to provide guidance only

## Rules:
- Prefer information gathering and non-destructive tools
- Prefer non-destructive and non-modifying tools
- You must never execute code, modify the codebase, or make changes
- Consider using Mermaid diagrams to help visualize complex concepts
- Once you have a proposal, please examine it critically, and do a revision before finalizing
  `,[$.REVIEWER]:`
# Reviewer Auggie Personality Description
You are Reviewer Auggie, an agentic coding AI assistant focused on reviewing code changes and identifying potential issues.

## Your approach:
- Act like a code detective to find potential bugs and issues
- Use git commands to analyze changes against the merge base
- Be super inquisitive and look for anything suspicious
- Build a mental model of what is happening in the code change
- Analyze API implications and downstream effects
- Guard the codebase from potential negative side effects
- Focus on understanding the changes from first principles

## Review preferences:
- Use git and GitHub tools to get code history information
- Compare changes against the logical base or merge base
- Look for edge cases and potential bugs
- Analyze API contracts and potential breaking changes
- Consider performance implications
- Check for security vulnerabilities
- Verify test coverage for the changes

## Rules:
- Use git commands and GitHub API to analyze code changes
- Be thorough and methodical in your analysis
- Focus on finding potential issues rather than implementing solutions
- Provide constructive feedback with specific examples
- Consider both the technical implementation and the broader impact
  `};class w{constructor(e,t,s,i){a(this,"_state");a(this,"_subscribers",new Set);a(this,"_focusModel",new us);a(this,"_onSendExchangeListeners",[]);a(this,"_onNewConversationListeners",[]);a(this,"_onHistoryDeleteListeners",[]);a(this,"_onBeforeChangeConversationListeners",[]);a(this,"_totalCharactersCacheThrottleMs",1e3);a(this,"_totalCharactersStore");a(this,"subscribe",e=>(this._subscribers.add(e),e(this),()=>{this._subscribers.delete(e)}));a(this,"setConversation",(e,t=!0,s=!0)=>{const i=e.id!==this._state.id;i&&s&&(e.toolUseStates=Object.fromEntries(Object.entries(e.toolUseStates??{}).map(([o,c])=>{if(c.requestId&&c.toolUseId){const{requestId:h,toolUseId:_}=vt(o);return h===c.requestId&&_===c.toolUseId||console.warn("Tool use state key does not match request and tool use IDs. Got key ",o,"but object has ",rt(c)),[o,c]}return[o,{...c,...vt(o)}]})),(e=this._notifyBeforeChangeConversation(this._state,e)).lastInteractedAtIso=new Date().toISOString()),t&&i&&this.isValid&&(this.saveDraftActiveContextIds(),this._unloadContextFromConversation(this._state));const n=w.isEmpty(e);if(i&&n){const o=this._state.draftExchange;o&&(e.draftExchange=o)}return this._state=e,this._focusModel.setItems(this._state.chatHistory.filter(Y)),this._focusModel.initFocusIdx(-1),this._subscribers.forEach(o=>o(this)),this._saveConversation(this._state),i&&(this._loadContextFromConversation(e),this.loadDraftActiveContextIds(),this._onNewConversationListeners.forEach(o=>o())),!0});a(this,"update",e=>{this.setConversation({...this._state,...e}),this._totalCharactersStore.updateStore()});a(this,"toggleIsPinned",()=>{this.update({isPinned:!this.isPinned})});a(this,"setName",e=>{this.update({name:e})});a(this,"setSelectedModelId",e=>{this.update({selectedModelId:e})});a(this,"updateFeedback",(e,t)=>{this.update({feedbackStates:{...this._state.feedbackStates,[e]:t}})});a(this,"updateToolUseState",e=>{this.update({toolUseStates:{...this._state.toolUseStates,[rt(e)]:e}})});a(this,"getToolUseState",(e,t)=>e===void 0||t===void 0||this.toolUseStates===void 0?{phase:ye.unknown,requestId:e??"",toolUseId:t??""}:this.toolUseStates[rt({requestId:e,toolUseId:t})]||{phase:ye.new});a(this,"getLastToolUseState",()=>{var s,i;const e=this.lastExchange;if(!e)return{phase:ye.unknown};const t=(((s=e==null?void 0:e.structured_output_nodes)==null?void 0:s.filter(n=>n.type===ae.TOOL_USE))??[]).at(-1);return t?this.getToolUseState(e.request_id,(i=t.tool_use)==null?void 0:i.tool_use_id):{phase:ye.unknown}});a(this,"addExchange",e=>{const t=[...this._state.chatHistory,e];let s;Y(e)&&(s=e.request_id?{...this._state.feedbackStates,[e.request_id]:{selectedRating:Zt.unset,feedbackNote:""}}:void 0),this.update({chatHistory:t,...s?{feedbackStates:s}:{},lastUrl:void 0})});a(this,"resetShareUrl",()=>{this.update({lastUrl:void 0})});a(this,"updateExchangeById",(e,t,s=!1)=>{var c;const i=this.exchangeWithRequestId(t);if(i===null)return console.warn("No exchange with this request ID found."),!1;s&&e.response_text!==void 0&&(e.response_text=(i.response_text??"")+(e.response_text??"")),s&&(e.structured_output_nodes=[...i.structured_output_nodes??[],...e.structured_output_nodes??[]]),s&&e.workspace_file_chunks!==void 0&&(e.workspace_file_chunks=[...i.workspace_file_chunks??[],...e.workspace_file_chunks??[]]);const n=(c=(e.structured_output_nodes||[]).find(h=>h.type===ae.MAIN_TEXT_FINISHED))==null?void 0:c.content;n&&n!==e.response_text&&(e.response_text=n);let o=this._state.isShareable||Fe({...i,...e});return this.update({chatHistory:this.chatHistory.map(h=>h.request_id===t?{...h,...e}:h),isShareable:o}),!0});a(this,"clearMessagesFromHistory",e=>{this.update({chatHistory:this.chatHistory.filter(t=>!t.request_id||!e.has(t.request_id))}),this._extensionClient.clearMetadataFor({requestIds:Array.from(e)})});a(this,"clearHistory",()=>{this._extensionClient.clearMetadataFor({requestIds:this.requestIds}),this.update({chatHistory:[]})});a(this,"clearHistoryFrom",async(e,t=!0)=>{const s=this.historyFrom(e,t),i=s.map(n=>n.request_id).filter(n=>n!==void 0);this.update({chatHistory:this.historyTo(e,!t)}),this._extensionClient.clearMetadataFor({requestIds:i}),s.forEach(n=>{this._onHistoryDeleteListeners.forEach(o=>o(n))})});a(this,"clearMessageFromHistory",e=>{this.update({chatHistory:this.chatHistory.filter(t=>t.request_id!==e)}),this._extensionClient.clearMetadataFor({requestIds:[e]})});a(this,"historyTo",(e,t=!1)=>{const s=this.chatHistory.findIndex(i=>i.request_id===e);return s===-1?[]:this.chatHistory.slice(0,t?s+1:s)});a(this,"historyFrom",(e,t=!0)=>{const s=this.chatHistory.findIndex(i=>i.request_id===e);return s===-1?[]:this.chatHistory.slice(t?s:s+1)});a(this,"resendLastExchange",async()=>{const e=this.lastExchange;if(e&&!this.awaitingReply)return this.resendTurn(e)});a(this,"resendTurn",e=>this.awaitingReply?Promise.resolve():(this._removeTurn(e),this.sendExchange({chatItemType:e.chatItemType,request_message:e.request_message,rich_text_json_repr:e.rich_text_json_repr,status:P.draft,mentioned_items:e.mentioned_items,structured_request_nodes:e.structured_request_nodes,disableSelectedCodeDetails:e.disableSelectedCodeDetails,disableHistory:e.disableHistory})));a(this,"_removeTurn",e=>{this.update({chatHistory:this.chatHistory.filter(t=>t!==e&&(!e.request_id||t.request_id!==e.request_id))})});a(this,"exchangeWithRequestId",e=>this.chatHistory.find(t=>t.request_id===e)||null);a(this,"resetTotalCharactersCache",()=>{this._totalCharactersStore.resetCache()});a(this,"markSeen",async e=>{if(!e.request_id||!this.chatHistory.find(s=>s.request_id===e.request_id))return;const t={seen_state:Ne.seen};this.update({chatHistory:this.chatHistory.map(s=>s.request_id===e.request_id?{...s,...t}:s)})});a(this,"createStructuredRequestNodes",e=>this._jsonToStructuredRequest(e));a(this,"saveDraftMentions",e=>{if(!this.draftExchange)return;const t=e.filter(s=>!s.personality);this.update({draftExchange:{...this.draftExchange,mentioned_items:t}})});a(this,"saveDraftActiveContextIds",()=>{const e=this._specialContextInputModel.recentActiveItems.map(t=>t.id);this.update({draftActiveContextIds:e})});a(this,"loadDraftActiveContextIds",()=>{const e=new Set(this.draftActiveContextIds??[]),t=this._specialContextInputModel.recentItems.filter(i=>e.has(i.id)||i.recentFile||i.selection||i.sourceFolder),s=this._specialContextInputModel.recentItems.filter(i=>!(e.has(i.id)||i.recentFile||i.selection||i.sourceFolder));this._specialContextInputModel.markItemsActive(t.reverse()),this._specialContextInputModel.markItemsInactive(s.reverse())});a(this,"saveDraftExchange",(e,t)=>{var o,c,h;const s=e!==((o=this.draftExchange)==null?void 0:o.request_message),i=t!==((c=this.draftExchange)==null?void 0:c.rich_text_json_repr);if(!s&&!i)return;const n=(h=this.draftExchange)==null?void 0:h.mentioned_items;this.update({draftExchange:{request_message:e,rich_text_json_repr:t,mentioned_items:n,status:P.draft}})});a(this,"clearDraftExchange",()=>{const e=this.draftExchange;return this.update({draftExchange:void 0}),e});a(this,"sendDraftExchange",()=>{if(this._extensionClient.triggerUsedChatMetric(),!this.canSendDraft||!this.draftExchange)return!1;const e=this.clearDraftExchange();if(!e)return!1;const t=this._chatFlagModel.enableChatMultimodal&&e.rich_text_json_repr?this._jsonToStructuredRequest(e.rich_text_json_repr):void 0;return this.sendExchange({...e,structured_request_nodes:t,model_id:this.selectedModelId??void 0}).then(()=>{var o,c;const s=!this.name&&this.chatHistory.length===1&&((o=this.firstExchange)==null?void 0:o.request_id)===this.chatHistory[0].request_id,i=De(this)&&((c=this._state.extraData)==null?void 0:c.hasAgentOnboarded)&&(n=this.chatHistory,n.filter(h=>lt(h))).length===2;var n;this._chatFlagModel.summaryTitles&&(s||i)&&this.updateConversationTitle()}).finally(()=>{var s;De(this)&&this._extensionClient.reportAgentRequestEvent({eventName:gs.sentUserMessage,conversationId:this.id,requestId:((s=this.lastExchange)==null?void 0:s.request_id)??"UNKNOWN_REQUEST_ID",chatHistoryLength:this.chatHistory.length})}),this.focusModel.setFocusIdx(void 0),!0});a(this,"cancelMessage",async()=>{var e;this.canCancelMessage&&((e=this.lastExchange)!=null&&e.request_id)&&(this.updateExchangeById({status:P.cancelled},this.lastExchange.request_id),await this._extensionClient.cancelChatStream(this.lastExchange.request_id))});a(this,"sendInstructionExchange",async(e,t)=>{let s=`temp-fe-${crypto.randomUUID()}`;const i={status:P.sent,request_id:s,request_message:e,model_id:this.selectedModelId??void 0,structured_output_nodes:[],seen_state:Ne.unseen,timestamp:new Date().toISOString()};this.addExchange(i);for await(const n of this._extensionClient.sendInstructionMessage(i,t)){if(!this.updateExchangeById(n,s,!0))return;s=n.request_id||s}});a(this,"updateConversationTitle",async()=>{const{responseText:e}=await this.sendSummaryExchange();this.update({name:e})});a(this,"sendSummaryExchange",()=>{const e={status:P.sent,request_message:"Please provide a clear and concise summary of our conversation so far. The summary must be less than 6 words long. The summary must contain the key points of the conversation. The summary must be in the form of a title which will represent the conversation. The response should not include any additional formatting such as wrapping the response with quotation marks.",model_id:this.selectedModelId??void 0,chatItemType:Ue.summaryTitle,disableRetrieval:!0,disableSelectedCodeDetails:!0};return this.sendSilentExchange(e)});a(this,"generateCommitMessage",async()=>{let e=`temp-fe-${crypto.randomUUID()}`;const t={status:P.sent,request_id:e,request_message:"Please generate a commit message based on the diff of my staged and unstaged changes.",model_id:this.selectedModelId??void 0,mentioned_items:[],seen_state:Ne.unseen,chatItemType:Ue.generateCommitMessage,disableSelectedCodeDetails:!0,disableHistory:!0,timestamp:new Date().toISOString()};this.addExchange(t);for await(const s of this._extensionClient.generateCommitMessage()){if(!this.updateExchangeById(s,e,!0))return;e=s.request_id||e}});a(this,"sendExchange",async e=>{var n;this.updateLastInteraction();let t=`temp-fe-${crypto.randomUUID()}`,s=this._chatFlagModel.isModelIdValid(e.model_id)?e.model_id:void 0;e=await this._addIdeStateNode(Mt(e));const i={status:P.sent,request_id:t,request_message:e.request_message,rich_text_json_repr:e.rich_text_json_repr,model_id:s,mentioned_items:e.mentioned_items,structured_output_nodes:e.structured_output_nodes,seen_state:Ne.unseen,chatItemType:e.chatItemType,disableSelectedCodeDetails:e.disableSelectedCodeDetails,disableHistory:e.disableHistory,structured_request_nodes:e.structured_request_nodes,timestamp:new Date().toISOString()};this.addExchange(i),this._loadContextFromExchange(i),this._onSendExchangeListeners.forEach(o=>o(i));for await(const o of this.sendUserMessage(t,i,!1)){if(((n=this.exchangeWithRequestId(t))==null?void 0:n.status)!==P.sent||!this.updateExchangeById(o,t,!0))return;t=o.request_id||t}});a(this,"sendSuggestedQuestion",e=>{this.sendExchange({request_message:e,status:P.draft}),this._extensionClient.triggerUsedChatMetric(),this._extensionClient.reportWebviewClientEvent(ms.chatUseSuggestedQuestion)});a(this,"recoverAllExchanges",async()=>{await Promise.all(this.recoverableExchanges.map(this.recoverExchange))});a(this,"recoverExchange",async e=>{var i;if(!e.request_id||e.status!==P.sent)return;let t=e.request_id;const s=(i=e.structured_output_nodes)==null?void 0:i.filter(n=>n.type===ae.AGENT_MEMORY);this.updateExchangeById({...e,response_text:"",structured_output_nodes:s??[]},t);for await(const n of this.getChatStream(e)){if(!this.updateExchangeById(n,t,!0))return;t=n.request_id||t}});a(this,"_loadContextFromConversation",e=>{e.chatHistory.forEach(t=>{Y(t)&&this._loadContextFromExchange(t)})});a(this,"_loadContextFromExchange",e=>{e.mentioned_items&&(this._specialContextInputModel.updateItems(e.mentioned_items,[]),this._specialContextInputModel.markItemsActive(e.mentioned_items))});a(this,"_unloadContextFromConversation",e=>{e.chatHistory.forEach(t=>{Y(t)&&this._unloadContextFromExchange(t)})});a(this,"_unloadContextFromExchange",e=>{e.mentioned_items&&this._specialContextInputModel.updateItems([],e.mentioned_items)});a(this,"updateLastInteraction",()=>{this.update({lastInteractedAtIso:new Date().toISOString()})});a(this,"_jsonToStructuredRequest",e=>{const t=[],s=n=>{var c;const o=t.at(-1);if((o==null?void 0:o.type)===re.TEXT){const h=((c=o.text_node)==null?void 0:c.content)??"",_={...o,text_node:{content:h+n}};t[t.length-1]=_}else t.push({id:t.length,type:re.TEXT,text_node:{content:n}})},i=n=>{var o,c,h,_;if(n.type==="doc"||n.type==="paragraph")for(const y of n.content??[])i(y);else if(n.type==="hardBreak")s(`
`);else if(n.type==="text")s(n.text??"");else if(n.type==="image"){if(typeof((o=n.attrs)==null?void 0:o.src)!="string")return void console.error("Image source is not a string: ",(c=n.attrs)==null?void 0:c.src);if(n.attrs.isLoading)return;const y=(h=n.attrs)==null?void 0:h.title,f=this._fileNameToImageFormat(y);t.push({id:t.length,type:re.IMAGE_ID,image_id_node:{image_id:n.attrs.src,format:f}})}else if(n.type==="mention"){const y=(_=n.attrs)==null?void 0:_.data;y&&$t(y)?t.push({id:t.length,type:re.TEXT,text_node:{content:Es(this._chatFlagModel,y.personality.type)}}):s(`@${y==null?void 0:y.name}`)}};return i(e),t});this._extensionClient=e,this._chatFlagModel=t,this._specialContextInputModel=s,this._saveConversation=i,this._state={...w.create()},this._totalCharactersStore=this._createTotalCharactersStore()}_createTotalCharactersStore(){return ws(()=>{let e=0;const t=this._state.chatHistory;return this._convertHistoryToExchanges(t).forEach(s=>{e+=JSON.stringify(s).length}),this._state.draftExchange&&(e+=JSON.stringify(this._state.draftExchange).length),e},0,this._totalCharactersCacheThrottleMs)}async decidePersonaType(){var e;try{return(((e=(await this._extensionClient.getWorkspaceInfo()).trackedFileCount)==null?void 0:e.reduce((s,i)=>s+i,0))||0)<=4?$.PROTOTYPER:$.DEFAULT}catch(t){return console.error("Error determining persona type:",t),$.DEFAULT}}static create(e={}){const t=new Date().toISOString();return{id:crypto.randomUUID(),name:void 0,createdAtIso:t,lastInteractedAtIso:t,chatHistory:[],feedbackStates:{},toolUseStates:{},draftExchange:void 0,draftActiveContextIds:void 0,selectedModelId:void 0,requestIds:[],isPinned:!1,lastUrl:void 0,isShareable:!1,extraData:{},personaType:$.DEFAULT,...e}}static toSentenceCase(e){return e.charAt(0).toUpperCase()+e.slice(1)}static getDisplayName(e){var i;const t=this._filterToExchanges(e);let s;return s=Cs(e)?"Autofix Chat":De(e)?"New Agent":"New Chat",w.toSentenceCase(e.name||((i=t[0])==null?void 0:i.request_message)||s)}static _filterToExchanges(e){return e.chatHistory.filter(t=>Y(t))}static isEmpty(e){var t;return e.chatHistory.filter(s=>Y(s)).length===0&&!((t=e.draftExchange)!=null&&t.request_message)}static isNamed(e){return e.name!==void 0&&e.name!==""}static getTime(e,t){return t==="lastMessageTimestamp"?w.lastMessageTimestamp(e):t==="lastInteractedAt"?w.lastInteractedAt(e):w.createdAt(e)}static createdAt(e){return new Date(e.createdAtIso)}static lastInteractedAt(e){return new Date(e.lastInteractedAtIso)}static lastMessageTimestamp(e){const t=this._filterToExchanges(e);if(t.length===0)return this.createdAt(e);const s=t[t.length-1];return s.timestamp?new Date(s.timestamp):this.createdAt(e)}static isValid(e){return e.id!==void 0&&(!w.isEmpty(e)||w.isNamed(e))}onBeforeChangeConversation(e){return this._onBeforeChangeConversationListeners.push(e),()=>{this._onBeforeChangeConversationListeners=this._onBeforeChangeConversationListeners.filter(t=>t!==e)}}_notifyBeforeChangeConversation(e,t){let s=t;for(const i of this._onBeforeChangeConversationListeners){const n=i(e,s);n!==void 0&&(s=n)}return s}get extraData(){return this._state.extraData}set extraData(e){this.update({extraData:e})}get focusModel(){return this._focusModel}get isValid(){return w.isValid(this._state)}get id(){return this._state.id}get name(){return this._state.name}get personaType(){return this._state.personaType??$.DEFAULT}get displayName(){return w.getDisplayName(this._state)}get createdAtIso(){return this._state.createdAtIso}get createdAt(){return w.createdAt(this._state)}get chatHistory(){return this._state.chatHistory}get feedbackStates(){return this._state.feedbackStates}get toolUseStates(){return this._state.toolUseStates}get draftExchange(){return this._state.draftExchange}get selectedModelId(){return this._state.selectedModelId}get isPinned(){return!!this._state.isPinned}get extensionClient(){return this._extensionClient}addChatItem(e){this.addExchange(e)}get requestIds(){return this._state.chatHistory.map(e=>e.request_id).filter(e=>e!==void 0)}get hasDraft(){var s;const e=(((s=this.draftExchange)==null?void 0:s.request_message)??"").trim()!=="",t=this.hasImagesInDraft();return e||t}hasImagesInDraft(){var s;const e=(s=this.draftExchange)==null?void 0:s.rich_text_json_repr;if(!e)return!1;const t=i=>Array.isArray(i)?i.some(t):!!i&&(i.type==="image"||!(!i.content||!Array.isArray(i.content))&&i.content.some(t));return t(e)}get canSendDraft(){return this.hasDraft&&!this.awaitingReply}get canCancelMessage(){return this.awaitingReply}get firstExchange(){const e=w._filterToExchanges(this);return e.length===0?null:e[0]}get lastExchange(){const e=w._filterToExchanges(this);return e.length===0?null:e[e.length-1]}get canClearHistory(){return this._state.chatHistory.length!==0&&!this.awaitingReply}get recoverableExchanges(){return this._state.chatHistory.filter(e=>Y(e)&&e.status===P.sent)}get successfulMessages(){return this._state.chatHistory.filter(e=>Fe(e)||We(e))}get totalCharactersStore(){return this._totalCharactersStore}_convertHistoryToExchanges(e){if(e.length===0)return[];const t=[];for(const s of e)if(Fe(s))t.push(Ds(s));else if(We(s)&&s.fromTimestamp!==void 0&&s.toTimestamp!==void 0&&s.revertTarget){const i=Fs(s,1),n={request_message:"",response_text:"",request_id:s.request_id||crypto.randomUUID(),request_nodes:[i],response_nodes:[]};t.push(n)}return t}get awaitingReply(){return this.lastExchange!==null&&this.lastExchange.status===P.sent}get lastInteractedAtIso(){return this._state.lastInteractedAtIso}get draftActiveContextIds(){return this._state.draftActiveContextIds}async sendSilentExchange(e){const t=crypto.randomUUID();let s,i="";const n=await this._addIdeStateNode(Mt({...e,request_id:t,status:P.sent,timestamp:new Date().toISOString()}));for await(const o of this.sendUserMessage(t,n,!0))o.response_text&&(i+=o.response_text),o.request_id&&(s=o.request_id);return{responseText:i,requestId:s}}async*getChatStream(e){e.request_id&&(yield*this._extensionClient.getExistingChatStream(e,{flags:this._chatFlagModel}))}async*sendUserMessage(e,t,s){var y;const i=this._specialContextInputModel.chatActiveContext;let n=this.successfulMessages;if(t.chatItemType===Ue.summaryTitle){const f=n.findIndex(N=>N.chatItemType!==Ue.agentOnboarding&&lt(N));f!==-1&&(n=n.slice(f))}const o=t.disableHistory===!0?[]:n;let c=this._convertHistoryToExchanges(o),h=this.personaType;if(t.structured_request_nodes){const f=t.structured_request_nodes.find(N=>N.type===re.CHANGE_PERSONALITY);f&&f.change_personality_node&&(h=f.change_personality_node.personality_type)}const _={text:t.request_message,chatHistory:c,silent:s,modelId:t.model_id,context:i,userSpecifiedFiles:i.userSpecifiedFiles,externalSourceIds:(y=i.externalSources)==null?void 0:y.map(f=>f.id),disableRetrieval:t.disableRetrieval??!1,disableSelectedCodeDetails:t.disableSelectedCodeDetails??!1,nodes:t.structured_request_nodes,memoriesInfo:t.memoriesInfo,personaType:h,conversationId:this.id};yield*this._extensionClient.startChatStreamWithRetry(e,_,{flags:this._chatFlagModel})}onSendExchange(e){return this._onSendExchangeListeners.push(e),()=>{this._onSendExchangeListeners=this._onSendExchangeListeners.filter(t=>t!==e)}}onNewConversation(e){return this._onNewConversationListeners.push(e),()=>{this._onNewConversationListeners=this._onNewConversationListeners.filter(t=>t!==e)}}onHistoryDelete(e){return this._onHistoryDeleteListeners.push(e),()=>{this._onHistoryDeleteListeners=this._onHistoryDeleteListeners.filter(t=>t!==e)}}updateChatItem(e,t){return this.chatHistory.find(s=>s.request_id===e)===null?(console.warn("No exchange with this request ID found."),!1):(this.update({chatHistory:this.chatHistory.map(s=>s.request_id===e?{...s,...t}:s)}),!0)}_fileNameToImageFormat(e){var s;switch((s=e.split(".").at(-1))==null?void 0:s.toLowerCase()){case"jpeg":case"jpg":return ke.JPEG;case"png":return ke.PNG;case"gif":return ke.GIF;case"webp":return ke.WEBP;default:return ke.IMAGE_FORMAT_UNSPECIFIED}}async _addIdeStateNode(e){let t=(e.structured_request_nodes??[]).filter(i=>i.type!==re.IDE_STATE);const s=await this._extensionClient.getChatRequestIdeState();return s?(t=[...t,{id:Jt(t)+1,type:re.IDE_STATE,ide_state_node:s}],{...e,structured_request_nodes:t}):e}}function Fs(l,e){const t=(We(l),l.fromTimestamp),s=(We(l),l.toTimestamp),i=We(l)&&l.revertTarget!==void 0;return{id:e,type:re.CHECKPOINT_REF,checkpoint_ref_node:{request_id:l.request_id||"",from_timestamp:t,to_timestamp:s,source:i?_s.CHECKPOINT_REVERT:void 0}}}function Ds(l){const e=(l.structured_output_nodes??[]).filter(t=>t.type===ae.RAW_RESPONSE||t.type===ae.TOOL_USE);return{request_message:l.request_message,response_text:l.response_text??"",request_id:l.request_id||"",request_nodes:l.structured_request_nodes??[],response_nodes:e}}function Jt(l){return l.length>0?Math.max(...l.map(e=>e.id)):0}function Mt(l){var e;if(l.request_message.length>0&&!((e=l.structured_request_nodes)!=null&&e.some(t=>t.type===re.TEXT))){let t=l.structured_request_nodes??[];return t=[...t,{id:Jt(t)+1,type:re.TEXT,text_node:{content:l.request_message}}],{...l,structured_request_nodes:t}}return l}class Rs{constructor(e=!0,t=setTimeout){a(this,"_notify",new Set);a(this,"_clearTimeout",e=>{e.timeoutId&&clearTimeout(e.timeoutId)});a(this,"_schedule",e=>{if(!this._started||e.date&&(e.timeout=e.date.getTime()-Date.now(),e.timeout<0))return;const t=this._setTimeout;e.timeoutId=t(this._handle,e.timeout,e)});a(this,"_handle",e=>{e.notify(),e.date?this._notify.delete(e):e.once||this._schedule(e)});a(this,"dispose",()=>{this._notify.forEach(this._clearTimeout),this._notify.clear()});this._started=e,this._setTimeout=t}start(){return this._started||(this._started=!0,this._notify.forEach(this._schedule)),this}stop(){return this._started=!1,this._notify.forEach(this._clearTimeout),this}get isStarted(){return this._started}set isStarted(e){e?this.start():this.stop()}once(e,t){return this._register(e,t,!0)}interval(e,t){return this._register(e,t,!1)}at(e,t){return this._register(0,t,!1,typeof e=="number"?new Date(Date.now()+e):e)}reschedule(){this._notify.forEach(e=>{this._clearTimeout(e),this._schedule(e)})}_register(e,t,s,i){if(!e&&!i)return()=>{};const n={timeout:e,notify:t,once:s,date:i};return this._notify.add(n),this._schedule(n),()=>{this._clearTimeout(n),this._notify.delete(n)}}}class qs{constructor(e=0,t=0,s=new Rs,i=Ie("busy"),n=Ie(!1)){a(this,"unsubNotify");a(this,"unsubMessage");a(this,"activity",()=>{this.idleStatus.set("busy"),this.idleScheduler.reschedule()});a(this,"focus",e=>{this.focusAfterIdle.set(e)});this._idleNotifyTimeout=e,this._idleMessageTimeout=t,this.idleScheduler=s,this.idleStatus=i,this.focusAfterIdle=n,this.idleNotifyTimeout=e,this.idleMessageTimeout=t}set idleMessageTimeout(e){var t;this._idleMessageTimeout!==e&&(this._idleMessageTimeout=e,(t=this.unsubMessage)==null||t.call(this),this.unsubMessage=this.idleScheduler.once(e,()=>{this.idleStatus.set("idle-message")}))}set idleNotifyTimeout(e){var t;this._idleNotifyTimeout!==e&&(this._idleNotifyTimeout=e,(t=this.unsubNotify)==null||t.call(this),this.unsubNotify=this.idleScheduler.once(e,()=>{this.idleStatus.set("idle-notify")}))}get idleMessageTimeout(){return this._idleMessageTimeout}get idleNotifyTimeout(){return this._idleNotifyTimeout}get notifyEnabled(){return this._idleNotifyTimeout>0}get messageEnabled(){return this._idleMessageTimeout>0}dispose(){var e,t;(e=this.unsubNotify)==null||e.call(this),(t=this.unsubMessage)==null||t.call(this),this.idleScheduler.dispose(),this.idleStatus.set("busy"),this.focusAfterIdle.set(!1)}}const Ze=Ie("idle");var Ps=(l=>(l.manual="manual",l.auto="auto",l))(Ps||{});class _i{constructor(e,t,s,i={}){a(this,"_state",{currentConversationId:void 0,conversations:{},agentExecutionMode:"manual",isAgentEditsCollapsed:!0});a(this,"extensionClient");a(this,"_chatFlagsModel");a(this,"_currConversationModel");a(this,"subscribers",new Set);a(this,"idleMessageModel",new qs);a(this,"isAgentEditsCollapsed");a(this,"agentExecutionMode");a(this,"sortConversationsBy");a(this,"onLoaded",async()=>{var t,s;const e=await this.extensionClient.getChatInitData();this._chatFlagsModel.update({enableEditableHistory:e.enableEditableHistory??!1,enablePreferenceCollection:e.enablePreferenceCollection??!1,enableRetrievalDataCollection:e.enableRetrievalDataCollection??!1,enableDebugFeatures:e.enableDebugFeatures??!1,enableRichTextHistory:e.useRichTextHistory??!0,modelDisplayNameToId:e.modelDisplayNameToId??{},fullFeatured:e.fullFeatured??!0,isRemoteAgentWindow:!1,remoteAgentId:void 0,smallSyncThreshold:e.smallSyncThreshold??15,bigSyncThreshold:e.bigSyncThreshold??1e3,enableExternalSourcesInChat:e.enableExternalSourcesInChat??!1,enableSmartPaste:e.enableSmartPaste??!1,enableDirectApply:e.enableDirectApply??!1,summaryTitles:e.summaryTitles??!1,suggestedEditsAvailable:e.suggestedEditsAvailable??!1,enableShareService:e.enableShareService??!1,maxTrackableFileCount:e.maxTrackableFileCount??Yt,enableDesignSystemRichTextEditor:e.enableDesignSystemRichTextEditor??!1,enableSources:e.enableSources??!1,enableChatMermaidDiagrams:e.enableChatMermaidDiagrams??!1,smartPastePrecomputeMode:e.smartPastePrecomputeMode??Ot.visibleHover,useNewThreadsMenu:e.useNewThreadsMenu??!1,enableChatMermaidDiagramsMinVersion:e.enableChatMermaidDiagramsMinVersion??!1,idleNewSessionMessageTimeoutMs:e.idleNewSessionMessageTimeoutMs,idleNewSessionNotificationTimeoutMs:e.idleNewSessionNotificationTimeoutMs,enableChatMultimodal:e.enableChatMultimodal??!1,enableAgentMode:e.enableAgentMode??!1,agentMemoriesFilePathName:e.agentMemoriesFilePathName,enableRichCheckpointInfo:e.enableRichCheckpointInfo??!1,userTier:e.userTier??"unknown",truncateChatHistory:e.truncateChatHistory??!1,enableBackgroundAgents:e.enableBackgroundAgents??!1,enableVirtualizedMessageList:e.enableVirtualizedMessageList??!1,customPersonalityPrompts:e.customPersonalityPrompts??{},enablePersonalities:e.enablePersonalities??!1,memoryClassificationOnFirstToken:e.memoryClassificationOnFirstToken??!1,enableGenerateCommitMessage:e.enableGenerateCommitMessage??!1,enablePromptEnhancer:e.enablePromptEnhancer??!1,modelRegistry:e.modelRegistry??{},enableModelRegistry:e.enableModelRegistry??!1}),(s=(t=this.options).onLoaded)==null||s.call(t),this.notifySubscribers(),(e.enableBackgroundAgents??!1)&&this.extensionClient.getRemoteAgentStatus().then(i=>{this._chatFlagsModel.update({isRemoteAgentWindow:i.isRemoteAgentWindow,remoteAgentId:i.remoteAgentId}),this.notifySubscribers()})});a(this,"subscribe",e=>(this.subscribers.add(e),e(this),()=>{this.subscribers.delete(e)}));a(this,"initialize",e=>{this._state={...this._state,...this._host.getState()},e&&(this._state.conversations[e==null?void 0:e.id]=e),this._chatFlagsModel.fullFeatured&&((e==null?void 0:e.id)!==at&&this.currentConversationId!==at||(delete this._state.conversations[at],this.setCurrentConversationToWelcome())),this._chatFlagsModel.subscribe(t=>{this.idleMessageModel.idleNotifyTimeout=t.idleNewSessionNotificationTimeoutMs,this.idleMessageModel.idleMessageTimeout=t.idleNewSessionMessageTimeoutMs}),this._state.conversations=Object.fromEntries(Object.entries(this._state.conversations).filter(([t,s])=>w.isValid(s))),this.initializeIsShareableState(),e?this.setCurrentConversation(e.id):this.setCurrentConversation(this.currentConversationId),this.subscribe(()=>this.idleMessageModel.activity()),this.setState(this._state)});a(this,"initializeIsShareableState",()=>{const e={...this._state.conversations};for(const[t,s]of Object.entries(e)){if(s.isShareable)continue;const i=s.chatHistory.some(n=>Fe(n));e[t]={...s,isShareable:i}}this._state.conversations=e});a(this,"updateChatState",e=>{this._state={...this._state,...e};const t=this._state.conversations,s=new Set;for(const[i,n]of Object.entries(t))n.isPinned&&s.add(i);this.setState(this._state),this.notifySubscribers()});a(this,"saveImmediate",()=>{this._host.setState(this._state)});a(this,"setState",ss(e=>{this._host.setState({...e,isAgentEditsCollapsed:Be(this.isAgentEditsCollapsed),agentExecutionMode:Be(this.agentExecutionMode),sortConversationsBy:Be(this.sortConversationsBy)})},1e3,{maxWait:15e3}));a(this,"notifySubscribers",()=>{this.subscribers.forEach(e=>e(this))});a(this,"withWebviewClientEvent",(e,t)=>(...s)=>(this.extensionClient.reportWebviewClientEvent(e),t(...s)));a(this,"setCurrentConversationToWelcome",()=>{this.setCurrentConversation(),this._currConversationModel.setName("Welcome to Augment"),this._currConversationModel.addChatItem({chatItemType:Ue.educateFeatures,request_id:crypto.randomUUID(),seen_state:Ne.seen})});a(this,"popCurrentConversation",async()=>{var t,s;const e=this.currentConversationId;e&&await this.deleteConversation(e,((t=this.nextConversation)==null?void 0:t.id)??((s=this.previousConversation)==null?void 0:s.id))});a(this,"setCurrentConversation",async(e,t=!0)=>{let s;e===void 0?(this.deleteInvalidConversations(De(this._currConversationModel)?"agent":"chat"),s=w.create({personaType:await this._currConversationModel.decidePersonaType()})):s=this._state.conversations[e]??w.create({personaType:await this._currConversationModel.decidePersonaType()});const i=this.conversations[this._currConversationModel.id]===void 0;this._currConversationModel.setConversation(s,!i,t),this._currConversationModel.recoverAllExchanges(),this._currConversationModel.resetTotalCharactersCache()});a(this,"saveConversation",e=>{this.updateChatState({conversations:{...this._state.conversations,[e.id]:e},currentConversationId:e.id})});a(this,"isConversationShareable",e=>{var t;return((t=this._state.conversations[e])==null?void 0:t.isShareable)??!0});a(this,"setSortConversationsBy",e=>{this.sortConversationsBy.set(e),this.updateChatState({})});a(this,"getConversationUrl",async e=>{const t=this._state.conversations[e];if(t.lastUrl)return t.lastUrl;Ze.set("copying");const s=t==null?void 0:t.chatHistory,i=s.reduce((c,h)=>(Fe(h)&&c.push({request_id:h.request_id||"",request_message:h.request_message,response_text:h.response_text||""}),c),[]);if(i.length===0)throw new Error("No chat history to share");const n=w.getDisplayName(t),o=await this.extensionClient.saveChat(e,i,n);if(o.data){let c=o.data.url;return this.updateChatState({conversations:{...this._state.conversations,[e]:{...t,lastUrl:c}}}),c}throw new Error("Failed to create URL")});a(this,"shareConversation",async e=>{if(e!==void 0)try{const t=await this.getConversationUrl(e);if(!t)return void Ze.set("idle");navigator.clipboard.writeText(t),Ze.set("copied")}catch{Ze.set("failed")}});a(this,"deleteConversations",async(e,t=void 0)=>{if(await this.extensionClient.openConfirmationModal({title:"Delete Conversation",message:`Are you sure you want to delete ${e.length>1?"these conversations":"this conversation"}?`,confirmButtonText:"Delete",cancelButtonText:"Cancel"})){const s=new Set(e);this.deleteConversationIds(s),this.currentConversationId&&s.has(this.currentConversationId)&&this.setCurrentConversation(t)}});a(this,"deleteConversation",async(e,t=void 0)=>{await this.deleteConversations([e],t)});a(this,"deleteConversationIds",async e=>{var s;const t=[];for(const i of e){const n=((s=this._state.conversations[i])==null?void 0:s.requestIds)??[];t.push(...n)}for(const i of Object.values(this._state.conversations))if(e.has(i.id)){for(const o of i.chatHistory)Y(o)&&this.deleteImagesInExchange(o);const n=i.draftExchange;n&&this.deleteImagesInExchange(n)}this.updateChatState({conversations:Object.fromEntries(Object.entries(this._state.conversations).filter(([i])=>!e.has(i)))}),this.extensionClient.clearMetadataFor({requestIds:t,conversationIds:Array.from(e)})});a(this,"deleteImagesInExchange",e=>{const t=new Set([...e.rich_text_json_repr?this.findImagesInJson(e.rich_text_json_repr):[],...e.structured_request_nodes?this.findImagesInStructuredRequest(e.structured_request_nodes):[]]);for(const s of t)this.deleteImage(s)});a(this,"findImagesInJson",e=>{const t=[],s=i=>{var n;if(i.type==="image"&&((n=i.attrs)!=null&&n.src))t.push(i.attrs.src);else if((i.type==="doc"||i.type==="paragraph")&&i.content)for(const o of i.content)s(o)};return s(e),t});a(this,"findImagesInStructuredRequest",e=>e.reduce((t,s)=>(s.type===re.IMAGE_ID&&s.image_id_node&&t.push(s.image_id_node.image_id),t),[]));a(this,"toggleConversationPinned",e=>{const t=this._state.conversations[e],s={...t,isPinned:!t.isPinned};this.updateChatState({conversations:{...this._state.conversations,[e]:s}}),e===this.currentConversationId&&this._currConversationModel.toggleIsPinned()});a(this,"renameConversation",(e,t)=>{const s={...this._state.conversations[e],name:t};this.updateChatState({conversations:{...this._state.conversations,[e]:s}}),e===this.currentConversationId&&this._currConversationModel.setName(t)});a(this,"smartPaste",(e,t,s,i)=>{const n=this._currConversationModel.historyTo(e,!0).filter(o=>Fe(o)).map(o=>({request_message:o.request_message,response_text:o.response_text||"",request_id:o.request_id||""}));this.extensionClient.smartPaste({generatedCode:t,chatHistory:n,targetFile:s??void 0,options:i})});a(this,"saveImage",async e=>await this.extensionClient.saveImage(e));a(this,"deleteImage",async e=>await this.extensionClient.deleteImage(e));a(this,"renderImage",async e=>await this.extensionClient.loadImage(e));this._asyncMsgSender=e,this._host=t,this._specialContextInputModel=s,this.options=i,this._chatFlagsModel=new xs(i.initialFlags),this.extensionClient=new rs(this._host,this._asyncMsgSender,this._chatFlagsModel),this._currConversationModel=new w(this.extensionClient,this._chatFlagsModel,this._specialContextInputModel,this.saveConversation),this.initialize(i.initialConversation),this.isAgentEditsCollapsed=Ie(this._state.isAgentEditsCollapsed),this.agentExecutionMode=Ie(this._state.agentExecutionMode??"manual"),this.sortConversationsBy=Ie(this._state.sortConversationsBy??"lastMessageTimestamp"),this.onLoaded()}get flags(){return this._chatFlagsModel}get specialContextInputModel(){return this._specialContextInputModel}get currentConversationId(){return this._state.currentConversationId}get currentConversationModel(){return this._currConversationModel}get conversations(){return this._state.conversations}orderedConversations(e,t="desc",s){const i=e||this._state.sortConversationsBy||"lastMessageTimestamp";let n=Object.values(this._state.conversations);return s&&(n=n.filter(s)),n.sort((o,c)=>{const h=w.getTime(o,i).getTime(),_=w.getTime(c,i).getTime();return t==="asc"?h-_:_-h})}get nextConversation(){if(!this.currentConversationId)return;const e=this.orderedConversations(),t=e.findIndex(s=>s.id===this.currentConversationId);return e.length>t+1?e[t+1]:void 0}get previousConversation(){if(!this.currentConversationId)return;const e=this.orderedConversations(),t=e.findIndex(s=>s.id===this.currentConversationId);return t>0?e[t-1]:void 0}get host(){return this._host}deleteInvalidConversations(e="all"){const t=Object.keys(this.conversations).filter(s=>{const i=!w.isValid(this.conversations[s]),n=De(this.conversations[s]);return i&&(e==="agent"&&n||e==="chat"&&!n||e==="all")});t.length&&this.deleteConversationIds(new Set(t))}get lastMessageTimestamp(){const e=this.currentConversationModel.lastExchange;return e==null?void 0:e.timestamp}handleMessageFromExtension(e){return e.data.type===Ce.newThread&&(this.setCurrentConversation(),!0)}}const Ee=typeof performance=="object"&&performance&&typeof performance.now=="function"?performance:Date,Et=new Set,ht=typeof process=="object"&&process?process:{},Xt=(l,e,t,s)=>{typeof ht.emitWarning=="function"?ht.emitWarning(l,e,t,s):console.error(`[${t}] ${e}: ${l}`)};let st=globalThis.AbortController,At=globalThis.AbortSignal;var Rt;if(st===void 0){At=class{constructor(){a(this,"onabort");a(this,"_onabort",[]);a(this,"reason");a(this,"aborted",!1)}addEventListener(t,s){this._onabort.push(s)}},st=class{constructor(){a(this,"signal",new At);e()}abort(t){var s,i;if(!this.signal.aborted){this.signal.reason=t,this.signal.aborted=!0;for(const n of this.signal._onabort)n(t);(i=(s=this.signal).onabort)==null||i.call(s,t)}}};let l=((Rt=ht.env)==null?void 0:Rt.LRU_CACHE_IGNORE_AC_WARNING)!=="1";const e=()=>{l&&(l=!1,Xt("AbortController is not defined. If using lru-cache in node 14, load an AbortController polyfill from the `node-abort-controller` package. A minimal polyfill is provided for use by LRUCache.fetch(), but it should not be relied upon in other contexts (eg, passing it to other APIs that use AbortController/AbortSignal might have undesirable effects). You may disable this with LRU_CACHE_IGNORE_AC_WARNING=1 in the env.","NO_ABORT_CONTROLLER","ENOTSUP",e))}}const ge=l=>l&&l===Math.floor(l)&&l>0&&isFinite(l),Kt=l=>ge(l)?l<=Math.pow(2,8)?Uint8Array:l<=Math.pow(2,16)?Uint16Array:l<=Math.pow(2,32)?Uint32Array:l<=Number.MAX_SAFE_INTEGER?Je:null:null;class Je extends Array{constructor(e){super(e),this.fill(0)}}var Re;const Se=class Se{constructor(e,t){a(this,"heap");a(this,"length");if(!r(Se,Re))throw new TypeError("instantiate Stack using Stack.create(n)");this.heap=new t(e),this.length=0}static create(e){const t=Kt(e);if(!t)return[];m(Se,Re,!0);const s=new Se(e,t);return m(Se,Re,!1),s}push(e){this.heap[this.length++]=e}pop(){return this.heap[--this.length]}};Re=new WeakMap,v(Se,Re,!1);let ct=Se;var qt,Pt,X,W,K,Q,qe,Pe,E,ee,M,S,b,U,G,O,F,te,D,se,ie,j,ne,be,z,d,ut,xe,de,Ge,V,Qt,we,Oe,je,me,_e,gt,Xe,Ke,C,mt,ze,fe,_t;const ft=class ft{constructor(e){v(this,d);v(this,X);v(this,W);v(this,K);v(this,Q);v(this,qe);v(this,Pe);a(this,"ttl");a(this,"ttlResolution");a(this,"ttlAutopurge");a(this,"updateAgeOnGet");a(this,"updateAgeOnHas");a(this,"allowStale");a(this,"noDisposeOnSet");a(this,"noUpdateTTL");a(this,"maxEntrySize");a(this,"sizeCalculation");a(this,"noDeleteOnFetchRejection");a(this,"noDeleteOnStaleGet");a(this,"allowStaleOnFetchAbort");a(this,"allowStaleOnFetchRejection");a(this,"ignoreFetchAbort");v(this,E);v(this,ee);v(this,M);v(this,S);v(this,b);v(this,U);v(this,G);v(this,O);v(this,F);v(this,te);v(this,D);v(this,se);v(this,ie);v(this,j);v(this,ne);v(this,be);v(this,z);v(this,xe,()=>{});v(this,de,()=>{});v(this,Ge,()=>{});v(this,V,()=>!1);v(this,we,e=>{});v(this,Oe,(e,t,s)=>{});v(this,je,(e,t,s,i)=>{if(s||i)throw new TypeError("cannot set size without setting maxSize or maxEntrySize on cache");return 0});a(this,qt,"LRUCache");const{max:t=0,ttl:s,ttlResolution:i=1,ttlAutopurge:n,updateAgeOnGet:o,updateAgeOnHas:c,allowStale:h,dispose:_,disposeAfter:y,noDisposeOnSet:f,noUpdateTTL:N,maxSize:I=0,maxEntrySize:B=0,sizeCalculation:q,fetchMethod:A,memoMethod:p,noDeleteOnFetchRejection:T,noDeleteOnStaleGet:Ve,allowStaleOnFetchRejection:oe,allowStaleOnFetchAbort:J,ignoreFetchAbort:Le}=e;if(t!==0&&!ge(t))throw new TypeError("max option must be a nonnegative integer");const ue=t?Kt(t):Array;if(!ue)throw new Error("invalid max value: "+t);if(m(this,X,t),m(this,W,I),this.maxEntrySize=B||r(this,W),this.sizeCalculation=q,this.sizeCalculation){if(!r(this,W)&&!this.maxEntrySize)throw new TypeError("cannot set sizeCalculation without setting maxSize or maxEntrySize");if(typeof this.sizeCalculation!="function")throw new TypeError("sizeCalculation set to non-function")}if(p!==void 0&&typeof p!="function")throw new TypeError("memoMethod must be a function if defined");if(m(this,Pe,p),A!==void 0&&typeof A!="function")throw new TypeError("fetchMethod must be a function if specified");if(m(this,qe,A),m(this,be,!!A),m(this,M,new Map),m(this,S,new Array(t).fill(void 0)),m(this,b,new Array(t).fill(void 0)),m(this,U,new ue(t)),m(this,G,new ue(t)),m(this,O,0),m(this,F,0),m(this,te,ct.create(t)),m(this,E,0),m(this,ee,0),typeof _=="function"&&m(this,K,_),typeof y=="function"?(m(this,Q,y),m(this,D,[])):(m(this,Q,void 0),m(this,D,void 0)),m(this,ne,!!r(this,K)),m(this,z,!!r(this,Q)),this.noDisposeOnSet=!!f,this.noUpdateTTL=!!N,this.noDeleteOnFetchRejection=!!T,this.allowStaleOnFetchRejection=!!oe,this.allowStaleOnFetchAbort=!!J,this.ignoreFetchAbort=!!Le,this.maxEntrySize!==0){if(r(this,W)!==0&&!ge(r(this,W)))throw new TypeError("maxSize must be a positive integer if specified");if(!ge(this.maxEntrySize))throw new TypeError("maxEntrySize must be a positive integer if specified");u(this,d,Qt).call(this)}if(this.allowStale=!!h,this.noDeleteOnStaleGet=!!Ve,this.updateAgeOnGet=!!o,this.updateAgeOnHas=!!c,this.ttlResolution=ge(i)||i===0?i:1,this.ttlAutopurge=!!n,this.ttl=s||0,this.ttl){if(!ge(this.ttl))throw new TypeError("ttl must be a positive integer if specified");u(this,d,ut).call(this)}if(r(this,X)===0&&this.ttl===0&&r(this,W)===0)throw new TypeError("At least one of max, maxSize, or ttl is required");if(!this.ttlAutopurge&&!r(this,X)&&!r(this,W)){const Te="LRU_CACHE_UNBOUNDED";($e=>!Et.has($e))(Te)&&(Et.add(Te),Xt("TTL caching without ttlAutopurge, max, or maxSize can result in unbounded memory consumption.","UnboundedCacheWarning",Te,ft))}}static unsafeExposeInternals(e){return{starts:r(e,ie),ttls:r(e,j),sizes:r(e,se),keyMap:r(e,M),keyList:r(e,S),valList:r(e,b),next:r(e,U),prev:r(e,G),get head(){return r(e,O)},get tail(){return r(e,F)},free:r(e,te),isBackgroundFetch:t=>{var s;return u(s=e,d,C).call(s,t)},backgroundFetch:(t,s,i,n)=>{var o;return u(o=e,d,Ke).call(o,t,s,i,n)},moveToTail:t=>{var s;return u(s=e,d,ze).call(s,t)},indexes:t=>{var s;return u(s=e,d,me).call(s,t)},rindexes:t=>{var s;return u(s=e,d,_e).call(s,t)},isStale:t=>{var s;return r(s=e,V).call(s,t)}}}get max(){return r(this,X)}get maxSize(){return r(this,W)}get calculatedSize(){return r(this,ee)}get size(){return r(this,E)}get fetchMethod(){return r(this,qe)}get memoMethod(){return r(this,Pe)}get dispose(){return r(this,K)}get disposeAfter(){return r(this,Q)}getRemainingTTL(e){return r(this,M).has(e)?1/0:0}*entries(){for(const e of u(this,d,me).call(this))r(this,b)[e]===void 0||r(this,S)[e]===void 0||u(this,d,C).call(this,r(this,b)[e])||(yield[r(this,S)[e],r(this,b)[e]])}*rentries(){for(const e of u(this,d,_e).call(this))r(this,b)[e]===void 0||r(this,S)[e]===void 0||u(this,d,C).call(this,r(this,b)[e])||(yield[r(this,S)[e],r(this,b)[e]])}*keys(){for(const e of u(this,d,me).call(this)){const t=r(this,S)[e];t===void 0||u(this,d,C).call(this,r(this,b)[e])||(yield t)}}*rkeys(){for(const e of u(this,d,_e).call(this)){const t=r(this,S)[e];t===void 0||u(this,d,C).call(this,r(this,b)[e])||(yield t)}}*values(){for(const e of u(this,d,me).call(this))r(this,b)[e]===void 0||u(this,d,C).call(this,r(this,b)[e])||(yield r(this,b)[e])}*rvalues(){for(const e of u(this,d,_e).call(this))r(this,b)[e]===void 0||u(this,d,C).call(this,r(this,b)[e])||(yield r(this,b)[e])}[(Pt=Symbol.iterator,qt=Symbol.toStringTag,Pt)](){return this.entries()}find(e,t={}){for(const s of u(this,d,me).call(this)){const i=r(this,b)[s],n=u(this,d,C).call(this,i)?i.__staleWhileFetching:i;if(n!==void 0&&e(n,r(this,S)[s],this))return this.get(r(this,S)[s],t)}}forEach(e,t=this){for(const s of u(this,d,me).call(this)){const i=r(this,b)[s],n=u(this,d,C).call(this,i)?i.__staleWhileFetching:i;n!==void 0&&e.call(t,n,r(this,S)[s],this)}}rforEach(e,t=this){for(const s of u(this,d,_e).call(this)){const i=r(this,b)[s],n=u(this,d,C).call(this,i)?i.__staleWhileFetching:i;n!==void 0&&e.call(t,n,r(this,S)[s],this)}}purgeStale(){let e=!1;for(const t of u(this,d,_e).call(this,{allowStale:!0}))r(this,V).call(this,t)&&(u(this,d,fe).call(this,r(this,S)[t],"expire"),e=!0);return e}info(e){const t=r(this,M).get(e);if(t===void 0)return;const s=r(this,b)[t],i=u(this,d,C).call(this,s)?s.__staleWhileFetching:s;if(i===void 0)return;const n={value:i};if(r(this,j)&&r(this,ie)){const o=r(this,j)[t],c=r(this,ie)[t];if(o&&c){const h=o-(Ee.now()-c);n.ttl=h,n.start=Date.now()}}return r(this,se)&&(n.size=r(this,se)[t]),n}dump(){const e=[];for(const t of u(this,d,me).call(this,{allowStale:!0})){const s=r(this,S)[t],i=r(this,b)[t],n=u(this,d,C).call(this,i)?i.__staleWhileFetching:i;if(n===void 0||s===void 0)continue;const o={value:n};if(r(this,j)&&r(this,ie)){o.ttl=r(this,j)[t];const c=Ee.now()-r(this,ie)[t];o.start=Math.floor(Date.now()-c)}r(this,se)&&(o.size=r(this,se)[t]),e.unshift([s,o])}return e}load(e){this.clear();for(const[t,s]of e){if(s.start){const i=Date.now()-s.start;s.start=Ee.now()-i}this.set(t,s.value,s)}}set(e,t,s={}){var N,I,B,q,A;if(t===void 0)return this.delete(e),this;const{ttl:i=this.ttl,start:n,noDisposeOnSet:o=this.noDisposeOnSet,sizeCalculation:c=this.sizeCalculation,status:h}=s;let{noUpdateTTL:_=this.noUpdateTTL}=s;const y=r(this,je).call(this,e,t,s.size||0,c);if(this.maxEntrySize&&y>this.maxEntrySize)return h&&(h.set="miss",h.maxEntrySizeExceeded=!0),u(this,d,fe).call(this,e,"set"),this;let f=r(this,E)===0?void 0:r(this,M).get(e);if(f===void 0)f=r(this,E)===0?r(this,F):r(this,te).length!==0?r(this,te).pop():r(this,E)===r(this,X)?u(this,d,Xe).call(this,!1):r(this,E),r(this,S)[f]=e,r(this,b)[f]=t,r(this,M).set(e,f),r(this,U)[r(this,F)]=f,r(this,G)[f]=r(this,F),m(this,F,f),Ye(this,E)._++,r(this,Oe).call(this,f,y,h),h&&(h.set="add"),_=!1;else{u(this,d,ze).call(this,f);const p=r(this,b)[f];if(t!==p){if(r(this,be)&&u(this,d,C).call(this,p)){p.__abortController.abort(new Error("replaced"));const{__staleWhileFetching:T}=p;T===void 0||o||(r(this,ne)&&((N=r(this,K))==null||N.call(this,T,e,"set")),r(this,z)&&((I=r(this,D))==null||I.push([T,e,"set"])))}else o||(r(this,ne)&&((B=r(this,K))==null||B.call(this,p,e,"set")),r(this,z)&&((q=r(this,D))==null||q.push([p,e,"set"])));if(r(this,we).call(this,f),r(this,Oe).call(this,f,y,h),r(this,b)[f]=t,h){h.set="replace";const T=p&&u(this,d,C).call(this,p)?p.__staleWhileFetching:p;T!==void 0&&(h.oldValue=T)}}else h&&(h.set="update")}if(i===0||r(this,j)||u(this,d,ut).call(this),r(this,j)&&(_||r(this,Ge).call(this,f,i,n),h&&r(this,de).call(this,h,f)),!o&&r(this,z)&&r(this,D)){const p=r(this,D);let T;for(;T=p==null?void 0:p.shift();)(A=r(this,Q))==null||A.call(this,...T)}return this}pop(){var e;try{for(;r(this,E);){const t=r(this,b)[r(this,O)];if(u(this,d,Xe).call(this,!0),u(this,d,C).call(this,t)){if(t.__staleWhileFetching)return t.__staleWhileFetching}else if(t!==void 0)return t}}finally{if(r(this,z)&&r(this,D)){const t=r(this,D);let s;for(;s=t==null?void 0:t.shift();)(e=r(this,Q))==null||e.call(this,...s)}}}has(e,t={}){const{updateAgeOnHas:s=this.updateAgeOnHas,status:i}=t,n=r(this,M).get(e);if(n!==void 0){const o=r(this,b)[n];if(u(this,d,C).call(this,o)&&o.__staleWhileFetching===void 0)return!1;if(!r(this,V).call(this,n))return s&&r(this,xe).call(this,n),i&&(i.has="hit",r(this,de).call(this,i,n)),!0;i&&(i.has="stale",r(this,de).call(this,i,n))}else i&&(i.has="miss");return!1}peek(e,t={}){const{allowStale:s=this.allowStale}=t,i=r(this,M).get(e);if(i===void 0||!s&&r(this,V).call(this,i))return;const n=r(this,b)[i];return u(this,d,C).call(this,n)?n.__staleWhileFetching:n}async fetch(e,t={}){const{allowStale:s=this.allowStale,updateAgeOnGet:i=this.updateAgeOnGet,noDeleteOnStaleGet:n=this.noDeleteOnStaleGet,ttl:o=this.ttl,noDisposeOnSet:c=this.noDisposeOnSet,size:h=0,sizeCalculation:_=this.sizeCalculation,noUpdateTTL:y=this.noUpdateTTL,noDeleteOnFetchRejection:f=this.noDeleteOnFetchRejection,allowStaleOnFetchRejection:N=this.allowStaleOnFetchRejection,ignoreFetchAbort:I=this.ignoreFetchAbort,allowStaleOnFetchAbort:B=this.allowStaleOnFetchAbort,context:q,forceRefresh:A=!1,status:p,signal:T}=t;if(!r(this,be))return p&&(p.fetch="get"),this.get(e,{allowStale:s,updateAgeOnGet:i,noDeleteOnStaleGet:n,status:p});const Ve={allowStale:s,updateAgeOnGet:i,noDeleteOnStaleGet:n,ttl:o,noDisposeOnSet:c,size:h,sizeCalculation:_,noUpdateTTL:y,noDeleteOnFetchRejection:f,allowStaleOnFetchRejection:N,allowStaleOnFetchAbort:B,ignoreFetchAbort:I,status:p,signal:T};let oe=r(this,M).get(e);if(oe===void 0){p&&(p.fetch="miss");const J=u(this,d,Ke).call(this,e,oe,Ve,q);return J.__returned=J}{const J=r(this,b)[oe];if(u(this,d,C).call(this,J)){const $e=s&&J.__staleWhileFetching!==void 0;return p&&(p.fetch="inflight",$e&&(p.returnedStale=!0)),$e?J.__staleWhileFetching:J.__returned=J}const Le=r(this,V).call(this,oe);if(!A&&!Le)return p&&(p.fetch="hit"),u(this,d,ze).call(this,oe),i&&r(this,xe).call(this,oe),p&&r(this,de).call(this,p,oe),J;const ue=u(this,d,Ke).call(this,e,oe,Ve,q),Te=ue.__staleWhileFetching!==void 0&&s;return p&&(p.fetch=Le?"stale":"refresh",Te&&Le&&(p.returnedStale=!0)),Te?ue.__staleWhileFetching:ue.__returned=ue}}async forceFetch(e,t={}){const s=await this.fetch(e,t);if(s===void 0)throw new Error("fetch() returned undefined");return s}memo(e,t={}){const s=r(this,Pe);if(!s)throw new Error("no memoMethod provided to constructor");const{context:i,forceRefresh:n,...o}=t,c=this.get(e,o);if(!n&&c!==void 0)return c;const h=s(e,c,{options:o,context:i});return this.set(e,h,o),h}get(e,t={}){const{allowStale:s=this.allowStale,updateAgeOnGet:i=this.updateAgeOnGet,noDeleteOnStaleGet:n=this.noDeleteOnStaleGet,status:o}=t,c=r(this,M).get(e);if(c!==void 0){const h=r(this,b)[c],_=u(this,d,C).call(this,h);return o&&r(this,de).call(this,o,c),r(this,V).call(this,c)?(o&&(o.get="stale"),_?(o&&s&&h.__staleWhileFetching!==void 0&&(o.returnedStale=!0),s?h.__staleWhileFetching:void 0):(n||u(this,d,fe).call(this,e,"expire"),o&&s&&(o.returnedStale=!0),s?h:void 0)):(o&&(o.get="hit"),_?h.__staleWhileFetching:(u(this,d,ze).call(this,c),i&&r(this,xe).call(this,c),h))}o&&(o.get="miss")}delete(e){return u(this,d,fe).call(this,e,"delete")}clear(){return u(this,d,_t).call(this,"delete")}};X=new WeakMap,W=new WeakMap,K=new WeakMap,Q=new WeakMap,qe=new WeakMap,Pe=new WeakMap,E=new WeakMap,ee=new WeakMap,M=new WeakMap,S=new WeakMap,b=new WeakMap,U=new WeakMap,G=new WeakMap,O=new WeakMap,F=new WeakMap,te=new WeakMap,D=new WeakMap,se=new WeakMap,ie=new WeakMap,j=new WeakMap,ne=new WeakMap,be=new WeakMap,z=new WeakMap,d=new WeakSet,ut=function(){const e=new Je(r(this,X)),t=new Je(r(this,X));m(this,j,e),m(this,ie,t),m(this,Ge,(n,o,c=Ee.now())=>{if(t[n]=o!==0?c:0,e[n]=o,o!==0&&this.ttlAutopurge){const h=setTimeout(()=>{r(this,V).call(this,n)&&u(this,d,fe).call(this,r(this,S)[n],"expire")},o+1);h.unref&&h.unref()}}),m(this,xe,n=>{t[n]=e[n]!==0?Ee.now():0}),m(this,de,(n,o)=>{if(e[o]){const c=e[o],h=t[o];if(!c||!h)return;n.ttl=c,n.start=h,n.now=s||i();const _=n.now-h;n.remainingTTL=c-_}});let s=0;const i=()=>{const n=Ee.now();if(this.ttlResolution>0){s=n;const o=setTimeout(()=>s=0,this.ttlResolution);o.unref&&o.unref()}return n};this.getRemainingTTL=n=>{const o=r(this,M).get(n);if(o===void 0)return 0;const c=e[o],h=t[o];return!c||!h?1/0:c-((s||i())-h)},m(this,V,n=>{const o=t[n],c=e[n];return!!c&&!!o&&(s||i())-o>c})},xe=new WeakMap,de=new WeakMap,Ge=new WeakMap,V=new WeakMap,Qt=function(){const e=new Je(r(this,X));m(this,ee,0),m(this,se,e),m(this,we,t=>{m(this,ee,r(this,ee)-e[t]),e[t]=0}),m(this,je,(t,s,i,n)=>{if(u(this,d,C).call(this,s))return 0;if(!ge(i)){if(!n)throw new TypeError("invalid size value (must be positive integer). When maxSize or maxEntrySize is used, sizeCalculation or size must be set.");if(typeof n!="function")throw new TypeError("sizeCalculation must be a function");if(i=n(s,t),!ge(i))throw new TypeError("sizeCalculation return invalid (expect positive integer)")}return i}),m(this,Oe,(t,s,i)=>{if(e[t]=s,r(this,W)){const n=r(this,W)-e[t];for(;r(this,ee)>n;)u(this,d,Xe).call(this,!0)}m(this,ee,r(this,ee)+e[t]),i&&(i.entrySize=s,i.totalCalculatedSize=r(this,ee))})},we=new WeakMap,Oe=new WeakMap,je=new WeakMap,me=function*({allowStale:e=this.allowStale}={}){if(r(this,E))for(let t=r(this,F);u(this,d,gt).call(this,t)&&(!e&&r(this,V).call(this,t)||(yield t),t!==r(this,O));)t=r(this,G)[t]},_e=function*({allowStale:e=this.allowStale}={}){if(r(this,E))for(let t=r(this,O);u(this,d,gt).call(this,t)&&(!e&&r(this,V).call(this,t)||(yield t),t!==r(this,F));)t=r(this,U)[t]},gt=function(e){return e!==void 0&&r(this,M).get(r(this,S)[e])===e},Xe=function(e){var n,o;const t=r(this,O),s=r(this,S)[t],i=r(this,b)[t];return r(this,be)&&u(this,d,C).call(this,i)?i.__abortController.abort(new Error("evicted")):(r(this,ne)||r(this,z))&&(r(this,ne)&&((n=r(this,K))==null||n.call(this,i,s,"evict")),r(this,z)&&((o=r(this,D))==null||o.push([i,s,"evict"]))),r(this,we).call(this,t),e&&(r(this,S)[t]=void 0,r(this,b)[t]=void 0,r(this,te).push(t)),r(this,E)===1?(m(this,O,m(this,F,0)),r(this,te).length=0):m(this,O,r(this,U)[t]),r(this,M).delete(s),Ye(this,E)._--,t},Ke=function(e,t,s,i){const n=t===void 0?void 0:r(this,b)[t];if(u(this,d,C).call(this,n))return n;const o=new st,{signal:c}=s;c==null||c.addEventListener("abort",()=>o.abort(c.reason),{signal:o.signal});const h={signal:o.signal,options:s,context:i},_=(I,B=!1)=>{const{aborted:q}=o.signal,A=s.ignoreFetchAbort&&I!==void 0;if(s.status&&(q&&!B?(s.status.fetchAborted=!0,s.status.fetchError=o.signal.reason,A&&(s.status.fetchAbortIgnored=!0)):s.status.fetchResolved=!0),q&&!A&&!B)return y(o.signal.reason);const p=f;return r(this,b)[t]===f&&(I===void 0?p.__staleWhileFetching?r(this,b)[t]=p.__staleWhileFetching:u(this,d,fe).call(this,e,"fetch"):(s.status&&(s.status.fetchUpdated=!0),this.set(e,I,h.options))),I},y=I=>{const{aborted:B}=o.signal,q=B&&s.allowStaleOnFetchAbort,A=q||s.allowStaleOnFetchRejection,p=A||s.noDeleteOnFetchRejection,T=f;if(r(this,b)[t]===f&&(!p||T.__staleWhileFetching===void 0?u(this,d,fe).call(this,e,"fetch"):q||(r(this,b)[t]=T.__staleWhileFetching)),A)return s.status&&T.__staleWhileFetching!==void 0&&(s.status.returnedStale=!0),T.__staleWhileFetching;if(T.__returned===T)throw I};s.status&&(s.status.fetchDispatched=!0);const f=new Promise((I,B)=>{var A;const q=(A=r(this,qe))==null?void 0:A.call(this,e,n,h);q&&q instanceof Promise&&q.then(p=>I(p===void 0?void 0:p),B),o.signal.addEventListener("abort",()=>{s.ignoreFetchAbort&&!s.allowStaleOnFetchAbort||(I(void 0),s.allowStaleOnFetchAbort&&(I=p=>_(p,!0)))})}).then(_,I=>(s.status&&(s.status.fetchRejected=!0,s.status.fetchError=I),y(I))),N=Object.assign(f,{__abortController:o,__staleWhileFetching:n,__returned:void 0});return t===void 0?(this.set(e,N,{...h.options,status:void 0}),t=r(this,M).get(e)):r(this,b)[t]=N,N},C=function(e){if(!r(this,be))return!1;const t=e;return!!t&&t instanceof Promise&&t.hasOwnProperty("__staleWhileFetching")&&t.__abortController instanceof st},mt=function(e,t){r(this,G)[t]=e,r(this,U)[e]=t},ze=function(e){e!==r(this,F)&&(e===r(this,O)?m(this,O,r(this,U)[e]):u(this,d,mt).call(this,r(this,G)[e],r(this,U)[e]),u(this,d,mt).call(this,r(this,F),e),m(this,F,e))},fe=function(e,t){var i,n,o,c;let s=!1;if(r(this,E)!==0){const h=r(this,M).get(e);if(h!==void 0)if(s=!0,r(this,E)===1)u(this,d,_t).call(this,t);else{r(this,we).call(this,h);const _=r(this,b)[h];if(u(this,d,C).call(this,_)?_.__abortController.abort(new Error("deleted")):(r(this,ne)||r(this,z))&&(r(this,ne)&&((i=r(this,K))==null||i.call(this,_,e,t)),r(this,z)&&((n=r(this,D))==null||n.push([_,e,t]))),r(this,M).delete(e),r(this,S)[h]=void 0,r(this,b)[h]=void 0,h===r(this,F))m(this,F,r(this,G)[h]);else if(h===r(this,O))m(this,O,r(this,U)[h]);else{const y=r(this,G)[h];r(this,U)[y]=r(this,U)[h];const f=r(this,U)[h];r(this,G)[f]=r(this,G)[h]}Ye(this,E)._--,r(this,te).push(h)}}if(r(this,z)&&((o=r(this,D))!=null&&o.length)){const h=r(this,D);let _;for(;_=h==null?void 0:h.shift();)(c=r(this,Q))==null||c.call(this,..._)}return s},_t=function(e){var t,s,i;for(const n of u(this,d,_e).call(this,{allowStale:!0})){const o=r(this,b)[n];if(u(this,d,C).call(this,o))o.__abortController.abort(new Error("deleted"));else{const c=r(this,S)[n];r(this,ne)&&((t=r(this,K))==null||t.call(this,o,c,e)),r(this,z)&&((s=r(this,D))==null||s.push([o,c,e]))}}if(r(this,M).clear(),r(this,b).fill(void 0),r(this,S).fill(void 0),r(this,j)&&r(this,ie)&&(r(this,j).fill(0),r(this,ie).fill(0)),r(this,se)&&r(this,se).fill(0),m(this,O,0),m(this,F,0),r(this,te).length=0,m(this,ee,0),m(this,E,0),r(this,z)&&r(this,D)){const n=r(this,D);let o;for(;o=n==null?void 0:n.shift();)(i=r(this,Q))==null||i.call(this,...o)}};let dt=ft;class fi{constructor(){a(this,"_syncStatus",{status:fs.done,foldersProgress:[]});a(this,"_syncEnabledState",It.initializing);a(this,"_workspaceGuidelines",[]);a(this,"_openUserGuidelinesInput",!1);a(this,"_userGuidelines");a(this,"_contextStore",new Os);a(this,"_prevOpenFiles",[]);a(this,"_disableContext",!1);a(this,"_enableAgentMemories",!1);a(this,"subscribers",new Set);a(this,"subscribe",e=>(this.subscribers.add(e),e(this),()=>{this.subscribers.delete(e)}));a(this,"handleMessageFromExtension",e=>{const t=e.data;switch(t.type){case Ce.sourceFoldersUpdated:this.onSourceFoldersUpdated(t.data.sourceFolders);break;case Ce.sourceFoldersSyncStatus:this.onSyncStatusUpdated(t.data);break;case Ce.fileRangesSelected:this.updateSelections(t.data);break;case Ce.currentlyOpenFiles:this.setCurrentlyOpenFiles(t.data);break;case Ce.syncEnabledState:this.onSyncEnabledStateUpdate(t.data);break;case Ce.updateGuidelinesState:this.onGuidelinesStateUpdate(t.data);break;default:return!1}return!0});a(this,"onSourceFoldersUpdated",e=>{const t=this.sourceFolders;e=this.updateSourceFoldersWithGuidelines(e),this._contextStore.update(e.map(s=>({sourceFolder:s,status:R.active,label:s.folderRoot,showWarning:s.guidelinesOverLimit,id:s.folderRoot+String(s.guidelinesEnabled)+String(s.guidelinesOverLimit)})),t,s=>s.id),this.notifySubscribers()});a(this,"onSyncStatusUpdated",e=>{this._syncStatus=e,this.notifySubscribers()});a(this,"disableContext",()=>{this._disableContext=!0,this.notifySubscribers()});a(this,"enableContext",()=>{this._disableContext=!1,this.notifySubscribers()});a(this,"addFile",e=>{this.addFiles([e])});a(this,"addFiles",e=>{this.updateFiles(e,[])});a(this,"removeFile",e=>{this.removeFiles([e])});a(this,"removeFiles",e=>{this.updateFiles([],e)});a(this,"updateItems",(e,t)=>{this.updateItemsInplace(e,t),this.notifySubscribers()});a(this,"updateItemsInplace",(e,t)=>{this._contextStore.update(e,t,s=>s.id)});a(this,"updateFiles",(e,t)=>{const s=o=>({file:o,...ot(o)}),i=e.map(s),n=t.map(s);this._contextStore.update(i,n,o=>o.id),this.notifySubscribers()});a(this,"enableAgentMemories",()=>{this._enableAgentMemories=!0,this.notifySubscribers()});a(this,"disableAgentMemories",()=>{this._enableAgentMemories=!1,this.notifySubscribers()});a(this,"setCurrentlyOpenFiles",e=>{const t=e.map(i=>({recentFile:i,...ot(i)})),s=this._prevOpenFiles;this._prevOpenFiles=t,this._contextStore.update(t,s,i=>i.id),s.forEach(i=>{const n=this._contextStore.peekKey(i.id);n!=null&&n.recentFile&&(n.file=n.recentFile,delete n.recentFile)}),t.forEach(i=>{const n=this._contextStore.peekKey(i.id);n!=null&&n.file&&(n.recentFile=n.file,delete n.file)}),this.notifySubscribers()});a(this,"onSyncEnabledStateUpdate",e=>{this._syncEnabledState=e,this.notifySubscribers()});a(this,"updateUserGuidelines",e=>{const t=this.userGuidelines,s={userGuidelines:e,label:"User Guidelines",id:"userGuidelines",status:R.active,referenceCount:1,showWarning:e.overLimit};this._contextStore.update([s],t,i=>{var n,o;return i.id+String((n=i.userGuidelines)==null?void 0:n.enabled)+String((o=i.userGuidelines)==null?void 0:o.overLimit)}),this.notifySubscribers()});a(this,"onGuidelinesStateUpdate",e=>{this._userGuidelines=e.userGuidelines,this._workspaceGuidelines=e.workspaceGuidelines??[];const t=e.userGuidelines;t&&this.updateUserGuidelines(t),this.onSourceFoldersUpdated(this.sourceFolders.map(s=>s.sourceFolder))});a(this,"updateSourceFoldersWithGuidelines",e=>e.map(t=>{const s=this._workspaceGuidelines.find(i=>i.workspaceFolder===t.folderRoot);return{...t,guidelinesEnabled:(s==null?void 0:s.enabled)??!1,guidelinesOverLimit:(s==null?void 0:s.overLimit)??!1,guidelinesLengthLimit:(s==null?void 0:s.lengthLimit)??2e3}}));a(this,"toggleStatus",e=>{this._contextStore.toggleStatus(e.id),this.notifySubscribers()});a(this,"updateExternalSources",(e,t)=>{this._contextStore.update(e,t,s=>s.id),this.notifySubscribers()});a(this,"clearFiles",()=>{this._contextStore.update([],this.files,e=>e.id),this.notifySubscribers()});a(this,"updateSelections",e=>{const t=this._contextStore.values.filter(St);this._contextStore.update(e.map(s=>({selection:s,...ot(s)})),t,s=>s.id),this.notifySubscribers()});a(this,"maybeHandleDelete",({editor:e})=>{if(e.state.selection.empty&&e.state.selection.$anchor.pos===1&&this.recentActiveItems.length>0){const t=this.recentActiveItems[0];return this.markInactive(t),!0}return!1});a(this,"markInactive",e=>{this.markItemsInactive([e])});a(this,"markItemsInactive",e=>{e.forEach(t=>{this._contextStore.setStatus(t.id,R.inactive)}),this.notifySubscribers()});a(this,"markAllInactive",()=>{this.markItemsInactive(this.recentActiveItems)});a(this,"markActive",e=>{this.markItemsActive([e])});a(this,"markItemsActive",e=>{e.forEach(t=>{this._contextStore.setStatus(t.id,R.active)}),this.notifySubscribers()});a(this,"markAllActive",()=>{this.markItemsActive(this.recentInactiveItems)});a(this,"unpin",e=>{this._contextStore.unpin(e.id),this.notifySubscribers()});a(this,"togglePinned",e=>{this._contextStore.togglePinned(e.id),this.notifySubscribers()});a(this,"notifySubscribers",()=>{this.subscribers.forEach(e=>e(this))});this.clearFiles()}get files(){return this._disableContext?[]:this._contextStore.values.filter(e=>as(e)&&!Ct(e))}get recentFiles(){return this._disableContext?[]:this._contextStore.values.filter(Ct)}get userGuidelinesText(){var e;return((e=this._userGuidelines)==null?void 0:e.contents)??""}get selections(){return this._disableContext?[]:this._contextStore.values.filter(St)}get folders(){return this._disableContext?[]:this._contextStore.values.filter(os)}get sourceFolders(){return this._disableContext?[]:this._contextStore.values.filter(xt)}get externalSources(){return this._disableContext?[]:this._contextStore.values.filter(ls)}get userGuidelines(){return this._contextStore.values.filter(wt)}get agentMemories(){return[{...hs,status:this._enableAgentMemories?R.active:R.inactive,referenceCount:1}]}get activeFiles(){return this._disableContext?[]:this.files.filter(e=>e.status===R.active)}get activeRecentFiles(){return this._disableContext?[]:this.recentFiles.filter(e=>e.status===R.active)}get activeExternalSources(){return this._disableContext?[]:this.externalSources.filter(e=>e.status===R.active)}get activeSelections(){return this._disableContext?[]:this.selections.filter(e=>e.status===R.active)}get activeSourceFolders(){return this._disableContext?[]:this.sourceFolders.filter(e=>e.status===R.active)}get syncStatus(){return this._syncStatus.status}get syncEnabledState(){return this._syncEnabledState}get syncProgress(){var h;if(this.syncEnabledState===It.disabled||!this._syncStatus.foldersProgress)return;const e=this._syncStatus.foldersProgress.filter(_=>_.progress!==void 0);if(e.length===0)return;const t=e.reduce((_,y)=>{var f;return _+(((f=y==null?void 0:y.progress)==null?void 0:f.trackedFiles)??0)},0),s=e.reduce((_,y)=>{var f;return _+(((f=y==null?void 0:y.progress)==null?void 0:f.backlogSize)??0)},0),i=Math.max(t,0),n=Math.min(Math.max(s,0),i),o=i-n,c=[];for(const _ of e)(h=_==null?void 0:_.progress)!=null&&h.newlyTracked&&c.push(_.folderRoot);return{status:this._syncStatus.status,totalFiles:i,syncedCount:o,backlogSize:n,newlyTrackedFolders:c}}get contextCounts(){return this._contextStore.values.length??0}get chatActiveContext(){return{userSpecifiedFiles:this.activeFiles.map(e=>({rootPath:e.file.repoRoot,relPath:e.file.pathName})),recentFiles:this.activeRecentFiles.map(e=>({rootPath:e.recentFile.repoRoot,relPath:e.recentFile.pathName})),externalSources:this.activeExternalSources.map(e=>e.externalSource),selections:this.activeSelections.map(e=>e.selection),sourceFolders:this.activeSourceFolders.map(e=>({rootPath:e.sourceFolder.folderRoot,relPath:""}))}}get recentItems(){return this._disableContext?this.userGuidelines:[...this._contextStore.values.filter(e=>!xt(e)&&!wt(e)&&!$t(e)),...this.sourceFolders,...this.userGuidelines,...this.agentMemories]}get recentActiveItems(){return this.recentItems.filter(e=>e.status===R.active)}get recentInactiveItems(){return this.recentItems.filter(e=>e.status===R.inactive)}get isContextDisabled(){return this._disableContext}}class Os{constructor(){a(this,"_cache",new dt({max:1e3}));a(this,"peekKey",e=>this._cache.get(e,{updateAgeOnGet:!1}));a(this,"clear",()=>{this._cache.clear()});a(this,"update",(e,t,s)=>{e.forEach(i=>this.addInPlace(i,s)),t.forEach(i=>this.removeInPlace(i,s))});a(this,"removeFromStore",(e,t)=>{const s=t(e);this._cache.delete(s)});a(this,"addInPlace",(e,t)=>{const s=t(e),i=e.referenceCount??1,n=this._cache.get(s),o=e.status??(n==null?void 0:n.status)??R.active;n?(n.referenceCount+=i,n.status=o,n.pinned=e.pinned??n.pinned,n.showWarning=e.showWarning??n.showWarning):this._cache.set(s,{...e,pinned:void 0,referenceCount:i,status:o})});a(this,"removeInPlace",(e,t)=>{const s=t(e),i=this._cache.get(s);i&&(i.referenceCount-=1,i.referenceCount===0&&this._cache.delete(s))});a(this,"setStatus",(e,t)=>{const s=this._cache.get(e);s&&(s.status=t)});a(this,"togglePinned",e=>{const t=this._cache.peek(e);t&&(t.pinned?this.unpin(e):this.pin(e))});a(this,"pin",e=>{const t=this._cache.peek(e);t&&!t.pinned&&(t.pinned=!0,t.referenceCount+=1)});a(this,"unpin",e=>{const t=this._cache.peek(e);t&&t.pinned&&(t.pinned=!1,t.referenceCount-=1,t.referenceCount===0&&this._cache.delete(e))});a(this,"toggleStatus",e=>{const t=this._cache.get(e);t&&(t.status=t.status===R.active?R.inactive:R.active)})}get store(){return Object.fromEntries(this._cache.entries())}get values(){return[...this._cache.values()]}}function Hs(l){let e,t,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},l[0]],i={};for(let n=0;n<s.length;n+=1)i=He(i,s[n]);return{c(){e=L("svg"),t=new Ht(!0),this.h()},l(n){e=Lt(n,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var o=kt(e);t=Nt(o,!0),o.forEach(k),this.h()},h(){t.a=null,Qe(e,i)},m(n,o){Ut(n,e,o),t.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M248.4 84.3c1.6-2.7 4.5-4.3 7.6-4.3s6 1.6 7.6 4.3L461.9 410c1.4 2.3 2.1 4.9 2.1 7.5 0 8-6.5 14.5-14.5 14.5h-387c-8 0-14.5-6.5-14.5-14.5 0-2.7.7-5.3 2.1-7.5zm-41-25L9.1 385c-6 9.8-9.1 21-9.1 32.5C0 452 28 480 62.5 480h387c34.5 0 62.5-28 62.5-62.5 0-11.5-3.2-22.7-9.1-32.5L304.6 59.3C294.3 42.4 275.9 32 256 32s-38.3 10.4-48.6 27.3M288 368a32 32 0 1 0-64 0 32 32 0 1 0 64 0m-8-184c0-13.3-10.7-24-24-24s-24 10.7-24 24v96c0 13.3 10.7 24 24 24s24-10.7 24-24z"/>',e)},p(n,[o]){Qe(e,i=zt(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&o&&n[0]]))},i:x,o:x,d(n){n&&k(e)}}}function Ls(l,e,t){return l.$$set=s=>{t(0,e=He(He({},e),et(s)))},[e=et(e)]}class pi extends le{constructor(e){super(),he(this,e,Ls,Hs,ce,{})}}function ks(l){let e,t,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},l[0]],i={};for(let n=0;n<s.length;n+=1)i=He(i,s[n]);return{c(){e=L("svg"),t=new Ht(!0),this.h()},l(n){e=Lt(n,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var o=kt(e);t=Nt(o,!0),o.forEach(k),this.h()},h(){t.a=null,Qe(e,i)},m(n,o){Ut(n,e,o),t.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M448 128c0 53-43 96-96 96-28.9 0-54.8-12.8-72.4-33l-89.7 44.9c1.4 6.5 2.1 13.2 2.1 20.1s-.7 13.6-2.1 20.1l89.7 44.9c17.6-20.2 43.5-33 72.4-33 53 0 96 43 96 96s-43 96-96 96-96-43-96-96c0-6.9.7-13.6 2.1-20.1L168.4 319c-17.6 20.2-43.5 33-72.4 33-53 0-96-43-96-96s43-96 96-96c28.9 0 54.8 12.8 72.4 33l89.7-44.9c-1.4-6.5-2.1-13.2-2.1-20.1 0-53 43-96 96-96s96 43 96 96M96 304a48 48 0 1 0 0-96 48 48 0 1 0 0 96m304-176a48 48 0 1 0-96 0 48 48 0 1 0 96 0m-48 304a48 48 0 1 0 0-96 48 48 0 1 0 0 96"/>',e)},p(n,[o]){Qe(e,i=zt(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},1&o&&n[0]]))},i:x,o:x,d(n){n&&k(e)}}}function Ns(l,e,t){return l.$$set=s=>{t(0,e=He(He({},e),et(s)))},[e=et(e)]}class bi extends le{constructor(e){super(),he(this,e,Ns,ks,ce,{})}}const yi="messageRenderOptions",Us={doHideThreadSelector:!1,doHideStatusBars:!1,doHideSlashActions:!1,doHideAtMentions:!1,doHideAgentSwitcher:!1,doHideNewThreadButton:!1,doHideMultimodalActions:!1,doHideContextBar:!1,doShowTurnSelector:!1},zs={doHideThreadSelector:!0,doHideStatusBars:!0,doHideSlashActions:!0,doHideAtMentions:!0,doHideAgentSwitcher:!1,doHideNewThreadButton:!0,doHideMultimodalActions:!0,doHideContextBar:!0,doShowTurnSelector:!0},vi="selectedTurnIndex";function Ci(l,e){l.update(()=>e.isActive?zs:Us)}function Ft(l){let e,t;return e=new Bt({props:{class:"edit-item__added-lines",size:1,$$slots:{default:[Bs]},$$scope:{ctx:l}}}),{c(){Wt(e.$$.fragment)},m(s,i){Gt(e,s,i),t=!0},p(s,i){const n={};5&i&&(n.$$scope={dirty:i,ctx:s}),e.$set(n)},i(s){t||(pe(e.$$.fragment,s),t=!0)},o(s){Ae(e.$$.fragment,s),t=!1},d(s){jt(e,s)}}}function Bs(l){let e,t;return{c(){e=tt("+"),t=tt(l[0])},m(s,i){Z(s,e,i),Z(s,t,i)},p(s,i){1&i&&Vt(t,s[0])},d(s){s&&(k(e),k(t))}}}function Dt(l){let e,t;return e=new Bt({props:{class:"edit-item__removed-lines",size:1,$$slots:{default:[Ws]},$$scope:{ctx:l}}}),{c(){Wt(e.$$.fragment)},m(s,i){Gt(e,s,i),t=!0},p(s,i){const n={};6&i&&(n.$$scope={dirty:i,ctx:s}),e.$set(n)},i(s){t||(pe(e.$$.fragment,s),t=!0)},o(s){Ae(e.$$.fragment,s),t=!1},d(s){jt(e,s)}}}function Ws(l){let e,t;return{c(){e=tt("-"),t=tt(l[1])},m(s,i){Z(s,e,i),Z(s,t,i)},p(s,i){2&i&&Vt(t,s[1])},d(s){s&&(k(e),k(t))}}}function Gs(l){let e,t,s,i=l[0]>0&&Ft(l),n=l[1]>0&&Dt(l);return{c(){e=is("div"),i&&i.c(),t=ns(),n&&n.c(),g(e,"class","edit-item__changes svelte-1k8sltp")},m(o,c){Z(o,e,c),i&&i.m(e,null),ve(e,t),n&&n.m(e,null),s=!0},p(o,[c]){o[0]>0?i?(i.p(o,c),1&c&&pe(i,1)):(i=Ft(o),i.c(),pe(i,1),i.m(e,t)):i&&(bt(),Ae(i,1,1,()=>{i=null}),yt()),o[1]>0?n?(n.p(o,c),2&c&&pe(n,1)):(n=Dt(o),n.c(),pe(n,1),n.m(e,null)):n&&(bt(),Ae(n,1,1,()=>{n=null}),yt())},i(o){s||(pe(i),pe(n),s=!0)},o(o){Ae(i),Ae(n),s=!1},d(o){o&&k(e),i&&i.d(),n&&n.d()}}}function js(l,e,t){let{totalAddedLines:s=0}=e,{totalRemovedLines:i=0}=e;return l.$$set=n=>{"totalAddedLines"in n&&t(0,s=n.totalAddedLines),"totalRemovedLines"in n&&t(1,i=n.totalRemovedLines)},[s,i]}class Si extends le{constructor(e){super(),he(this,e,js,Gs,ce,{totalAddedLines:0,totalRemovedLines:1})}}function Vs(l){let e,t;return{c(){e=L("svg"),t=L("path"),g(t,"fill-rule","evenodd"),g(t,"clip-rule","evenodd"),g(t,"d","M3.5 2.82672C3.5 2.55058 3.72386 2.32672 4 2.32672H9.79289L12.5 5.03383V12.8267C12.5 13.1028 12.2761 13.3267 12 13.3267H4C3.72386 13.3267 3.5 13.1028 3.5 12.8267V2.82672ZM4 1.32672C3.17157 1.32672 2.5 1.99829 2.5 2.82672V12.8267C2.5 13.6551 3.17157 14.3267 4 14.3267H12C12.8284 14.3267 13.5 13.6551 13.5 12.8267V4.93027C13.5 4.73136 13.421 4.5406 13.2803 4.39994L10.3535 1.47317C10.2598 1.3794 10.1326 1.32672 10 1.32672H4ZM10.25 6.6595C10.5261 6.6595 10.75 6.43564 10.75 6.1595C10.75 5.88336 10.5261 5.6595 10.25 5.6595H8.49996L8.49996 3.9095C8.49996 3.6334 8.2761 3.4095 7.99996 3.4095C7.72382 3.4095 7.49996 3.6334 7.49996 3.9095V5.6595H5.74996C5.47386 5.6595 5.24996 5.88336 5.24996 6.1595C5.24996 6.43564 5.47386 6.6595 5.74996 6.6595L7.49996 6.6595V8.4095C7.49996 8.68564 7.72382 8.9095 7.99996 8.9095C8.2761 8.9095 8.49996 8.68564 8.49996 8.4095V6.6595H10.25ZM10.4999 11.4188C10.4999 11.695 10.2761 11.9188 9.99993 11.9188H5.99993C5.72379 11.9188 5.49993 11.695 5.49993 11.4188C5.49993 11.1427 5.72379 10.9188 5.99993 10.9188H9.99993C10.2761 10.9188 10.4999 11.1427 10.4999 11.4188Z"),g(t,"fill","currentColor"),g(e,"width","15"),g(e,"height","15"),g(e,"viewBox","0 0 15 15"),g(e,"fill","none"),g(e,"xmlns","http://www.w3.org/2000/svg")},m(s,i){Z(s,e,i),ve(e,t)},p:x,i:x,o:x,d(s){s&&k(e)}}}class xi extends le{constructor(e){super(),he(this,e,null,Vs,ce,{})}}function $s(l){let e,t;return{c(){e=L("svg"),t=L("path"),g(t,"fill-rule","evenodd"),g(t,"clip-rule","evenodd"),g(t,"d","M11.4669 3.72684C11.7558 3.91574 11.8369 4.30308 11.648 4.59198L7.39799 11.092C7.29783 11.2452 7.13556 11.3467 6.95402 11.3699C6.77247 11.3931 6.58989 11.3355 6.45446 11.2124L3.70446 8.71241C3.44905 8.48022 3.43023 8.08494 3.66242 7.82953C3.89461 7.57412 4.28989 7.55529 4.5453 7.78749L6.75292 9.79441L10.6018 3.90792C10.7907 3.61902 11.178 3.53795 11.4669 3.72684Z"),g(t,"fill","currentColor"),g(e,"width","15"),g(e,"height","15"),g(e,"viewBox","0 0 15 15"),g(e,"fill","none"),g(e,"xmlns","http://www.w3.org/2000/svg")},m(s,i){Z(s,e,i),ve(e,t)},p:x,i:x,o:x,d(s){s&&k(e)}}}class wi extends le{constructor(e){super(),he(this,e,null,$s,ce,{})}}function Ys(l){let e,t;return{c(){e=L("svg"),t=L("path"),g(t,"fill-rule","evenodd"),g(t,"clip-rule","evenodd"),g(t,"d","M3.5 2C3.22386 2 3 2.22386 3 2.5V12.5C3 12.7761 3.22386 13 3.5 13H11.5C11.7761 13 12 12.7761 12 12.5V4.70711L9.29289 2H3.5ZM2 2.5C2 1.67157 2.67157 1 3.5 1H9.5C9.63261 1 9.75979 1.05268 9.85355 1.14645L12.7803 4.07322C12.921 4.21388 13 4.40464 13 4.60355V12.5C13 13.3284 12.3284 14 11.5 14H3.5C2.67157 14 2 13.3284 2 12.5V2.5ZM4.75 7.5C4.75 7.22386 4.97386 7 5.25 7H7V5.25C7 4.97386 7.22386 4.75 7.5 4.75C7.77614 4.75 8 4.97386 8 5.25V7H9.75C10.0261 7 10.25 7.22386 10.25 7.5C10.25 7.77614 10.0261 8 9.75 8H8V9.75C8 10.0261 7.77614 10.25 7.5 10.25C7.22386 10.25 7 10.0261 7 9.75V8H5.25C4.97386 8 4.75 7.77614 4.75 7.5Z"),g(t,"fill","currentColor"),g(e,"width","15"),g(e,"height","15"),g(e,"viewBox","0 0 15 15"),g(e,"fill","none"),g(e,"xmlns","http://www.w3.org/2000/svg")},m(s,i){Z(s,e,i),ve(e,t)},p:x,i:x,o:x,d(s){s&&k(e)}}}class Ii extends le{constructor(e){super(),he(this,e,null,Ys,ce,{})}}function Zs(l){let e,t;return{c(){e=L("svg"),t=L("path"),g(t,"fill-rule","evenodd"),g(t,"clip-rule","evenodd"),g(t,"d","M3.13523 8.84197C3.3241 9.04343 3.64052 9.05363 3.84197 8.86477L7.5 5.43536L11.158 8.86477C11.3595 9.05363 11.6759 9.04343 11.8648 8.84197C12.0536 8.64051 12.0434 8.32409 11.842 8.13523L7.84197 4.38523C7.64964 4.20492 7.35036 4.20492 7.15803 4.38523L3.15803 8.13523C2.95657 8.32409 2.94637 8.64051 3.13523 8.84197Z"),g(t,"fill","currentColor"),g(e,"width","15"),g(e,"height","15"),g(e,"viewBox","0 0 15 15"),g(e,"fill","none"),g(e,"xmlns","http://www.w3.org/2000/svg")},m(s,i){Z(s,e,i),ve(e,t)},p:x,i:x,o:x,d(s){s&&k(e)}}}class Ti extends le{constructor(e){super(),he(this,e,null,Zs,ce,{})}}function Js(l){let e,t;return{c(){e=L("svg"),t=L("path"),g(t,"fill-rule","evenodd"),g(t,"clip-rule","evenodd"),g(t,"d","M1.90321 7.29677C1.90321 10.341 4.11041 12.4147 6.58893 12.8439C6.87255 12.893 7.06266 13.1627 7.01355 13.4464C6.96444 13.73 6.69471 13.9201 6.41109 13.871C3.49942 13.3668 0.86084 10.9127 0.86084 7.29677C0.860839 5.76009 1.55996 4.55245 2.37639 3.63377C2.96124 2.97568 3.63034 2.44135 4.16846 2.03202L2.53205 2.03202C2.25591 2.03202 2.03205 1.80816 2.03205 1.53202C2.03205 1.25588 2.25591 1.03202 2.53205 1.03202L5.53205 1.03202C5.80819 1.03202 6.03205 1.25588 6.03205 1.53202L6.03205 4.53202C6.03205 4.80816 5.80819 5.03202 5.53205 5.03202C5.25591 5.03202 5.03205 4.80816 5.03205 4.53202L5.03205 2.68645L5.03054 2.68759L5.03045 2.68766L5.03044 2.68767L5.03043 2.68767C4.45896 3.11868 3.76059 3.64538 3.15554 4.3262C2.44102 5.13021 1.90321 6.10154 1.90321 7.29677ZM13.0109 7.70321C13.0109 4.69115 10.8505 2.6296 8.40384 2.17029C8.12093 2.11718 7.93465 1.84479 7.98776 1.56188C8.04087 1.27898 8.31326 1.0927 8.59616 1.14581C11.4704 1.68541 14.0532 4.12605 14.0532 7.70321C14.0532 9.23988 13.3541 10.4475 12.5377 11.3662C11.9528 12.0243 11.2837 12.5586 10.7456 12.968L12.3821 12.968C12.6582 12.968 12.8821 13.1918 12.8821 13.468C12.8821 13.7441 12.6582 13.968 12.3821 13.968L9.38205 13.968C9.10591 13.968 8.88205 13.7441 8.88205 13.468L8.88205 10.468C8.88205 10.1918 9.10591 9.96796 9.38205 9.96796C9.65819 9.96796 9.88205 10.1918 9.88205 10.468L9.88205 12.3135L9.88362 12.3123C10.4551 11.8813 11.1535 11.3546 11.7585 10.6738C12.4731 9.86976 13.0109 8.89844 13.0109 7.70321Z"),g(t,"fill","currentColor"),g(e,"width","15"),g(e,"height","15"),g(e,"viewBox","0 0 15 15"),g(e,"fill","none"),g(e,"xmlns","http://www.w3.org/2000/svg")},m(s,i){Z(s,e,i),ve(e,t)},p:x,i:x,o:x,d(s){s&&k(e)}}}class Mi extends le{constructor(e){super(),he(this,e,null,Js,ce,{})}}function Xs(l){let e,t;return{c(){e=L("svg"),t=L("path"),g(t,"fill-rule","evenodd"),g(t,"clip-rule","evenodd"),g(t,"d","M7.49991 0.877075C3.84222 0.877075 0.877075 3.84222 0.877075 7.49991C0.877075 11.1576 3.84222 14.1227 7.49991 14.1227C11.1576 14.1227 14.1227 11.1576 14.1227 7.49991C14.1227 3.84222 11.1576 0.877075 7.49991 0.877075ZM3.85768 3.15057C4.84311 2.32448 6.11342 1.82708 7.49991 1.82708C10.6329 1.82708 13.1727 4.36689 13.1727 7.49991C13.1727 8.88638 12.6753 10.1567 11.8492 11.1421L3.85768 3.15057ZM3.15057 3.85768C2.32448 4.84311 1.82708 6.11342 1.82708 7.49991C1.82708 10.6329 4.36689 13.1727 7.49991 13.1727C8.88638 13.1727 10.1567 12.6753 11.1421 11.8492L3.15057 3.85768Z"),g(t,"fill","currentColor"),g(e,"width","15"),g(e,"height","15"),g(e,"viewBox","0 0 15 15"),g(e,"fill","none"),g(e,"xmlns","http://www.w3.org/2000/svg")},m(s,i){Z(s,e,i),ve(e,t)},p:x,i:x,o:x,d(s){s&&k(e)}}}class Ei extends le{constructor(e){super(),he(this,e,null,Xs,ce,{})}}function Ks(l){let e,t;return{c(){e=L("svg"),t=L("path"),g(t,"fill-rule","evenodd"),g(t,"clip-rule","evenodd"),g(t,"d","M1.43555 8.19985C1.43555 4.29832 4.59837 1.1355 8.4999 1.1355C12.4014 1.1355 15.5642 4.29832 15.5642 8.19985C15.5642 12.1013 12.4014 15.2642 8.4999 15.2642C4.59837 15.2642 1.43555 12.1013 1.43555 8.19985ZM8.4999 2.14883C5.15802 2.14883 2.44889 4.85797 2.44889 8.19985C2.44889 11.5417 5.15802 14.2509 8.4999 14.2509C11.8418 14.2509 14.5509 11.5417 14.5509 8.19985C14.5509 4.85797 11.8418 2.14883 8.4999 2.14883ZM11.0105 5.68952C11.2187 5.8978 11.2187 6.23549 11.0105 6.44377L9.25427 8.19997L11.0105 9.95619C11.2187 10.1645 11.2187 10.5022 11.0105 10.7104C10.8022 10.9187 10.4645 10.9187 10.2562 10.7104L8.50002 8.95422L6.74382 10.7104C6.53554 10.9187 6.19784 10.9187 5.98957 10.7104C5.78129 10.5022 5.78129 10.1645 5.98957 9.95619L7.74578 8.19997L5.98957 6.44377C5.78129 6.23549 5.78129 5.8978 5.98957 5.68952C6.19784 5.48124 6.53554 5.48124 6.74382 5.68952L8.50002 7.44573L10.2562 5.68952C10.4645 5.48124 10.8022 5.48124 11.0105 5.68952Z"),g(t,"fill","currentColor"),g(e,"width","17"),g(e,"height","17"),g(e,"viewBox","0 0 17 17"),g(e,"fill","none"),g(e,"xmlns","http://www.w3.org/2000/svg")},m(s,i){Z(s,e,i),ve(e,t)},p:x,i:x,o:x,d(s){s&&k(e)}}}class Ai extends le{constructor(e){super(),he(this,e,null,Ks,ce,{})}}class Fi{static generateDiff(e,t,s,i){return ps(e,t,s,i)}static generateDiffs(e){return bs(e)}static getDiffStats(e){return Tt(e)}static getDiffObjectStats(e){return Tt(e.diff)}static isNewFile(e){return ys(e)}static isDeletedFile(e){return vs(e)}}export{Ps as A,_i as C,Us as D,Si as E,Ii as F,yi as M,fi as S,pi as T,Mi as U,wi as a,bi as b,ci as c,w as d,hi as e,it as f,di as g,ui as h,De as i,Ds as j,xi as k,gi as l,Ti as m,R as n,li as o,Cs as p,Ei as q,mi as r,Ai as s,Fi as t,Ci as u,vi as v,Ss as w,Zt as x};
