var Ol=Object.defineProperty;var Tl=(r,e,t)=>e in r?Ol(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t;var x=(r,e,t)=>Tl(r,typeof e!="symbol"?e+"":e,t);import{S as Y,i as X,s as Q,J as be,c as y,a7 as Xt,e as Z,n as J,h as U,ae as El,a0 as cs,T as Pe,f as E,a1 as ds,b as D,a as Eo,H as Nl,w as $l,x as Dl,y as Al,d as lr,z as Ll,g as Il,j as ar,ai as et,ah as lt,aj as he,ak as Pl,ac as Ke,V as Ne,W as $e,u as I,t as z,Z as De,a6 as Zo,an as hs,I as Ce,a4 as Rl,P as Ht,q as He,r as je,M as ye,N as we,O as ve,a9 as zl,A as ps,G as cr,ad as jt,D as us,Q as fs,_ as Go,$ as ms,L as qe}from"./SpinnerAugment-BUJasFTo.js";import{e as dr}from"./each-DUdYBCJG.js";import{C as Bl}from"./CardAugment-DvO45c5p.js";import{T as _l}from"./TextTooltipAugment-UDQF2J4S.js";import{D as fn,I as Vl}from"./index-yg8vr2DA.js";import{E as Fl}from"./exclamation-triangle-uzqmF3G7.js";import{P as Hl}from"./pen-to-square-DxHNIIBu.js";import{A as jl}from"./augment-logo-CSOE_v2f.js";function hr(r,e,t){const n=r.slice();return n[3]=e[t],n}function pr(r){let e,t,n,o=r[3]+"";return{c(){e=be("span"),t=cs(o),n=Pe(),y(e,"class","c-keyboard-shortcut-hint__icon svelte-1txw16l")},m(i,s){Z(i,e,s),E(e,t),E(e,n)},p(i,s){2&s&&o!==(o=i[3]+"")&&ds(t,o)},d(i){i&&U(e)}}}function ql(r){let e,t,n=dr(r[1]),o=[];for(let i=0;i<n.length;i+=1)o[i]=pr(hr(r,n,i));return{c(){e=be("span");for(let i=0;i<o.length;i+=1)o[i].c();y(e,"class",t=Xt(`c-keyboard-shortcut-hint ${r[0]}`)+" svelte-1txw16l")},m(i,s){Z(i,e,s);for(let l=0;l<o.length;l+=1)o[l]&&o[l].m(e,null)},p(i,[s]){if(2&s){let l;for(n=dr(i[1]),l=0;l<n.length;l+=1){const a=hr(i,n,l);o[l]?o[l].p(a,s):(o[l]=pr(a),o[l].c(),o[l].m(e,null))}for(;l<o.length;l+=1)o[l].d(1);o.length=n.length}1&s&&t!==(t=Xt(`c-keyboard-shortcut-hint ${i[0]}`)+" svelte-1txw16l")&&y(e,"class",t)},i:J,o:J,d(i){i&&U(e),El(o,i)}}}function Jl(r,e,t){let{class:n=""}=e,{keybinding:o}=e,{icons:i=(o==null?void 0:o.split("-"))??[]}=e;return r.$$set=s=>{"class"in s&&t(0,n=s.class),"keybinding"in s&&t(2,o=s.keybinding),"icons"in s&&t(1,i=s.icons)},[n,i,o]}class Jd extends Y{constructor(e){super(),X(this,e,Jl,ql,Q,{class:0,keybinding:2,icons:1})}}function Kl(r){let e,t;return{c(){e=D("svg"),t=D("path"),y(t,"fill-rule","evenodd"),y(t,"clip-rule","evenodd"),y(t,"d","M5 2V1H10V2H5ZM4.75 0C4.33579 0 4 0.335786 4 0.75V1H3.5C2.67157 1 2 1.67157 2 2.5V12.5C2 13.3284 2.67157 14 3.5 14H7V13H3.5C3.22386 13 3 12.7761 3 12.5V2.5C3 2.22386 3.22386 2 3.5 2H4V2.25C4 2.66421 4.33579 3 4.75 3H10.25C10.6642 3 11 2.66421 11 2.25V2H11.5C11.7761 2 12 2.22386 12 2.5V7H13V2.5C13 1.67157 12.3284 1 11.5 1H11V0.75C11 0.335786 10.6642 0 10.25 0H4.75ZM9 8.5C9 8.77614 8.77614 9 8.5 9C8.22386 9 8 8.77614 8 8.5C8 8.22386 8.22386 8 8.5 8C8.77614 8 9 8.22386 9 8.5ZM10.5 9C10.7761 9 11 8.77614 11 8.5C11 8.22386 10.7761 8 10.5 8C10.2239 8 10 8.22386 10 8.5C10 8.77614 10.2239 9 10.5 9ZM13 8.5C13 8.77614 12.7761 9 12.5 9C12.2239 9 12 8.77614 12 8.5C12 8.22386 12.2239 8 12.5 8C12.7761 8 13 8.22386 13 8.5ZM14.5 9C14.7761 9 15 8.77614 15 8.5C15 8.22386 14.7761 8 14.5 8C14.2239 8 14 8.22386 14 8.5C14 8.77614 14.2239 9 14.5 9ZM15 10.5C15 10.7761 14.7761 11 14.5 11C14.2239 11 14 10.7761 14 10.5C14 10.2239 14.2239 10 14.5 10C14.7761 10 15 10.2239 15 10.5ZM14.5 13C14.7761 13 15 12.7761 15 12.5C15 12.2239 14.7761 12 14.5 12C14.2239 12 14 12.2239 14 12.5C14 12.7761 14.2239 13 14.5 13ZM14.5 15C14.7761 15 15 14.7761 15 14.5C15 14.2239 14.7761 14 14.5 14C14.2239 14 14 14.2239 14 14.5C14 14.7761 14.2239 15 14.5 15ZM8.5 11C8.77614 11 9 10.7761 9 10.5C9 10.2239 8.77614 10 8.5 10C8.22386 10 8 10.2239 8 10.5C8 10.7761 8.22386 11 8.5 11ZM9 12.5C9 12.7761 8.77614 13 8.5 13C8.22386 13 8 12.7761 8 12.5C8 12.2239 8.22386 12 8.5 12C8.77614 12 9 12.2239 9 12.5ZM8.5 15C8.77614 15 9 14.7761 9 14.5C9 14.2239 8.77614 14 8.5 14C8.22386 14 8 14.2239 8 14.5C8 14.7761 8.22386 15 8.5 15ZM11 14.5C11 14.7761 10.7761 15 10.5 15C10.2239 15 10 14.7761 10 14.5C10 14.2239 10.2239 14 10.5 14C10.7761 14 11 14.2239 11 14.5ZM12.5 15C12.7761 15 13 14.7761 13 14.5C13 14.2239 12.7761 14 12.5 14C12.2239 14 12 14.2239 12 14.5C12 14.7761 12.2239 15 12.5 15Z"),y(t,"fill","currentColor"),y(e,"width","15"),y(e,"height","15"),y(e,"viewBox","0 0 15 15"),y(e,"fill","none"),y(e,"xmlns","http://www.w3.org/2000/svg")},m(n,o){Z(n,e,o),E(e,t)},p:J,i:J,o:J,d(n){n&&U(e)}}}class Kd extends Y{constructor(e){super(),X(this,e,null,Kl,Q,{})}}function Wl(r){let e,t,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},r[0]],o={};for(let i=0;i<n.length;i+=1)o=Eo(o,n[i]);return{c(){e=D("svg"),t=new Nl(!0),this.h()},l(i){e=$l(i,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var s=Dl(e);t=Al(s,!0),s.forEach(U),this.h()},h(){t.a=null,lr(e,o)},m(i,s){Ll(i,e,s),t.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M133.9 232 65.8 95.9 383.4 232zm0 48h249.5L65.8 416.1l68-136.1zM44.6 34.6C32.3 29.3 17.9 32.3 8.7 42S-2.6 66.3 3.4 78.3L92.2 256 3.4 433.7c-6 12-3.9 26.5 5.3 36.3s23.5 12.7 35.9 7.5l448-192c11.8-5 19.4-16.6 19.4-29.4s-7.6-24.4-19.4-29.4l-448-192z"/>',e)},p(i,[s]){lr(e,o=Il(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&s&&i[0]]))},i:J,o:J,d(i){i&&U(e)}}}function Ul(r,e,t){return r.$$set=n=>{t(0,e=Eo(Eo({},e),ar(n)))},[e=ar(e)]}class Wd extends Y{constructor(e){super(),X(this,e,Ul,Wl,Q,{})}}function le(r){this.content=r}function gs(r,e,t){for(let n=0;;n++){if(n==r.childCount||n==e.childCount)return r.childCount==e.childCount?null:t;let o=r.child(n),i=e.child(n);if(o!=i){if(!o.sameMarkup(i))return t;if(o.isText&&o.text!=i.text){for(let s=0;o.text[s]==i.text[s];s++)t++;return t}if(o.content.size||i.content.size){let s=gs(o.content,i.content,t+1);if(s!=null)return s}t+=o.nodeSize}else t+=o.nodeSize}}function Cs(r,e,t,n){for(let o=r.childCount,i=e.childCount;;){if(o==0||i==0)return o==i?null:{a:t,b:n};let s=r.child(--o),l=e.child(--i),a=s.nodeSize;if(s!=l){if(!s.sameMarkup(l))return{a:t,b:n};if(s.isText&&s.text!=l.text){let c=0,d=Math.min(s.text.length,l.text.length);for(;c<d&&s.text[s.text.length-c-1]==l.text[l.text.length-c-1];)c++,t--,n--;return{a:t,b:n}}if(s.content.size||l.content.size){let c=Cs(s.content,l.content,t-1,n-1);if(c)return c}t-=a,n-=a}else t-=a,n-=a}}le.prototype={constructor:le,find:function(r){for(var e=0;e<this.content.length;e+=2)if(this.content[e]===r)return e;return-1},get:function(r){var e=this.find(r);return e==-1?void 0:this.content[e+1]},update:function(r,e,t){var n=t&&t!=r?this.remove(t):this,o=n.find(r),i=n.content.slice();return o==-1?i.push(t||r,e):(i[o+1]=e,t&&(i[o]=t)),new le(i)},remove:function(r){var e=this.find(r);if(e==-1)return this;var t=this.content.slice();return t.splice(e,2),new le(t)},addToStart:function(r,e){return new le([r,e].concat(this.remove(r).content))},addToEnd:function(r,e){var t=this.remove(r).content.slice();return t.push(r,e),new le(t)},addBefore:function(r,e,t){var n=this.remove(e),o=n.content.slice(),i=n.find(r);return o.splice(i==-1?o.length:i,0,e,t),new le(o)},forEach:function(r){for(var e=0;e<this.content.length;e+=2)r(this.content[e],this.content[e+1])},prepend:function(r){return(r=le.from(r)).size?new le(r.content.concat(this.subtract(r).content)):this},append:function(r){return(r=le.from(r)).size?new le(this.subtract(r).content.concat(r.content)):this},subtract:function(r){var e=this;r=le.from(r);for(var t=0;t<r.content.length;t+=2)e=e.remove(r.content[t]);return e},toObject:function(){var r={};return this.forEach(function(e,t){r[e]=t}),r},get size(){return this.content.length>>1}},le.from=function(r){if(r instanceof le)return r;var e=[];if(r)for(var t in r)e.push(t,r[t]);return new le(e)};class S{constructor(e,t){if(this.content=e,this.size=t||0,t==null)for(let n=0;n<e.length;n++)this.size+=e[n].nodeSize}nodesBetween(e,t,n,o=0,i){for(let s=0,l=0;l<t;s++){let a=this.content[s],c=l+a.nodeSize;if(c>e&&n(a,o+l,i||null,s)!==!1&&a.content.size){let d=l+1;a.nodesBetween(Math.max(0,e-d),Math.min(a.content.size,t-d),n,o+d)}l=c}}descendants(e){this.nodesBetween(0,this.size,e)}textBetween(e,t,n,o){let i="",s=!0;return this.nodesBetween(e,t,(l,a)=>{let c=l.isText?l.text.slice(Math.max(e,a)-a,t-a):l.isLeaf?o?typeof o=="function"?o(l):o:l.type.spec.leafText?l.type.spec.leafText(l):"":"";l.isBlock&&(l.isLeaf&&c||l.isTextblock)&&n&&(s?s=!1:i+=n),i+=c},0),i}append(e){if(!e.size)return this;if(!this.size)return e;let t=this.lastChild,n=e.firstChild,o=this.content.slice(),i=0;for(t.isText&&t.sameMarkup(n)&&(o[o.length-1]=t.withText(t.text+n.text),i=1);i<e.content.length;i++)o.push(e.content[i]);return new S(o,this.size+e.size)}cut(e,t=this.size){if(e==0&&t==this.size)return this;let n=[],o=0;if(t>e)for(let i=0,s=0;s<t;i++){let l=this.content[i],a=s+l.nodeSize;a>e&&((s<e||a>t)&&(l=l.isText?l.cut(Math.max(0,e-s),Math.min(l.text.length,t-s)):l.cut(Math.max(0,e-s-1),Math.min(l.content.size,t-s-1))),n.push(l),o+=l.nodeSize),s=a}return new S(n,o)}cutByIndex(e,t){return e==t?S.empty:e==0&&t==this.content.length?this:new S(this.content.slice(e,t))}replaceChild(e,t){let n=this.content[e];if(n==t)return this;let o=this.content.slice(),i=this.size+t.nodeSize-n.nodeSize;return o[e]=t,new S(o,i)}addToStart(e){return new S([e].concat(this.content),this.size+e.nodeSize)}addToEnd(e){return new S(this.content.concat(e),this.size+e.nodeSize)}eq(e){if(this.content.length!=e.content.length)return!1;for(let t=0;t<this.content.length;t++)if(!this.content[t].eq(e.content[t]))return!1;return!0}get firstChild(){return this.content.length?this.content[0]:null}get lastChild(){return this.content.length?this.content[this.content.length-1]:null}get childCount(){return this.content.length}child(e){let t=this.content[e];if(!t)throw new RangeError("Index "+e+" out of range for "+this);return t}maybeChild(e){return this.content[e]||null}forEach(e){for(let t=0,n=0;t<this.content.length;t++){let o=this.content[t];e(o,n,t),n+=o.nodeSize}}findDiffStart(e,t=0){return gs(this,e,t)}findDiffEnd(e,t=this.size,n=e.size){return Cs(this,e,t,n)}findIndex(e,t=-1){if(e==0)return kn(0,e);if(e==this.size)return kn(this.content.length,e);if(e>this.size||e<0)throw new RangeError(`Position ${e} outside of fragment (${this})`);for(let n=0,o=0;;n++){let i=o+this.child(n).nodeSize;if(i>=e)return i==e||t>0?kn(n+1,i):kn(n,o);o=i}}toString(){return"<"+this.toStringInner()+">"}toStringInner(){return this.content.join(", ")}toJSON(){return this.content.length?this.content.map(e=>e.toJSON()):null}static fromJSON(e,t){if(!t)return S.empty;if(!Array.isArray(t))throw new RangeError("Invalid input for Fragment.fromJSON");return new S(t.map(e.nodeFromJSON))}static fromArray(e){if(!e.length)return S.empty;let t,n=0;for(let o=0;o<e.length;o++){let i=e[o];n+=i.nodeSize,o&&i.isText&&e[o-1].sameMarkup(i)?(t||(t=e.slice(0,o)),t[t.length-1]=i.withText(t[t.length-1].text+i.text)):t&&t.push(i)}return new S(t||e,n)}static from(e){if(!e)return S.empty;if(e instanceof S)return e;if(Array.isArray(e))return this.fromArray(e);if(e.attrs)return new S([e],e.nodeSize);throw new RangeError("Can not convert "+e+" to a Fragment"+(e.nodesBetween?" (looks like multiple versions of prosemirror-model were loaded)":""))}}S.empty=new S([],0);const to={index:0,offset:0};function kn(r,e){return to.index=r,to.offset=e,to}function Dn(r,e){if(r===e)return!0;if(!r||typeof r!="object"||!e||typeof e!="object")return!1;let t=Array.isArray(r);if(Array.isArray(e)!=t)return!1;if(t){if(r.length!=e.length)return!1;for(let n=0;n<r.length;n++)if(!Dn(r[n],e[n]))return!1}else{for(let n in r)if(!(n in e)||!Dn(r[n],e[n]))return!1;for(let n in e)if(!(n in r))return!1}return!0}let W=class No{constructor(e,t){this.type=e,this.attrs=t}addToSet(e){let t,n=!1;for(let o=0;o<e.length;o++){let i=e[o];if(this.eq(i))return e;if(this.type.excludes(i.type))t||(t=e.slice(0,o));else{if(i.type.excludes(this.type))return e;!n&&i.type.rank>this.type.rank&&(t||(t=e.slice(0,o)),t.push(this),n=!0),t&&t.push(i)}}return t||(t=e.slice()),n||t.push(this),t}removeFromSet(e){for(let t=0;t<e.length;t++)if(this.eq(e[t]))return e.slice(0,t).concat(e.slice(t+1));return e}isInSet(e){for(let t=0;t<e.length;t++)if(this.eq(e[t]))return!0;return!1}eq(e){return this==e||this.type==e.type&&Dn(this.attrs,e.attrs)}toJSON(){let e={type:this.type.name};for(let t in this.attrs){e.attrs=this.attrs;break}return e}static fromJSON(e,t){if(!t)throw new RangeError("Invalid input for Mark.fromJSON");let n=e.marks[t.type];if(!n)throw new RangeError(`There is no mark type ${t.type} in this schema`);let o=n.create(t.attrs);return n.checkAttrs(o.attrs),o}static sameSet(e,t){if(e==t)return!0;if(e.length!=t.length)return!1;for(let n=0;n<e.length;n++)if(!e[n].eq(t[n]))return!1;return!0}static setFrom(e){if(!e||Array.isArray(e)&&e.length==0)return No.none;if(e instanceof No)return[e];let t=e.slice();return t.sort((n,o)=>n.type.rank-o.type.rank),t}};W.none=[];class An extends Error{}class T{constructor(e,t,n){this.content=e,this.openStart=t,this.openEnd=n}get size(){return this.content.size-this.openStart-this.openEnd}insertAt(e,t){let n=ws(this.content,e+this.openStart,t);return n&&new T(n,this.openStart,this.openEnd)}removeBetween(e,t){return new T(ys(this.content,e+this.openStart,t+this.openStart),this.openStart,this.openEnd)}eq(e){return this.content.eq(e.content)&&this.openStart==e.openStart&&this.openEnd==e.openEnd}toString(){return this.content+"("+this.openStart+","+this.openEnd+")"}toJSON(){if(!this.content.size)return null;let e={content:this.content.toJSON()};return this.openStart>0&&(e.openStart=this.openStart),this.openEnd>0&&(e.openEnd=this.openEnd),e}static fromJSON(e,t){if(!t)return T.empty;let n=t.openStart||0,o=t.openEnd||0;if(typeof n!="number"||typeof o!="number")throw new RangeError("Invalid input for Slice.fromJSON");return new T(S.fromJSON(e,t.content),n,o)}static maxOpen(e,t=!0){let n=0,o=0;for(let i=e.firstChild;i&&!i.isLeaf&&(t||!i.type.spec.isolating);i=i.firstChild)n++;for(let i=e.lastChild;i&&!i.isLeaf&&(t||!i.type.spec.isolating);i=i.lastChild)o++;return new T(e,n,o)}}function ys(r,e,t){let{index:n,offset:o}=r.findIndex(e),i=r.maybeChild(n),{index:s,offset:l}=r.findIndex(t);if(o==e||i.isText){if(l!=t&&!r.child(s).isText)throw new RangeError("Removing non-flat range");return r.cut(0,e).append(r.cut(t))}if(n!=s)throw new RangeError("Removing non-flat range");return r.replaceChild(n,i.copy(ys(i.content,e-o-1,t-o-1)))}function ws(r,e,t,n){let{index:o,offset:i}=r.findIndex(e),s=r.maybeChild(o);if(i==e||s.isText)return r.cut(0,e).append(t).append(r.cut(e));let l=ws(s.content,e-i-1,t);return l&&r.replaceChild(o,s.copy(l))}function Zl(r,e,t){if(t.openStart>r.depth)throw new An("Inserted content deeper than insertion position");if(r.depth-t.openStart!=e.depth-t.openEnd)throw new An("Inconsistent open depths");return vs(r,e,t,0)}function vs(r,e,t,n){let o=r.index(n),i=r.node(n);if(o==e.index(n)&&n<r.depth-t.openStart){let s=vs(r,e,t,n+1);return i.copy(i.content.replaceChild(o,s))}if(t.content.size){if(t.openStart||t.openEnd||r.depth!=n||e.depth!=n){let{start:s,end:l}=function(a,c){let d=c.depth-a.openStart,h=c.node(d).copy(a.content);for(let p=d-1;p>=0;p--)h=c.node(p).copy(S.from(h));return{start:h.resolveNoCache(a.openStart+d),end:h.resolveNoCache(h.content.size-a.openEnd-d)}}(t,r);return St(i,bs(r,s,l,e,n))}{let s=r.parent,l=s.content;return St(s,l.cut(0,r.parentOffset).append(t.content).append(l.cut(e.parentOffset)))}}return St(i,Ln(r,e,n))}function xs(r,e){if(!e.type.compatibleContent(r.type))throw new An("Cannot join "+e.type.name+" onto "+r.type.name)}function $o(r,e,t){let n=r.node(t);return xs(n,e.node(t)),n}function Mt(r,e){let t=e.length-1;t>=0&&r.isText&&r.sameMarkup(e[t])?e[t]=r.withText(e[t].text+r.text):e.push(r)}function cn(r,e,t,n){let o=(e||r).node(t),i=0,s=e?e.index(t):o.childCount;r&&(i=r.index(t),r.depth>t?i++:r.textOffset&&(Mt(r.nodeAfter,n),i++));for(let l=i;l<s;l++)Mt(o.child(l),n);e&&e.depth==t&&e.textOffset&&Mt(e.nodeBefore,n)}function St(r,e){return r.type.checkContent(e),r.copy(e)}function bs(r,e,t,n,o){let i=r.depth>o&&$o(r,e,o+1),s=n.depth>o&&$o(t,n,o+1),l=[];return cn(null,r,o,l),i&&s&&e.index(o)==t.index(o)?(xs(i,s),Mt(St(i,bs(r,e,t,n,o+1)),l)):(i&&Mt(St(i,Ln(r,e,o+1)),l),cn(e,t,o,l),s&&Mt(St(s,Ln(t,n,o+1)),l)),cn(n,null,o,l),new S(l)}function Ln(r,e,t){let n=[];return cn(null,r,t,n),r.depth>t&&Mt(St($o(r,e,t+1),Ln(r,e,t+1)),n),cn(e,null,t,n),new S(n)}T.empty=new T(S.empty,0,0);class mn{constructor(e,t,n){this.pos=e,this.path=t,this.parentOffset=n,this.depth=t.length/3-1}resolveDepth(e){return e==null?this.depth:e<0?this.depth+e:e}get parent(){return this.node(this.depth)}get doc(){return this.node(0)}node(e){return this.path[3*this.resolveDepth(e)]}index(e){return this.path[3*this.resolveDepth(e)+1]}indexAfter(e){return e=this.resolveDepth(e),this.index(e)+(e!=this.depth||this.textOffset?1:0)}start(e){return(e=this.resolveDepth(e))==0?0:this.path[3*e-1]+1}end(e){return e=this.resolveDepth(e),this.start(e)+this.node(e).content.size}before(e){if(!(e=this.resolveDepth(e)))throw new RangeError("There is no position before the top-level node");return e==this.depth+1?this.pos:this.path[3*e-1]}after(e){if(!(e=this.resolveDepth(e)))throw new RangeError("There is no position after the top-level node");return e==this.depth+1?this.pos:this.path[3*e-1]+this.path[3*e].nodeSize}get textOffset(){return this.pos-this.path[this.path.length-1]}get nodeAfter(){let e=this.parent,t=this.index(this.depth);if(t==e.childCount)return null;let n=this.pos-this.path[this.path.length-1],o=e.child(t);return n?e.child(t).cut(n):o}get nodeBefore(){let e=this.index(this.depth),t=this.pos-this.path[this.path.length-1];return t?this.parent.child(e).cut(0,t):e==0?null:this.parent.child(e-1)}posAtIndex(e,t){t=this.resolveDepth(t);let n=this.path[3*t],o=t==0?0:this.path[3*t-1]+1;for(let i=0;i<e;i++)o+=n.child(i).nodeSize;return o}marks(){let e=this.parent,t=this.index();if(e.content.size==0)return W.none;if(this.textOffset)return e.child(t).marks;let n=e.maybeChild(t-1),o=e.maybeChild(t);if(!n){let l=n;n=o,o=l}let i=n.marks;for(var s=0;s<i.length;s++)i[s].type.spec.inclusive!==!1||o&&i[s].isInSet(o.marks)||(i=i[s--].removeFromSet(i));return i}marksAcross(e){let t=this.parent.maybeChild(this.index());if(!t||!t.isInline)return null;let n=t.marks,o=e.parent.maybeChild(e.index());for(var i=0;i<n.length;i++)n[i].type.spec.inclusive!==!1||o&&n[i].isInSet(o.marks)||(n=n[i--].removeFromSet(n));return n}sharedDepth(e){for(let t=this.depth;t>0;t--)if(this.start(t)<=e&&this.end(t)>=e)return t;return 0}blockRange(e=this,t){if(e.pos<this.pos)return e.blockRange(this);for(let n=this.depth-(this.parent.inlineContent||this.pos==e.pos?1:0);n>=0;n--)if(e.pos<=this.end(n)&&(!t||t(this.node(n))))return new In(this,e,n);return null}sameParent(e){return this.pos-this.parentOffset==e.pos-e.parentOffset}max(e){return e.pos>this.pos?e:this}min(e){return e.pos<this.pos?e:this}toString(){let e="";for(let t=1;t<=this.depth;t++)e+=(e?"/":"")+this.node(t).type.name+"_"+this.index(t-1);return e+":"+this.parentOffset}static resolve(e,t){if(!(t>=0&&t<=e.content.size))throw new RangeError("Position "+t+" out of range");let n=[],o=0,i=t;for(let s=e;;){let{index:l,offset:a}=s.content.findIndex(i),c=i-a;if(n.push(s,l,o+a),!c||(s=s.child(l),s.isText))break;i=c-1,o+=a+1}return new mn(t,n,i)}static resolveCached(e,t){let n=ur.get(e);if(n)for(let i=0;i<n.elts.length;i++){let s=n.elts[i];if(s.pos==t)return s}else ur.set(e,n=new Gl);let o=n.elts[n.i]=mn.resolve(e,t);return n.i=(n.i+1)%Yl,o}}class Gl{constructor(){this.elts=[],this.i=0}}const Yl=12,ur=new WeakMap;class In{constructor(e,t,n){this.$from=e,this.$to=t,this.depth=n}get start(){return this.$from.before(this.depth+1)}get end(){return this.$to.after(this.depth+1)}get parent(){return this.$from.node(this.depth)}get startIndex(){return this.$from.index(this.depth)}get endIndex(){return this.$to.indexAfter(this.depth)}}const Xl=Object.create(null);let Ot=class Do{constructor(e,t,n,o=W.none){this.type=e,this.attrs=t,this.marks=o,this.content=n||S.empty}get nodeSize(){return this.isLeaf?1:2+this.content.size}get childCount(){return this.content.childCount}child(e){return this.content.child(e)}maybeChild(e){return this.content.maybeChild(e)}forEach(e){this.content.forEach(e)}nodesBetween(e,t,n,o=0){this.content.nodesBetween(e,t,n,o,this)}descendants(e){this.nodesBetween(0,this.content.size,e)}get textContent(){return this.isLeaf&&this.type.spec.leafText?this.type.spec.leafText(this):this.textBetween(0,this.content.size,"")}textBetween(e,t,n,o){return this.content.textBetween(e,t,n,o)}get firstChild(){return this.content.firstChild}get lastChild(){return this.content.lastChild}eq(e){return this==e||this.sameMarkup(e)&&this.content.eq(e.content)}sameMarkup(e){return this.hasMarkup(e.type,e.attrs,e.marks)}hasMarkup(e,t,n){return this.type==e&&Dn(this.attrs,t||e.defaultAttrs||Xl)&&W.sameSet(this.marks,n||W.none)}copy(e=null){return e==this.content?this:new Do(this.type,this.attrs,e,this.marks)}mark(e){return e==this.marks?this:new Do(this.type,this.attrs,this.content,e)}cut(e,t=this.content.size){return e==0&&t==this.content.size?this:this.copy(this.content.cut(e,t))}slice(e,t=this.content.size,n=!1){if(e==t)return T.empty;let o=this.resolve(e),i=this.resolve(t),s=n?0:o.sharedDepth(t),l=o.start(s),a=o.node(s).content.cut(o.pos-l,i.pos-l);return new T(a,o.depth-s,i.depth-s)}replace(e,t,n){return Zl(this.resolve(e),this.resolve(t),n)}nodeAt(e){for(let t=this;;){let{index:n,offset:o}=t.content.findIndex(e);if(t=t.maybeChild(n),!t)return null;if(o==e||t.isText)return t;e-=o+1}}childAfter(e){let{index:t,offset:n}=this.content.findIndex(e);return{node:this.content.maybeChild(t),index:t,offset:n}}childBefore(e){if(e==0)return{node:null,index:0,offset:0};let{index:t,offset:n}=this.content.findIndex(e);if(n<e)return{node:this.content.child(t),index:t,offset:n};let o=this.content.child(t-1);return{node:o,index:t-1,offset:n-o.nodeSize}}resolve(e){return mn.resolveCached(this,e)}resolveNoCache(e){return mn.resolve(this,e)}rangeHasMark(e,t,n){let o=!1;return t>e&&this.nodesBetween(e,t,i=>(n.isInSet(i.marks)&&(o=!0),!o)),o}get isBlock(){return this.type.isBlock}get isTextblock(){return this.type.isTextblock}get inlineContent(){return this.type.inlineContent}get isInline(){return this.type.isInline}get isText(){return this.type.isText}get isLeaf(){return this.type.isLeaf}get isAtom(){return this.type.isAtom}toString(){if(this.type.spec.toDebugString)return this.type.spec.toDebugString(this);let e=this.type.name;return this.content.size&&(e+="("+this.content.toStringInner()+")"),ks(this.marks,e)}contentMatchAt(e){let t=this.type.contentMatch.matchFragment(this.content,0,e);if(!t)throw new Error("Called contentMatchAt on a node with invalid content");return t}canReplace(e,t,n=S.empty,o=0,i=n.childCount){let s=this.contentMatchAt(e).matchFragment(n,o,i),l=s&&s.matchFragment(this.content,t);if(!l||!l.validEnd)return!1;for(let a=o;a<i;a++)if(!this.type.allowsMarks(n.child(a).marks))return!1;return!0}canReplaceWith(e,t,n,o){if(o&&!this.type.allowsMarks(o))return!1;let i=this.contentMatchAt(e).matchType(n),s=i&&i.matchFragment(this.content,t);return!!s&&s.validEnd}canAppend(e){return e.content.size?this.canReplace(this.childCount,this.childCount,e.content):this.type.compatibleContent(e.type)}check(){this.type.checkContent(this.content),this.type.checkAttrs(this.attrs);let e=W.none;for(let t=0;t<this.marks.length;t++){let n=this.marks[t];n.type.checkAttrs(n.attrs),e=n.addToSet(e)}if(!W.sameSet(e,this.marks))throw new RangeError(`Invalid collection of marks for node ${this.type.name}: ${this.marks.map(t=>t.type.name)}`);this.content.forEach(t=>t.check())}toJSON(){let e={type:this.type.name};for(let t in this.attrs){e.attrs=this.attrs;break}return this.content.size&&(e.content=this.content.toJSON()),this.marks.length&&(e.marks=this.marks.map(t=>t.toJSON())),e}static fromJSON(e,t){if(!t)throw new RangeError("Invalid input for Node.fromJSON");let n;if(t.marks){if(!Array.isArray(t.marks))throw new RangeError("Invalid mark data for Node.fromJSON");n=t.marks.map(e.markFromJSON)}if(t.type=="text"){if(typeof t.text!="string")throw new RangeError("Invalid text node in JSON");return e.text(t.text,n)}let o=S.fromJSON(e,t.content),i=e.nodeType(t.type).create(t.attrs,o,n);return i.type.checkAttrs(i.attrs),i}};Ot.prototype.text=void 0;class Pn extends Ot{constructor(e,t,n,o){if(super(e,t,null,o),!n)throw new RangeError("Empty text nodes are not allowed");this.text=n}toString(){return this.type.spec.toDebugString?this.type.spec.toDebugString(this):ks(this.marks,JSON.stringify(this.text))}get textContent(){return this.text}textBetween(e,t){return this.text.slice(e,t)}get nodeSize(){return this.text.length}mark(e){return e==this.marks?this:new Pn(this.type,this.attrs,this.text,e)}withText(e){return e==this.text?this:new Pn(this.type,this.attrs,e,this.marks)}cut(e=0,t=this.text.length){return e==0&&t==this.text.length?this:this.withText(this.text.slice(e,t))}eq(e){return this.sameMarkup(e)&&this.text==e.text}toJSON(){let e=super.toJSON();return e.text=this.text,e}}function ks(r,e){for(let t=r.length-1;t>=0;t--)e=r[t].type.name+"("+e+")";return e}class Nt{constructor(e){this.validEnd=e,this.next=[],this.wrapCache=[]}static parse(e,t){let n=new Ql(e,t);if(n.next==null)return Nt.empty;let o=Ms(n);n.next&&n.err("Unexpected trailing text");let i=function(s){let l=Object.create(null);return a(mr(s,0));function a(c){let d=[];c.forEach(p=>{s[p].forEach(({term:f,to:u})=>{if(!f)return;let m;for(let g=0;g<d.length;g++)d[g][0]==f&&(m=d[g][1]);mr(s,u).forEach(g=>{m||d.push([f,m=[]]),m.indexOf(g)==-1&&m.push(g)})})});let h=l[c.join(",")]=new Nt(c.indexOf(s.length-1)>-1);for(let p=0;p<d.length;p++){let f=d[p][1].sort(Ss);h.next.push({type:d[p][0],next:l[f.join(",")]||a(f)})}return h}}(function(s){let l=[[]];return d(h(s,0),a()),l;function a(){return l.push([])-1}function c(p,f,u){let m={term:u,to:f};return l[p].push(m),m}function d(p,f){p.forEach(u=>u.to=f)}function h(p,f){if(p.type=="choice")return p.exprs.reduce((u,m)=>u.concat(h(m,f)),[]);if(p.type!="seq"){if(p.type=="star"){let u=a();return c(f,u),d(h(p.expr,u),u),[c(u)]}if(p.type=="plus"){let u=a();return d(h(p.expr,f),u),d(h(p.expr,u),u),[c(u)]}if(p.type=="opt")return[c(f)].concat(h(p.expr,f));if(p.type=="range"){let u=f;for(let m=0;m<p.min;m++){let g=a();d(h(p.expr,u),g),u=g}if(p.max==-1)d(h(p.expr,u),u);else for(let m=p.min;m<p.max;m++){let g=a();c(u,g),d(h(p.expr,u),g),u=g}return[c(u)]}if(p.type=="name")return[c(f,void 0,p.value)];throw new Error("Unknown expr type")}for(let u=0;;u++){let m=h(p.exprs[u],f);if(u==p.exprs.length-1)return m;d(m,f=a())}}}(o));return function(s,l){for(let a=0,c=[s];a<c.length;a++){let d=c[a],h=!d.validEnd,p=[];for(let f=0;f<d.next.length;f++){let{type:u,next:m}=d.next[f];p.push(u.name),!h||u.isText||u.hasRequiredAttrs()||(h=!1),c.indexOf(m)==-1&&c.push(m)}h&&l.err("Only non-generatable nodes ("+p.join(", ")+") in a required position (see https://prosemirror.net/docs/guide/#generatable)")}}(i,n),i}matchType(e){for(let t=0;t<this.next.length;t++)if(this.next[t].type==e)return this.next[t].next;return null}matchFragment(e,t=0,n=e.childCount){let o=this;for(let i=t;o&&i<n;i++)o=o.matchType(e.child(i).type);return o}get inlineContent(){return this.next.length!=0&&this.next[0].type.isInline}get defaultType(){for(let e=0;e<this.next.length;e++){let{type:t}=this.next[e];if(!t.isText&&!t.hasRequiredAttrs())return t}return null}compatible(e){for(let t=0;t<this.next.length;t++)for(let n=0;n<e.next.length;n++)if(this.next[t].type==e.next[n].type)return!0;return!1}fillBefore(e,t=!1,n=0){let o=[this];return function i(s,l){let a=s.matchFragment(e,n);if(a&&(!t||a.validEnd))return S.from(l.map(c=>c.createAndFill()));for(let c=0;c<s.next.length;c++){let{type:d,next:h}=s.next[c];if(!d.isText&&!d.hasRequiredAttrs()&&o.indexOf(h)==-1){o.push(h);let p=i(h,l.concat(d));if(p)return p}}return null}(this,[])}findWrapping(e){for(let n=0;n<this.wrapCache.length;n+=2)if(this.wrapCache[n]==e)return this.wrapCache[n+1];let t=this.computeWrapping(e);return this.wrapCache.push(e,t),t}computeWrapping(e){let t=Object.create(null),n=[{match:this,type:null,via:null}];for(;n.length;){let o=n.shift(),i=o.match;if(i.matchType(e)){let s=[];for(let l=o;l.type;l=l.via)s.push(l.type);return s.reverse()}for(let s=0;s<i.next.length;s++){let{type:l,next:a}=i.next[s];l.isLeaf||l.hasRequiredAttrs()||l.name in t||o.type&&!a.validEnd||(n.push({match:l.contentMatch,type:l,via:o}),t[l.name]=!0)}}return null}get edgeCount(){return this.next.length}edge(e){if(e>=this.next.length)throw new RangeError(`There's no ${e}th edge in this content match`);return this.next[e]}toString(){let e=[];return function t(n){e.push(n);for(let o=0;o<n.next.length;o++)e.indexOf(n.next[o].next)==-1&&t(n.next[o].next)}(this),e.map((t,n)=>{let o=n+(t.validEnd?"*":" ")+" ";for(let i=0;i<t.next.length;i++)o+=(i?", ":"")+t.next[i].type.name+"->"+e.indexOf(t.next[i].next);return o}).join(`
`)}}Nt.empty=new Nt(!0);class Ql{constructor(e,t){this.string=e,this.nodeTypes=t,this.inline=null,this.pos=0,this.tokens=e.split(/\s*(?=\b|\W|$)/),this.tokens[this.tokens.length-1]==""&&this.tokens.pop(),this.tokens[0]==""&&this.tokens.shift()}get next(){return this.tokens[this.pos]}eat(e){return this.next==e&&(this.pos++||!0)}err(e){throw new SyntaxError(e+" (in content expression '"+this.string+"')")}}function Ms(r){let e=[];do e.push(ea(r));while(r.eat("|"));return e.length==1?e[0]:{type:"choice",exprs:e}}function ea(r){let e=[];do e.push(ta(r));while(r.next&&r.next!=")"&&r.next!="|");return e.length==1?e[0]:{type:"seq",exprs:e}}function ta(r){let e=function(t){if(t.eat("(")){let n=Ms(t);return t.eat(")")||t.err("Missing closing paren"),n}if(!/\W/.test(t.next)){let n=function(o,i){let s=o.nodeTypes,l=s[i];if(l)return[l];let a=[];for(let c in s){let d=s[c];d.isInGroup(i)&&a.push(d)}return a.length==0&&o.err("No node type or group '"+i+"' found"),a}(t,t.next).map(o=>(t.inline==null?t.inline=o.isInline:t.inline!=o.isInline&&t.err("Mixing inline and block content"),{type:"name",value:o}));return t.pos++,n.length==1?n[0]:{type:"choice",exprs:n}}t.err("Unexpected token '"+t.next+"'")}(r);for(;;)if(r.eat("+"))e={type:"plus",expr:e};else if(r.eat("*"))e={type:"star",expr:e};else if(r.eat("?"))e={type:"opt",expr:e};else{if(!r.eat("{"))break;e=na(r,e)}return e}function fr(r){/\D/.test(r.next)&&r.err("Expected number, got '"+r.next+"'");let e=Number(r.next);return r.pos++,e}function na(r,e){let t=fr(r),n=t;return r.eat(",")&&(n=r.next!="}"?fr(r):-1),r.eat("}")||r.err("Unclosed braced range"),{type:"range",min:t,max:n,expr:e}}function Ss(r,e){return e-r}function mr(r,e){let t=[];return function n(o){let i=r[o];if(i.length==1&&!i[0].term)return n(i[0].to);t.push(o);for(let s=0;s<i.length;s++){let{term:l,to:a}=i[s];l||t.indexOf(a)!=-1||n(a)}}(e),t.sort(Ss)}function Os(r){let e=Object.create(null);for(let t in r){let n=r[t];if(!n.hasDefault)return null;e[t]=n.default}return e}function Ts(r,e){let t=Object.create(null);for(let n in r){let o=e&&e[n];if(o===void 0){let i=r[n];if(!i.hasDefault)throw new RangeError("No value supplied for attribute "+n);o=i.default}t[n]=o}return t}function Es(r,e,t,n){for(let o in e)if(!(o in r))throw new RangeError(`Unsupported attribute ${o} for ${t} of type ${o}`);for(let o in r){let i=r[o];i.validate&&i.validate(e[o])}}function Ns(r,e){let t=Object.create(null);if(e)for(let n in e)t[n]=new oa(r,n,e[n]);return t}let gr=class $s{constructor(e,t,n){this.name=e,this.schema=t,this.spec=n,this.markSet=null,this.groups=n.group?n.group.split(" "):[],this.attrs=Ns(e,n.attrs),this.defaultAttrs=Os(this.attrs),this.contentMatch=null,this.inlineContent=null,this.isBlock=!(n.inline||e=="text"),this.isText=e=="text"}get isInline(){return!this.isBlock}get isTextblock(){return this.isBlock&&this.inlineContent}get isLeaf(){return this.contentMatch==Nt.empty}get isAtom(){return this.isLeaf||!!this.spec.atom}isInGroup(e){return this.groups.indexOf(e)>-1}get whitespace(){return this.spec.whitespace||(this.spec.code?"pre":"normal")}hasRequiredAttrs(){for(let e in this.attrs)if(this.attrs[e].isRequired)return!0;return!1}compatibleContent(e){return this==e||this.contentMatch.compatible(e.contentMatch)}computeAttrs(e){return!e&&this.defaultAttrs?this.defaultAttrs:Ts(this.attrs,e)}create(e=null,t,n){if(this.isText)throw new Error("NodeType.create can't construct text nodes");return new Ot(this,this.computeAttrs(e),S.from(t),W.setFrom(n))}createChecked(e=null,t,n){return t=S.from(t),this.checkContent(t),new Ot(this,this.computeAttrs(e),t,W.setFrom(n))}createAndFill(e=null,t,n){if(e=this.computeAttrs(e),(t=S.from(t)).size){let s=this.contentMatch.fillBefore(t);if(!s)return null;t=s.append(t)}let o=this.contentMatch.matchFragment(t),i=o&&o.fillBefore(S.empty,!0);return i?new Ot(this,e,t.append(i),W.setFrom(n)):null}validContent(e){let t=this.contentMatch.matchFragment(e);if(!t||!t.validEnd)return!1;for(let n=0;n<e.childCount;n++)if(!this.allowsMarks(e.child(n).marks))return!1;return!0}checkContent(e){if(!this.validContent(e))throw new RangeError(`Invalid content for node ${this.name}: ${e.toString().slice(0,50)}`)}checkAttrs(e){Es(this.attrs,e,"node",this.name)}allowsMarkType(e){return this.markSet==null||this.markSet.indexOf(e)>-1}allowsMarks(e){if(this.markSet==null)return!0;for(let t=0;t<e.length;t++)if(!this.allowsMarkType(e[t].type))return!1;return!0}allowedMarks(e){if(this.markSet==null)return e;let t;for(let n=0;n<e.length;n++)this.allowsMarkType(e[n].type)?t&&t.push(e[n]):t||(t=e.slice(0,n));return t?t.length?t:W.none:e}static compile(e,t){let n=Object.create(null);e.forEach((i,s)=>n[i]=new $s(i,t,s));let o=t.spec.topNode||"doc";if(!n[o])throw new RangeError("Schema is missing its top node type ('"+o+"')");if(!n.text)throw new RangeError("Every schema needs a 'text' type");for(let i in n.text.attrs)throw new RangeError("The text node type should not have attributes");return n}};class oa{constructor(e,t,n){this.hasDefault=Object.prototype.hasOwnProperty.call(n,"default"),this.default=n.default,this.validate=typeof n.validate=="string"?function(o,i,s){let l=s.split("|");return a=>{let c=a===null?"null":typeof a;if(l.indexOf(c)<0)throw new RangeError(`Expected value of type ${l} for attribute ${i} on type ${o}, got ${c}`)}}(e,t,n.validate):n.validate}get isRequired(){return!this.hasDefault}}class Wn{constructor(e,t,n,o){this.name=e,this.rank=t,this.schema=n,this.spec=o,this.attrs=Ns(e,o.attrs),this.excluded=null;let i=Os(this.attrs);this.instance=i?new W(this,i):null}create(e=null){return!e&&this.instance?this.instance:new W(this,Ts(this.attrs,e))}static compile(e,t){let n=Object.create(null),o=0;return e.forEach((i,s)=>n[i]=new Wn(i,o++,t,s)),n}removeFromSet(e){for(var t=0;t<e.length;t++)e[t].type==this&&(e=e.slice(0,t).concat(e.slice(t+1)),t--);return e}isInSet(e){for(let t=0;t<e.length;t++)if(e[t].type==this)return e[t]}checkAttrs(e){Es(this.attrs,e,"mark",this.name)}excludes(e){return this.excluded.indexOf(e)>-1}}class ra{constructor(e){this.linebreakReplacement=null,this.cached=Object.create(null);let t=this.spec={};for(let o in e)t[o]=e[o];t.nodes=le.from(e.nodes),t.marks=le.from(e.marks||{}),this.nodes=gr.compile(this.spec.nodes,this),this.marks=Wn.compile(this.spec.marks,this);let n=Object.create(null);for(let o in this.nodes){if(o in this.marks)throw new RangeError(o+" can not be both a node and a mark");let i=this.nodes[o],s=i.spec.content||"",l=i.spec.marks;if(i.contentMatch=n[s]||(n[s]=Nt.parse(s,this.nodes)),i.inlineContent=i.contentMatch.inlineContent,i.spec.linebreakReplacement){if(this.linebreakReplacement)throw new RangeError("Multiple linebreak nodes defined");if(!i.isInline||!i.isLeaf)throw new RangeError("Linebreak replacement nodes must be inline leaf nodes");this.linebreakReplacement=i}i.markSet=l=="_"?null:l?Cr(this,l.split(" ")):l!=""&&i.inlineContent?null:[]}for(let o in this.marks){let i=this.marks[o],s=i.spec.excludes;i.excluded=s==null?[i]:s==""?[]:Cr(this,s.split(" "))}this.nodeFromJSON=this.nodeFromJSON.bind(this),this.markFromJSON=this.markFromJSON.bind(this),this.topNodeType=this.nodes[this.spec.topNode||"doc"],this.cached.wrappings=Object.create(null)}node(e,t=null,n,o){if(typeof e=="string")e=this.nodeType(e);else{if(!(e instanceof gr))throw new RangeError("Invalid node type: "+e);if(e.schema!=this)throw new RangeError("Node type from different schema used ("+e.name+")")}return e.createChecked(t,n,o)}text(e,t){let n=this.nodes.text;return new Pn(n,n.defaultAttrs,e,W.setFrom(t))}mark(e,t){return typeof e=="string"&&(e=this.marks[e]),e.create(t)}nodeFromJSON(e){return Ot.fromJSON(this,e)}markFromJSON(e){return W.fromJSON(this,e)}nodeType(e){let t=this.nodes[e];if(!t)throw new RangeError("Unknown node type: "+e);return t}}function Cr(r,e){let t=[];for(let n=0;n<e.length;n++){let o=e[n],i=r.marks[o],s=i;if(i)t.push(i);else for(let l in r.marks){let a=r.marks[l];(o=="_"||a.spec.group&&a.spec.group.split(" ").indexOf(o)>-1)&&t.push(s=a)}if(!s)throw new SyntaxError("Unknown mark type: '"+e[n]+"'")}return t}class Qt{constructor(e,t){this.schema=e,this.rules=t,this.tags=[],this.styles=[];let n=this.matchedStyles=[];t.forEach(o=>{if(function(i){return i.tag!=null}(o))this.tags.push(o);else if(function(i){return i.style!=null}(o)){let i=/[^=]*/.exec(o.style)[0];n.indexOf(i)<0&&n.push(i),this.styles.push(o)}}),this.normalizeLists=!this.tags.some(o=>{if(!/^(ul|ol)\b/.test(o.tag)||!o.node)return!1;let i=e.nodes[o.node];return i.contentMatch.matchType(i)})}parse(e,t={}){let n=new vr(this,t,!1);return n.addAll(e,W.none,t.from,t.to),n.finish()}parseSlice(e,t={}){let n=new vr(this,t,!0);return n.addAll(e,W.none,t.from,t.to),T.maxOpen(n.finish())}matchTag(e,t,n){for(let o=n?this.tags.indexOf(n)+1:0;o<this.tags.length;o++){let i=this.tags[o];if(sa(e,i.tag)&&(i.namespace===void 0||e.namespaceURI==i.namespace)&&(!i.context||t.matchesContext(i.context))){if(i.getAttrs){let s=i.getAttrs(e);if(s===!1)continue;i.attrs=s||void 0}return i}}}matchStyle(e,t,n,o){for(let i=o?this.styles.indexOf(o)+1:0;i<this.styles.length;i++){let s=this.styles[i],l=s.style;if(!(l.indexOf(e)!=0||s.context&&!n.matchesContext(s.context)||l.length>e.length&&(l.charCodeAt(e.length)!=61||l.slice(e.length+1)!=t))){if(s.getAttrs){let a=s.getAttrs(t);if(a===!1)continue;s.attrs=a||void 0}return s}}}static schemaRules(e){let t=[];function n(o){let i=o.priority==null?50:o.priority,s=0;for(;s<t.length;s++){let l=t[s];if((l.priority==null?50:l.priority)<i)break}t.splice(s,0,o)}for(let o in e.marks){let i=e.marks[o].spec.parseDOM;i&&i.forEach(s=>{n(s=xr(s)),s.mark||s.ignore||s.clearMark||(s.mark=o)})}for(let o in e.nodes){let i=e.nodes[o].spec.parseDOM;i&&i.forEach(s=>{n(s=xr(s)),s.node||s.ignore||s.mark||(s.node=o)})}return t}static fromSchema(e){return e.cached.domParser||(e.cached.domParser=new Qt(e,Qt.schemaRules(e)))}}const Ds={address:!0,article:!0,aside:!0,blockquote:!0,canvas:!0,dd:!0,div:!0,dl:!0,fieldset:!0,figcaption:!0,figure:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,li:!0,noscript:!0,ol:!0,output:!0,p:!0,pre:!0,section:!0,table:!0,tfoot:!0,ul:!0},ia={head:!0,noscript:!0,object:!0,script:!0,style:!0,title:!0},yr={ol:!0,ul:!0};function wr(r,e,t){return e!=null?(e?1:0)|(e==="full"?2:0):r&&r.whitespace=="pre"?3:-5&t}class no{constructor(e,t,n,o,i,s){this.type=e,this.attrs=t,this.marks=n,this.solid=o,this.options=s,this.content=[],this.activeMarks=W.none,this.match=i||(4&s?null:e.contentMatch)}findWrapping(e){if(!this.match){if(!this.type)return[];let t=this.type.contentMatch.fillBefore(S.from(e));if(!t){let n,o=this.type.contentMatch;return(n=o.findWrapping(e.type))?(this.match=o,n):null}this.match=this.type.contentMatch.matchFragment(t)}return this.match.findWrapping(e.type)}finish(e){if(!(1&this.options)){let n,o=this.content[this.content.length-1];if(o&&o.isText&&(n=/[ \t\r\n\u000c]+$/.exec(o.text))){let i=o;o.text.length==n[0].length?this.content.pop():this.content[this.content.length-1]=i.withText(i.text.slice(0,i.text.length-n[0].length))}}let t=S.from(this.content);return!e&&this.match&&(t=t.append(this.match.fillBefore(S.empty,!0))),this.type?this.type.create(this.attrs,t,this.marks):t}inlineContext(e){return this.type?this.type.inlineContent:this.content.length?this.content[0].isInline:e.parentNode&&!Ds.hasOwnProperty(e.parentNode.nodeName.toLowerCase())}}class vr{constructor(e,t,n){this.parser=e,this.options=t,this.isOpen=n,this.open=0;let o,i=t.topNode,s=wr(null,t.preserveWhitespace,0)|(n?4:0);o=i?new no(i.type,i.attrs,W.none,!0,t.topMatch||i.type.contentMatch,s):new no(n?null:e.schema.topNodeType,null,W.none,!0,null,s),this.nodes=[o],this.find=t.findPositions,this.needsBlock=!1}get top(){return this.nodes[this.open]}addDOM(e,t){e.nodeType==3?this.addTextNode(e,t):e.nodeType==1&&this.addElement(e,t)}addTextNode(e,t){let n=e.nodeValue,o=this.top;if(2&o.options||o.inlineContext(e)||/[^ \t\r\n\u000c]/.test(n)){if(1&o.options)n=2&o.options?n.replace(/\r\n?/g,`
`):n.replace(/\r?\n|\r/g," ");else if(n=n.replace(/[ \t\r\n\u000c]+/g," "),/^[ \t\r\n\u000c]/.test(n)&&this.open==this.nodes.length-1){let i=o.content[o.content.length-1],s=e.previousSibling;(!i||s&&s.nodeName=="BR"||i.isText&&/[ \t\r\n\u000c]$/.test(i.text))&&(n=n.slice(1))}n&&this.insertNode(this.parser.schema.text(n),t),this.findInText(e)}else this.findInside(e)}addElement(e,t,n){let o,i=e.nodeName.toLowerCase();yr.hasOwnProperty(i)&&this.parser.normalizeLists&&function(l){for(let a=l.firstChild,c=null;a;a=a.nextSibling){let d=a.nodeType==1?a.nodeName.toLowerCase():null;d&&yr.hasOwnProperty(d)&&c?(c.appendChild(a),a=c):d=="li"?c=a:d&&(c=null)}}(e);let s=this.options.ruleFromNode&&this.options.ruleFromNode(e)||(o=this.parser.matchTag(e,this,n));if(s?s.ignore:ia.hasOwnProperty(i))this.findInside(e),this.ignoreFallback(e,t);else if(!s||s.skip||s.closeParent){s&&s.closeParent?this.open=Math.max(0,this.open-1):s&&s.skip.nodeType&&(e=s.skip);let l,a=this.top,c=this.needsBlock;if(Ds.hasOwnProperty(i))a.content.length&&a.content[0].isInline&&this.open&&(this.open--,a=this.top),l=!0,a.type||(this.needsBlock=!0);else if(!e.firstChild)return void this.leafFallback(e,t);let d=s&&s.skip?t:this.readStyles(e,t);d&&this.addAll(e,d),l&&this.sync(a),this.needsBlock=c}else{let l=this.readStyles(e,t);l&&this.addElementByRule(e,s,l,s.consuming===!1?o:void 0)}}leafFallback(e,t){e.nodeName=="BR"&&this.top.type&&this.top.type.inlineContent&&this.addTextNode(e.ownerDocument.createTextNode(`
`),t)}ignoreFallback(e,t){e.nodeName!="BR"||this.top.type&&this.top.type.inlineContent||this.findPlace(this.parser.schema.text("-"),t)}readStyles(e,t){let n=e.style;if(n&&n.length)for(let o=0;o<this.parser.matchedStyles.length;o++){let i=this.parser.matchedStyles[o],s=n.getPropertyValue(i);if(s)for(let l;;){let a=this.parser.matchStyle(i,s,this,l);if(!a)break;if(a.ignore)return null;if(t=a.clearMark?t.filter(c=>!a.clearMark(c)):t.concat(this.parser.schema.marks[a.mark].create(a.attrs)),a.consuming!==!1)break;l=a}}return t}addElementByRule(e,t,n,o){let i,s;if(t.node)if(s=this.parser.schema.nodes[t.node],s.isLeaf)this.insertNode(s.create(t.attrs),n)||this.leafFallback(e,n);else{let a=this.enter(s,t.attrs||null,n,t.preserveWhitespace);a&&(i=!0,n=a)}else{let a=this.parser.schema.marks[t.mark];n=n.concat(a.create(t.attrs))}let l=this.top;if(s&&s.isLeaf)this.findInside(e);else if(o)this.addElement(e,n,o);else if(t.getContent)this.findInside(e),t.getContent(e,this.parser.schema).forEach(a=>this.insertNode(a,n));else{let a=e;typeof t.contentElement=="string"?a=e.querySelector(t.contentElement):typeof t.contentElement=="function"?a=t.contentElement(e):t.contentElement&&(a=t.contentElement),this.findAround(e,a,!0),this.addAll(a,n),this.findAround(e,a,!1)}i&&this.sync(l)&&this.open--}addAll(e,t,n,o){let i=n||0;for(let s=n?e.childNodes[n]:e.firstChild,l=o==null?null:e.childNodes[o];s!=l;s=s.nextSibling,++i)this.findAtPoint(e,i),this.addDOM(s,t);this.findAtPoint(e,i)}findPlace(e,t){let n,o;for(let i=this.open;i>=0;i--){let s=this.nodes[i],l=s.findWrapping(e);if(l&&(!n||n.length>l.length)&&(n=l,o=s,!l.length)||s.solid)break}if(!n)return null;this.sync(o);for(let i=0;i<n.length;i++)t=this.enterInner(n[i],null,t,!1);return t}insertNode(e,t){if(e.isInline&&this.needsBlock&&!this.top.type){let o=this.textblockFromContext();o&&(t=this.enterInner(o,null,t))}let n=this.findPlace(e,t);if(n){this.closeExtra();let o=this.top;o.match&&(o.match=o.match.matchType(e.type));let i=W.none;for(let s of n.concat(e.marks))(o.type?o.type.allowsMarkType(s.type):br(s.type,e.type))&&(i=s.addToSet(i));return o.content.push(e.mark(i)),!0}return!1}enter(e,t,n,o){let i=this.findPlace(e.create(t),n);return i&&(i=this.enterInner(e,t,n,!0,o)),i}enterInner(e,t,n,o=!1,i){this.closeExtra();let s=this.top;s.match=s.match&&s.match.matchType(e);let l=wr(e,i,s.options);4&s.options&&s.content.length==0&&(l|=4);let a=W.none;return n=n.filter(c=>!(s.type?s.type.allowsMarkType(c.type):br(c.type,e))||(a=c.addToSet(a),!1)),this.nodes.push(new no(e,t,a,o,null,l)),this.open++,n}closeExtra(e=!1){let t=this.nodes.length-1;if(t>this.open){for(;t>this.open;t--)this.nodes[t-1].content.push(this.nodes[t].finish(e));this.nodes.length=this.open+1}}finish(){return this.open=0,this.closeExtra(this.isOpen),this.nodes[0].finish(this.isOpen||this.options.topOpen)}sync(e){for(let t=this.open;t>=0;t--)if(this.nodes[t]==e)return this.open=t,!0;return!1}get currentPos(){this.closeExtra();let e=0;for(let t=this.open;t>=0;t--){let n=this.nodes[t].content;for(let o=n.length-1;o>=0;o--)e+=n[o].nodeSize;t&&e++}return e}findAtPoint(e,t){if(this.find)for(let n=0;n<this.find.length;n++)this.find[n].node==e&&this.find[n].offset==t&&(this.find[n].pos=this.currentPos)}findInside(e){if(this.find)for(let t=0;t<this.find.length;t++)this.find[t].pos==null&&e.nodeType==1&&e.contains(this.find[t].node)&&(this.find[t].pos=this.currentPos)}findAround(e,t,n){if(e!=t&&this.find)for(let o=0;o<this.find.length;o++)this.find[o].pos==null&&e.nodeType==1&&e.contains(this.find[o].node)&&t.compareDocumentPosition(this.find[o].node)&(n?2:4)&&(this.find[o].pos=this.currentPos)}findInText(e){if(this.find)for(let t=0;t<this.find.length;t++)this.find[t].node==e&&(this.find[t].pos=this.currentPos-(e.nodeValue.length-this.find[t].offset))}matchesContext(e){if(e.indexOf("|")>-1)return e.split(/\s*\|\s*/).some(this.matchesContext,this);let t=e.split("/"),n=this.options.context,o=!(this.isOpen||n&&n.parent.type!=this.nodes[0].type),i=-(n?n.depth+1:0)+(o?0:1),s=(l,a)=>{for(;l>=0;l--){let c=t[l];if(c==""){if(l==t.length-1||l==0)continue;for(;a>=i;a--)if(s(l-1,a))return!0;return!1}{let d=a>0||a==0&&o?this.nodes[a].type:n&&a>=i?n.node(a-i).type:null;if(!d||d.name!=c&&!d.isInGroup(c))return!1;a--}}return!0};return s(t.length-1,this.open)}textblockFromContext(){let e=this.options.context;if(e)for(let t=e.depth;t>=0;t--){let n=e.node(t).contentMatchAt(e.indexAfter(t)).defaultType;if(n&&n.isTextblock&&n.defaultAttrs)return n}for(let t in this.parser.schema.nodes){let n=this.parser.schema.nodes[t];if(n.isTextblock&&n.defaultAttrs)return n}}}function sa(r,e){return(r.matches||r.msMatchesSelector||r.webkitMatchesSelector||r.mozMatchesSelector).call(r,e)}function xr(r){let e={};for(let t in r)e[t]=r[t];return e}function br(r,e){let t=e.schema.nodes;for(let n in t){let o=t[n];if(!o.allowsMarkType(r))continue;let i=[],s=l=>{i.push(l);for(let a=0;a<l.edgeCount;a++){let{type:c,next:d}=l.edge(a);if(c==e||i.indexOf(d)<0&&s(d))return!0}};if(s(o.contentMatch))return!0}}class Lt{constructor(e,t){this.nodes=e,this.marks=t}serializeFragment(e,t={},n){n||(n=oo(t).createDocumentFragment());let o=n,i=[];return e.forEach(s=>{if(i.length||s.marks.length){let l=0,a=0;for(;l<i.length&&a<s.marks.length;){let c=s.marks[a];if(this.marks[c.type.name]){if(!c.eq(i[l][0])||c.type.spec.spanning===!1)break;l++,a++}else a++}for(;l<i.length;)o=i.pop()[1];for(;a<s.marks.length;){let c=s.marks[a++],d=this.serializeMark(c,s.isInline,t);d&&(i.push([c,o]),o.appendChild(d.dom),o=d.contentDOM||d.dom)}}o.appendChild(this.serializeNodeInner(s,t))}),n}serializeNodeInner(e,t){let{dom:n,contentDOM:o}=En(oo(t),this.nodes[e.type.name](e),null,e.attrs);if(o){if(e.isLeaf)throw new RangeError("Content hole not allowed in a leaf node spec");this.serializeFragment(e.content,t,o)}return n}serializeNode(e,t={}){let n=this.serializeNodeInner(e,t);for(let o=e.marks.length-1;o>=0;o--){let i=this.serializeMark(e.marks[o],e.isInline,t);i&&((i.contentDOM||i.dom).appendChild(n),n=i.dom)}return n}serializeMark(e,t,n={}){let o=this.marks[e.type.name];return o&&En(oo(n),o(e,t),null,e.attrs)}static renderSpec(e,t,n=null,o){return En(e,t,n,o)}static fromSchema(e){return e.cached.domSerializer||(e.cached.domSerializer=new Lt(this.nodesFromSchema(e),this.marksFromSchema(e)))}static nodesFromSchema(e){let t=kr(e.nodes);return t.text||(t.text=n=>n.text),t}static marksFromSchema(e){return kr(e.marks)}}function kr(r){let e={};for(let t in r){let n=r[t].spec.toDOM;n&&(e[t]=n)}return e}function oo(r){return r.document||window.document}const Mr=new WeakMap;function la(r){let e=Mr.get(r);return e===void 0&&Mr.set(r,e=function(t){let n=null;function o(i){if(i&&typeof i=="object")if(Array.isArray(i))if(typeof i[0]=="string")n||(n=[]),n.push(i);else for(let s=0;s<i.length;s++)o(i[s]);else for(let s in i)o(i[s])}return o(t),n}(r)),e}function En(r,e,t,n){if(typeof e=="string")return{dom:r.createTextNode(e)};if(e.nodeType!=null)return{dom:e};if(e.dom&&e.dom.nodeType!=null)return e;let o,i=e[0];if(typeof i!="string")throw new RangeError("Invalid array passed to renderSpec");if(n&&(o=la(n))&&o.indexOf(e)>-1)throw new RangeError("Using an array from an attribute object as a DOM spec. This may be an attempted cross site scripting attack.");let s,l=i.indexOf(" ");l>0&&(t=i.slice(0,l),i=i.slice(l+1));let a=t?r.createElementNS(t,i):r.createElement(i),c=e[1],d=1;if(c&&typeof c=="object"&&c.nodeType==null&&!Array.isArray(c)){d=2;for(let h in c)if(c[h]!=null){let p=h.indexOf(" ");p>0?a.setAttributeNS(h.slice(0,p),h.slice(p+1),c[h]):a.setAttribute(h,c[h])}}for(let h=d;h<e.length;h++){let p=e[h];if(p===0){if(h<e.length-1||h>d)throw new RangeError("Content hole must be the only child of its parent node");return{dom:a,contentDOM:a}}{let{dom:f,contentDOM:u}=En(r,p,t,n);if(a.appendChild(f),u){if(s)throw new RangeError("Multiple content holes");s=u}}}return{dom:a,contentDOM:s}}const Sr=Math.pow(2,16);function Or(r){return 65535&r}class Ao{constructor(e,t,n){this.pos=e,this.delInfo=t,this.recover=n}get deleted(){return(8&this.delInfo)>0}get deletedBefore(){return(5&this.delInfo)>0}get deletedAfter(){return(6&this.delInfo)>0}get deletedAcross(){return(4&this.delInfo)>0}}class Me{constructor(e,t=!1){if(this.ranges=e,this.inverted=t,!e.length&&Me.empty)return Me.empty}recover(e){let t=0,n=Or(e);if(!this.inverted)for(let o=0;o<n;o++)t+=this.ranges[3*o+2]-this.ranges[3*o+1];return this.ranges[3*n]+t+function(o){return(o-(65535&o))/Sr}(e)}mapResult(e,t=1){return this._map(e,t,!1)}map(e,t=1){return this._map(e,t,!0)}_map(e,t,n){let o=0,i=this.inverted?2:1,s=this.inverted?1:2;for(let l=0;l<this.ranges.length;l+=3){let a=this.ranges[l]-(this.inverted?o:0);if(a>e)break;let c=this.ranges[l+i],d=this.ranges[l+s],h=a+c;if(e<=h){let p=a+o+((c?e==a?-1:e==h?1:t:t)<0?0:d);if(n)return p;let f=e==(t<0?a:h)?null:l/3+(e-a)*Sr,u=e==a?2:e==h?1:4;return(t<0?e!=a:e!=h)&&(u|=8),new Ao(p,u,f)}o+=d-c}return n?e+o:new Ao(e+o,0,null)}touches(e,t){let n=0,o=Or(t),i=this.inverted?2:1,s=this.inverted?1:2;for(let l=0;l<this.ranges.length;l+=3){let a=this.ranges[l]-(this.inverted?n:0);if(a>e)break;let c=this.ranges[l+i];if(e<=a+c&&l==3*o)return!0;n+=this.ranges[l+s]-c}return!1}forEach(e){let t=this.inverted?2:1,n=this.inverted?1:2;for(let o=0,i=0;o<this.ranges.length;o+=3){let s=this.ranges[o],l=s-(this.inverted?i:0),a=s+(this.inverted?0:i),c=this.ranges[o+t],d=this.ranges[o+n];e(l,l+c,a,a+d),i+=d-c}}invert(){return new Me(this.ranges,!this.inverted)}toString(){return(this.inverted?"-":"")+JSON.stringify(this.ranges)}static offset(e){return e==0?Me.empty:new Me(e<0?[0,-e,0]:[0,0,e])}}Me.empty=new Me([]);class Zt{constructor(e=[],t,n=0,o=e.length){this.maps=e,this.mirror=t,this.from=n,this.to=o}slice(e=0,t=this.maps.length){return new Zt(this.maps,this.mirror,e,t)}copy(){return new Zt(this.maps.slice(),this.mirror&&this.mirror.slice(),this.from,this.to)}appendMap(e,t){this.to=this.maps.push(e),t!=null&&this.setMirror(this.maps.length-1,t)}appendMapping(e){for(let t=0,n=this.maps.length;t<e.maps.length;t++){let o=e.getMirror(t);this.appendMap(e.maps[t],o!=null&&o<t?n+o:void 0)}}getMirror(e){if(this.mirror){for(let t=0;t<this.mirror.length;t++)if(this.mirror[t]==e)return this.mirror[t+(t%2?-1:1)]}}setMirror(e,t){this.mirror||(this.mirror=[]),this.mirror.push(e,t)}appendMappingInverted(e){for(let t=e.maps.length-1,n=this.maps.length+e.maps.length;t>=0;t--){let o=e.getMirror(t);this.appendMap(e.maps[t].invert(),o!=null&&o>t?n-o-1:void 0)}}invert(){let e=new Zt;return e.appendMappingInverted(this),e}map(e,t=1){if(this.mirror)return this._map(e,t,!0);for(let n=this.from;n<this.to;n++)e=this.maps[n].map(e,t);return e}mapResult(e,t=1){return this._map(e,t,!1)}_map(e,t,n){let o=0;for(let i=this.from;i<this.to;i++){let s=this.maps[i].mapResult(e,t);if(s.recover!=null){let l=this.getMirror(i);if(l!=null&&l>i&&l<this.to){i=l,e=this.maps[l].recover(s.recover);continue}}o|=s.delInfo,e=s.pos}return n?e:new Ao(e,o,null)}}const ro=Object.create(null);class pe{getMap(){return Me.empty}merge(e){return null}static fromJSON(e,t){if(!t||!t.stepType)throw new RangeError("Invalid input for Step.fromJSON");let n=ro[t.stepType];if(!n)throw new RangeError(`No step type ${t.stepType} defined`);return n.fromJSON(e,t)}static jsonID(e,t){if(e in ro)throw new RangeError("Duplicate use of step JSON ID "+e);return ro[e]=t,t.prototype.jsonID=e,t}}class ne{constructor(e,t){this.doc=e,this.failed=t}static ok(e){return new ne(e,null)}static fail(e){return new ne(null,e)}static fromReplace(e,t,n,o){try{return ne.ok(e.replace(t,n,o))}catch(i){if(i instanceof An)return ne.fail(i.message);throw i}}}function Yo(r,e,t){let n=[];for(let o=0;o<r.childCount;o++){let i=r.child(o);i.content.size&&(i=i.copy(Yo(i.content,e,i))),i.isInline&&(i=e(i,t,o)),n.push(i)}return S.fromArray(n)}class rt extends pe{constructor(e,t,n){super(),this.from=e,this.to=t,this.mark=n}apply(e){let t=e.slice(this.from,this.to),n=e.resolve(this.from),o=n.node(n.sharedDepth(this.to)),i=new T(Yo(t.content,(s,l)=>s.isAtom&&l.type.allowsMarkType(this.mark.type)?s.mark(this.mark.addToSet(s.marks)):s,o),t.openStart,t.openEnd);return ne.fromReplace(e,this.from,this.to,i)}invert(){return new Je(this.from,this.to,this.mark)}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1);return t.deleted&&n.deleted||t.pos>=n.pos?null:new rt(t.pos,n.pos,this.mark)}merge(e){return e instanceof rt&&e.mark.eq(this.mark)&&this.from<=e.to&&this.to>=e.from?new rt(Math.min(this.from,e.from),Math.max(this.to,e.to),this.mark):null}toJSON(){return{stepType:"addMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(e,t){if(typeof t.from!="number"||typeof t.to!="number")throw new RangeError("Invalid input for AddMarkStep.fromJSON");return new rt(t.from,t.to,e.markFromJSON(t.mark))}}pe.jsonID("addMark",rt);class Je extends pe{constructor(e,t,n){super(),this.from=e,this.to=t,this.mark=n}apply(e){let t=e.slice(this.from,this.to),n=new T(Yo(t.content,o=>o.mark(this.mark.removeFromSet(o.marks)),e),t.openStart,t.openEnd);return ne.fromReplace(e,this.from,this.to,n)}invert(){return new rt(this.from,this.to,this.mark)}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1);return t.deleted&&n.deleted||t.pos>=n.pos?null:new Je(t.pos,n.pos,this.mark)}merge(e){return e instanceof Je&&e.mark.eq(this.mark)&&this.from<=e.to&&this.to>=e.from?new Je(Math.min(this.from,e.from),Math.max(this.to,e.to),this.mark):null}toJSON(){return{stepType:"removeMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(e,t){if(typeof t.from!="number"||typeof t.to!="number")throw new RangeError("Invalid input for RemoveMarkStep.fromJSON");return new Je(t.from,t.to,e.markFromJSON(t.mark))}}pe.jsonID("removeMark",Je);class it extends pe{constructor(e,t){super(),this.pos=e,this.mark=t}apply(e){let t=e.nodeAt(this.pos);if(!t)return ne.fail("No node at mark step's position");let n=t.type.create(t.attrs,null,this.mark.addToSet(t.marks));return ne.fromReplace(e,this.pos,this.pos+1,new T(S.from(n),0,t.isLeaf?0:1))}invert(e){let t=e.nodeAt(this.pos);if(t){let n=this.mark.addToSet(t.marks);if(n.length==t.marks.length){for(let o=0;o<t.marks.length;o++)if(!t.marks[o].isInSet(n))return new it(this.pos,t.marks[o]);return new it(this.pos,this.mark)}}return new en(this.pos,this.mark)}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new it(t.pos,this.mark)}toJSON(){return{stepType:"addNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(e,t){if(typeof t.pos!="number")throw new RangeError("Invalid input for AddNodeMarkStep.fromJSON");return new it(t.pos,e.markFromJSON(t.mark))}}pe.jsonID("addNodeMark",it);class en extends pe{constructor(e,t){super(),this.pos=e,this.mark=t}apply(e){let t=e.nodeAt(this.pos);if(!t)return ne.fail("No node at mark step's position");let n=t.type.create(t.attrs,null,this.mark.removeFromSet(t.marks));return ne.fromReplace(e,this.pos,this.pos+1,new T(S.from(n),0,t.isLeaf?0:1))}invert(e){let t=e.nodeAt(this.pos);return t&&this.mark.isInSet(t.marks)?new it(this.pos,this.mark):this}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new en(t.pos,this.mark)}toJSON(){return{stepType:"removeNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(e,t){if(typeof t.pos!="number")throw new RangeError("Invalid input for RemoveNodeMarkStep.fromJSON");return new en(t.pos,e.markFromJSON(t.mark))}}pe.jsonID("removeNodeMark",en);class ie extends pe{constructor(e,t,n,o=!1){super(),this.from=e,this.to=t,this.slice=n,this.structure=o}apply(e){return this.structure&&Lo(e,this.from,this.to)?ne.fail("Structure replace would overwrite content"):ne.fromReplace(e,this.from,this.to,this.slice)}getMap(){return new Me([this.from,this.to-this.from,this.slice.size])}invert(e){return new ie(this.from,this.from+this.slice.size,e.slice(this.from,this.to))}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1);return t.deletedAcross&&n.deletedAcross?null:new ie(t.pos,Math.max(t.pos,n.pos),this.slice)}merge(e){if(!(e instanceof ie)||e.structure||this.structure)return null;if(this.from+this.slice.size!=e.from||this.slice.openEnd||e.slice.openStart){if(e.to!=this.from||this.slice.openStart||e.slice.openEnd)return null;{let t=this.slice.size+e.slice.size==0?T.empty:new T(e.slice.content.append(this.slice.content),e.slice.openStart,this.slice.openEnd);return new ie(e.from,this.to,t,this.structure)}}{let t=this.slice.size+e.slice.size==0?T.empty:new T(this.slice.content.append(e.slice.content),this.slice.openStart,e.slice.openEnd);return new ie(this.from,this.to+(e.to-e.from),t,this.structure)}}toJSON(){let e={stepType:"replace",from:this.from,to:this.to};return this.slice.size&&(e.slice=this.slice.toJSON()),this.structure&&(e.structure=!0),e}static fromJSON(e,t){if(typeof t.from!="number"||typeof t.to!="number")throw new RangeError("Invalid input for ReplaceStep.fromJSON");return new ie(t.from,t.to,T.fromJSON(e,t.slice),!!t.structure)}}pe.jsonID("replace",ie);class se extends pe{constructor(e,t,n,o,i,s,l=!1){super(),this.from=e,this.to=t,this.gapFrom=n,this.gapTo=o,this.slice=i,this.insert=s,this.structure=l}apply(e){if(this.structure&&(Lo(e,this.from,this.gapFrom)||Lo(e,this.gapTo,this.to)))return ne.fail("Structure gap-replace would overwrite content");let t=e.slice(this.gapFrom,this.gapTo);if(t.openStart||t.openEnd)return ne.fail("Gap is not a flat range");let n=this.slice.insertAt(this.insert,t.content);return n?ne.fromReplace(e,this.from,this.to,n):ne.fail("Content does not fit in gap")}getMap(){return new Me([this.from,this.gapFrom-this.from,this.insert,this.gapTo,this.to-this.gapTo,this.slice.size-this.insert])}invert(e){let t=this.gapTo-this.gapFrom;return new se(this.from,this.from+this.slice.size+t,this.from+this.insert,this.from+this.insert+t,e.slice(this.from,this.to).removeBetween(this.gapFrom-this.from,this.gapTo-this.from),this.gapFrom-this.from,this.structure)}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1),o=this.from==this.gapFrom?t.pos:e.map(this.gapFrom,-1),i=this.to==this.gapTo?n.pos:e.map(this.gapTo,1);return t.deletedAcross&&n.deletedAcross||o<t.pos||i>n.pos?null:new se(t.pos,n.pos,o,i,this.slice,this.insert,this.structure)}toJSON(){let e={stepType:"replaceAround",from:this.from,to:this.to,gapFrom:this.gapFrom,gapTo:this.gapTo,insert:this.insert};return this.slice.size&&(e.slice=this.slice.toJSON()),this.structure&&(e.structure=!0),e}static fromJSON(e,t){if(typeof t.from!="number"||typeof t.to!="number"||typeof t.gapFrom!="number"||typeof t.gapTo!="number"||typeof t.insert!="number")throw new RangeError("Invalid input for ReplaceAroundStep.fromJSON");return new se(t.from,t.to,t.gapFrom,t.gapTo,T.fromJSON(e,t.slice),t.insert,!!t.structure)}}function Lo(r,e,t){let n=r.resolve(e),o=t-e,i=n.depth;for(;o>0&&i>0&&n.indexAfter(i)==n.node(i).childCount;)i--,o--;if(o>0){let s=n.node(i).maybeChild(n.indexAfter(i));for(;o>0;){if(!s||s.isLeaf)return!0;s=s.firstChild,o--}}return!1}function Tr(r,e,t,n=t.contentMatch,o=!0){let i=r.doc.nodeAt(e),s=[],l=e+1;for(let a=0;a<i.childCount;a++){let c=i.child(a),d=l+c.nodeSize,h=n.matchType(c.type);if(h){n=h;for(let p=0;p<c.marks.length;p++)t.allowsMarkType(c.marks[p].type)||r.step(new Je(l,d,c.marks[p]));if(o&&c.isText&&t.whitespace!="pre"){let p,f,u=/\r?\n|\r/g;for(;p=u.exec(c.text);)f||(f=new T(S.from(t.schema.text(" ",t.allowedMarks(c.marks))),0,0)),s.push(new ie(l+p.index,l+p.index+p[0].length,f))}}else s.push(new ie(l,d,T.empty));l=d}if(!n.validEnd){let a=n.fillBefore(S.empty,!0);r.replace(l,l,new T(a,0,0))}for(let a=s.length-1;a>=0;a--)r.step(s[a])}function aa(r,e,t){return(e==0||r.canReplace(e,r.childCount))&&(t==r.childCount||r.canReplace(0,t))}function qt(r){let e=r.parent.content.cutByIndex(r.startIndex,r.endIndex);for(let t=r.depth;;--t){let n=r.$from.node(t),o=r.$from.index(t),i=r.$to.indexAfter(t);if(t<r.depth&&n.canReplace(o,i,e))return t;if(t==0||n.type.spec.isolating||!aa(n,o,i))break}return null}function As(r,e,t=null,n=r){let o=function(s,l){let{parent:a,startIndex:c,endIndex:d}=s,h=a.contentMatchAt(c).findWrapping(l);if(!h)return null;let p=h.length?h[0]:l;return a.canReplaceWith(c,d,p)?h:null}(r,e),i=o&&function(s,l){let{parent:a,startIndex:c,endIndex:d}=s,h=a.child(c),p=l.contentMatch.findWrapping(h.type);if(!p)return null;let f=(p.length?p[p.length-1]:l).contentMatch;for(let u=c;f&&u<d;u++)f=f.matchType(a.child(u).type);return f&&f.validEnd?p:null}(n,e);return i?o.map(Er).concat({type:e,attrs:t}).concat(i.map(Er)):null}function Er(r){return{type:r,attrs:null}}function _t(r,e,t=1,n){let o=r.resolve(e),i=o.depth-t,s=n&&n[n.length-1]||o.parent;if(i<0||o.parent.type.spec.isolating||!o.parent.canReplace(o.index(),o.parent.childCount)||!s.type.validContent(o.parent.content.cutByIndex(o.index(),o.parent.childCount)))return!1;for(let c=o.depth-1,d=t-2;c>i;c--,d--){let h=o.node(c),p=o.index(c);if(h.type.spec.isolating)return!1;let f=h.content.cutByIndex(p,h.childCount),u=n&&n[d+1];u&&(f=f.replaceChild(0,u.type.create(u.attrs)));let m=n&&n[d]||h;if(!h.canReplace(p+1,h.childCount)||!m.type.validContent(f))return!1}let l=o.indexAfter(i),a=n&&n[0];return o.node(i).canReplaceWith(l,l,a?a.type:o.node(i+1).type)}function $t(r,e){let t=r.resolve(e),n=t.index();return Ls(t.nodeBefore,t.nodeAfter)&&t.parent.canReplace(n,n+1)}function Ls(r,e){return!(!r||!e||r.isLeaf||!r.canAppend(e))}function Mn(r,e,t=-1){let n=r.resolve(e);for(let o=n.depth;;o--){let i,s,l=n.index(o);if(o==n.depth?(i=n.nodeBefore,s=n.nodeAfter):t>0?(i=n.node(o+1),l++,s=n.node(o).maybeChild(l)):(i=n.node(o).maybeChild(l-1),s=n.node(o+1)),i&&!i.isTextblock&&Ls(i,s)&&n.node(o).canReplace(l,l+1))return e;if(o==0)break;e=t<0?n.before(o):n.after(o)}}function Rn(r,e,t=e,n=T.empty){if(e==t&&!n.size)return null;let o=r.resolve(e),i=r.resolve(t);return Is(o,i,n)?new ie(e,t,n):new ca(o,i,n).fit()}function Is(r,e,t){return!t.openStart&&!t.openEnd&&r.start()==e.start()&&r.parent.canReplace(r.index(),e.index(),t.content)}pe.jsonID("replaceAround",se);class ca{constructor(e,t,n){this.$from=e,this.$to=t,this.unplaced=n,this.frontier=[],this.placed=S.empty;for(let o=0;o<=e.depth;o++){let i=e.node(o);this.frontier.push({type:i.type,match:i.contentMatchAt(e.indexAfter(o))})}for(let o=e.depth;o>0;o--)this.placed=S.from(e.node(o).copy(this.placed))}get depth(){return this.frontier.length-1}fit(){for(;this.unplaced.size;){let c=this.findFittable();c?this.placeNodes(c):this.openMore()||this.dropNode()}let e=this.mustMoveInline(),t=this.placed.size-this.depth-this.$from.depth,n=this.$from,o=this.close(e<0?this.$to:n.doc.resolve(e));if(!o)return null;let i=this.placed,s=n.depth,l=o.depth;for(;s&&l&&i.childCount==1;)i=i.firstChild.content,s--,l--;let a=new T(i,s,l);return e>-1?new se(n.pos,e,this.$to.pos,this.$to.end(),a,t):a.size||n.pos!=this.$to.pos?new ie(n.pos,o.pos,a):null}findFittable(){let e=this.unplaced.openStart;for(let t=this.unplaced.content,n=0,o=this.unplaced.openEnd;n<e;n++){let i=t.firstChild;if(t.childCount>1&&(o=0),i.type.spec.isolating&&o<=n){e=n;break}t=i.content}for(let t=1;t<=2;t++)for(let n=t==1?e:this.unplaced.openStart;n>=0;n--){let o,i=null;n?(i=io(this.unplaced.content,n-1).firstChild,o=i.content):o=this.unplaced.content;let s=o.firstChild;for(let l=this.depth;l>=0;l--){let a,{type:c,match:d}=this.frontier[l],h=null;if(t==1&&(s?d.matchType(s.type)||(h=d.fillBefore(S.from(s),!1)):i&&c.compatibleContent(i.type)))return{sliceDepth:n,frontierDepth:l,parent:i,inject:h};if(t==2&&s&&(a=d.findWrapping(s.type)))return{sliceDepth:n,frontierDepth:l,parent:i,wrap:a};if(i&&d.matchType(i.type))break}}}openMore(){let{content:e,openStart:t,openEnd:n}=this.unplaced,o=io(e,t);return!(!o.childCount||o.firstChild.isLeaf)&&(this.unplaced=new T(e,t+1,Math.max(n,o.size+t>=e.size-n?t+1:0)),!0)}dropNode(){let{content:e,openStart:t,openEnd:n}=this.unplaced,o=io(e,t);if(o.childCount<=1&&t>0){let i=e.size-t<=t+o.size;this.unplaced=new T(rn(e,t-1,1),t-1,i?t-1:n)}else this.unplaced=new T(rn(e,t,1),t,n)}placeNodes({sliceDepth:e,frontierDepth:t,parent:n,inject:o,wrap:i}){for(;this.depth>t;)this.closeFrontierNode();if(i)for(let m=0;m<i.length;m++)this.openFrontierNode(i[m]);let s=this.unplaced,l=n?n.content:s.content,a=s.openStart-e,c=0,d=[],{match:h,type:p}=this.frontier[t];if(o){for(let m=0;m<o.childCount;m++)d.push(o.child(m));h=h.matchFragment(o)}let f=l.size+e-(s.content.size-s.openEnd);for(;c<l.childCount;){let m=l.child(c),g=h.matchType(m.type);if(!g)break;c++,(c>1||a==0||m.content.size)&&(h=g,d.push(Ps(m.mark(p.allowedMarks(m.marks)),c==1?a:0,c==l.childCount?f:-1)))}let u=c==l.childCount;u||(f=-1),this.placed=sn(this.placed,t,S.from(d)),this.frontier[t].match=h,u&&f<0&&n&&n.type==this.frontier[this.depth].type&&this.frontier.length>1&&this.closeFrontierNode();for(let m=0,g=l;m<f;m++){let C=g.lastChild;this.frontier.push({type:C.type,match:C.contentMatchAt(C.childCount)}),g=C.content}this.unplaced=u?e==0?T.empty:new T(rn(s.content,e-1,1),e-1,f<0?s.openEnd:e-1):new T(rn(s.content,e,c),s.openStart,s.openEnd)}mustMoveInline(){if(!this.$to.parent.isTextblock)return-1;let e,t=this.frontier[this.depth];if(!t.type.isTextblock||!so(this.$to,this.$to.depth,t.type,t.match,!1)||this.$to.depth==this.depth&&(e=this.findCloseLevel(this.$to))&&e.depth==this.depth)return-1;let{depth:n}=this.$to,o=this.$to.after(n);for(;n>1&&o==this.$to.end(--n);)++o;return o}findCloseLevel(e){e:for(let t=Math.min(this.depth,e.depth);t>=0;t--){let{match:n,type:o}=this.frontier[t],i=t<e.depth&&e.end(t+1)==e.pos+(e.depth-(t+1)),s=so(e,t,o,n,i);if(s){for(let l=t-1;l>=0;l--){let{match:a,type:c}=this.frontier[l],d=so(e,l,c,a,!0);if(!d||d.childCount)continue e}return{depth:t,fit:s,move:i?e.doc.resolve(e.after(t+1)):e}}}}close(e){let t=this.findCloseLevel(e);if(!t)return null;for(;this.depth>t.depth;)this.closeFrontierNode();t.fit.childCount&&(this.placed=sn(this.placed,t.depth,t.fit)),e=t.move;for(let n=t.depth+1;n<=e.depth;n++){let o=e.node(n),i=o.type.contentMatch.fillBefore(o.content,!0,e.index(n));this.openFrontierNode(o.type,o.attrs,i)}return e}openFrontierNode(e,t=null,n){let o=this.frontier[this.depth];o.match=o.match.matchType(e),this.placed=sn(this.placed,this.depth,S.from(e.create(t,n))),this.frontier.push({type:e,match:e.contentMatch})}closeFrontierNode(){let e=this.frontier.pop().match.fillBefore(S.empty,!0);e.childCount&&(this.placed=sn(this.placed,this.frontier.length,e))}}function rn(r,e,t){return e==0?r.cutByIndex(t,r.childCount):r.replaceChild(0,r.firstChild.copy(rn(r.firstChild.content,e-1,t)))}function sn(r,e,t){return e==0?r.append(t):r.replaceChild(r.childCount-1,r.lastChild.copy(sn(r.lastChild.content,e-1,t)))}function io(r,e){for(let t=0;t<e;t++)r=r.firstChild.content;return r}function Ps(r,e,t){if(e<=0)return r;let n=r.content;return e>1&&(n=n.replaceChild(0,Ps(n.firstChild,e-1,n.childCount==1?t-1:0))),e>0&&(n=r.type.contentMatch.fillBefore(n).append(n),t<=0&&(n=n.append(r.type.contentMatch.matchFragment(n).fillBefore(S.empty,!0)))),r.copy(n)}function so(r,e,t,n,o){let i=r.node(e),s=o?r.indexAfter(e):r.index(e);if(s==i.childCount&&!t.compatibleContent(i.type))return null;let l=n.fillBefore(i.content,!0,s);return l&&!function(a,c,d){for(let h=d;h<c.childCount;h++)if(!a.allowsMarks(c.child(h).marks))return!0;return!1}(t,i.content,s)?l:null}function Rs(r,e,t,n,o){if(e<t){let i=r.firstChild;r=r.replaceChild(0,i.copy(Rs(i.content,e+1,t,n,i)))}if(e>n){let i=o.contentMatchAt(0),s=i.fillBefore(r).append(r);r=s.append(i.matchFragment(s).fillBefore(S.empty,!0))}return r}function Nr(r,e){let t=[];for(let n=Math.min(r.depth,e.depth);n>=0;n--){let o=r.start(n);if(o<r.pos-(r.depth-n)||e.end(n)>e.pos+(e.depth-n)||r.node(n).type.spec.isolating||e.node(n).type.spec.isolating)break;(o==e.start(n)||n==r.depth&&n==e.depth&&r.parent.inlineContent&&e.parent.inlineContent&&n&&e.start(n-1)==o-1)&&t.push(n)}return t}class Gt extends pe{constructor(e,t,n){super(),this.pos=e,this.attr=t,this.value=n}apply(e){let t=e.nodeAt(this.pos);if(!t)return ne.fail("No node at attribute step's position");let n=Object.create(null);for(let i in t.attrs)n[i]=t.attrs[i];n[this.attr]=this.value;let o=t.type.create(n,null,t.marks);return ne.fromReplace(e,this.pos,this.pos+1,new T(S.from(o),0,t.isLeaf?0:1))}getMap(){return Me.empty}invert(e){return new Gt(this.pos,this.attr,e.nodeAt(this.pos).attrs[this.attr])}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new Gt(t.pos,this.attr,this.value)}toJSON(){return{stepType:"attr",pos:this.pos,attr:this.attr,value:this.value}}static fromJSON(e,t){if(typeof t.pos!="number"||typeof t.attr!="string")throw new RangeError("Invalid input for AttrStep.fromJSON");return new Gt(t.pos,t.attr,t.value)}}pe.jsonID("attr",Gt);class gn extends pe{constructor(e,t){super(),this.attr=e,this.value=t}apply(e){let t=Object.create(null);for(let o in e.attrs)t[o]=e.attrs[o];t[this.attr]=this.value;let n=e.type.create(t,e.content,e.marks);return ne.ok(n)}getMap(){return Me.empty}invert(e){return new gn(this.attr,e.attrs[this.attr])}map(e){return this}toJSON(){return{stepType:"docAttr",attr:this.attr,value:this.value}}static fromJSON(e,t){if(typeof t.attr!="string")throw new RangeError("Invalid input for DocAttrStep.fromJSON");return new gn(t.attr,t.value)}}pe.jsonID("docAttr",gn);let ln=class extends Error{};ln=function r(e){let t=Error.call(this,e);return t.__proto__=r.prototype,t},(ln.prototype=Object.create(Error.prototype)).constructor=ln,ln.prototype.name="TransformError";class da{constructor(e){this.doc=e,this.steps=[],this.docs=[],this.mapping=new Zt}get before(){return this.docs.length?this.docs[0]:this.doc}step(e){let t=this.maybeStep(e);if(t.failed)throw new ln(t.failed);return this}maybeStep(e){let t=e.apply(this.doc);return t.failed||this.addStep(e,t.doc),t}get docChanged(){return this.steps.length>0}addStep(e,t){this.docs.push(this.doc),this.steps.push(e),this.mapping.appendMap(e.getMap()),this.doc=t}replace(e,t=e,n=T.empty){let o=Rn(this.doc,e,t,n);return o&&this.step(o),this}replaceWith(e,t,n){return this.replace(e,t,new T(S.from(n),0,0))}delete(e,t){return this.replace(e,t,T.empty)}insert(e,t){return this.replaceWith(e,e,t)}replaceRange(e,t,n){return function(o,i,s,l){if(!l.size)return o.deleteRange(i,s);let a=o.doc.resolve(i),c=o.doc.resolve(s);if(Is(a,c,l))return o.step(new ie(i,s,l));let d=Nr(a,o.doc.resolve(s));d[d.length-1]==0&&d.pop();let h=-(a.depth+1);d.unshift(h);for(let C=a.depth,w=a.pos-1;C>0;C--,w--){let v=a.node(C).type.spec;if(v.defining||v.definingAsContext||v.isolating)break;d.indexOf(C)>-1?h=C:a.before(C)==w&&d.splice(1,0,-C)}let p=d.indexOf(h),f=[],u=l.openStart;for(let C=l.content,w=0;;w++){let v=C.firstChild;if(f.push(v),w==l.openStart)break;C=v.content}for(let C=u-1;C>=0;C--){let w=f[C],v=(m=w.type).spec.defining||m.spec.definingForContent;if(v&&!w.sameMarkup(a.node(Math.abs(h)-1)))u=C;else if(v||!w.type.isTextblock)break}var m;for(let C=l.openStart;C>=0;C--){let w=(C+u+1)%(l.openStart+1),v=f[w];if(v)for(let b=0;b<d.length;b++){let k=d[(b+p)%d.length],N=!0;k<0&&(N=!1,k=-k);let L=a.node(k-1),O=a.index(k-1);if(L.canReplaceWith(O,O,v.type,v.marks))return o.replace(a.before(k),N?c.after(k):s,new T(Rs(l.content,0,l.openStart,w),w,l.openEnd))}}let g=o.steps.length;for(let C=d.length-1;C>=0&&(o.replace(i,s,l),!(o.steps.length>g));C--){let w=d[C];w<0||(i=a.before(w),s=c.after(w))}}(this,e,t,n),this}replaceRangeWith(e,t,n){return function(o,i,s,l){if(!l.isInline&&i==s&&o.doc.resolve(i).parent.content.size){let a=function(c,d,h){let p=c.resolve(d);if(p.parent.canReplaceWith(p.index(),p.index(),h))return d;if(p.parentOffset==0)for(let f=p.depth-1;f>=0;f--){let u=p.index(f);if(p.node(f).canReplaceWith(u,u,h))return p.before(f+1);if(u>0)return null}if(p.parentOffset==p.parent.content.size)for(let f=p.depth-1;f>=0;f--){let u=p.indexAfter(f);if(p.node(f).canReplaceWith(u,u,h))return p.after(f+1);if(u<p.node(f).childCount)return null}return null}(o.doc,i,l.type);a!=null&&(i=s=a)}o.replaceRange(i,s,new T(S.from(l),0,0))}(this,e,t,n),this}deleteRange(e,t){return function(n,o,i){let s=n.doc.resolve(o),l=n.doc.resolve(i),a=Nr(s,l);for(let c=0;c<a.length;c++){let d=a[c],h=c==a.length-1;if(h&&d==0||s.node(d).type.contentMatch.validEnd)return n.delete(s.start(d),l.end(d));if(d>0&&(h||s.node(d-1).canReplace(s.index(d-1),l.indexAfter(d-1))))return n.delete(s.before(d),l.after(d))}for(let c=1;c<=s.depth&&c<=l.depth;c++)if(o-s.start(c)==s.depth-c&&i>s.end(c)&&l.end(c)-i!=l.depth-c)return n.delete(s.before(c),i);n.delete(o,i)}(this,e,t),this}lift(e,t){return function(n,o,i){let{$from:s,$to:l,depth:a}=o,c=s.before(a+1),d=l.after(a+1),h=c,p=d,f=S.empty,u=0;for(let C=a,w=!1;C>i;C--)w||s.index(C)>0?(w=!0,f=S.from(s.node(C).copy(f)),u++):h--;let m=S.empty,g=0;for(let C=a,w=!1;C>i;C--)w||l.after(C+1)<l.end(C)?(w=!0,m=S.from(l.node(C).copy(m)),g++):p++;n.step(new se(h,p,c,d,new T(f.append(m),u,g),f.size-u,!0))}(this,e,t),this}join(e,t=1){return function(n,o,i){let s=new ie(o-i,o+i,T.empty,!0);n.step(s)}(this,e,t),this}wrap(e,t){return function(n,o,i){let s=S.empty;for(let c=i.length-1;c>=0;c--){if(s.size){let d=i[c].type.contentMatch.matchFragment(s);if(!d||!d.validEnd)throw new RangeError("Wrapper type given to Transform.wrap does not form valid content of its parent wrapper")}s=S.from(i[c].type.create(i[c].attrs,s))}let l=o.start,a=o.end;n.step(new se(l,a,l,a,new T(s,0,0),i.length,!0))}(this,e,t),this}setBlockType(e,t=e,n,o=null){return function(i,s,l,a,c){if(!a.isTextblock)throw new RangeError("Type given to setBlockType should be a textblock");let d=i.steps.length;i.doc.nodesBetween(s,l,(h,p)=>{if(h.isTextblock&&!h.hasMarkup(a,c)&&function(f,u,m){let g=f.resolve(u),C=g.index();return g.parent.canReplaceWith(C,C+1,m)}(i.doc,i.mapping.slice(d).map(p),a)){let f=null;if(a.schema.linebreakReplacement){let C=a.whitespace=="pre",w=!!a.contentMatch.matchType(a.schema.linebreakReplacement);C&&!w?f=!1:!C&&w&&(f=!0)}f===!1&&function(C,w,v,b){w.forEach((k,N)=>{if(k.type==k.type.schema.linebreakReplacement){let L=C.mapping.slice(b).map(v+1+N);C.replaceWith(L,L+1,w.type.schema.text(`
`))}})}(i,h,p,d),Tr(i,i.mapping.slice(d).map(p,1),a,void 0,f===null);let u=i.mapping.slice(d),m=u.map(p,1),g=u.map(p+h.nodeSize,1);return i.step(new se(m,g,m+1,g-1,new T(S.from(a.create(c,null,h.marks)),0,0),1,!0)),f===!0&&function(C,w,v,b){w.forEach((k,N)=>{if(k.isText){let L,O=/\r?\n|\r/g;for(;L=O.exec(k.text);){let M=C.mapping.slice(b).map(v+1+N+L.index);C.replaceWith(M,M+1,w.type.schema.linebreakReplacement.create())}}})}(i,h,p,d),!1}})}(this,e,t,n,o),this}setNodeMarkup(e,t,n=null,o){return function(i,s,l,a,c){let d=i.doc.nodeAt(s);if(!d)throw new RangeError("No node at given position");l||(l=d.type);let h=l.create(a,null,c||d.marks);if(d.isLeaf)return i.replaceWith(s,s+d.nodeSize,h);if(!l.validContent(d.content))throw new RangeError("Invalid content for node type "+l.name);i.step(new se(s,s+d.nodeSize,s+1,s+d.nodeSize-1,new T(S.from(h),0,0),1,!0))}(this,e,t,n,o),this}setNodeAttribute(e,t,n){return this.step(new Gt(e,t,n)),this}setDocAttribute(e,t){return this.step(new gn(e,t)),this}addNodeMark(e,t){return this.step(new it(e,t)),this}removeNodeMark(e,t){if(!(t instanceof W)){let n=this.doc.nodeAt(e);if(!n)throw new RangeError("No node at position "+e);if(!(t=t.isInSet(n.marks)))return this}return this.step(new en(e,t)),this}split(e,t=1,n){return function(o,i,s=1,l){let a=o.doc.resolve(i),c=S.empty,d=S.empty;for(let h=a.depth,p=a.depth-s,f=s-1;h>p;h--,f--){c=S.from(a.node(h).copy(c));let u=l&&l[f];d=S.from(u?u.type.create(u.attrs,d):a.node(h).copy(d))}o.step(new ie(i,i,new T(c.append(d),s,s),!0))}(this,e,t,n),this}addMark(e,t,n){return function(o,i,s,l){let a,c,d=[],h=[];o.doc.nodesBetween(i,s,(p,f,u)=>{if(!p.isInline)return;let m=p.marks;if(!l.isInSet(m)&&u.type.allowsMarkType(l.type)){let g=Math.max(f,i),C=Math.min(f+p.nodeSize,s),w=l.addToSet(m);for(let v=0;v<m.length;v++)m[v].isInSet(w)||(a&&a.to==g&&a.mark.eq(m[v])?a.to=C:d.push(a=new Je(g,C,m[v])));c&&c.to==g?c.to=C:h.push(c=new rt(g,C,l))}}),d.forEach(p=>o.step(p)),h.forEach(p=>o.step(p))}(this,e,t,n),this}removeMark(e,t,n){return function(o,i,s,l){let a=[],c=0;o.doc.nodesBetween(i,s,(d,h)=>{if(!d.isInline)return;c++;let p=null;if(l instanceof Wn){let f,u=d.marks;for(;f=l.isInSet(u);)(p||(p=[])).push(f),u=f.removeFromSet(u)}else l?l.isInSet(d.marks)&&(p=[l]):p=d.marks;if(p&&p.length){let f=Math.min(h+d.nodeSize,s);for(let u=0;u<p.length;u++){let m,g=p[u];for(let C=0;C<a.length;C++){let w=a[C];w.step==c-1&&g.eq(a[C].style)&&(m=w)}m?(m.to=f,m.step=c):a.push({style:g,from:Math.max(h,i),to:f,step:c})}}}),a.forEach(d=>o.step(new Je(d.from,d.to,d.style)))}(this,e,t,n),this}clearIncompatible(e,t,n){return Tr(this,e,t,n),this}}const lo=Object.create(null);class V{constructor(e,t,n){this.$anchor=e,this.$head=t,this.ranges=n||[new ha(e.min(t),e.max(t))]}get anchor(){return this.$anchor.pos}get head(){return this.$head.pos}get from(){return this.$from.pos}get to(){return this.$to.pos}get $from(){return this.ranges[0].$from}get $to(){return this.ranges[0].$to}get empty(){let e=this.ranges;for(let t=0;t<e.length;t++)if(e[t].$from.pos!=e[t].$to.pos)return!1;return!0}content(){return this.$from.doc.slice(this.from,this.to,!0)}replace(e,t=T.empty){let n=t.content.lastChild,o=null;for(let l=0;l<t.openEnd;l++)o=n,n=n.lastChild;let i=e.steps.length,s=this.ranges;for(let l=0;l<s.length;l++){let{$from:a,$to:c}=s[l],d=e.mapping.slice(i);e.replaceRange(d.map(a.pos),d.map(c.pos),l?T.empty:t),l==0&&Ar(e,i,(n?n.isInline:o&&o.isTextblock)?-1:1)}}replaceWith(e,t){let n=e.steps.length,o=this.ranges;for(let i=0;i<o.length;i++){let{$from:s,$to:l}=o[i],a=e.mapping.slice(n),c=a.map(s.pos),d=a.map(l.pos);i?e.deleteRange(c,d):(e.replaceRangeWith(c,d,t),Ar(e,n,t.isInline?-1:1))}}static findFrom(e,t,n=!1){let o=e.parent.inlineContent?new _(e):Vt(e.node(0),e.parent,e.pos,e.index(),t,n);if(o)return o;for(let i=e.depth-1;i>=0;i--){let s=t<0?Vt(e.node(0),e.node(i),e.before(i+1),e.index(i),t,n):Vt(e.node(0),e.node(i),e.after(i+1),e.index(i)+1,t,n);if(s)return s}return null}static near(e,t=1){return this.findFrom(e,t)||this.findFrom(e,-t)||new Re(e.node(0))}static atStart(e){return Vt(e,e,0,0,1)||new Re(e)}static atEnd(e){return Vt(e,e,e.content.size,e.childCount,-1)||new Re(e)}static fromJSON(e,t){if(!t||!t.type)throw new RangeError("Invalid input for Selection.fromJSON");let n=lo[t.type];if(!n)throw new RangeError(`No selection type ${t.type} defined`);return n.fromJSON(e,t)}static jsonID(e,t){if(e in lo)throw new RangeError("Duplicate use of selection JSON ID "+e);return lo[e]=t,t.prototype.jsonID=e,t}getBookmark(){return _.between(this.$anchor,this.$head).getBookmark()}}V.prototype.visible=!0;class ha{constructor(e,t){this.$from=e,this.$to=t}}let $r=!1;function Dr(r){$r||r.parent.inlineContent||($r=!0,console.warn("TextSelection endpoint not pointing into a node with inline content ("+r.parent.type.name+")"))}class _ extends V{constructor(e,t=e){Dr(e),Dr(t),super(e,t)}get $cursor(){return this.$anchor.pos==this.$head.pos?this.$head:null}map(e,t){let n=e.resolve(t.map(this.head));if(!n.parent.inlineContent)return V.near(n);let o=e.resolve(t.map(this.anchor));return new _(o.parent.inlineContent?o:n,n)}replace(e,t=T.empty){if(super.replace(e,t),t==T.empty){let n=this.$from.marksAcross(this.$to);n&&e.ensureMarks(n)}}eq(e){return e instanceof _&&e.anchor==this.anchor&&e.head==this.head}getBookmark(){return new Un(this.anchor,this.head)}toJSON(){return{type:"text",anchor:this.anchor,head:this.head}}static fromJSON(e,t){if(typeof t.anchor!="number"||typeof t.head!="number")throw new RangeError("Invalid input for TextSelection.fromJSON");return new _(e.resolve(t.anchor),e.resolve(t.head))}static create(e,t,n=t){let o=e.resolve(t);return new this(o,n==t?o:e.resolve(n))}static between(e,t,n){let o=e.pos-t.pos;if(n&&!o||(n=o>=0?1:-1),!t.parent.inlineContent){let i=V.findFrom(t,n,!0)||V.findFrom(t,-n,!0);if(!i)return V.near(t,n);t=i.$head}return e.parent.inlineContent||(o==0||(e=(V.findFrom(e,-n,!0)||V.findFrom(e,n,!0)).$anchor).pos<t.pos!=o<0)&&(e=t),new _(e,t)}}V.jsonID("text",_);class Un{constructor(e,t){this.anchor=e,this.head=t}map(e){return new Un(e.map(this.anchor),e.map(this.head))}resolve(e){return _.between(e.resolve(this.anchor),e.resolve(this.head))}}class R extends V{constructor(e){let t=e.nodeAfter,n=e.node(0).resolve(e.pos+t.nodeSize);super(e,n),this.node=t}map(e,t){let{deleted:n,pos:o}=t.mapResult(this.anchor),i=e.resolve(o);return n?V.near(i):new R(i)}content(){return new T(S.from(this.node),0,0)}eq(e){return e instanceof R&&e.anchor==this.anchor}toJSON(){return{type:"node",anchor:this.anchor}}getBookmark(){return new Xo(this.anchor)}static fromJSON(e,t){if(typeof t.anchor!="number")throw new RangeError("Invalid input for NodeSelection.fromJSON");return new R(e.resolve(t.anchor))}static create(e,t){return new R(e.resolve(t))}static isSelectable(e){return!e.isText&&e.type.spec.selectable!==!1}}R.prototype.visible=!1,V.jsonID("node",R);class Xo{constructor(e){this.anchor=e}map(e){let{deleted:t,pos:n}=e.mapResult(this.anchor);return t?new Un(n,n):new Xo(n)}resolve(e){let t=e.resolve(this.anchor),n=t.nodeAfter;return n&&R.isSelectable(n)?new R(t):V.near(t)}}class Re extends V{constructor(e){super(e.resolve(0),e.resolve(e.content.size))}replace(e,t=T.empty){if(t==T.empty){e.delete(0,e.doc.content.size);let n=V.atStart(e.doc);n.eq(e.selection)||e.setSelection(n)}else super.replace(e,t)}toJSON(){return{type:"all"}}static fromJSON(e){return new Re(e)}map(e){return new Re(e)}eq(e){return e instanceof Re}getBookmark(){return pa}}V.jsonID("all",Re);const pa={map(){return this},resolve:r=>new Re(r)};function Vt(r,e,t,n,o,i=!1){if(e.inlineContent)return _.create(r,t);for(let s=n-(o>0?0:1);o>0?s<e.childCount:s>=0;s+=o){let l=e.child(s);if(l.isAtom){if(!i&&R.isSelectable(l))return R.create(r,t-(o<0?l.nodeSize:0))}else{let a=Vt(r,l,t+o,o<0?l.childCount:0,o,i);if(a)return a}t+=l.nodeSize*o}return null}function Ar(r,e,t){let n=r.steps.length-1;if(n<e)return;let o,i=r.steps[n];(i instanceof ie||i instanceof se)&&(r.mapping.maps[n].forEach((s,l,a,c)=>{o==null&&(o=c)}),r.setSelection(V.near(r.doc.resolve(o),t)))}class ua extends da{constructor(e){super(e.doc),this.curSelectionFor=0,this.updated=0,this.meta=Object.create(null),this.time=Date.now(),this.curSelection=e.selection,this.storedMarks=e.storedMarks}get selection(){return this.curSelectionFor<this.steps.length&&(this.curSelection=this.curSelection.map(this.doc,this.mapping.slice(this.curSelectionFor)),this.curSelectionFor=this.steps.length),this.curSelection}setSelection(e){if(e.$from.doc!=this.doc)throw new RangeError("Selection passed to setSelection must point at the current document");return this.curSelection=e,this.curSelectionFor=this.steps.length,this.updated=-3&this.updated|1,this.storedMarks=null,this}get selectionSet(){return(1&this.updated)>0}setStoredMarks(e){return this.storedMarks=e,this.updated|=2,this}ensureMarks(e){return W.sameSet(this.storedMarks||this.selection.$from.marks(),e)||this.setStoredMarks(e),this}addStoredMark(e){return this.ensureMarks(e.addToSet(this.storedMarks||this.selection.$head.marks()))}removeStoredMark(e){return this.ensureMarks(e.removeFromSet(this.storedMarks||this.selection.$head.marks()))}get storedMarksSet(){return(2&this.updated)>0}addStep(e,t){super.addStep(e,t),this.updated=-3&this.updated,this.storedMarks=null}setTime(e){return this.time=e,this}replaceSelection(e){return this.selection.replace(this,e),this}replaceSelectionWith(e,t=!0){let n=this.selection;return t&&(e=e.mark(this.storedMarks||(n.empty?n.$from.marks():n.$from.marksAcross(n.$to)||W.none))),n.replaceWith(this,e),this}deleteSelection(){return this.selection.replace(this),this}insertText(e,t,n){let o=this.doc.type.schema;if(t==null)return e?this.replaceSelectionWith(o.text(e),!0):this.deleteSelection();{if(n==null&&(n=t),n=n??t,!e)return this.deleteRange(t,n);let i=this.storedMarks;if(!i){let s=this.doc.resolve(t);i=n==t?s.marks():s.marksAcross(this.doc.resolve(n))}return this.replaceRangeWith(t,n,o.text(e,i)),this.selection.empty||this.setSelection(V.near(this.selection.$to)),this}}setMeta(e,t){return this.meta[typeof e=="string"?e:e.key]=t,this}getMeta(e){return this.meta[typeof e=="string"?e:e.key]}get isGeneric(){for(let e in this.meta)return!1;return!0}scrollIntoView(){return this.updated|=4,this}get scrolledIntoView(){return(4&this.updated)>0}}function Lr(r,e){return e&&r?r.bind(e):r}class an{constructor(e,t,n){this.name=e,this.init=Lr(t.init,n),this.apply=Lr(t.apply,n)}}const fa=[new an("doc",{init:r=>r.doc||r.schema.topNodeType.createAndFill(),apply:r=>r.doc}),new an("selection",{init:(r,e)=>r.selection||V.atStart(e.doc),apply:r=>r.selection}),new an("storedMarks",{init:r=>r.storedMarks||null,apply:(r,e,t,n)=>n.selection.$cursor?r.storedMarks:null}),new an("scrollToSelection",{init:()=>0,apply:(r,e)=>r.scrolledIntoView?e+1:e})];class ao{constructor(e,t){this.schema=e,this.plugins=[],this.pluginsByKey=Object.create(null),this.fields=fa.slice(),t&&t.forEach(n=>{if(this.pluginsByKey[n.key])throw new RangeError("Adding different instances of a keyed plugin ("+n.key+")");this.plugins.push(n),this.pluginsByKey[n.key]=n,n.spec.state&&this.fields.push(new an(n.key,n.spec.state,n))})}}class Jt{constructor(e){this.config=e}get schema(){return this.config.schema}get plugins(){return this.config.plugins}apply(e){return this.applyTransaction(e).state}filterTransaction(e,t=-1){for(let n=0;n<this.config.plugins.length;n++)if(n!=t){let o=this.config.plugins[n];if(o.spec.filterTransaction&&!o.spec.filterTransaction.call(o,e,this))return!1}return!0}applyTransaction(e){if(!this.filterTransaction(e))return{state:this,transactions:[]};let t=[e],n=this.applyInner(e),o=null;for(;;){let i=!1;for(let s=0;s<this.config.plugins.length;s++){let l=this.config.plugins[s];if(l.spec.appendTransaction){let a=o?o[s].n:0,c=o?o[s].state:this,d=a<t.length&&l.spec.appendTransaction.call(l,a?t.slice(a):t,c,n);if(d&&n.filterTransaction(d,s)){if(d.setMeta("appendedTransaction",e),!o){o=[];for(let h=0;h<this.config.plugins.length;h++)o.push(h<s?{state:n,n:t.length}:{state:this,n:0})}t.push(d),n=n.applyInner(d),i=!0}o&&(o[s]={state:n,n:t.length})}}if(!i)return{state:n,transactions:t}}}applyInner(e){if(!e.before.eq(this.doc))throw new RangeError("Applying a mismatched transaction");let t=new Jt(this.config),n=this.config.fields;for(let o=0;o<n.length;o++){let i=n[o];t[i.name]=i.apply(e,this[i.name],this,t)}return t}get tr(){return new ua(this)}static create(e){let t=new ao(e.doc?e.doc.type.schema:e.schema,e.plugins),n=new Jt(t);for(let o=0;o<t.fields.length;o++)n[t.fields[o].name]=t.fields[o].init(e,n);return n}reconfigure(e){let t=new ao(this.schema,e.plugins),n=t.fields,o=new Jt(t);for(let i=0;i<n.length;i++){let s=n[i].name;o[s]=this.hasOwnProperty(s)?this[s]:n[i].init(e,o)}return o}toJSON(e){let t={doc:this.doc.toJSON(),selection:this.selection.toJSON()};if(this.storedMarks&&(t.storedMarks=this.storedMarks.map(n=>n.toJSON())),e&&typeof e=="object")for(let n in e){if(n=="doc"||n=="selection")throw new RangeError("The JSON fields `doc` and `selection` are reserved");let o=e[n],i=o.spec.state;i&&i.toJSON&&(t[n]=i.toJSON.call(o,this[o.key]))}return t}static fromJSON(e,t,n){if(!t)throw new RangeError("Invalid input for EditorState.fromJSON");if(!e.schema)throw new RangeError("Required config field 'schema' missing");let o=new ao(e.schema,e.plugins),i=new Jt(o);return o.fields.forEach(s=>{if(s.name=="doc")i.doc=Ot.fromJSON(e.schema,t.doc);else if(s.name=="selection")i.selection=V.fromJSON(i.doc,t.selection);else if(s.name=="storedMarks")t.storedMarks&&(i.storedMarks=t.storedMarks.map(e.schema.markFromJSON));else{if(n)for(let l in n){let a=n[l],c=a.spec.state;if(a.key==s.name&&c&&c.fromJSON&&Object.prototype.hasOwnProperty.call(t,l))return void(i[s.name]=c.fromJSON.call(a,e,t[l],i))}i[s.name]=s.init(e,i)}}),i}}function zs(r,e,t){for(let n in r){let o=r[n];o instanceof Function?o=o.bind(e):n=="handleDOMEvents"&&(o=zs(o,e,{})),t[n]=o}return t}class Te{constructor(e){this.spec=e,this.props={},e.props&&zs(e.props,this,this.props),this.key=e.key?e.key.key:Bs("plugin")}getState(e){return e[this.key]}}const co=Object.create(null);function Bs(r){return r in co?r+"$"+ ++co[r]:(co[r]=0,r+"$")}class Oe{constructor(e="key"){this.key=Bs(e)}get(e){return e.config.pluginsByKey[this.key]}getState(e){return e[this.key]}}const ce=function(r){for(var e=0;;e++)if(!(r=r.previousSibling))return e},Cn=function(r){let e=r.assignedSlot||r.parentNode;return e&&e.nodeType==11?e.host:e};let Io=null;const Ze=function(r,e,t){let n=Io||(Io=document.createRange());return n.setEnd(r,t??r.nodeValue.length),n.setStart(r,e||0),n},Dt=function(r,e,t,n){return t&&(Ir(r,e,t,n,-1)||Ir(r,e,t,n,1))},ma=/^(img|br|input|textarea|hr)$/i;function Ir(r,e,t,n,o){for(;;){if(r==t&&e==n)return!0;if(e==(o<0?0:Fe(r))){let i=r.parentNode;if(!i||i.nodeType!=1||yn(r)||ma.test(r.nodeName)||r.contentEditable=="false")return!1;e=ce(r)+(o<0?0:1),r=i}else{if(r.nodeType!=1||(r=r.childNodes[e+(o<0?-1:0)]).contentEditable=="false")return!1;e=o<0?Fe(r):0}}}function Fe(r){return r.nodeType==3?r.nodeValue.length:r.childNodes.length}function yn(r){let e;for(let t=r;t&&!(e=t.pmViewDesc);t=t.parentNode);return e&&e.node&&e.node.isBlock&&(e.dom==r||e.contentDOM==r)}const Zn=function(r){return r.focusNode&&Dt(r.focusNode,r.focusOffset,r.anchorNode,r.anchorOffset)};function gt(r,e){let t=document.createEvent("Event");return t.initEvent("keydown",!0,!0),t.keyCode=r,t.key=t.code=e,t}const We=typeof navigator<"u"?navigator:null,Pr=typeof document<"u"?document:null,ut=We&&We.userAgent||"",Po=/Edge\/(\d+)/.exec(ut),_s=/MSIE \d/.exec(ut),Ro=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(ut),xe=!!(_s||Ro||Po),at=_s?document.documentMode:Ro?+Ro[1]:Po?+Po[1]:0,ze=!xe&&/gecko\/(\d+)/i.test(ut);ze&&(/Firefox\/(\d+)/.exec(ut)||[0,0])[1];const zo=!xe&&/Chrome\/(\d+)/.exec(ut),ue=!!zo,ga=zo?+zo[1]:0,ge=!xe&&!!We&&/Apple Computer/.test(We.vendor),tn=ge&&(/Mobile\/\w+/.test(ut)||!!We&&We.maxTouchPoints>2),Ee=tn||!!We&&/Mac/.test(We.platform),Ca=!!We&&/Win/.test(We.platform),Le=/Android \d/.test(ut),vn=!!Pr&&"webkitFontSmoothing"in Pr.documentElement.style,ya=vn?+(/\bAppleWebKit\/(\d+)/.exec(navigator.userAgent)||[0,0])[1]:0;function wa(r){let e=r.defaultView&&r.defaultView.visualViewport;return e?{left:0,right:e.width,top:0,bottom:e.height}:{left:0,right:r.documentElement.clientWidth,top:0,bottom:r.documentElement.clientHeight}}function Ue(r,e){return typeof r=="number"?r:r[e]}function va(r){let e=r.getBoundingClientRect(),t=e.width/r.offsetWidth||1,n=e.height/r.offsetHeight||1;return{left:e.left,right:e.left+r.clientWidth*t,top:e.top,bottom:e.top+r.clientHeight*n}}function Rr(r,e,t){let n=r.someProp("scrollThreshold")||0,o=r.someProp("scrollMargin")||5,i=r.dom.ownerDocument;for(let s=t||r.dom;s;s=Cn(s)){if(s.nodeType!=1)continue;let l=s,a=l==i.body,c=a?wa(i):va(l),d=0,h=0;if(e.top<c.top+Ue(n,"top")?h=-(c.top-e.top+Ue(o,"top")):e.bottom>c.bottom-Ue(n,"bottom")&&(h=e.bottom-e.top>c.bottom-c.top?e.top+Ue(o,"top")-c.top:e.bottom-c.bottom+Ue(o,"bottom")),e.left<c.left+Ue(n,"left")?d=-(c.left-e.left+Ue(o,"left")):e.right>c.right-Ue(n,"right")&&(d=e.right-c.right+Ue(o,"right")),d||h)if(a)i.defaultView.scrollBy(d,h);else{let p=l.scrollLeft,f=l.scrollTop;h&&(l.scrollTop+=h),d&&(l.scrollLeft+=d);let u=l.scrollLeft-p,m=l.scrollTop-f;e={left:e.left-u,top:e.top-m,right:e.right-u,bottom:e.bottom-m}}if(a||/^(fixed|sticky)$/.test(getComputedStyle(s).position))break}}function zr(r){let e=[],t=r.ownerDocument;for(let n=r;n&&(e.push({dom:n,top:n.scrollTop,left:n.scrollLeft}),r!=t);n=Cn(n));return e}function Br(r,e){for(let t=0;t<r.length;t++){let{dom:n,top:o,left:i}=r[t];n.scrollTop!=o+e&&(n.scrollTop=o+e),n.scrollLeft!=i&&(n.scrollLeft=i)}}let zt=null;function Vs(r,e){let t,n,o,i,s=2e8,l=0,a=e.top,c=e.top;for(let d=r.firstChild,h=0;d;d=d.nextSibling,h++){let p;if(d.nodeType==1)p=d.getClientRects();else{if(d.nodeType!=3)continue;p=Ze(d).getClientRects()}for(let f=0;f<p.length;f++){let u=p[f];if(u.top<=a&&u.bottom>=c){a=Math.max(u.bottom,a),c=Math.min(u.top,c);let m=u.left>e.left?u.left-e.left:u.right<e.left?e.left-u.right:0;if(m<s){t=d,s=m,n=m&&t.nodeType==3?{left:u.right<e.left?u.right:u.left,top:e.top}:e,d.nodeType==1&&m&&(l=h+(e.left>=(u.left+u.right)/2?1:0));continue}}else u.top>e.top&&!o&&u.left<=e.left&&u.right>=e.left&&(o=d,i={left:Math.max(u.left,Math.min(u.right,e.left)),top:u.top});!t&&(e.left>=u.right&&e.top>=u.top||e.left>=u.left&&e.top>=u.bottom)&&(l=h+1)}}return!t&&o&&(t=o,n=i,s=0),t&&t.nodeType==3?function(d,h){let p=d.nodeValue.length,f=document.createRange();for(let u=0;u<p;u++){f.setEnd(d,u+1),f.setStart(d,u);let m=Xe(f,1);if(m.top!=m.bottom&&Qo(h,m))return{node:d,offset:u+(h.left>=(m.left+m.right)/2?1:0)}}return{node:d,offset:0}}(t,n):!t||s&&t.nodeType==1?{node:r,offset:l}:Vs(t,n)}function Qo(r,e){return r.left>=e.left-1&&r.left<=e.right+1&&r.top>=e.top-1&&r.top<=e.bottom+1}function Fs(r,e,t){let n=r.childNodes.length;if(n&&t.top<t.bottom)for(let o=Math.max(0,Math.min(n-1,Math.floor(n*(e.top-t.top)/(t.bottom-t.top))-2)),i=o;;){let s=r.childNodes[i];if(s.nodeType==1){let l=s.getClientRects();for(let a=0;a<l.length;a++){let c=l[a];if(Qo(e,c))return Fs(s,e,c)}}if((i=(i+1)%n)==o)break}return r}function xa(r,e){let t,n=r.dom.ownerDocument,o=0,i=function(c,d,h){if(c.caretPositionFromPoint)try{let p=c.caretPositionFromPoint(d,h);if(p)return{node:p.offsetNode,offset:p.offset}}catch{}if(c.caretRangeFromPoint){let p=c.caretRangeFromPoint(d,h);if(p)return{node:p.startContainer,offset:p.startOffset}}}(n,e.left,e.top);i&&({node:t,offset:o}=i);let s,l=(r.root.elementFromPoint?r.root:n).elementFromPoint(e.left,e.top);if(!l||!r.dom.contains(l.nodeType!=1?l.parentNode:l)){let c=r.dom.getBoundingClientRect();if(!Qo(e,c)||(l=Fs(r.dom,e,c),!l))return null}if(ge)for(let c=l;t&&c;c=Cn(c))c.draggable&&(t=void 0);if(l=function(c,d){let h=c.parentNode;return h&&/^li$/i.test(h.nodeName)&&d.left<c.getBoundingClientRect().left?h:c}(l,e),t){if(ze&&t.nodeType==1&&(o=Math.min(o,t.childNodes.length),o<t.childNodes.length)){let d,h=t.childNodes[o];h.nodeName=="IMG"&&(d=h.getBoundingClientRect()).right<=e.left&&d.bottom>e.top&&o++}let c;vn&&o&&t.nodeType==1&&(c=t.childNodes[o-1]).nodeType==1&&c.contentEditable=="false"&&c.getBoundingClientRect().top>=e.top&&o--,t==r.dom&&o==t.childNodes.length-1&&t.lastChild.nodeType==1&&e.top>t.lastChild.getBoundingClientRect().bottom?s=r.state.doc.content.size:o!=0&&t.nodeType==1&&t.childNodes[o-1].nodeName=="BR"||(s=function(d,h,p,f){let u=-1;for(let m=h,g=!1;m!=d.dom;){let C=d.docView.nearestDesc(m,!0);if(!C)return null;if(C.dom.nodeType==1&&(C.node.isBlock&&C.parent&&!g||!C.contentDOM)){let w=C.dom.getBoundingClientRect();if(C.node.isBlock&&C.parent&&!g&&(g=!0,w.left>f.left||w.top>f.top?u=C.posBefore:(w.right<f.left||w.bottom<f.top)&&(u=C.posAfter)),!C.contentDOM&&u<0&&!C.node.isText)return(C.node.isBlock?f.top<(w.top+w.bottom)/2:f.left<(w.left+w.right)/2)?C.posBefore:C.posAfter}m=C.dom.parentNode}return u>-1?u:d.docView.posFromDOM(h,p,-1)}(r,t,o,e))}s==null&&(s=function(c,d,h){let{node:p,offset:f}=Vs(d,h),u=-1;if(p.nodeType==1&&!p.firstChild){let m=p.getBoundingClientRect();u=m.left!=m.right&&h.left>(m.left+m.right)/2?1:-1}return c.docView.posFromDOM(p,f,u)}(r,l,e));let a=r.docView.nearestDesc(l,!0);return{pos:s,inside:a?a.posAtStart-a.border:-1}}function _r(r){return r.top<r.bottom||r.left<r.right}function Xe(r,e){let t=r.getClientRects();if(t.length){let n=t[e<0?0:t.length-1];if(_r(n))return n}return Array.prototype.find.call(t,_r)||r.getBoundingClientRect()}const ba=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/;function Hs(r,e,t){let{node:n,offset:o,atom:i}=r.docView.domFromPos(e,t<0?-1:1),s=vn||ze;if(n.nodeType==3){if(!s||!ba.test(n.nodeValue)&&(t<0?o:o!=n.nodeValue.length)){let l=o,a=o,c=t<0?1:-1;return t<0&&!o?(a++,c=-1):t>=0&&o==n.nodeValue.length?(l--,c=1):t<0?l--:a++,nn(Xe(Ze(n,l,a),c),c<0)}{let l=Xe(Ze(n,o,o),t);if(ze&&o&&/\s/.test(n.nodeValue[o-1])&&o<n.nodeValue.length){let a=Xe(Ze(n,o-1,o-1),-1);if(a.top==l.top){let c=Xe(Ze(n,o,o+1),-1);if(c.top!=l.top)return nn(c,c.left<a.left)}}return l}}if(!r.state.doc.resolve(e-(i||0)).parent.inlineContent){if(i==null&&o&&(t<0||o==Fe(n))){let l=n.childNodes[o-1];if(l.nodeType==1)return ho(l.getBoundingClientRect(),!1)}if(i==null&&o<Fe(n)){let l=n.childNodes[o];if(l.nodeType==1)return ho(l.getBoundingClientRect(),!0)}return ho(n.getBoundingClientRect(),t>=0)}if(i==null&&o&&(t<0||o==Fe(n))){let l=n.childNodes[o-1],a=l.nodeType==3?Ze(l,Fe(l)-(s?0:1)):l.nodeType!=1||l.nodeName=="BR"&&l.nextSibling?null:l;if(a)return nn(Xe(a,1),!1)}if(i==null&&o<Fe(n)){let l=n.childNodes[o];for(;l.pmViewDesc&&l.pmViewDesc.ignoreForCoords;)l=l.nextSibling;let a=l?l.nodeType==3?Ze(l,0,s?0:1):l.nodeType==1?l:null:null;if(a)return nn(Xe(a,-1),!0)}return nn(Xe(n.nodeType==3?Ze(n):n,-t),t>=0)}function nn(r,e){if(r.width==0)return r;let t=e?r.left:r.right;return{top:r.top,bottom:r.bottom,left:t,right:t}}function ho(r,e){if(r.height==0)return r;let t=e?r.top:r.bottom;return{top:t,bottom:t,left:r.left,right:r.right}}function Vr(r,e,t){let n=r.state,o=r.root.activeElement;n!=e&&r.updateState(e),o!=r.dom&&r.focus();try{return t()}finally{n!=e&&r.updateState(n),o!=r.dom&&o&&o.focus()}}const ka=/[\u0590-\u08ac]/;let Fr=null,Hr=null,jr=!1;function Ma(r,e,t){return Fr==e&&Hr==t?jr:(Fr=e,Hr=t,jr=t=="up"||t=="down"?function(n,o,i){let s=o.selection,l=i=="up"?s.$from:s.$to;return Vr(n,o,()=>{let{node:a}=n.docView.domFromPos(l.pos,i=="up"?-1:1);for(;;){let d=n.docView.nearestDesc(a,!0);if(!d)break;if(d.node.isBlock){a=d.contentDOM||d.dom;break}a=d.dom.parentNode}let c=Hs(n,l.pos,1);for(let d=a.firstChild;d;d=d.nextSibling){let h;if(d.nodeType==1)h=d.getClientRects();else{if(d.nodeType!=3)continue;h=Ze(d,0,d.nodeValue.length).getClientRects()}for(let p=0;p<h.length;p++){let f=h[p];if(f.bottom>f.top+1&&(i=="up"?c.top-f.top>2*(f.bottom-c.top):f.bottom-c.bottom>2*(c.bottom-f.top)))return!1}}return!0})}(r,e,t):function(n,o,i){let{$head:s}=o.selection;if(!s.parent.isTextblock)return!1;let l=s.parentOffset,a=!l,c=l==s.parent.content.size,d=n.domSelection();return ka.test(s.parent.textContent)&&d.modify?Vr(n,o,()=>{let{focusNode:h,focusOffset:p,anchorNode:f,anchorOffset:u}=n.domSelectionRange(),m=d.caretBidiLevel;d.modify("move",i,"character");let g=s.depth?n.docView.domAfterPos(s.before()):n.dom,{focusNode:C,focusOffset:w}=n.domSelectionRange(),v=C&&!g.contains(C.nodeType==1?C:C.parentNode)||h==C&&p==w;try{d.collapse(f,u),h&&(h!=f||p!=u)&&d.extend&&d.extend(h,p)}catch{}return m!=null&&(d.caretBidiLevel=m),v}):i=="left"||i=="backward"?a:c}(r,e,t))}class xn{constructor(e,t,n,o){this.parent=e,this.children=t,this.dom=n,this.contentDOM=o,this.dirty=0,n.pmViewDesc=this}matchesWidget(e){return!1}matchesMark(e){return!1}matchesNode(e,t,n){return!1}matchesHack(e){return!1}parseRule(){return null}stopEvent(e){return!1}get size(){let e=0;for(let t=0;t<this.children.length;t++)e+=this.children[t].size;return e}get border(){return 0}destroy(){this.parent=void 0,this.dom.pmViewDesc==this&&(this.dom.pmViewDesc=void 0);for(let e=0;e<this.children.length;e++)this.children[e].destroy()}posBeforeChild(e){for(let t=0,n=this.posAtStart;;t++){let o=this.children[t];if(o==e)return n;n+=o.size}}get posBefore(){return this.parent.posBeforeChild(this)}get posAtStart(){return this.parent?this.parent.posBeforeChild(this)+this.border:0}get posAfter(){return this.posBefore+this.size}get posAtEnd(){return this.posAtStart+this.size-2*this.border}localPosFromDOM(e,t,n){if(this.contentDOM&&this.contentDOM.contains(e.nodeType==1?e:e.parentNode)){if(n<0){let i,s;if(e==this.contentDOM)i=e.childNodes[t-1];else{for(;e.parentNode!=this.contentDOM;)e=e.parentNode;i=e.previousSibling}for(;i&&(!(s=i.pmViewDesc)||s.parent!=this);)i=i.previousSibling;return i?this.posBeforeChild(s)+s.size:this.posAtStart}{let i,s;if(e==this.contentDOM)i=e.childNodes[t];else{for(;e.parentNode!=this.contentDOM;)e=e.parentNode;i=e.nextSibling}for(;i&&(!(s=i.pmViewDesc)||s.parent!=this);)i=i.nextSibling;return i?this.posBeforeChild(s):this.posAtEnd}}let o;if(e==this.dom&&this.contentDOM)o=t>ce(this.contentDOM);else if(this.contentDOM&&this.contentDOM!=this.dom&&this.dom.contains(this.contentDOM))o=2&e.compareDocumentPosition(this.contentDOM);else if(this.dom.firstChild){if(t==0)for(let i=e;;i=i.parentNode){if(i==this.dom){o=!1;break}if(i.previousSibling)break}if(o==null&&t==e.childNodes.length)for(let i=e;;i=i.parentNode){if(i==this.dom){o=!0;break}if(i.nextSibling)break}}return o??n>0?this.posAtEnd:this.posAtStart}nearestDesc(e,t=!1){for(let n=!0,o=e;o;o=o.parentNode){let i,s=this.getDesc(o);if(s&&(!t||s.node)){if(!n||!(i=s.nodeDOM)||(i.nodeType==1?i.contains(e.nodeType==1?e:e.parentNode):i==e))return s;n=!1}}}getDesc(e){let t=e.pmViewDesc;for(let n=t;n;n=n.parent)if(n==this)return t}posFromDOM(e,t,n){for(let o=e;o;o=o.parentNode){let i=this.getDesc(o);if(i)return i.localPosFromDOM(e,t,n)}return-1}descAt(e){for(let t=0,n=0;t<this.children.length;t++){let o=this.children[t],i=n+o.size;if(n==e&&i!=n){for(;!o.border&&o.children.length;)o=o.children[0];return o}if(e<i)return o.descAt(e-n-o.border);n=i}}domFromPos(e,t){if(!this.contentDOM)return{node:this.dom,offset:0,atom:e+1};let n=0,o=0;for(let i=0;n<this.children.length;n++){let s=this.children[n],l=i+s.size;if(l>e||s instanceof qs){o=e-i;break}i=l}if(o)return this.children[n].domFromPos(o-this.children[n].border,t);for(let i;n&&!(i=this.children[n-1]).size&&i instanceof js&&i.side>=0;n--);if(t<=0){let i,s=!0;for(;i=n?this.children[n-1]:null,i&&i.dom.parentNode!=this.contentDOM;n--,s=!1);return i&&t&&s&&!i.border&&!i.domAtom?i.domFromPos(i.size,t):{node:this.contentDOM,offset:i?ce(i.dom)+1:0}}{let i,s=!0;for(;i=n<this.children.length?this.children[n]:null,i&&i.dom.parentNode!=this.contentDOM;n++,s=!1);return i&&s&&!i.border&&!i.domAtom?i.domFromPos(0,t):{node:this.contentDOM,offset:i?ce(i.dom):this.contentDOM.childNodes.length}}}parseRange(e,t,n=0){if(this.children.length==0)return{node:this.contentDOM,from:e,to:t,fromOffset:0,toOffset:this.contentDOM.childNodes.length};let o=-1,i=-1;for(let s=n,l=0;;l++){let a=this.children[l],c=s+a.size;if(o==-1&&e<=c){let d=s+a.border;if(e>=d&&t<=c-a.border&&a.node&&a.contentDOM&&this.contentDOM.contains(a.contentDOM))return a.parseRange(e,t,d);e=s;for(let h=l;h>0;h--){let p=this.children[h-1];if(p.size&&p.dom.parentNode==this.contentDOM&&!p.emptyChildAt(1)){o=ce(p.dom)+1;break}e-=p.size}o==-1&&(o=0)}if(o>-1&&(c>t||l==this.children.length-1)){t=c;for(let d=l+1;d<this.children.length;d++){let h=this.children[d];if(h.size&&h.dom.parentNode==this.contentDOM&&!h.emptyChildAt(-1)){i=ce(h.dom);break}t+=h.size}i==-1&&(i=this.contentDOM.childNodes.length);break}s=c}return{node:this.contentDOM,from:e,to:t,fromOffset:o,toOffset:i}}emptyChildAt(e){if(this.border||!this.contentDOM||!this.children.length)return!1;let t=this.children[e<0?0:this.children.length-1];return t.size==0||t.emptyChildAt(e)}domAfterPos(e){let{node:t,offset:n}=this.domFromPos(e,0);if(t.nodeType!=1||n==t.childNodes.length)throw new RangeError("No node after pos "+e);return t.childNodes[n]}setSelection(e,t,n,o=!1){let i=Math.min(e,t),s=Math.max(e,t);for(let p=0,f=0;p<this.children.length;p++){let u=this.children[p],m=f+u.size;if(i>f&&s<m)return u.setSelection(e-f-u.border,t-f-u.border,n,o);f=m}let l=this.domFromPos(e,e?-1:1),a=t==e?l:this.domFromPos(t,t?-1:1),c=n.getSelection(),d=!1;if((ze||ge)&&e==t){let{node:p,offset:f}=l;if(p.nodeType==3){if(d=!(!f||p.nodeValue[f-1]!=`
`),d&&f==p.nodeValue.length)for(let u,m=p;m;m=m.parentNode){if(u=m.nextSibling){u.nodeName=="BR"&&(l=a={node:u.parentNode,offset:ce(u)+1});break}let g=m.pmViewDesc;if(g&&g.node&&g.node.isBlock)break}}else{let u=p.childNodes[f-1];d=u&&(u.nodeName=="BR"||u.contentEditable=="false")}}if(ze&&c.focusNode&&c.focusNode!=a.node&&c.focusNode.nodeType==1){let p=c.focusNode.childNodes[c.focusOffset];p&&p.contentEditable=="false"&&(o=!0)}if(!(o||d&&ge)&&Dt(l.node,l.offset,c.anchorNode,c.anchorOffset)&&Dt(a.node,a.offset,c.focusNode,c.focusOffset))return;let h=!1;if((c.extend||e==t)&&!d){c.collapse(l.node,l.offset);try{e!=t&&c.extend(a.node,a.offset),h=!0}catch{}}if(!h){if(e>t){let f=l;l=a,a=f}let p=document.createRange();p.setEnd(a.node,a.offset),p.setStart(l.node,l.offset),c.removeAllRanges(),c.addRange(p)}}ignoreMutation(e){return!this.contentDOM&&e.type!="selection"}get contentLost(){return this.contentDOM&&this.contentDOM!=this.dom&&!this.dom.contains(this.contentDOM)}markDirty(e,t){for(let n=0,o=0;o<this.children.length;o++){let i=this.children[o],s=n+i.size;if(n==s?e<=s&&t>=n:e<s&&t>n){let l=n+i.border,a=s-i.border;if(e>=l&&t<=a)return this.dirty=e==n||t==s?2:1,void(e!=l||t!=a||!i.contentLost&&i.dom.parentNode==this.contentDOM?i.markDirty(e-l,t-l):i.dirty=3);i.dirty=i.dom!=i.contentDOM||i.dom.parentNode!=this.contentDOM||i.children.length?3:2}n=s}this.dirty=2}markParentsDirty(){let e=1;for(let t=this.parent;t;t=t.parent,e++){let n=e==1?2:1;t.dirty<n&&(t.dirty=n)}}get domAtom(){return!1}get ignoreForCoords(){return!1}isText(e){return!1}}class js extends xn{constructor(e,t,n,o){let i,s=t.type.toDOM;if(typeof s=="function"&&(s=s(n,()=>i?i.parent?i.parent.posBeforeChild(i):void 0:o)),!t.type.spec.raw){if(s.nodeType!=1){let l=document.createElement("span");l.appendChild(s),s=l}s.contentEditable="false",s.classList.add("ProseMirror-widget")}super(e,[],s,null),this.widget=t,this.widget=t,i=this}matchesWidget(e){return this.dirty==0&&e.type.eq(this.widget.type)}parseRule(){return{ignore:!0}}stopEvent(e){let t=this.widget.spec.stopEvent;return!!t&&t(e)}ignoreMutation(e){return e.type!="selection"||this.widget.spec.ignoreSelection}destroy(){this.widget.type.destroy(this.dom),super.destroy()}get domAtom(){return!0}get side(){return this.widget.type.side}}class Sa extends xn{constructor(e,t,n,o){super(e,[],t,null),this.textDOM=n,this.text=o}get size(){return this.text.length}localPosFromDOM(e,t){return e!=this.textDOM?this.posAtStart+(t?this.size:0):this.posAtStart+t}domFromPos(e){return{node:this.textDOM,offset:e}}ignoreMutation(e){return e.type==="characterData"&&e.target.nodeValue==e.oldValue}}class Tt extends xn{constructor(e,t,n,o){super(e,[],n,o),this.mark=t}static create(e,t,n,o){let i=o.nodeViews[t.type.name],s=i&&i(t,o,n);return s&&s.dom||(s=Lt.renderSpec(document,t.type.spec.toDOM(t,n))),new Tt(e,t,s.dom,s.contentDOM||s.dom)}parseRule(){return 3&this.dirty||this.mark.type.spec.reparseInView?null:{mark:this.mark.type.name,attrs:this.mark.attrs,contentElement:this.contentDOM}}matchesMark(e){return this.dirty!=3&&this.mark.eq(e)}markDirty(e,t){if(super.markDirty(e,t),this.dirty!=0){let n=this.parent;for(;!n.node;)n=n.parent;n.dirty<this.dirty&&(n.dirty=this.dirty),this.dirty=0}}slice(e,t,n){let o=Tt.create(this.parent,this.mark,!0,n),i=this.children,s=this.size;t<s&&(i=Vo(i,t,s,n)),e>0&&(i=Vo(i,0,e,n));for(let l=0;l<i.length;l++)i[l].parent=o;return o.children=i,o}}class ct extends xn{constructor(e,t,n,o,i,s,l,a,c){super(e,[],i,s),this.node=t,this.outerDeco=n,this.innerDeco=o,this.nodeDOM=l}static create(e,t,n,o,i,s){let l,a=i.nodeViews[t.type.name],c=a&&a(t,i,()=>l?l.parent?l.parent.posBeforeChild(l):void 0:s,n,o),d=c&&c.dom,h=c&&c.contentDOM;if(t.isText)if(d){if(d.nodeType!=3)throw new RangeError("Text must be rendered as a DOM text node")}else d=document.createTextNode(t.text);else d||({dom:d,contentDOM:h}=Lt.renderSpec(document,t.type.spec.toDOM(t)));h||t.isText||d.nodeName=="BR"||(d.hasAttribute("contenteditable")||(d.contentEditable="false"),t.type.spec.draggable&&(d.draggable=!0));let p=d;return d=Ws(d,n,t),c?l=new Oa(e,t,n,o,d,h||null,p,c,i,s+1):t.isText?new Gn(e,t,n,o,d,p,i):new ct(e,t,n,o,d,h||null,p,i,s+1)}parseRule(){if(this.node.type.spec.reparseInView)return null;let e={node:this.node.type.name,attrs:this.node.attrs};if(this.node.type.whitespace=="pre"&&(e.preserveWhitespace="full"),this.contentDOM)if(this.contentLost){for(let t=this.children.length-1;t>=0;t--){let n=this.children[t];if(this.dom.contains(n.dom.parentNode)){e.contentElement=n.dom.parentNode;break}}e.contentElement||(e.getContent=()=>S.empty)}else e.contentElement=this.contentDOM;else e.getContent=()=>this.node.content;return e}matchesNode(e,t,n){return this.dirty==0&&e.eq(this.node)&&_o(t,this.outerDeco)&&n.eq(this.innerDeco)}get size(){return this.node.nodeSize}get border(){return this.node.isLeaf?0:1}updateChildren(e,t){let n=this.node.inlineContent,o=t,i=e.composing?this.localCompositionInfo(e,t):null,s=i&&i.pos>-1?i:null,l=i&&i.pos<0,a=new Ea(this,s&&s.node,e);(function(c,d,h,p){let f=d.locals(c),u=0;if(f.length==0){for(let w=0;w<c.childCount;w++){let v=c.child(w);p(v,f,d.forChild(u,v),w),u+=v.nodeSize}return}let m=0,g=[],C=null;for(let w=0;;){let v,b,k,N;for(;m<f.length&&f[m].to==u;){let O=f[m++];O.widget&&(v?(b||(b=[v])).push(O):v=O)}if(v)if(b){b.sort(Na);for(let O=0;O<b.length;O++)h(b[O],w,!!C)}else h(v,w,!!C);if(C)N=-1,k=C,C=null;else{if(!(w<c.childCount))break;N=w,k=c.child(w++)}for(let O=0;O<g.length;O++)g[O].to<=u&&g.splice(O--,1);for(;m<f.length&&f[m].from<=u&&f[m].to>u;)g.push(f[m++]);let L=u+k.nodeSize;if(k.isText){let O=L;m<f.length&&f[m].from<O&&(O=f[m].from);for(let M=0;M<g.length;M++)g[M].to<O&&(O=g[M].to);O<L&&(C=k.cut(O-u),k=k.cut(0,O-u),L=O,N=-1)}else for(;m<f.length&&f[m].to<L;)m++;p(k,k.isInline&&!k.isLeaf?g.filter(O=>!O.inline):g.slice(),d.forChild(u,k),N),u=L}})(this.node,this.innerDeco,(c,d,h)=>{c.spec.marks?a.syncToMarks(c.spec.marks,n,e):c.type.side>=0&&!h&&a.syncToMarks(d==this.node.childCount?W.none:this.node.child(d).marks,n,e),a.placeWidget(c,e,o)},(c,d,h,p)=>{let f;a.syncToMarks(c.marks,n,e),a.findNodeMatch(c,d,h,p)||l&&e.state.selection.from>o&&e.state.selection.to<o+c.nodeSize&&(f=a.findIndexWithChild(i.node))>-1&&a.updateNodeAt(c,d,h,f,e)||a.updateNextNode(c,d,h,e,p,o)||a.addNode(c,d,h,e,o),o+=c.nodeSize}),a.syncToMarks([],n,e),this.node.isTextblock&&a.addTextblockHacks(),a.destroyRest(),(a.changed||this.dirty==2)&&(s&&this.protectLocalComposition(e,s),Js(this.contentDOM,this.children,e),tn&&function(c){if(c.nodeName=="UL"||c.nodeName=="OL"){let d=c.style.cssText;c.style.cssText=d+"; list-style: square !important",window.getComputedStyle(c).listStyle,c.style.cssText=d}}(this.dom))}localCompositionInfo(e,t){let{from:n,to:o}=e.state.selection;if(!(e.state.selection instanceof _)||n<t||o>t+this.node.content.size)return null;let i=e.input.compositionNode;if(!i||!this.dom.contains(i.parentNode))return null;if(this.node.inlineContent){let s=i.nodeValue,l=function(a,c,d,h){for(let p=0,f=0;p<a.childCount&&f<=h;){let u=a.child(p++),m=f;if(f+=u.nodeSize,!u.isText)continue;let g=u.text;for(;p<a.childCount;){let C=a.child(p++);if(f+=C.nodeSize,!C.isText)break;g+=C.text}if(f>=d){if(f>=h&&g.slice(h-c.length-m,h-m)==c)return h-c.length;let C=m<h?g.lastIndexOf(c,h-m-1):-1;if(C>=0&&C+c.length+m>=d)return m+C;if(d==h&&g.length>=h+c.length-m&&g.slice(h-m,h-m+c.length)==c)return h}}return-1}(this.node.content,s,n-t,o-t);return l<0?null:{node:i,pos:l,text:s}}return{node:i,pos:-1,text:""}}protectLocalComposition(e,{node:t,pos:n,text:o}){if(this.getDesc(t))return;let i=t;for(;i.parentNode!=this.contentDOM;i=i.parentNode){for(;i.previousSibling;)i.parentNode.removeChild(i.previousSibling);for(;i.nextSibling;)i.parentNode.removeChild(i.nextSibling);i.pmViewDesc&&(i.pmViewDesc=void 0)}let s=new Sa(this,i,t,o);e.input.compositionNodes.push(s),this.children=Vo(this.children,n,n+o.length,e,s)}update(e,t,n,o){return!(this.dirty==3||!e.sameMarkup(this.node))&&(this.updateInner(e,t,n,o),!0)}updateInner(e,t,n,o){this.updateOuterDeco(t),this.node=e,this.innerDeco=n,this.contentDOM&&this.updateChildren(o,this.posAtStart),this.dirty=0}updateOuterDeco(e){if(_o(e,this.outerDeco))return;let t=this.nodeDOM.nodeType!=1,n=this.dom;this.dom=Ks(this.dom,this.nodeDOM,Bo(this.outerDeco,this.node,t),Bo(e,this.node,t)),this.dom!=n&&(n.pmViewDesc=void 0,this.dom.pmViewDesc=this),this.outerDeco=e}selectNode(){this.nodeDOM.nodeType==1&&this.nodeDOM.classList.add("ProseMirror-selectednode"),!this.contentDOM&&this.node.type.spec.draggable||(this.dom.draggable=!0)}deselectNode(){this.nodeDOM.nodeType==1&&this.nodeDOM.classList.remove("ProseMirror-selectednode"),!this.contentDOM&&this.node.type.spec.draggable||this.dom.removeAttribute("draggable")}get domAtom(){return this.node.isAtom}}function qr(r,e,t,n,o){Ws(n,e,r);let i=new ct(void 0,r,e,t,n,n,n,o,0);return i.contentDOM&&i.updateChildren(o,0),i}class Gn extends ct{constructor(e,t,n,o,i,s,l){super(e,t,n,o,i,null,s,l,0)}parseRule(){let e=this.nodeDOM.parentNode;for(;e&&e!=this.dom&&!e.pmIsDeco;)e=e.parentNode;return{skip:e||!0}}update(e,t,n,o){return!(this.dirty==3||this.dirty!=0&&!this.inParent()||!e.sameMarkup(this.node))&&(this.updateOuterDeco(t),this.dirty==0&&e.text==this.node.text||e.text==this.nodeDOM.nodeValue||(this.nodeDOM.nodeValue=e.text,o.trackWrites==this.nodeDOM&&(o.trackWrites=null)),this.node=e,this.dirty=0,!0)}inParent(){let e=this.parent.contentDOM;for(let t=this.nodeDOM;t;t=t.parentNode)if(t==e)return!0;return!1}domFromPos(e){return{node:this.nodeDOM,offset:e}}localPosFromDOM(e,t,n){return e==this.nodeDOM?this.posAtStart+Math.min(t,this.node.text.length):super.localPosFromDOM(e,t,n)}ignoreMutation(e){return e.type!="characterData"&&e.type!="selection"}slice(e,t,n){let o=this.node.cut(e,t),i=document.createTextNode(o.text);return new Gn(this.parent,o,this.outerDeco,this.innerDeco,i,i,n)}markDirty(e,t){super.markDirty(e,t),this.dom==this.nodeDOM||e!=0&&t!=this.nodeDOM.nodeValue.length||(this.dirty=3)}get domAtom(){return!1}isText(e){return this.node.text==e}}class qs extends xn{parseRule(){return{ignore:!0}}matchesHack(e){return this.dirty==0&&this.dom.nodeName==e}get domAtom(){return!0}get ignoreForCoords(){return this.dom.nodeName=="IMG"}}class Oa extends ct{constructor(e,t,n,o,i,s,l,a,c,d){super(e,t,n,o,i,s,l,c,d),this.spec=a}update(e,t,n,o){if(this.dirty==3)return!1;if(this.spec.update){let i=this.spec.update(e,t,n);return i&&this.updateInner(e,t,n,o),i}return!(!this.contentDOM&&!e.isLeaf)&&super.update(e,t,n,o)}selectNode(){this.spec.selectNode?this.spec.selectNode():super.selectNode()}deselectNode(){this.spec.deselectNode?this.spec.deselectNode():super.deselectNode()}setSelection(e,t,n,o){this.spec.setSelection?this.spec.setSelection(e,t,n):super.setSelection(e,t,n,o)}destroy(){this.spec.destroy&&this.spec.destroy(),super.destroy()}stopEvent(e){return!!this.spec.stopEvent&&this.spec.stopEvent(e)}ignoreMutation(e){return this.spec.ignoreMutation?this.spec.ignoreMutation(e):super.ignoreMutation(e)}}function Js(r,e,t){let n=r.firstChild,o=!1;for(let i=0;i<e.length;i++){let s=e[i],l=s.dom;if(l.parentNode==r){for(;l!=n;)n=Jr(n),o=!0;n=n.nextSibling}else o=!0,r.insertBefore(l,n);if(s instanceof Tt){let a=n?n.previousSibling:r.lastChild;Js(s.contentDOM,s.children,t),n=a?a.nextSibling:r.firstChild}}for(;n;)n=Jr(n),o=!0;o&&t.trackWrites==r&&(t.trackWrites=null)}const dn=function(r){r&&(this.nodeName=r)};dn.prototype=Object.create(null);const wt=[new dn];function Bo(r,e,t){if(r.length==0)return wt;let n=t?wt[0]:new dn,o=[n];for(let i=0;i<r.length;i++){let s=r[i].type.attrs;if(s){s.nodeName&&o.push(n=new dn(s.nodeName));for(let l in s){let a=s[l];a!=null&&(t&&o.length==1&&o.push(n=new dn(e.isInline?"span":"div")),l=="class"?n.class=(n.class?n.class+" ":"")+a:l=="style"?n.style=(n.style?n.style+";":"")+a:l!="nodeName"&&(n[l]=a))}}}return o}function Ks(r,e,t,n){if(t==wt&&n==wt)return e;let o=e;for(let i=0;i<n.length;i++){let s=n[i],l=t[i];if(i){let a;l&&l.nodeName==s.nodeName&&o!=r&&(a=o.parentNode)&&a.nodeName.toLowerCase()==s.nodeName||(a=document.createElement(s.nodeName),a.pmIsDeco=!0,a.appendChild(o),l=wt[0]),o=a}Ta(o,l||wt[0],s)}return o}function Ta(r,e,t){for(let n in e)n=="class"||n=="style"||n=="nodeName"||n in t||r.removeAttribute(n);for(let n in t)n!="class"&&n!="style"&&n!="nodeName"&&t[n]!=e[n]&&r.setAttribute(n,t[n]);if(e.class!=t.class){let n=e.class?e.class.split(" ").filter(Boolean):[],o=t.class?t.class.split(" ").filter(Boolean):[];for(let i=0;i<n.length;i++)o.indexOf(n[i])==-1&&r.classList.remove(n[i]);for(let i=0;i<o.length;i++)n.indexOf(o[i])==-1&&r.classList.add(o[i]);r.classList.length==0&&r.removeAttribute("class")}if(e.style!=t.style){if(e.style){let n,o=/\s*([\w\-\xa1-\uffff]+)\s*:(?:"(?:\\.|[^"])*"|'(?:\\.|[^'])*'|\(.*?\)|[^;])*/g;for(;n=o.exec(e.style);)r.style.removeProperty(n[1])}t.style&&(r.style.cssText+=t.style)}}function Ws(r,e,t){return Ks(r,r,wt,Bo(e,t,r.nodeType!=1))}function _o(r,e){if(r.length!=e.length)return!1;for(let t=0;t<r.length;t++)if(!r[t].type.eq(e[t].type))return!1;return!0}function Jr(r){let e=r.nextSibling;return r.parentNode.removeChild(r),e}class Ea{constructor(e,t,n){this.lock=t,this.view=n,this.index=0,this.stack=[],this.changed=!1,this.top=e,this.preMatch=function(o,i){let s=i,l=s.children.length,a=o.childCount,c=new Map,d=[];e:for(;a>0;){let h;for(;;)if(l){let f=s.children[l-1];if(!(f instanceof Tt)){h=f,l--;break}s=f,l=f.children.length}else{if(s==i)break e;l=s.parent.children.indexOf(s),s=s.parent}let p=h.node;if(p){if(p!=o.child(a-1))break;--a,c.set(h,a),d.push(h)}}return{index:a,matched:c,matches:d.reverse()}}(e.node.content,e)}destroyBetween(e,t){if(e!=t){for(let n=e;n<t;n++)this.top.children[n].destroy();this.top.children.splice(e,t-e),this.changed=!0}}destroyRest(){this.destroyBetween(this.index,this.top.children.length)}syncToMarks(e,t,n){let o=0,i=this.stack.length>>1,s=Math.min(i,e.length);for(;o<s&&(o==i-1?this.top:this.stack[o+1<<1]).matchesMark(e[o])&&e[o].type.spec.spanning!==!1;)o++;for(;o<i;)this.destroyRest(),this.top.dirty=0,this.index=this.stack.pop(),this.top=this.stack.pop(),i--;for(;i<e.length;){this.stack.push(this.top,this.index+1);let l=-1;for(let a=this.index;a<Math.min(this.index+3,this.top.children.length);a++){let c=this.top.children[a];if(c.matchesMark(e[i])&&!this.isLocked(c.dom)){l=a;break}}if(l>-1)l>this.index&&(this.changed=!0,this.destroyBetween(this.index,l)),this.top=this.top.children[this.index];else{let a=Tt.create(this.top,e[i],t,n);this.top.children.splice(this.index,0,a),this.top=a,this.changed=!0}this.index=0,i++}}findNodeMatch(e,t,n,o){let i,s=-1;if(o>=this.preMatch.index&&(i=this.preMatch.matches[o-this.preMatch.index]).parent==this.top&&i.matchesNode(e,t,n))s=this.top.children.indexOf(i,this.index);else for(let l=this.index,a=Math.min(this.top.children.length,l+5);l<a;l++){let c=this.top.children[l];if(c.matchesNode(e,t,n)&&!this.preMatch.matched.has(c)){s=l;break}}return!(s<0)&&(this.destroyBetween(this.index,s),this.index++,!0)}updateNodeAt(e,t,n,o,i){let s=this.top.children[o];return s.dirty==3&&s.dom==s.contentDOM&&(s.dirty=2),!!s.update(e,t,n,i)&&(this.destroyBetween(this.index,o),this.index++,!0)}findIndexWithChild(e){for(;;){let t=e.parentNode;if(!t)return-1;if(t==this.top.contentDOM){let n=e.pmViewDesc;if(n){for(let o=this.index;o<this.top.children.length;o++)if(this.top.children[o]==n)return o}return-1}e=t}}updateNextNode(e,t,n,o,i,s){for(let l=this.index;l<this.top.children.length;l++){let a=this.top.children[l];if(a instanceof ct){let c=this.preMatch.matched.get(a);if(c!=null&&c!=i)return!1;let d,h=a.dom,p=this.isLocked(h)&&!(e.isText&&a.node&&a.node.isText&&a.nodeDOM.nodeValue==e.text&&a.dirty!=3&&_o(t,a.outerDeco));if(!p&&a.update(e,t,n,o))return this.destroyBetween(this.index,l),a.dom!=h&&(this.changed=!0),this.index++,!0;if(!p&&(d=this.recreateWrapper(a,e,t,n,o,s)))return this.top.children[this.index]=d,d.contentDOM&&(d.dirty=2,d.updateChildren(o,s+1),d.dirty=0),this.changed=!0,this.index++,!0;break}}return!1}recreateWrapper(e,t,n,o,i,s){if(e.dirty||t.isAtom||!e.children.length||!e.node.content.eq(t.content))return null;let l=ct.create(this.top,t,n,o,i,s);if(l.contentDOM){l.children=e.children,e.children=[];for(let a of l.children)a.parent=l}return e.destroy(),l}addNode(e,t,n,o,i){let s=ct.create(this.top,e,t,n,o,i);s.contentDOM&&s.updateChildren(o,i+1),this.top.children.splice(this.index++,0,s),this.changed=!0}placeWidget(e,t,n){let o=this.index<this.top.children.length?this.top.children[this.index]:null;if(!o||!o.matchesWidget(e)||e!=o.widget&&o.widget.type.toDOM.parentNode){let i=new js(this.top,e,t,n);this.top.children.splice(this.index++,0,i),this.changed=!0}else this.index++}addTextblockHacks(){let e=this.top.children[this.index-1],t=this.top;for(;e instanceof Tt;)t=e,e=t.children[t.children.length-1];(!e||!(e instanceof Gn)||/\n$/.test(e.node.text)||this.view.requiresGeckoHackNode&&/\s$/.test(e.node.text))&&((ge||ue)&&e&&e.dom.contentEditable=="false"&&this.addHackNode("IMG",t),this.addHackNode("BR",this.top))}addHackNode(e,t){if(t==this.top&&this.index<t.children.length&&t.children[this.index].matchesHack(e))this.index++;else{let n=document.createElement(e);e=="IMG"&&(n.className="ProseMirror-separator",n.alt=""),e=="BR"&&(n.className="ProseMirror-trailingBreak");let o=new qs(this.top,[],n,null);t!=this.top?t.children.push(o):t.children.splice(this.index++,0,o),this.changed=!0}}isLocked(e){return this.lock&&(e==this.lock||e.nodeType==1&&e.contains(this.lock.parentNode))}}function Na(r,e){return r.type.side-e.type.side}function Vo(r,e,t,n,o){let i=[];for(let s=0,l=0;s<r.length;s++){let a=r[s],c=l,d=l+=a.size;c>=t||d<=e?i.push(a):(c<e&&i.push(a.slice(0,e-c,n)),o&&(i.push(o),o=void 0),d>t&&i.push(a.slice(t-c,a.size,n)))}return i}function er(r,e=null){let t=r.domSelectionRange(),n=r.state.doc;if(!t.focusNode)return null;let o=r.docView.nearestDesc(t.focusNode),i=o&&o.size==0,s=r.docView.posFromDOM(t.focusNode,t.focusOffset,1);if(s<0)return null;let l,a,c=n.resolve(s);if(Zn(t)){for(l=c;o&&!o.node;)o=o.parent;let d=o.node;if(o&&d.isAtom&&R.isSelectable(d)&&o.parent&&(!d.isInline||!function(h,p,f){for(let u=p==0,m=p==Fe(h);u||m;){if(h==f)return!0;let g=ce(h);if(!(h=h.parentNode))return!1;u=u&&g==0,m=m&&g==Fe(h)}}(t.focusNode,t.focusOffset,o.dom))){let h=o.posBefore;a=new R(s==h?c:n.resolve(h))}}else{let d=r.docView.posFromDOM(t.anchorNode,t.anchorOffset,1);if(d<0)return null;l=n.resolve(d)}return a||(a=tr(r,l,c,e=="pointer"||r.state.selection.head<c.pos&&!i?1:-1)),a}function Kr(r){return r.editable?r.hasFocus():Zs(r)&&document.activeElement&&document.activeElement.contains(r.dom)}function Ye(r,e=!1){let t=r.state.selection;if(Us(r,t),Kr(r)){if(!e&&r.input.mouseDown&&r.input.mouseDown.allowDefault&&ue){let n=r.domSelectionRange(),o=r.domObserver.currentSelection;if(n.anchorNode&&o.anchorNode&&Dt(n.anchorNode,n.anchorOffset,o.anchorNode,o.anchorOffset))return r.input.mouseDown.delayedSelectionSync=!0,void r.domObserver.setCurSelection()}if(r.domObserver.disconnectSelection(),r.cursorWrapper)(function(n){let o=n.domSelection(),i=document.createRange(),s=n.cursorWrapper.dom,l=s.nodeName=="IMG";l?i.setEnd(s.parentNode,ce(s)+1):i.setEnd(s,0),i.collapse(!1),o.removeAllRanges(),o.addRange(i),!l&&!n.state.selection.visible&&xe&&at<=11&&(s.disabled=!0,s.disabled=!1)})(r);else{let n,o,{anchor:i,head:s}=t;!Wr||t instanceof _||(t.$from.parent.inlineContent||(n=Ur(r,t.from)),t.empty||t.$from.parent.inlineContent||(o=Ur(r,t.to))),r.docView.setSelection(i,s,r.root,e),Wr&&(n&&Zr(n),o&&Zr(o)),t.visible?r.dom.classList.remove("ProseMirror-hideselection"):(r.dom.classList.add("ProseMirror-hideselection"),"onselectionchange"in document&&function(l){let a=l.dom.ownerDocument;a.removeEventListener("selectionchange",l.input.hideSelectionGuard);let c=l.domSelectionRange(),d=c.anchorNode,h=c.anchorOffset;a.addEventListener("selectionchange",l.input.hideSelectionGuard=()=>{c.anchorNode==d&&c.anchorOffset==h||(a.removeEventListener("selectionchange",l.input.hideSelectionGuard),setTimeout(()=>{Kr(l)&&!l.state.selection.visible||l.dom.classList.remove("ProseMirror-hideselection")},20))})}(r))}r.domObserver.setCurSelection(),r.domObserver.connectSelection()}}const Wr=ge||ue&&ga<63;function Ur(r,e){let{node:t,offset:n}=r.docView.domFromPos(e,0),o=n<t.childNodes.length?t.childNodes[n]:null,i=n?t.childNodes[n-1]:null;if(ge&&o&&o.contentEditable=="false")return po(o);if(!(o&&o.contentEditable!="false"||i&&i.contentEditable!="false")){if(o)return po(o);if(i)return po(i)}}function po(r){return r.contentEditable="true",ge&&r.draggable&&(r.draggable=!1,r.wasDraggable=!0),r}function Zr(r){r.contentEditable="false",r.wasDraggable&&(r.draggable=!0,r.wasDraggable=null)}function Us(r,e){if(e instanceof R){let t=r.docView.descAt(e.from);t!=r.lastSelectedViewDesc&&(Gr(r),t&&t.selectNode(),r.lastSelectedViewDesc=t)}else Gr(r)}function Gr(r){r.lastSelectedViewDesc&&(r.lastSelectedViewDesc.parent&&r.lastSelectedViewDesc.deselectNode(),r.lastSelectedViewDesc=void 0)}function tr(r,e,t,n){return r.someProp("createSelectionBetween",o=>o(r,e,t))||_.between(e,t,n)}function Yr(r){return!(r.editable&&!r.hasFocus())&&Zs(r)}function Zs(r){let e=r.domSelectionRange();if(!e.anchorNode)return!1;try{return r.dom.contains(e.anchorNode.nodeType==3?e.anchorNode.parentNode:e.anchorNode)&&(r.editable||r.dom.contains(e.focusNode.nodeType==3?e.focusNode.parentNode:e.focusNode))}catch{return!1}}function Fo(r,e){let{$anchor:t,$head:n}=r.selection,o=e>0?t.max(n):t.min(n),i=o.parent.inlineContent?o.depth?r.doc.resolve(e>0?o.after():o.before()):null:o;return i&&V.findFrom(i,e)}function tt(r,e){return r.dispatch(r.state.tr.setSelection(e).scrollIntoView()),!0}function Xr(r,e,t){let n=r.state.selection;if(!(n instanceof _)){if(n instanceof R&&n.node.isInline)return tt(r,new _(e>0?n.$to:n.$from));{let o=Fo(r.state,e);return!!o&&tt(r,o)}}if(t.indexOf("s")>-1){let{$head:o}=n,i=o.textOffset?null:e<0?o.nodeBefore:o.nodeAfter;if(!i||i.isText||!i.isLeaf)return!1;let s=r.state.doc.resolve(o.pos+i.nodeSize*(e<0?-1:1));return tt(r,new _(n.$anchor,s))}if(!n.empty)return!1;if(r.endOfTextblock(e>0?"forward":"backward")){let o=Fo(r.state,e);return!!(o&&o instanceof R)&&tt(r,o)}if(!(Ee&&t.indexOf("m")>-1)){let o,i=n.$head,s=i.textOffset?null:e<0?i.nodeBefore:i.nodeAfter;if(!s||s.isText)return!1;let l=e<0?i.pos-s.nodeSize:i.pos;return!!(s.isAtom||(o=r.docView.descAt(l))&&!o.contentDOM)&&(R.isSelectable(s)?tt(r,new R(e<0?r.state.doc.resolve(i.pos-s.nodeSize):i)):!!vn&&tt(r,new _(r.state.doc.resolve(e<0?l:l+s.nodeSize))))}}function Sn(r){return r.nodeType==3?r.nodeValue.length:r.childNodes.length}function on(r,e){let t=r.pmViewDesc;return t&&t.size==0&&(e<0||r.nextSibling||r.nodeName!="BR")}function Bt(r,e){return e<0?function(t){let n=t.domSelectionRange(),o=n.focusNode,i=n.focusOffset;if(!o)return;let s,l,a=!1;for(ze&&o.nodeType==1&&i<Sn(o)&&on(o.childNodes[i],-1)&&(a=!0);;)if(i>0){if(o.nodeType!=1)break;{let c=o.childNodes[i-1];if(on(c,-1))s=o,l=--i;else{if(c.nodeType!=3)break;o=c,i=o.nodeValue.length}}}else{if(Qr(o))break;{let c=o.previousSibling;for(;c&&on(c,-1);)s=o.parentNode,l=ce(c),c=c.previousSibling;if(c)o=c,i=Sn(o);else{if(o=o.parentNode,o==t.dom)break;i=0}}}a?uo(t,o,i):s&&uo(t,s,l)}(r):function(t){let n=t.domSelectionRange(),o=n.focusNode,i=n.focusOffset;if(!o)return;let s,l,a=Sn(o);for(;;)if(i<a){if(o.nodeType!=1||!on(o.childNodes[i],1))break;s=o,l=++i}else{if(Qr(o))break;{let c=o.nextSibling;for(;c&&on(c,1);)s=c.parentNode,l=ce(c)+1,c=c.nextSibling;if(c)o=c,i=0,a=Sn(o);else{if(o=o.parentNode,o==t.dom)break;i=a=0}}}s&&uo(t,s,l)}(r)}function Qr(r){let e=r.pmViewDesc;return e&&e.node&&e.node.isBlock}function uo(r,e,t){if(e.nodeType!=3){let i,s;(s=function(l,a){for(;l&&a==l.childNodes.length&&!yn(l);)a=ce(l)+1,l=l.parentNode;for(;l&&a<l.childNodes.length;){let c=l.childNodes[a];if(c.nodeType==3)return c;if(c.nodeType==1&&c.contentEditable=="false")break;l=c,a=0}}(e,t))?(e=s,t=0):(i=function(l,a){for(;l&&!a&&!yn(l);)a=ce(l),l=l.parentNode;for(;l&&a;){let c=l.childNodes[a-1];if(c.nodeType==3)return c;if(c.nodeType==1&&c.contentEditable=="false")break;a=(l=c).childNodes.length}}(e,t))&&(e=i,t=i.nodeValue.length)}let n=r.domSelection();if(Zn(n)){let i=document.createRange();i.setEnd(e,t),i.setStart(e,t),n.removeAllRanges(),n.addRange(i)}else n.extend&&n.extend(e,t);r.domObserver.setCurSelection();let{state:o}=r;setTimeout(()=>{r.state==o&&Ye(r)},50)}function ei(r,e){let t=r.state.doc.resolve(e);if(!ue&&!Ca&&t.parent.inlineContent){let n=r.coordsAtPos(e);if(e>t.start()){let o=r.coordsAtPos(e-1),i=(o.top+o.bottom)/2;if(i>n.top&&i<n.bottom&&Math.abs(o.left-n.left)>1)return o.left<n.left?"ltr":"rtl"}if(e<t.end()){let o=r.coordsAtPos(e+1),i=(o.top+o.bottom)/2;if(i>n.top&&i<n.bottom&&Math.abs(o.left-n.left)>1)return o.left>n.left?"ltr":"rtl"}}return getComputedStyle(r.dom).direction=="rtl"?"rtl":"ltr"}function ti(r,e,t){let n=r.state.selection;if(n instanceof _&&!n.empty||t.indexOf("s")>-1||Ee&&t.indexOf("m")>-1)return!1;let{$from:o,$to:i}=n;if(!o.parent.inlineContent||r.endOfTextblock(e<0?"up":"down")){let s=Fo(r.state,e);if(s&&s instanceof R)return tt(r,s)}if(!o.parent.inlineContent){let s=e<0?o:i,l=n instanceof Re?V.near(s,e):V.findFrom(s,e);return!!l&&tt(r,l)}return!1}function ni(r,e){if(!(r.state.selection instanceof _))return!0;let{$head:t,$anchor:n,empty:o}=r.state.selection;if(!t.sameParent(n))return!0;if(!o)return!1;if(r.endOfTextblock(e>0?"forward":"backward"))return!0;let i=!t.textOffset&&(e<0?t.nodeBefore:t.nodeAfter);if(i&&!i.isText){let s=r.state.tr;return e<0?s.delete(t.pos-i.nodeSize,t.pos):s.delete(t.pos,t.pos+i.nodeSize),r.dispatch(s),!0}return!1}function oi(r,e,t){r.domObserver.stop(),e.contentEditable=t,r.domObserver.start()}function $a(r,e){let t=e.keyCode,n=function(o){let i="";return o.ctrlKey&&(i+="c"),o.metaKey&&(i+="m"),o.altKey&&(i+="a"),o.shiftKey&&(i+="s"),i}(e);if(t==8||Ee&&t==72&&n=="c")return ni(r,-1)||Bt(r,-1);if(t==46&&!e.shiftKey||Ee&&t==68&&n=="c")return ni(r,1)||Bt(r,1);if(t==13||t==27)return!0;if(t==37||Ee&&t==66&&n=="c"){let o=t==37?ei(r,r.state.selection.from)=="ltr"?-1:1:-1;return Xr(r,o,n)||Bt(r,o)}if(t==39||Ee&&t==70&&n=="c"){let o=t==39?ei(r,r.state.selection.from)=="ltr"?1:-1:1;return Xr(r,o,n)||Bt(r,o)}return t==38||Ee&&t==80&&n=="c"?ti(r,-1,n)||Bt(r,-1):t==40||Ee&&t==78&&n=="c"?function(o){if(!ge||o.state.selection.$head.parentOffset>0)return!1;let{focusNode:i,focusOffset:s}=o.domSelectionRange();if(i&&i.nodeType==1&&s==0&&i.firstChild&&i.firstChild.contentEditable=="false"){let l=i.firstChild;oi(o,l,"true"),setTimeout(()=>oi(o,l,"false"),20)}return!1}(r)||ti(r,1,n)||Bt(r,1):n==(Ee?"m":"c")&&(t==66||t==73||t==89||t==90)}function Gs(r,e){r.someProp("transformCopied",p=>{e=p(e,r)});let t=[],{content:n,openStart:o,openEnd:i}=e;for(;o>1&&i>1&&n.childCount==1&&n.firstChild.childCount==1;){o--,i--;let p=n.firstChild;t.push(p.type.name,p.attrs!=p.type.defaultAttrs?p.attrs:null),n=p.content}let s=r.someProp("clipboardSerializer")||Lt.fromSchema(r.state.schema),l=nl(),a=l.createElement("div");a.appendChild(s.serializeFragment(n,{document:l}));let c,d=a.firstChild,h=0;for(;d&&d.nodeType==1&&(c=tl[d.nodeName.toLowerCase()]);){for(let p=c.length-1;p>=0;p--){let f=l.createElement(c[p]);for(;a.firstChild;)f.appendChild(a.firstChild);a.appendChild(f),h++}d=a.firstChild}return d&&d.nodeType==1&&d.setAttribute("data-pm-slice",`${o} ${i}${h?` -${h}`:""} ${JSON.stringify(t)}`),{dom:a,text:r.someProp("clipboardTextSerializer",p=>p(e,r))||e.content.textBetween(0,e.content.size,`

`),slice:e}}function Ys(r,e,t,n,o){let i,s,l=o.parent.type.spec.code;if(!t&&!e)return null;let a=e&&(n||l||!t);if(a){if(r.someProp("transformPastedText",p=>{e=p(e,l||n,r)}),l)return e?new T(S.from(r.state.schema.text(e.replace(/\r\n?/g,`
`))),0,0):T.empty;let h=r.someProp("clipboardTextParser",p=>p(e,o,n,r));if(h)s=h;else{let p=o.marks(),{schema:f}=r.state,u=Lt.fromSchema(f);i=document.createElement("div"),e.split(/(?:\r\n?|\n)+/).forEach(m=>{let g=i.appendChild(document.createElement("p"));m&&g.appendChild(u.serializeNode(f.text(m,p)))})}}else r.someProp("transformPastedHTML",h=>{t=h(t,r)}),i=function(h){let p=/^(\s*<meta [^>]*>)*/.exec(h);p&&(h=h.slice(p[0].length));let f,u=nl().createElement("div"),m=/<([a-z][^>\s]+)/i.exec(h);if((f=m&&tl[m[1].toLowerCase()])&&(h=f.map(g=>"<"+g+">").join("")+h+f.map(g=>"</"+g+">").reverse().join("")),u.innerHTML=h,f)for(let g=0;g<f.length;g++)u=u.querySelector(f[g])||u;return u}(t),vn&&function(h){let p=h.querySelectorAll(ue?"span:not([class]):not([style])":"span.Apple-converted-space");for(let f=0;f<p.length;f++){let u=p[f];u.childNodes.length==1&&u.textContent==" "&&u.parentNode&&u.parentNode.replaceChild(h.ownerDocument.createTextNode(" "),u)}}(i);let c=i&&i.querySelector("[data-pm-slice]"),d=c&&/^(\d+) (\d+)(?: -(\d+))? (.*)/.exec(c.getAttribute("data-pm-slice")||"");if(d&&d[3])for(let h=+d[3];h>0;h--){let p=i.firstChild;for(;p&&p.nodeType!=1;)p=p.nextSibling;if(!p)break;i=p}if(s||(s=(r.someProp("clipboardParser")||r.someProp("domParser")||Qt.fromSchema(r.state.schema)).parseSlice(i,{preserveWhitespace:!(!a&&!d),context:o,ruleFromNode:p=>p.nodeName!="BR"||p.nextSibling||!p.parentNode||Da.test(p.parentNode.nodeName)?null:{ignore:!0}})),d)s=function(h,p){if(!h.size)return h;let f,u=h.content.firstChild.type.schema;try{f=JSON.parse(p)}catch{return h}let{content:m,openStart:g,openEnd:C}=h;for(let w=f.length-2;w>=0;w-=2){let v=u.nodes[f[w]];if(!v||v.hasRequiredAttrs())break;m=S.from(v.create(f[w+1],m)),g++,C++}return new T(m,g,C)}(ri(s,+d[1],+d[2]),d[4]);else if(s=T.maxOpen(function(h,p){if(h.childCount<2)return h;for(let f=p.depth;f>=0;f--){let u,m=p.node(f).contentMatchAt(p.index(f)),g=[];if(h.forEach(C=>{if(!g)return;let w,v=m.findWrapping(C.type);if(!v)return g=null;if(w=g.length&&u.length&&Qs(v,u,C,g[g.length-1],0))g[g.length-1]=w;else{g.length&&(g[g.length-1]=el(g[g.length-1],u.length));let b=Xs(C,v);g.push(b),m=m.matchType(b.type),u=v}}),g)return S.from(g)}return h}(s.content,o),!0),s.openStart||s.openEnd){let h=0,p=0;for(let f=s.content.firstChild;h<s.openStart&&!f.type.spec.isolating;h++,f=f.firstChild);for(let f=s.content.lastChild;p<s.openEnd&&!f.type.spec.isolating;p++,f=f.lastChild);s=ri(s,h,p)}return r.someProp("transformPasted",h=>{s=h(s,r)}),s}const Da=/^(a|abbr|acronym|b|cite|code|del|em|i|ins|kbd|label|output|q|ruby|s|samp|span|strong|sub|sup|time|u|tt|var)$/i;function Xs(r,e,t=0){for(let n=e.length-1;n>=t;n--)r=e[n].create(null,S.from(r));return r}function Qs(r,e,t,n,o){if(o<r.length&&o<e.length&&r[o]==e[o]){let i=Qs(r,e,t,n.lastChild,o+1);if(i)return n.copy(n.content.replaceChild(n.childCount-1,i));if(n.contentMatchAt(n.childCount).matchType(o==r.length-1?t.type:r[o+1]))return n.copy(n.content.append(S.from(Xs(t,r,o+1))))}}function el(r,e){if(e==0)return r;let t=r.content.replaceChild(r.childCount-1,el(r.lastChild,e-1)),n=r.contentMatchAt(r.childCount).fillBefore(S.empty,!0);return r.copy(t.append(n))}function Ho(r,e,t,n,o,i){let s=e<0?r.firstChild:r.lastChild,l=s.content;return r.childCount>1&&(i=0),o<n-1&&(l=Ho(l,e,t,n,o+1,i)),o>=t&&(l=e<0?s.contentMatchAt(0).fillBefore(l,i<=o).append(l):l.append(s.contentMatchAt(s.childCount).fillBefore(S.empty,!0))),r.replaceChild(e<0?0:r.childCount-1,s.copy(l))}function ri(r,e,t){return e<r.openStart&&(r=new T(Ho(r.content,-1,e,r.openStart,0,r.openEnd),e,r.openEnd)),t<r.openEnd&&(r=new T(Ho(r.content,1,t,r.openEnd,0,0),r.openStart,t)),r}const tl={thead:["table"],tbody:["table"],tfoot:["table"],caption:["table"],colgroup:["table"],col:["table","colgroup"],tr:["table","tbody"],td:["table","tbody","tr"],th:["table","tbody","tr"]};let ii=null;function nl(){return ii||(ii=document.implementation.createHTMLDocument("title"))}const fe={},me={},Aa={touchstart:!0,touchmove:!0};class La{constructor(){this.shiftKey=!1,this.mouseDown=null,this.lastKeyCode=null,this.lastKeyCodeTime=0,this.lastClick={time:0,x:0,y:0,type:""},this.lastSelectionOrigin=null,this.lastSelectionTime=0,this.lastIOSEnter=0,this.lastIOSEnterFallbackTimeout=-1,this.lastFocus=0,this.lastTouch=0,this.lastAndroidDelete=0,this.composing=!1,this.compositionNode=null,this.composingTimeout=-1,this.compositionNodes=[],this.compositionEndedAt=-2e8,this.compositionID=1,this.compositionPendingChanges=0,this.domChangeCount=0,this.eventHandlers=Object.create(null),this.hideSelectionGuard=null}}function st(r,e){r.input.lastSelectionOrigin=e,r.input.lastSelectionTime=Date.now()}function fo(r){r.someProp("handleDOMEvents",e=>{for(let t in e)r.input.eventHandlers[t]||r.dom.addEventListener(t,r.input.eventHandlers[t]=n=>jo(r,n))})}function jo(r,e){return r.someProp("handleDOMEvents",t=>{let n=t[e.type];return!!n&&(n(r,e)||e.defaultPrevented)})}function Ia(r,e){if(!e.bubbles)return!0;if(e.defaultPrevented)return!1;for(let t=e.target;t!=r.dom;t=t.parentNode)if(!t||t.nodeType==11||t.pmViewDesc&&t.pmViewDesc.stopEvent(e))return!1;return!0}function zn(r){return{left:r.clientX,top:r.clientY}}function nr(r,e,t,n,o){if(n==-1)return!1;let i=r.state.doc.resolve(n);for(let s=i.depth+1;s>0;s--)if(r.someProp(e,l=>s>i.depth?l(r,t,i.nodeAfter,i.before(s),o,!0):l(r,t,i.node(s),i.before(s),o,!1)))return!0;return!1}function Yt(r,e,t){r.focused||r.focus();let n=r.state.tr.setSelection(e);n.setMeta("pointer",!0),r.dispatch(n)}function Pa(r,e,t,n,o){return nr(r,"handleClickOn",e,t,n)||r.someProp("handleClick",i=>i(r,e,n))||(o?function(i,s){if(s==-1)return!1;let l,a,c=i.state.selection;c instanceof R&&(l=c.node);let d=i.state.doc.resolve(s);for(let h=d.depth+1;h>0;h--){let p=h>d.depth?d.nodeAfter:d.node(h);if(R.isSelectable(p)){a=l&&c.$from.depth>0&&h>=c.$from.depth&&d.before(c.$from.depth+1)==c.$from.pos?d.before(c.$from.depth):d.before(h);break}}return a!=null&&(Yt(i,R.create(i.state.doc,a)),!0)}(r,t):function(i,s){if(s==-1)return!1;let l=i.state.doc.resolve(s),a=l.nodeAfter;return!!(a&&a.isAtom&&R.isSelectable(a))&&(Yt(i,new R(l)),!0)}(r,t))}function Ra(r,e,t,n){return nr(r,"handleDoubleClickOn",e,t,n)||r.someProp("handleDoubleClick",o=>o(r,e,n))}function za(r,e,t,n){return nr(r,"handleTripleClickOn",e,t,n)||r.someProp("handleTripleClick",o=>o(r,e,n))||function(o,i,s){if(s.button!=0)return!1;let l=o.state.doc;if(i==-1)return!!l.inlineContent&&(Yt(o,_.create(l,0,l.content.size)),!0);let a=l.resolve(i);for(let c=a.depth+1;c>0;c--){let d=c>a.depth?a.nodeAfter:a.node(c),h=a.before(c);if(d.inlineContent)Yt(o,_.create(l,h+1,h+1+d.content.size));else{if(!R.isSelectable(d))continue;Yt(o,R.create(l,h))}return!0}}(r,t,n)}function qo(r){return Bn(r)}me.keydown=(r,e)=>{let t=e;if(r.input.shiftKey=t.keyCode==16||t.shiftKey,!si(r,t)&&(r.input.lastKeyCode=t.keyCode,r.input.lastKeyCodeTime=Date.now(),!Le||!ue||t.keyCode!=13))if(t.keyCode!=229&&r.domObserver.forceFlush(),!tn||t.keyCode!=13||t.ctrlKey||t.altKey||t.metaKey)r.someProp("handleKeyDown",n=>n(r,t))||$a(r,t)?t.preventDefault():st(r,"key");else{let n=Date.now();r.input.lastIOSEnter=n,r.input.lastIOSEnterFallbackTimeout=setTimeout(()=>{r.input.lastIOSEnter==n&&(r.someProp("handleKeyDown",o=>o(r,gt(13,"Enter"))),r.input.lastIOSEnter=0)},200)}},me.keyup=(r,e)=>{e.keyCode==16&&(r.input.shiftKey=!1)},me.keypress=(r,e)=>{let t=e;if(si(r,t)||!t.charCode||t.ctrlKey&&!t.altKey||Ee&&t.metaKey)return;if(r.someProp("handleKeyPress",o=>o(r,t)))return void t.preventDefault();let n=r.state.selection;if(!(n instanceof _&&n.$from.sameParent(n.$to))){let o=String.fromCharCode(t.charCode);/[\r\n]/.test(o)||r.someProp("handleTextInput",i=>i(r,n.$from.pos,n.$to.pos,o))||r.dispatch(r.state.tr.insertText(o).scrollIntoView()),t.preventDefault()}};const ol=Ee?"metaKey":"ctrlKey";fe.mousedown=(r,e)=>{let t=e;r.input.shiftKey=t.shiftKey;let n=qo(r),o=Date.now(),i="singleClick";o-r.input.lastClick.time<500&&function(l,a){let c=a.x-l.clientX,d=a.y-l.clientY;return c*c+d*d<100}(t,r.input.lastClick)&&!t[ol]&&(r.input.lastClick.type=="singleClick"?i="doubleClick":r.input.lastClick.type=="doubleClick"&&(i="tripleClick")),r.input.lastClick={time:o,x:t.clientX,y:t.clientY,type:i};let s=r.posAtCoords(zn(t));s&&(i=="singleClick"?(r.input.mouseDown&&r.input.mouseDown.done(),r.input.mouseDown=new Ba(r,s,t,!!n)):(i=="doubleClick"?Ra:za)(r,s.pos,s.inside,t)?t.preventDefault():st(r,"pointer"))};class Ba{constructor(e,t,n,o){let i,s;if(this.view=e,this.pos=t,this.event=n,this.flushed=o,this.delayedSelectionSync=!1,this.mightDrag=null,this.startDoc=e.state.doc,this.selectNode=!!n[ol],this.allowDefault=n.shiftKey,t.inside>-1)i=e.state.doc.nodeAt(t.inside),s=t.inside;else{let d=e.state.doc.resolve(t.pos);i=d.parent,s=d.depth?d.before():0}const l=o?null:n.target,a=l?e.docView.nearestDesc(l,!0):null;this.target=a?a.dom:null;let{selection:c}=e.state;(n.button==0&&i.type.spec.draggable&&i.type.spec.selectable!==!1||c instanceof R&&c.from<=s&&c.to>s)&&(this.mightDrag={node:i,pos:s,addAttr:!(!this.target||this.target.draggable),setUneditable:!(!this.target||!ze||this.target.hasAttribute("contentEditable"))}),this.target&&this.mightDrag&&(this.mightDrag.addAttr||this.mightDrag.setUneditable)&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&(this.target.draggable=!0),this.mightDrag.setUneditable&&setTimeout(()=>{this.view.input.mouseDown==this&&this.target.setAttribute("contentEditable","false")},20),this.view.domObserver.start()),e.root.addEventListener("mouseup",this.up=this.up.bind(this)),e.root.addEventListener("mousemove",this.move=this.move.bind(this)),st(e,"pointer")}done(){this.view.root.removeEventListener("mouseup",this.up),this.view.root.removeEventListener("mousemove",this.move),this.mightDrag&&this.target&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&this.target.removeAttribute("draggable"),this.mightDrag.setUneditable&&this.target.removeAttribute("contentEditable"),this.view.domObserver.start()),this.delayedSelectionSync&&setTimeout(()=>Ye(this.view)),this.view.input.mouseDown=null}up(e){if(this.done(),!this.view.dom.contains(e.target))return;let t=this.pos;this.view.state.doc!=this.startDoc&&(t=this.view.posAtCoords(zn(e))),this.updateAllowDefault(e),this.allowDefault||!t?st(this.view,"pointer"):Pa(this.view,t.pos,t.inside,e,this.selectNode)?e.preventDefault():e.button==0&&(this.flushed||ge&&this.mightDrag&&!this.mightDrag.node.isAtom||ue&&!this.view.state.selection.visible&&Math.min(Math.abs(t.pos-this.view.state.selection.from),Math.abs(t.pos-this.view.state.selection.to))<=2)?(Yt(this.view,V.near(this.view.state.doc.resolve(t.pos))),e.preventDefault()):st(this.view,"pointer")}move(e){this.updateAllowDefault(e),st(this.view,"pointer"),e.buttons==0&&this.done()}updateAllowDefault(e){!this.allowDefault&&(Math.abs(this.event.x-e.clientX)>4||Math.abs(this.event.y-e.clientY)>4)&&(this.allowDefault=!0)}}function si(r,e){return!!r.composing||!!(ge&&Math.abs(e.timeStamp-r.input.compositionEndedAt)<500)&&(r.input.compositionEndedAt=-2e8,!0)}fe.touchstart=r=>{r.input.lastTouch=Date.now(),qo(r),st(r,"pointer")},fe.touchmove=r=>{r.input.lastTouch=Date.now(),st(r,"pointer")},fe.contextmenu=r=>qo(r);const _a=Le?5e3:-1;function li(r,e){clearTimeout(r.input.composingTimeout),e>-1&&(r.input.composingTimeout=setTimeout(()=>Bn(r),e))}function rl(r){for(r.composing&&(r.input.composing=!1,r.input.compositionEndedAt=function(){let e=document.createEvent("Event");return e.initEvent("event",!0,!0),e.timeStamp}());r.input.compositionNodes.length>0;)r.input.compositionNodes.pop().markParentsDirty()}function Va(r){let e=r.domSelectionRange();if(!e.focusNode)return null;let t=function(o,i){for(;;){if(o.nodeType==3&&i)return o;if(o.nodeType==1&&i>0){if(o.contentEditable=="false")return null;i=Fe(o=o.childNodes[i-1])}else{if(!o.parentNode||yn(o))return null;i=ce(o),o=o.parentNode}}}(e.focusNode,e.focusOffset),n=function(o,i){for(;;){if(o.nodeType==3&&i<o.nodeValue.length)return o;if(o.nodeType==1&&i<o.childNodes.length){if(o.contentEditable=="false")return null;o=o.childNodes[i],i=0}else{if(!o.parentNode||yn(o))return null;i=ce(o)+1,o=o.parentNode}}}(e.focusNode,e.focusOffset);if(t&&n&&t!=n){let o=n.pmViewDesc;if(!o||!o.isText(n.nodeValue))return n;if(r.input.compositionNode==n){let i=t.pmViewDesc;if(i&&i.isText(t.nodeValue))return n}}return t||n}function Bn(r,e=!1){if(!(Le&&r.domObserver.flushingSoon>=0)){if(r.domObserver.forceFlush(),rl(r),e||r.docView&&r.docView.dirty){let t=er(r);return t&&!t.eq(r.state.selection)?r.dispatch(r.state.tr.setSelection(t)):r.updateState(r.state),!0}return!1}}me.compositionstart=me.compositionupdate=r=>{if(!r.composing){r.domObserver.flush();let{state:e}=r,t=e.selection.$from;if(e.selection.empty&&(e.storedMarks||!t.textOffset&&t.parentOffset&&t.nodeBefore.marks.some(n=>n.type.spec.inclusive===!1)))r.markCursor=r.state.storedMarks||t.marks(),Bn(r,!0),r.markCursor=null;else if(Bn(r),ze&&e.selection.empty&&t.parentOffset&&!t.textOffset&&t.nodeBefore.marks.length){let n=r.domSelectionRange();for(let o=n.focusNode,i=n.focusOffset;o&&o.nodeType==1&&i!=0;){let s=i<0?o.lastChild:o.childNodes[i-1];if(!s)break;if(s.nodeType==3){r.domSelection().collapse(s,s.nodeValue.length);break}o=s,i=-1}}r.input.composing=!0}li(r,_a)},me.compositionend=(r,e)=>{r.composing&&(r.input.composing=!1,r.input.compositionEndedAt=e.timeStamp,r.input.compositionPendingChanges=r.domObserver.pendingRecords().length?r.input.compositionID:0,r.input.compositionNode=null,r.input.compositionPendingChanges&&Promise.resolve().then(()=>r.domObserver.flush()),r.input.compositionID++,li(r,20))};const hn=xe&&at<15||tn&&ya<604;function pn(r,e,t,n,o){let i=Ys(r,e,t,n,r.state.selection.$from);if(r.someProp("handlePaste",a=>a(r,o,i||T.empty)))return!0;if(!i)return!1;let s=function(a){return a.openStart==0&&a.openEnd==0&&a.content.childCount==1?a.content.firstChild:null}(i),l=s?r.state.tr.replaceSelectionWith(s,n):r.state.tr.replaceSelection(i);return r.dispatch(l.scrollIntoView().setMeta("paste",!0).setMeta("uiEvent","paste")),!0}function il(r){let e=r.getData("text/plain")||r.getData("Text");if(e)return e;let t=r.getData("text/uri-list");return t?t.replace(/\r?\n/g," "):""}fe.copy=me.cut=(r,e)=>{let t=e,n=r.state.selection,o=t.type=="cut";if(n.empty)return;let i=hn?null:t.clipboardData,s=n.content(),{dom:l,text:a}=Gs(r,s);i?(t.preventDefault(),i.clearData(),i.setData("text/html",l.innerHTML),i.setData("text/plain",a)):function(c,d){if(!c.dom.parentNode)return;let h=c.dom.parentNode.appendChild(document.createElement("div"));h.appendChild(d),h.style.cssText="position: fixed; left: -10000px; top: 10px";let p=getSelection(),f=document.createRange();f.selectNodeContents(d),c.dom.blur(),p.removeAllRanges(),p.addRange(f),setTimeout(()=>{h.parentNode&&h.parentNode.removeChild(h),c.focus()},50)}(r,l),o&&r.dispatch(r.state.tr.deleteSelection().scrollIntoView().setMeta("uiEvent","cut"))},me.paste=(r,e)=>{let t=e;if(r.composing&&!Le)return;let n=hn?null:t.clipboardData,o=r.input.shiftKey&&r.input.lastKeyCode!=45;n&&pn(r,il(n),n.getData("text/html"),o,t)?t.preventDefault():function(i,s){if(!i.dom.parentNode)return;let l=i.input.shiftKey||i.state.selection.$from.parent.type.spec.code,a=i.dom.parentNode.appendChild(document.createElement(l?"textarea":"div"));l||(a.contentEditable="true"),a.style.cssText="position: fixed; left: -10000px; top: 10px",a.focus();let c=i.input.shiftKey&&i.input.lastKeyCode!=45;setTimeout(()=>{i.focus(),a.parentNode&&a.parentNode.removeChild(a),l?pn(i,a.value,null,c,s):pn(i,a.textContent,a.innerHTML,c,s)},50)}(r,t)};class sl{constructor(e,t,n){this.slice=e,this.move=t,this.node=n}}const ai=Ee?"altKey":"ctrlKey";fe.dragstart=(r,e)=>{let t=e,n=r.input.mouseDown;if(n&&n.done(),!t.dataTransfer)return;let o,i=r.state.selection,s=i.empty?null:r.posAtCoords(zn(t));if(!(s&&s.pos>=i.from&&s.pos<=(i instanceof R?i.to-1:i.to))){if(n&&n.mightDrag)o=R.create(r.state.doc,n.mightDrag.pos);else if(t.target&&t.target.nodeType==1){let h=r.docView.nearestDesc(t.target,!0);h&&h.node.type.spec.draggable&&h!=r.docView&&(o=R.create(r.state.doc,h.posBefore))}}let l=(o||r.state.selection).content(),{dom:a,text:c,slice:d}=Gs(r,l);t.dataTransfer.clearData(),t.dataTransfer.setData(hn?"Text":"text/html",a.innerHTML),t.dataTransfer.effectAllowed="copyMove",hn||t.dataTransfer.setData("text/plain",c),r.dragging=new sl(d,!t[ai],o)},fe.dragend=r=>{let e=r.dragging;window.setTimeout(()=>{r.dragging==e&&(r.dragging=null)},50)},me.dragover=me.dragenter=(r,e)=>e.preventDefault(),me.drop=(r,e)=>{let t=e,n=r.dragging;if(r.dragging=null,!t.dataTransfer)return;let o=r.posAtCoords(zn(t));if(!o)return;let i=r.state.doc.resolve(o.pos),s=n&&n.slice;s?r.someProp("transformPasted",u=>{s=u(s,r)}):s=Ys(r,il(t.dataTransfer),hn?null:t.dataTransfer.getData("text/html"),!1,i);let l=!(!n||t[ai]);if(r.someProp("handleDrop",u=>u(r,t,s||T.empty,l)))return void t.preventDefault();if(!s)return;t.preventDefault();let a=s?function(u,m,g){let C=u.resolve(m);if(!g.content.size)return m;let w=g.content;for(let v=0;v<g.openStart;v++)w=w.firstChild.content;for(let v=1;v<=(g.openStart==0&&g.size?2:1);v++)for(let b=C.depth;b>=0;b--){let k=b==C.depth?0:C.pos<=(C.start(b+1)+C.end(b+1))/2?-1:1,N=C.index(b)+(k>0?1:0),L=C.node(b),O=!1;if(v==1)O=L.canReplace(N,N,w);else{let M=L.contentMatchAt(N).findWrapping(w.firstChild.type);O=M&&L.canReplaceWith(N,N,M[0])}if(O)return k==0?C.pos:k<0?C.before(b+1):C.after(b+1)}return null}(r.state.doc,i.pos,s):i.pos;a==null&&(a=i.pos);let c=r.state.tr;if(l){let{node:u}=n;u?u.replace(c):c.deleteSelection()}let d=c.mapping.map(a),h=s.openStart==0&&s.openEnd==0&&s.content.childCount==1,p=c.doc;if(h?c.replaceRangeWith(d,d,s.content.firstChild):c.replaceRange(d,d,s),c.doc.eq(p))return;let f=c.doc.resolve(d);if(h&&R.isSelectable(s.content.firstChild)&&f.nodeAfter&&f.nodeAfter.sameMarkup(s.content.firstChild))c.setSelection(new R(f));else{let u=c.mapping.map(a);c.mapping.maps[c.mapping.maps.length-1].forEach((m,g,C,w)=>u=w),c.setSelection(tr(r,f,c.doc.resolve(u)))}r.focus(),r.dispatch(c.setMeta("uiEvent","drop"))},fe.focus=r=>{r.input.lastFocus=Date.now(),r.focused||(r.domObserver.stop(),r.dom.classList.add("ProseMirror-focused"),r.domObserver.start(),r.focused=!0,setTimeout(()=>{r.docView&&r.hasFocus()&&!r.domObserver.currentSelection.eq(r.domSelectionRange())&&Ye(r)},20))},fe.blur=(r,e)=>{let t=e;r.focused&&(r.domObserver.stop(),r.dom.classList.remove("ProseMirror-focused"),r.domObserver.start(),t.relatedTarget&&r.dom.contains(t.relatedTarget)&&r.domObserver.currentSelection.clear(),r.focused=!1)},fe.beforeinput=(r,e)=>{if(ue&&Le&&e.inputType=="deleteContentBackward"){r.domObserver.flushSoon();let{domChangeCount:t}=r.input;setTimeout(()=>{if(r.input.domChangeCount!=t||(r.dom.blur(),r.focus(),r.someProp("handleKeyDown",o=>o(r,gt(8,"Backspace")))))return;let{$cursor:n}=r.state.selection;n&&n.pos>0&&r.dispatch(r.state.tr.delete(n.pos-1,n.pos).scrollIntoView())},50)}};for(let r in me)fe[r]=me[r];function wn(r,e){if(r==e)return!0;for(let t in r)if(r[t]!==e[t])return!1;for(let t in e)if(!(t in r))return!1;return!0}class _n{constructor(e,t){this.toDOM=e,this.spec=t||Et,this.side=this.spec.side||0}map(e,t,n,o){let{pos:i,deleted:s}=e.mapResult(t.from+o,this.side<0?-1:1);return s?null:new Se(i-n,i-n,this)}valid(){return!0}eq(e){return this==e||e instanceof _n&&(this.spec.key&&this.spec.key==e.spec.key||this.toDOM==e.toDOM&&wn(this.spec,e.spec))}destroy(e){this.spec.destroy&&this.spec.destroy(e)}}class dt{constructor(e,t){this.attrs=e,this.spec=t||Et}map(e,t,n,o){let i=e.map(t.from+o,this.spec.inclusiveStart?-1:1)-n,s=e.map(t.to+o,this.spec.inclusiveEnd?1:-1)-n;return i>=s?null:new Se(i,s,this)}valid(e,t){return t.from<t.to}eq(e){return this==e||e instanceof dt&&wn(this.attrs,e.attrs)&&wn(this.spec,e.spec)}static is(e){return e.type instanceof dt}destroy(){}}class or{constructor(e,t){this.attrs=e,this.spec=t||Et}map(e,t,n,o){let i=e.mapResult(t.from+o,1);if(i.deleted)return null;let s=e.mapResult(t.to+o,-1);return s.deleted||s.pos<=i.pos?null:new Se(i.pos-n,s.pos-n,this)}valid(e,t){let n,{index:o,offset:i}=e.content.findIndex(t.from);return i==t.from&&!(n=e.child(o)).isText&&i+n.nodeSize==t.to}eq(e){return this==e||e instanceof or&&wn(this.attrs,e.attrs)&&wn(this.spec,e.spec)}destroy(){}}class Se{constructor(e,t,n){this.from=e,this.to=t,this.type=n}copy(e,t){return new Se(e,t,this.type)}eq(e,t=0){return this.type.eq(e.type)&&this.from+t==e.from&&this.to+t==e.to}map(e,t,n){return this.type.map(e,this,t,n)}static widget(e,t,n){return new Se(e,e,new _n(t,n))}static inline(e,t,n,o){return new Se(e,t,new dt(n,o))}static node(e,t,n,o){return new Se(e,t,new or(n,o))}get spec(){return this.type.spec}get inline(){return this.type instanceof dt}get widget(){return this.type instanceof _n}}const Ft=[],Et={};class te{constructor(e,t){this.local=e.length?e:Ft,this.children=t.length?t:Ft}static create(e,t){return t.length?Nn(t,e,0,Et):de}find(e,t,n){let o=[];return this.findInner(e??0,t??1e9,o,0,n),o}findInner(e,t,n,o,i){for(let s=0;s<this.local.length;s++){let l=this.local[s];l.from<=t&&l.to>=e&&(!i||i(l.spec))&&n.push(l.copy(l.from+o,l.to+o))}for(let s=0;s<this.children.length;s+=3)if(this.children[s]<t&&this.children[s+1]>e){let l=this.children[s]+1;this.children[s+2].findInner(e-l,t-l,n,o+l,i)}}map(e,t,n){return this==de||e.maps.length==0?this:this.mapInner(e,t,0,0,n||Et)}mapInner(e,t,n,o,i){let s;for(let l=0;l<this.local.length;l++){let a=this.local[l].map(e,n,o);a&&a.type.valid(t,a)?(s||(s=[])).push(a):i.onRemove&&i.onRemove(this.local[l].spec)}return this.children.length?function(l,a,c,d,h,p,f){let u=l.slice();for(let g=0,C=p;g<c.maps.length;g++){let w=0;c.maps[g].forEach((v,b,k,N)=>{let L=N-k-(b-v);for(let O=0;O<u.length;O+=3){let M=u[O+1];if(M<0||v>M+C-w)continue;let A=u[O]+C-w;b>=A?u[O+1]=v<=A?-2:-1:v>=C&&L&&(u[O]+=L,u[O+1]+=L)}w+=L}),C=c.maps[g].map(C,-1)}let m=!1;for(let g=0;g<u.length;g+=3)if(u[g+1]<0){if(u[g+1]==-2){m=!0,u[g+1]=-1;continue}let C=c.map(l[g]+p),w=C-h;if(w<0||w>=d.content.size){m=!0;continue}let v=c.map(l[g+1]+p,-1)-h,{index:b,offset:k}=d.content.findIndex(w),N=d.maybeChild(b);if(N&&k==w&&k+N.nodeSize==v){let L=u[g+2].mapInner(c,N,C+1,l[g]+p+1,f);L!=de?(u[g]=w,u[g+1]=v,u[g+2]=L):(u[g+1]=-2,m=!0)}else m=!0}if(m){let g=function(w,v,b,k,N,L,O){function M(A,j){for(let B=0;B<A.local.length;B++){let ee=A.local[B].map(k,N,j);ee?b.push(ee):O.onRemove&&O.onRemove(A.local[B].spec)}for(let B=0;B<A.children.length;B+=3)M(A.children[B+2],A.children[B]+j+1)}for(let A=0;A<w.length;A+=3)w[A+1]==-1&&M(w[A+2],v[A]+L+1);return b}(u,l,a,c,h,p,f),C=Nn(g,d,0,f);a=C.local;for(let w=0;w<u.length;w+=3)u[w+1]<0&&(u.splice(w,3),w-=3);for(let w=0,v=0;w<C.children.length;w+=3){let b=C.children[w];for(;v<u.length&&u[v]<b;)v+=3;u.splice(v,0,C.children[w],C.children[w+1],C.children[w+2])}}return new te(a.sort(vt),u)}(this.children,s||[],e,t,n,o,i):s?new te(s.sort(vt),Ft):de}add(e,t){return t.length?this==de?te.create(e,t):this.addInner(e,t,0):this}addInner(e,t,n){let o,i=0;e.forEach((l,a)=>{let c,d=a+n;if(c=al(t,l,d)){for(o||(o=this.children.slice());i<o.length&&o[i]<a;)i+=3;o[i]==a?o[i+2]=o[i+2].addInner(l,c,d+1):o.splice(i,0,a,a+l.nodeSize,Nn(c,l,d+1,Et)),i+=3}});let s=ll(i?cl(t):t,-n);for(let l=0;l<s.length;l++)s[l].type.valid(e,s[l])||s.splice(l--,1);return new te(s.length?this.local.concat(s).sort(vt):this.local,o||this.children)}remove(e){return e.length==0||this==de?this:this.removeInner(e,0)}removeInner(e,t){let n=this.children,o=this.local;for(let i=0;i<n.length;i+=3){let s,l=n[i]+t,a=n[i+1]+t;for(let d,h=0;h<e.length;h++)(d=e[h])&&d.from>l&&d.to<a&&(e[h]=null,(s||(s=[])).push(d));if(!s)continue;n==this.children&&(n=this.children.slice());let c=n[i+2].removeInner(s,l+1);c!=de?n[i+2]=c:(n.splice(i,3),i-=3)}if(o.length){for(let i,s=0;s<e.length;s++)if(i=e[s])for(let l=0;l<o.length;l++)o[l].eq(i,t)&&(o==this.local&&(o=this.local.slice()),o.splice(l--,1))}return n==this.children&&o==this.local?this:o.length||n.length?new te(o,n):de}forChild(e,t){if(this==de)return this;if(t.isLeaf)return te.empty;let n,o;for(let l=0;l<this.children.length;l+=3)if(this.children[l]>=e){this.children[l]==e&&(n=this.children[l+2]);break}let i=e+1,s=i+t.content.size;for(let l=0;l<this.local.length;l++){let a=this.local[l];if(a.from<s&&a.to>i&&a.type instanceof dt){let c=Math.max(i,a.from)-i,d=Math.min(s,a.to)-i;c<d&&(o||(o=[])).push(a.copy(c,d))}}if(o){let l=new te(o.sort(vt),Ft);return n?new ot([l,n]):l}return n||de}eq(e){if(this==e)return!0;if(!(e instanceof te)||this.local.length!=e.local.length||this.children.length!=e.children.length)return!1;for(let t=0;t<this.local.length;t++)if(!this.local[t].eq(e.local[t]))return!1;for(let t=0;t<this.children.length;t+=3)if(this.children[t]!=e.children[t]||this.children[t+1]!=e.children[t+1]||!this.children[t+2].eq(e.children[t+2]))return!1;return!0}locals(e){return rr(this.localsInner(e))}localsInner(e){if(this==de)return Ft;if(e.inlineContent||!this.local.some(dt.is))return this.local;let t=[];for(let n=0;n<this.local.length;n++)this.local[n].type instanceof dt||t.push(this.local[n]);return t}}te.empty=new te([],[]),te.removeOverlap=rr;const de=te.empty;class ot{constructor(e){this.members=e}map(e,t){const n=this.members.map(o=>o.map(e,t,Et));return ot.from(n)}forChild(e,t){if(t.isLeaf)return te.empty;let n=[];for(let o=0;o<this.members.length;o++){let i=this.members[o].forChild(e,t);i!=de&&(i instanceof ot?n=n.concat(i.members):n.push(i))}return ot.from(n)}eq(e){if(!(e instanceof ot)||e.members.length!=this.members.length)return!1;for(let t=0;t<this.members.length;t++)if(!this.members[t].eq(e.members[t]))return!1;return!0}locals(e){let t,n=!0;for(let o=0;o<this.members.length;o++){let i=this.members[o].localsInner(e);if(i.length)if(t){n&&(t=t.slice(),n=!1);for(let s=0;s<i.length;s++)t.push(i[s])}else t=i}return t?rr(n?t:t.sort(vt)):Ft}static from(e){switch(e.length){case 0:return de;case 1:return e[0];default:return new ot(e.every(t=>t instanceof te)?e:e.reduce((t,n)=>t.concat(n instanceof te?n:n.members),[]))}}}function ll(r,e){if(!e||!r.length)return r;let t=[];for(let n=0;n<r.length;n++){let o=r[n];t.push(new Se(o.from+e,o.to+e,o.type))}return t}function al(r,e,t){if(e.isLeaf)return null;let n=t+e.nodeSize,o=null;for(let i,s=0;s<r.length;s++)(i=r[s])&&i.from>t&&i.to<n&&((o||(o=[])).push(i),r[s]=null);return o}function cl(r){let e=[];for(let t=0;t<r.length;t++)r[t]!=null&&e.push(r[t]);return e}function Nn(r,e,t,n){let o=[],i=!1;e.forEach((l,a)=>{let c=al(r,l,a+t);if(c){i=!0;let d=Nn(c,l,t+a+1,n);d!=de&&o.push(a,a+l.nodeSize,d)}});let s=ll(i?cl(r):r,-t).sort(vt);for(let l=0;l<s.length;l++)s[l].type.valid(e,s[l])||(n.onRemove&&n.onRemove(s[l].spec),s.splice(l--,1));return s.length||o.length?new te(s,o):de}function vt(r,e){return r.from-e.from||r.to-e.to}function rr(r){let e=r;for(let t=0;t<e.length-1;t++){let n=e[t];if(n.from!=n.to)for(let o=t+1;o<e.length;o++){let i=e[o];if(i.from!=n.from){i.from<n.to&&(e==r&&(e=r.slice()),e[t]=n.copy(n.from,i.from),ci(e,o,n.copy(i.from,n.to)));break}i.to!=n.to&&(e==r&&(e=r.slice()),e[o]=i.copy(i.from,n.to),ci(e,o+1,i.copy(n.to,i.to)))}}return e}function ci(r,e,t){for(;e<r.length&&vt(t,r[e])>0;)e++;r.splice(e,0,t)}function mo(r){let e=[];return r.someProp("decorations",t=>{let n=t(r.state);n&&n!=de&&e.push(n)}),r.cursorWrapper&&e.push(te.create(r.state.doc,[r.cursorWrapper.deco])),ot.from(e)}const Fa={childList:!0,characterData:!0,characterDataOldValue:!0,attributes:!0,attributeOldValue:!0,subtree:!0},Ha=xe&&at<=11;class ja{constructor(){this.anchorNode=null,this.anchorOffset=0,this.focusNode=null,this.focusOffset=0}set(e){this.anchorNode=e.anchorNode,this.anchorOffset=e.anchorOffset,this.focusNode=e.focusNode,this.focusOffset=e.focusOffset}clear(){this.anchorNode=this.focusNode=null}eq(e){return e.anchorNode==this.anchorNode&&e.anchorOffset==this.anchorOffset&&e.focusNode==this.focusNode&&e.focusOffset==this.focusOffset}}class qa{constructor(e,t){this.view=e,this.handleDOMChange=t,this.queue=[],this.flushingSoon=-1,this.observer=null,this.currentSelection=new ja,this.onCharData=null,this.suppressingSelectionUpdates=!1,this.observer=window.MutationObserver&&new window.MutationObserver(n=>{for(let o=0;o<n.length;o++)this.queue.push(n[o]);xe&&at<=11&&n.some(o=>o.type=="childList"&&o.removedNodes.length||o.type=="characterData"&&o.oldValue.length>o.target.nodeValue.length)?this.flushSoon():this.flush()}),Ha&&(this.onCharData=n=>{this.queue.push({target:n.target,type:"characterData",oldValue:n.prevValue}),this.flushSoon()}),this.onSelectionChange=this.onSelectionChange.bind(this)}flushSoon(){this.flushingSoon<0&&(this.flushingSoon=window.setTimeout(()=>{this.flushingSoon=-1,this.flush()},20))}forceFlush(){this.flushingSoon>-1&&(window.clearTimeout(this.flushingSoon),this.flushingSoon=-1,this.flush())}start(){this.observer&&(this.observer.takeRecords(),this.observer.observe(this.view.dom,Fa)),this.onCharData&&this.view.dom.addEventListener("DOMCharacterDataModified",this.onCharData),this.connectSelection()}stop(){if(this.observer){let e=this.observer.takeRecords();if(e.length){for(let t=0;t<e.length;t++)this.queue.push(e[t]);window.setTimeout(()=>this.flush(),20)}this.observer.disconnect()}this.onCharData&&this.view.dom.removeEventListener("DOMCharacterDataModified",this.onCharData),this.disconnectSelection()}connectSelection(){this.view.dom.ownerDocument.addEventListener("selectionchange",this.onSelectionChange)}disconnectSelection(){this.view.dom.ownerDocument.removeEventListener("selectionchange",this.onSelectionChange)}suppressSelectionUpdates(){this.suppressingSelectionUpdates=!0,setTimeout(()=>this.suppressingSelectionUpdates=!1,50)}onSelectionChange(){if(Yr(this.view)){if(this.suppressingSelectionUpdates)return Ye(this.view);if(xe&&at<=11&&!this.view.state.selection.empty){let e=this.view.domSelectionRange();if(e.focusNode&&Dt(e.focusNode,e.focusOffset,e.anchorNode,e.anchorOffset))return this.flushSoon()}this.flush()}}setCurSelection(){this.currentSelection.set(this.view.domSelectionRange())}ignoreSelectionChange(e){if(!e.focusNode)return!0;let t,n=new Set;for(let i=e.focusNode;i;i=Cn(i))n.add(i);for(let i=e.anchorNode;i;i=Cn(i))if(n.has(i)){t=i;break}let o=t&&this.view.docView.nearestDesc(t);return o&&o.ignoreMutation({type:"selection",target:t.nodeType==3?t.parentNode:t})?(this.setCurSelection(),!0):void 0}pendingRecords(){if(this.observer)for(let e of this.observer.takeRecords())this.queue.push(e);return this.queue}flush(){let{view:e}=this;if(!e.docView||this.flushingSoon>-1)return;let t=this.pendingRecords();t.length&&(this.queue=[]);let n=e.domSelectionRange(),o=!this.suppressingSelectionUpdates&&!this.currentSelection.eq(n)&&Yr(e)&&!this.ignoreSelectionChange(n),i=-1,s=-1,l=!1,a=[];if(e.editable)for(let d=0;d<t.length;d++){let h=this.registerMutation(t[d],a);h&&(i=i<0?h.from:Math.min(h.from,i),s=s<0?h.to:Math.max(h.to,s),h.typeOver&&(l=!0))}if(ze&&a.length>1){let d=a.filter(h=>h.nodeName=="BR");if(d.length==2){let h=d[0],p=d[1];h.parentNode&&h.parentNode.parentNode==p.parentNode?p.remove():h.remove()}}let c=null;i<0&&o&&e.input.lastFocus>Date.now()-200&&Math.max(e.input.lastTouch,e.input.lastClick.time)<Date.now()-300&&Zn(n)&&(c=er(e))&&c.eq(V.near(e.state.doc.resolve(0),1))?(e.input.lastFocus=0,Ye(e),this.currentSelection.set(n),e.scrollToSelection()):(i>-1||o)&&(i>-1&&(e.docView.markDirty(i,s),function(d){if(!di.has(d)&&(di.set(d,null),["normal","nowrap","pre-line"].indexOf(getComputedStyle(d.dom).whiteSpace)!==-1)){if(d.requiresGeckoHackNode=ze,hi)return;console.warn("ProseMirror expects the CSS white-space property to be set, preferably to 'pre-wrap'. It is recommended to load style/prosemirror.css from the prosemirror-view package."),hi=!0}}(e)),this.handleDOMChange(i,s,l,a),e.docView&&e.docView.dirty?e.updateState(e.state):this.currentSelection.eq(n)||Ye(e),this.currentSelection.set(n))}registerMutation(e,t){if(t.indexOf(e.target)>-1)return null;let n=this.view.docView.nearestDesc(e.target);if(e.type=="attributes"&&(n==this.view.docView||e.attributeName=="contenteditable"||e.attributeName=="style"&&!e.oldValue&&!e.target.getAttribute("style"))||!n||n.ignoreMutation(e))return null;if(e.type=="childList"){for(let c=0;c<e.addedNodes.length;c++)t.push(e.addedNodes[c]);if(n.contentDOM&&n.contentDOM!=n.dom&&!n.contentDOM.contains(e.target))return{from:n.posBefore,to:n.posAfter};let o=e.previousSibling,i=e.nextSibling;if(xe&&at<=11&&e.addedNodes.length)for(let c=0;c<e.addedNodes.length;c++){let{previousSibling:d,nextSibling:h}=e.addedNodes[c];(!d||Array.prototype.indexOf.call(e.addedNodes,d)<0)&&(o=d),(!h||Array.prototype.indexOf.call(e.addedNodes,h)<0)&&(i=h)}let s=o&&o.parentNode==e.target?ce(o)+1:0,l=n.localPosFromDOM(e.target,s,-1),a=i&&i.parentNode==e.target?ce(i):e.target.childNodes.length;return{from:l,to:n.localPosFromDOM(e.target,a,1)}}return e.type=="attributes"?{from:n.posAtStart-n.border,to:n.posAtEnd+n.border}:{from:n.posAtStart,to:n.posAtEnd,typeOver:e.target.nodeValue==e.oldValue}}}let di=new WeakMap,hi=!1;function pi(r,e){let t=e.startContainer,n=e.startOffset,o=e.endContainer,i=e.endOffset,s=r.domAtPos(r.state.selection.anchor);return Dt(s.node,s.offset,o,i)&&([t,n,o,i]=[o,i,t,n]),{anchorNode:t,anchorOffset:n,focusNode:o,focusOffset:i}}function Ja(r){let e=r.pmViewDesc;if(e)return e.parseRule();if(r.nodeName=="BR"&&r.parentNode){if(ge&&/^(ul|ol)$/i.test(r.parentNode.nodeName)){let t=document.createElement("div");return t.appendChild(document.createElement("li")),{skip:t}}if(r.parentNode.lastChild==r||ge&&/^(tr|table)$/i.test(r.parentNode.nodeName))return{ignore:!0}}else if(r.nodeName=="IMG"&&r.getAttribute("mark-placeholder"))return{ignore:!0};return null}const Ka=/^(a|abbr|acronym|b|bd[io]|big|br|button|cite|code|data(list)?|del|dfn|em|i|ins|kbd|label|map|mark|meter|output|q|ruby|s|samp|small|span|strong|su[bp]|time|u|tt|var)$/i;function Wa(r,e,t,n,o){let i=r.input.compositionPendingChanges||(r.composing?r.input.compositionID:0);if(r.input.compositionPendingChanges=0,e<0){let M=r.input.lastSelectionTime>Date.now()-50?r.input.lastSelectionOrigin:null,A=er(r,M);if(A&&!r.state.selection.eq(A)){if(ue&&Le&&r.input.lastKeyCode===13&&Date.now()-100<r.input.lastKeyCodeTime&&r.someProp("handleKeyDown",B=>B(r,gt(13,"Enter"))))return;let j=r.state.tr.setSelection(A);M=="pointer"?j.setMeta("pointer",!0):M=="key"&&j.scrollIntoView(),i&&j.setMeta("composition",i),r.dispatch(j)}return}let s=r.state.doc.resolve(e),l=s.sharedDepth(t);e=s.before(l+1),t=r.state.doc.resolve(t).after(l+1);let a,c,d=r.state.selection,h=function(M,A,j){let B,{node:ee,fromOffset:P,toOffset:q,from:K,to:G}=M.docView.parseRange(A,j),ke=M.domSelectionRange(),H=ke.anchorNode;if(H&&M.dom.contains(H.nodeType==1?H:H.parentNode)&&(B=[{node:H,offset:ke.anchorOffset}],Zn(ke)||B.push({node:ke.focusNode,offset:ke.focusOffset})),ue&&M.input.lastKeyCode===8)for(let mt=q;mt>P;mt--){let Rt=ee.childNodes[mt-1],eo=Rt.pmViewDesc;if(Rt.nodeName=="BR"&&!eo){q=mt;break}if(!eo||eo.size)break}let It=M.state.doc,Pt=M.someProp("domParser")||Qt.fromSchema(M.state.schema),ft=It.resolve(K),bn=null,Sl=Pt.parse(ee,{topNode:ft.parent,topMatch:ft.parent.contentMatchAt(ft.index()),topOpen:!0,from:P,to:q,preserveWhitespace:ft.parent.type.whitespace!="pre"||"full",findPositions:B,ruleFromNode:Ja,context:ft});if(B&&B[0].pos!=null){let mt=B[0].pos,Rt=B[1]&&B[1].pos;Rt==null&&(Rt=mt),bn={anchor:mt+K,head:Rt+K}}return{doc:Sl,sel:bn,from:K,to:G}}(r,e,t),p=r.state.doc,f=p.slice(h.from,h.to);r.input.lastKeyCode===8&&Date.now()-100<r.input.lastKeyCodeTime?(a=r.state.selection.to,c="end"):(a=r.state.selection.from,c="start"),r.input.lastKeyCode=null;let u=function(M,A,j,B,ee){let P=M.findDiffStart(A,j);if(P==null)return null;let{a:q,b:K}=M.findDiffEnd(A,j+M.size,j+A.size);if(ee=="end"&&(B-=q+Math.max(0,P-Math.min(q,K))-P),q<P&&M.size<A.size){let G=B<=P&&B>=q?P-B:0;P-=G,P&&P<A.size&&fi(A.textBetween(P-1,P+1))&&(P+=G?1:-1),K=P+(K-q),q=P}else if(K<P){let G=B<=P&&B>=K?P-B:0;P-=G,P&&P<M.size&&fi(M.textBetween(P-1,P+1))&&(P+=G?1:-1),q=P+(q-K),K=P}return{start:P,endA:q,endB:K}}(f.content,h.doc.content,h.from,a,c);if((tn&&r.input.lastIOSEnter>Date.now()-225||Le)&&o.some(M=>M.nodeType==1&&!Ka.test(M.nodeName))&&(!u||u.endA>=u.endB)&&r.someProp("handleKeyDown",M=>M(r,gt(13,"Enter"))))return void(r.input.lastIOSEnter=0);if(!u){if(!(n&&d instanceof _&&!d.empty&&d.$head.sameParent(d.$anchor))||r.composing||h.sel&&h.sel.anchor!=h.sel.head){if(h.sel){let M=ui(r,r.state.doc,h.sel);if(M&&!M.eq(r.state.selection)){let A=r.state.tr.setSelection(M);i&&A.setMeta("composition",i),r.dispatch(A)}}return}u={start:d.from,endA:d.to,endB:d.to}}r.input.domChangeCount++,r.state.selection.from<r.state.selection.to&&u.start==u.endB&&r.state.selection instanceof _&&(u.start>r.state.selection.from&&u.start<=r.state.selection.from+2&&r.state.selection.from>=h.from?u.start=r.state.selection.from:u.endA<r.state.selection.to&&u.endA>=r.state.selection.to-2&&r.state.selection.to<=h.to&&(u.endB+=r.state.selection.to-u.endA,u.endA=r.state.selection.to)),xe&&at<=11&&u.endB==u.start+1&&u.endA==u.start&&u.start>h.from&&h.doc.textBetween(u.start-h.from-1,u.start-h.from+1)=="  "&&(u.start--,u.endA--,u.endB--);let m,g=h.doc.resolveNoCache(u.start-h.from),C=h.doc.resolveNoCache(u.endB-h.from),w=p.resolve(u.start),v=g.sameParent(C)&&g.parent.inlineContent&&w.end()>=u.endA;if((tn&&r.input.lastIOSEnter>Date.now()-225&&(!v||o.some(M=>M.nodeName=="DIV"||M.nodeName=="P"))||!v&&g.pos<h.doc.content.size&&!g.sameParent(C)&&(m=V.findFrom(h.doc.resolve(g.pos+1),1,!0))&&m.head==C.pos)&&r.someProp("handleKeyDown",M=>M(r,gt(13,"Enter"))))return void(r.input.lastIOSEnter=0);if(r.state.selection.anchor>u.start&&function(M,A,j,B,ee){if(j-A<=ee.pos-B.pos||go(B,!0,!1)<ee.pos)return!1;let P=M.resolve(A);if(!B.parent.isTextblock){let K=P.nodeAfter;return K!=null&&j==A+K.nodeSize}if(P.parentOffset<P.parent.content.size||!P.parent.isTextblock)return!1;let q=M.resolve(go(P,!0,!0));return!(!q.parent.isTextblock||q.pos>j||go(q,!0,!1)<j)&&B.parent.content.cut(B.parentOffset).eq(q.parent.content)}(p,u.start,u.endA,g,C)&&r.someProp("handleKeyDown",M=>M(r,gt(8,"Backspace"))))return void(Le&&ue&&r.domObserver.suppressSelectionUpdates());ue&&Le&&u.endB==u.start&&(r.input.lastAndroidDelete=Date.now()),Le&&!v&&g.start()!=C.start()&&C.parentOffset==0&&g.depth==C.depth&&h.sel&&h.sel.anchor==h.sel.head&&h.sel.head==u.endA&&(u.endB-=2,C=h.doc.resolveNoCache(u.endB-h.from),setTimeout(()=>{r.someProp("handleKeyDown",function(M){return M(r,gt(13,"Enter"))})},20));let b,k,N,L=u.start,O=u.endA;if(v){if(g.pos==C.pos)xe&&at<=11&&g.parentOffset==0&&(r.domObserver.suppressSelectionUpdates(),setTimeout(()=>Ye(r),20)),b=r.state.tr.delete(L,O),k=p.resolve(u.start).marksAcross(p.resolve(u.endA));else if(u.endA==u.endB&&(N=function(M,A){let j,B,ee,P=M.firstChild.marks,q=A.firstChild.marks,K=P,G=q;for(let H=0;H<q.length;H++)K=q[H].removeFromSet(K);for(let H=0;H<P.length;H++)G=P[H].removeFromSet(G);if(K.length==1&&G.length==0)B=K[0],j="add",ee=H=>H.mark(B.addToSet(H.marks));else{if(K.length!=0||G.length!=1)return null;B=G[0],j="remove",ee=H=>H.mark(B.removeFromSet(H.marks))}let ke=[];for(let H=0;H<A.childCount;H++)ke.push(ee(A.child(H)));if(S.from(ke).eq(M))return{mark:B,type:j}}(g.parent.content.cut(g.parentOffset,C.parentOffset),w.parent.content.cut(w.parentOffset,u.endA-w.start()))))b=r.state.tr,N.type=="add"?b.addMark(L,O,N.mark):b.removeMark(L,O,N.mark);else if(g.parent.child(g.index()).isText&&g.index()==C.index()-(C.textOffset?0:1)){let M=g.parent.textBetween(g.parentOffset,C.parentOffset);if(r.someProp("handleTextInput",A=>A(r,L,O,M)))return;b=r.state.tr.insertText(M,L,O)}}if(b||(b=r.state.tr.replace(L,O,h.doc.slice(u.start-h.from,u.endB-h.from))),h.sel){let M=ui(r,b.doc,h.sel);M&&!(ue&&Le&&r.composing&&M.empty&&(u.start!=u.endB||r.input.lastAndroidDelete<Date.now()-100)&&(M.head==L||M.head==b.mapping.map(O)-1)||xe&&M.empty&&M.head==L)&&b.setSelection(M)}k&&b.ensureMarks(k),i&&b.setMeta("composition",i),r.dispatch(b.scrollIntoView())}function ui(r,e,t){return Math.max(t.anchor,t.head)>e.content.size?null:tr(r,e.resolve(t.anchor),e.resolve(t.head))}function go(r,e,t){let n=r.depth,o=e?r.end():r.pos;for(;n>0&&(e||r.indexAfter(n)==r.node(n).childCount);)n--,o++,e=!1;if(t){let i=r.node(n).maybeChild(r.indexAfter(n));for(;i&&!i.isLeaf;)i=i.firstChild,o++}return o}function fi(r){if(r.length!=2)return!1;let e=r.charCodeAt(0),t=r.charCodeAt(1);return e>=56320&&e<=57343&&t>=55296&&t<=56319}class Ua{constructor(e,t){this._root=null,this.focused=!1,this.trackWrites=null,this.mounted=!1,this.markCursor=null,this.cursorWrapper=null,this.lastSelectedViewDesc=void 0,this.input=new La,this.prevDirectPlugins=[],this.pluginViews=[],this.requiresGeckoHackNode=!1,this.dragging=null,this._props=t,this.state=t.state,this.directPlugins=t.plugins||[],this.directPlugins.forEach(wi),this.dispatch=this.dispatch.bind(this),this.dom=e&&e.mount||document.createElement("div"),e&&(e.appendChild?e.appendChild(this.dom):typeof e=="function"?e(this.dom):e.mount&&(this.mounted=!0)),this.editable=Ci(this),gi(this),this.nodeViews=yi(this),this.docView=qr(this.state.doc,mi(this),mo(this),this.dom,this),this.domObserver=new qa(this,(n,o,i,s)=>Wa(this,n,o,i,s)),this.domObserver.start(),function(n){for(let o in fe){let i=fe[o];n.dom.addEventListener(o,n.input.eventHandlers[o]=s=>{!Ia(n,s)||jo(n,s)||!n.editable&&s.type in me||i(n,s)},Aa[o]?{passive:!0}:void 0)}ge&&n.dom.addEventListener("input",()=>null),fo(n)}(this),this.updatePluginViews()}get composing(){return this.input.composing}get props(){if(this._props.state!=this.state){let e=this._props;this._props={};for(let t in e)this._props[t]=e[t];this._props.state=this.state}return this._props}update(e){e.handleDOMEvents!=this._props.handleDOMEvents&&fo(this);let t=this._props;this._props=e,e.plugins&&(e.plugins.forEach(wi),this.directPlugins=e.plugins),this.updateStateInner(e.state,t)}setProps(e){let t={};for(let n in this._props)t[n]=this._props[n];t.state=this.state;for(let n in e)t[n]=e[n];this.update(t)}updateState(e){this.updateStateInner(e,this._props)}updateStateInner(e,t){var n;let o=this.state,i=!1,s=!1;e.storedMarks&&this.composing&&(rl(this),s=!0),this.state=e;let l=o.plugins!=e.plugins||this._props.plugins!=t.plugins;if(l||this._props.plugins!=t.plugins||this._props.nodeViews!=t.nodeViews){let f=yi(this);(function(u,m){let g=0,C=0;for(let w in u){if(u[w]!=m[w])return!0;g++}for(let w in m)C++;return g!=C})(f,this.nodeViews)&&(this.nodeViews=f,i=!0)}(l||t.handleDOMEvents!=this._props.handleDOMEvents)&&fo(this),this.editable=Ci(this),gi(this);let a=mo(this),c=mi(this),d=o.plugins==e.plugins||o.doc.eq(e.doc)?e.scrollToSelection>o.scrollToSelection?"to selection":"preserve":"reset",h=i||!this.docView.matchesNode(e.doc,c,a);!h&&e.selection.eq(o.selection)||(s=!0);let p=d=="preserve"&&s&&this.dom.style.overflowAnchor==null&&function(f){let u,m,g=f.dom.getBoundingClientRect(),C=Math.max(0,g.top);for(let w=(g.left+g.right)/2,v=C+1;v<Math.min(innerHeight,g.bottom);v+=5){let b=f.root.elementFromPoint(w,v);if(!b||b==f.dom||!f.dom.contains(b))continue;let k=b.getBoundingClientRect();if(k.top>=C-20){u=b,m=k.top;break}}return{refDOM:u,refTop:m,stack:zr(f.dom)}}(this);if(s){this.domObserver.stop();let f=h&&(xe||ue)&&!this.composing&&!o.selection.empty&&!e.selection.empty&&function(u,m){let g=Math.min(u.$anchor.sharedDepth(u.head),m.$anchor.sharedDepth(m.head));return u.$anchor.start(g)!=m.$anchor.start(g)}(o.selection,e.selection);if(h){let u=ue?this.trackWrites=this.domSelectionRange().focusNode:null;this.composing&&(this.input.compositionNode=Va(this)),!i&&this.docView.update(e.doc,c,a,this)||(this.docView.updateOuterDeco(c),this.docView.destroy(),this.docView=qr(e.doc,c,a,this.dom,this)),u&&!this.trackWrites&&(f=!0)}f||!(this.input.mouseDown&&this.domObserver.currentSelection.eq(this.domSelectionRange())&&function(u){let m=u.docView.domFromPos(u.state.selection.anchor,0),g=u.domSelectionRange();return Dt(m.node,m.offset,g.anchorNode,g.anchorOffset)}(this))?Ye(this,f):(Us(this,e.selection),this.domObserver.setCurSelection()),this.domObserver.start()}this.updatePluginViews(o),!((n=this.dragging)===null||n===void 0)&&n.node&&!o.doc.eq(e.doc)&&this.updateDraggedNode(this.dragging,o),d=="reset"?this.dom.scrollTop=0:d=="to selection"?this.scrollToSelection():p&&function({refDOM:f,refTop:u,stack:m}){let g=f?f.getBoundingClientRect().top:0;Br(m,g==0?0:g-u)}(p)}scrollToSelection(){let e=this.domSelectionRange().focusNode;if(!this.someProp("handleScrollToSelection",t=>t(this)))if(this.state.selection instanceof R){let t=this.docView.domAfterPos(this.state.selection.from);t.nodeType==1&&Rr(this,t.getBoundingClientRect(),e)}else Rr(this,this.coordsAtPos(this.state.selection.head,1),e)}destroyPluginViews(){let e;for(;e=this.pluginViews.pop();)e.destroy&&e.destroy()}updatePluginViews(e){if(e&&e.plugins==this.state.plugins&&this.directPlugins==this.prevDirectPlugins)for(let t=0;t<this.pluginViews.length;t++){let n=this.pluginViews[t];n.update&&n.update(this,e)}else{this.prevDirectPlugins=this.directPlugins,this.destroyPluginViews();for(let t=0;t<this.directPlugins.length;t++){let n=this.directPlugins[t];n.spec.view&&this.pluginViews.push(n.spec.view(this))}for(let t=0;t<this.state.plugins.length;t++){let n=this.state.plugins[t];n.spec.view&&this.pluginViews.push(n.spec.view(this))}}}updateDraggedNode(e,t){let n=e.node,o=-1;if(this.state.doc.nodeAt(n.from)==n.node)o=n.from;else{let i=n.from+(this.state.doc.content.size-t.doc.content.size);(i>0&&this.state.doc.nodeAt(i))==n.node&&(o=i)}this.dragging=new sl(e.slice,e.move,o<0?void 0:R.create(this.state.doc,o))}someProp(e,t){let n,o=this._props&&this._props[e];if(o!=null&&(n=t?t(o):o))return n;for(let s=0;s<this.directPlugins.length;s++){let l=this.directPlugins[s].props[e];if(l!=null&&(n=t?t(l):l))return n}let i=this.state.plugins;if(i)for(let s=0;s<i.length;s++){let l=i[s].props[e];if(l!=null&&(n=t?t(l):l))return n}}hasFocus(){if(xe){let e=this.root.activeElement;if(e==this.dom)return!0;if(!e||!this.dom.contains(e))return!1;for(;e&&this.dom!=e&&this.dom.contains(e);){if(e.contentEditable=="false")return!1;e=e.parentElement}return!0}return this.root.activeElement==this.dom}focus(){this.domObserver.stop(),this.editable&&function(e){if(e.setActive)return e.setActive();if(zt)return e.focus(zt);let t=zr(e);e.focus(zt==null?{get preventScroll(){return zt={preventScroll:!0},!0}}:void 0),zt||(zt=!1,Br(t,0))}(this.dom),Ye(this),this.domObserver.start()}get root(){let e=this._root;if(e==null){for(let t=this.dom.parentNode;t;t=t.parentNode)if(t.nodeType==9||t.nodeType==11&&t.host)return t.getSelection||(Object.getPrototypeOf(t).getSelection=()=>t.ownerDocument.getSelection()),this._root=t}return e||document}updateRoot(){this._root=null}posAtCoords(e){return xa(this,e)}coordsAtPos(e,t=1){return Hs(this,e,t)}domAtPos(e,t=0){return this.docView.domFromPos(e,t)}nodeDOM(e){let t=this.docView.descAt(e);return t?t.nodeDOM:null}posAtDOM(e,t,n=-1){let o=this.docView.posFromDOM(e,t,n);if(o==null)throw new RangeError("DOM position not inside the editor");return o}endOfTextblock(e,t){return Ma(this,t||this.state,e)}pasteHTML(e,t){return pn(this,"",e,!1,t||new ClipboardEvent("paste"))}pasteText(e,t){return pn(this,e,null,!0,t||new ClipboardEvent("paste"))}destroy(){this.docView&&(function(e){e.domObserver.stop();for(let t in e.input.eventHandlers)e.dom.removeEventListener(t,e.input.eventHandlers[t]);clearTimeout(e.input.composingTimeout),clearTimeout(e.input.lastIOSEnterFallbackTimeout)}(this),this.destroyPluginViews(),this.mounted?(this.docView.update(this.state.doc,[],mo(this),this),this.dom.textContent=""):this.dom.parentNode&&this.dom.parentNode.removeChild(this.dom),this.docView.destroy(),this.docView=null,Io=null)}get isDestroyed(){return this.docView==null}dispatchEvent(e){return function(t,n){jo(t,n)||!fe[n.type]||!t.editable&&n.type in me||fe[n.type](t,n)}(this,e)}dispatch(e){let t=this._props.dispatchTransaction;t?t.call(this,e):this.updateState(this.state.apply(e))}domSelectionRange(){let e=this.domSelection();return ge&&this.root.nodeType===11&&function(t){let n=t.activeElement;for(;n&&n.shadowRoot;)n=n.shadowRoot.activeElement;return n}(this.dom.ownerDocument)==this.dom&&function(t,n){if(n.getComposedRanges){let s=n.getComposedRanges(t.root)[0];if(s)return pi(t,s)}let o;function i(s){s.preventDefault(),s.stopImmediatePropagation(),o=s.getTargetRanges()[0]}return t.dom.addEventListener("beforeinput",i,!0),document.execCommand("indent"),t.dom.removeEventListener("beforeinput",i,!0),o?pi(t,o):null}(this,e)||e}domSelection(){return this.root.getSelection()}}function mi(r){let e=Object.create(null);return e.class="ProseMirror",e.contenteditable=String(r.editable),r.someProp("attributes",t=>{if(typeof t=="function"&&(t=t(r.state)),t)for(let n in t)n=="class"?e.class+=" "+t[n]:n=="style"?e.style=(e.style?e.style+";":"")+t[n]:e[n]||n=="contenteditable"||n=="nodeName"||(e[n]=String(t[n]))}),e.translate||(e.translate="no"),[Se.node(0,r.state.doc.content.size,e)]}function gi(r){if(r.markCursor){let e=document.createElement("img");e.className="ProseMirror-separator",e.setAttribute("mark-placeholder","true"),e.setAttribute("alt",""),r.cursorWrapper={dom:e,deco:Se.widget(r.state.selection.head,e,{raw:!0,marks:r.markCursor})}}else r.cursorWrapper=null}function Ci(r){return!r.someProp("editable",e=>e(r.state)===!1)}function yi(r){let e=Object.create(null);function t(n){for(let o in n)Object.prototype.hasOwnProperty.call(e,o)||(e[o]=n[o])}return r.someProp("nodeViews",t),r.someProp("markViews",t),e}function wi(r){if(r.spec.state||r.spec.filterTransaction||r.spec.appendTransaction)throw new RangeError("Plugins passed directly to the view must not have a state component")}for(var ht={8:"Backspace",9:"Tab",10:"Enter",12:"NumLock",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",44:"PrintScreen",45:"Insert",46:"Delete",59:";",61:"=",91:"Meta",92:"Meta",106:"*",107:"+",108:",",109:"-",110:".",111:"/",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},Vn={48:")",49:"!",50:"@",51:"#",52:"$",53:"%",54:"^",55:"&",56:"*",57:"(",59:":",61:"+",173:"_",186:":",187:"+",188:"<",189:"_",190:">",191:"?",192:"~",219:"{",220:"|",221:"}",222:'"'},Za=typeof navigator<"u"&&/Mac/.test(navigator.platform),Ga=typeof navigator<"u"&&/MSIE \d|Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(navigator.userAgent),ae=0;ae<10;ae++)ht[48+ae]=ht[96+ae]=String(ae);for(ae=1;ae<=24;ae++)ht[ae+111]="F"+ae;for(ae=65;ae<=90;ae++)ht[ae]=String.fromCharCode(ae+32),Vn[ae]=String.fromCharCode(ae);for(var Co in ht)Vn.hasOwnProperty(Co)||(Vn[Co]=ht[Co]);const Ya=typeof navigator<"u"&&/Mac|iP(hone|[oa]d)/.test(navigator.platform);function Xa(r){let e,t,n,o,i=r.split(/-(?!$)/),s=i[i.length-1];s=="Space"&&(s=" ");for(let l=0;l<i.length-1;l++){let a=i[l];if(/^(cmd|meta|m)$/i.test(a))o=!0;else if(/^a(lt)?$/i.test(a))e=!0;else if(/^(c|ctrl|control)$/i.test(a))t=!0;else if(/^s(hift)?$/i.test(a))n=!0;else{if(!/^mod$/i.test(a))throw new Error("Unrecognized modifier name: "+a);Ya?o=!0:t=!0}}return e&&(s="Alt-"+s),t&&(s="Ctrl-"+s),o&&(s="Meta-"+s),n&&(s="Shift-"+s),s}function yo(r,e,t=!0){return e.altKey&&(r="Alt-"+r),e.ctrlKey&&(r="Ctrl-"+r),e.metaKey&&(r="Meta-"+r),t&&e.shiftKey&&(r="Shift-"+r),r}function dl(r){let e=function(t){let n=Object.create(null);for(let o in t)n[Xa(o)]=t[o];return n}(r);return function(t,n){let o,i=function(l){var a=!(Za&&l.metaKey&&l.shiftKey&&!l.ctrlKey&&!l.altKey||Ga&&l.shiftKey&&l.key&&l.key.length==1||l.key=="Unidentified")&&l.key||(l.shiftKey?Vn:ht)[l.keyCode]||l.key||"Unidentified";return a=="Esc"&&(a="Escape"),a=="Del"&&(a="Delete"),a=="Left"&&(a="ArrowLeft"),a=="Up"&&(a="ArrowUp"),a=="Right"&&(a="ArrowRight"),a=="Down"&&(a="ArrowDown"),a}(n),s=e[yo(i,n)];if(s&&s(t.state,t.dispatch,t))return!0;if(i.length==1&&i!=" "){if(n.shiftKey){let l=e[yo(i,n,!1)];if(l&&l(t.state,t.dispatch,t))return!0}if((n.shiftKey||n.altKey||n.metaKey||i.charCodeAt(0)>127)&&(o=ht[n.keyCode])&&o!=i){let l=e[yo(o,n)];if(l&&l(t.state,t.dispatch,t))return!0}}return!1}}function vi(r,e){let{$cursor:t}=r.selection;return!t||(e?!e.endOfTextblock("backward",r):t.parentOffset>0)?null:t}function xi(r,e,t){let n=e.nodeBefore,o=e.pos-1;for(;!n.isTextblock;o--){if(n.type.spec.isolating)return!1;let a=n.lastChild;if(!a)return!1;n=a}let i=e.nodeAfter,s=e.pos+1;for(;!i.isTextblock;s++){if(i.type.spec.isolating)return!1;let a=i.firstChild;if(!a)return!1;i=a}let l=Rn(r.doc,o,s,T.empty);if(!l||l.from!=o||l instanceof ie&&l.slice.size>=s-o)return!1;if(t){let a=r.tr.step(l);a.setSelection(_.create(a.doc,o)),t(a.scrollIntoView())}return!0}function Kt(r,e,t=!1){for(let n=r;n;n=e=="start"?n.firstChild:n.lastChild){if(n.isTextblock)return!0;if(t&&n.childCount!=1)return!1}return!1}function wo(r){if(!r.parent.type.spec.isolating)for(let e=r.depth-1;e>=0;e--){if(r.index(e)>0)return r.doc.resolve(r.before(e+1));if(r.node(e).type.spec.isolating)break}return null}function bi(r,e){let{$cursor:t}=r.selection;return!t||(e?!e.endOfTextblock("forward",r):t.parentOffset<t.parent.content.size)?null:t}function vo(r){if(!r.parent.type.spec.isolating)for(let e=r.depth-1;e>=0;e--){let t=r.node(e);if(r.index(e)+1<t.childCount)return r.doc.resolve(r.after(e+1));if(t.type.spec.isolating)break}return null}function ki(r){for(let e=0;e<r.edgeCount;e++){let{type:t}=r.edge(e);if(t.isTextblock&&!t.hasRequiredAttrs())return t}return null}function Mi(r,e,t){let n,o,i=e.nodeBefore,s=e.nodeAfter;if(i.type.spec.isolating||s.type.spec.isolating)return!1;if(function(h,p,f){let u=p.nodeBefore,m=p.nodeAfter,g=p.index();return!(!(u&&m&&u.type.compatibleContent(m.type))||(!u.content.size&&p.parent.canReplace(g-1,g)?(f&&f(h.tr.delete(p.pos-u.nodeSize,p.pos).scrollIntoView()),0):!p.parent.canReplace(g,g+1)||!m.isTextblock&&!$t(h.doc,p.pos)||(f&&f(h.tr.clearIncompatible(p.pos,u.type,u.contentMatchAt(u.childCount)).join(p.pos).scrollIntoView()),0)))}(r,e,t))return!0;let l=e.parent.canReplace(e.index(),e.index()+1);if(l&&(n=(o=i.contentMatchAt(i.childCount)).findWrapping(s.type))&&o.matchType(n[0]||s.type).validEnd){if(t){let h=e.pos+s.nodeSize,p=S.empty;for(let m=n.length-1;m>=0;m--)p=S.from(n[m].create(null,p));p=S.from(i.copy(p));let f=r.tr.step(new se(e.pos-1,h,e.pos,h,new T(p,1,0),n.length,!0)),u=h+2*n.length;$t(f.doc,u)&&f.join(u),t(f.scrollIntoView())}return!0}let a=V.findFrom(e,1),c=a&&a.$from.blockRange(a.$to),d=c&&qt(c);if(d!=null&&d>=e.depth)return t&&t(r.tr.lift(c,d).scrollIntoView()),!0;if(l&&Kt(s,"start",!0)&&Kt(i,"end")){let h=i,p=[];for(;p.push(h),!h.isTextblock;)h=h.lastChild;let f=s,u=1;for(;!f.isTextblock;f=f.firstChild)u++;if(h.canReplace(h.childCount,h.childCount,f.content)){if(t){let m=S.empty;for(let g=p.length-1;g>=0;g--)m=S.from(p[g].copy(m));t(r.tr.step(new se(e.pos-p.length,e.pos+s.nodeSize,e.pos+u,e.pos+s.nodeSize-u,new T(m,p.length,0),0,!0)).scrollIntoView())}return!0}}return!1}function hl(r){return function(e,t){let n=e.selection,o=r<0?n.$from:n.$to,i=o.depth;for(;o.node(i).isInline;){if(!i)return!1;i--}return!!o.node(i).isTextblock&&(t&&t(e.tr.setSelection(_.create(e.doc,r<0?o.start(i):o.end(i)))),!0)}}const Qa=hl(-1),ec=hl(1);function Si(r,e=null){return function(t,n){let o=!1;for(let i=0;i<t.selection.ranges.length&&!o;i++){let{$from:{pos:s},$to:{pos:l}}=t.selection.ranges[i];t.doc.nodesBetween(s,l,(a,c)=>{if(o)return!1;if(a.isTextblock&&!a.hasMarkup(r,e))if(a.type==r)o=!0;else{let d=t.doc.resolve(c),h=d.index();o=d.parent.canReplaceWith(h,h+1,r)}})}if(!o)return!1;if(n){let i=t.tr;for(let s=0;s<t.selection.ranges.length;s++){let{$from:{pos:l},$to:{pos:a}}=t.selection.ranges[s];i.setBlockType(l,a,r,e)}n(i.scrollIntoView())}return!0}}function tc(r,e=null){return function(t,n){let{$from:o,$to:i}=t.selection,s=o.blockRange(i),l=!1,a=s;if(!s)return!1;if(s.depth>=2&&o.node(s.depth-1).type.compatibleContent(r)&&s.startIndex==0){if(o.index(s.depth-1)==0)return!1;let d=t.doc.resolve(s.start-2);a=new In(d,d,s.depth),s.endIndex<s.parent.childCount&&(s=new In(o,t.doc.resolve(i.end(s.depth)),s.depth)),l=!0}let c=As(a,r,e,s);return!!c&&(n&&n(function(d,h,p,f,u){let m=S.empty;for(let b=p.length-1;b>=0;b--)m=S.from(p[b].type.create(p[b].attrs,m));d.step(new se(h.start-(f?2:0),h.end,h.start,h.end,new T(m,0,0),p.length,!0));let g=0;for(let b=0;b<p.length;b++)p[b].type==u&&(g=b+1);let C=p.length-g,w=h.start+p.length-(f?2:0),v=h.parent;for(let b=h.startIndex,k=h.endIndex,N=!0;b<k;b++,N=!1)!N&&_t(d.doc,w,C)&&(d.split(w,C),w+=2*C),w+=v.child(b).nodeSize;return d}(t.tr,s,c,l,r).scrollIntoView()),!0)}}function nc(r){return function(e,t){let{$from:n,$to:o}=e.selection,i=n.blockRange(o,s=>s.childCount>0&&s.firstChild.type==r);return!!i&&(!t||(n.node(i.depth-1).type==r?function(s,l,a,c){let d=s.tr,h=c.end,p=c.$to.end(c.depth);h<p&&(d.step(new se(h-1,p,h,p,new T(S.from(a.create(null,c.parent.copy())),1,0),1,!0)),c=new In(d.doc.resolve(c.$from.pos),d.doc.resolve(p),c.depth));const f=qt(c);if(f==null)return!1;d.lift(c,f);let u=d.mapping.map(h,-1)-1;return $t(d.doc,u)&&d.join(u),l(d.scrollIntoView()),!0}(e,t,r,i):function(s,l,a){let c=s.tr,d=a.parent;for(let v=a.end,b=a.endIndex-1,k=a.startIndex;b>k;b--)v-=d.child(b).nodeSize,c.delete(v-1,v+1);let h=c.doc.resolve(a.start),p=h.nodeAfter;if(c.mapping.map(a.end)!=a.start+h.nodeAfter.nodeSize)return!1;let f=a.startIndex==0,u=a.endIndex==d.childCount,m=h.node(-1),g=h.index(-1);if(!m.canReplace(g+(f?0:1),g+1,p.content.append(u?S.empty:S.from(d))))return!1;let C=h.pos,w=C+p.nodeSize;return c.step(new se(C-(f?1:0),w+(u?1:0),C+1,w-1,new T((f?S.empty:S.from(d.copy(S.empty))).append(u?S.empty:S.from(d.copy(S.empty))),f?0:1,u?0:1),f?0:1)),l(c.scrollIntoView()),!0}(e,t,i)))}}function Yn(r){const{state:e,transaction:t}=r;let{selection:n}=t,{doc:o}=t,{storedMarks:i}=t;return{...e,apply:e.apply.bind(e),applyTransaction:e.applyTransaction.bind(e),plugins:e.plugins,schema:e.schema,reconfigure:e.reconfigure.bind(e),toJSON:e.toJSON.bind(e),get storedMarks(){return i},get selection(){return n},get doc(){return o},get tr(){return n=t.selection,o=t.doc,i=t.storedMarks,t}}}typeof navigator<"u"?/Mac|iP(hone|[oa]d)/.test(navigator.platform):typeof os<"u"&&os.platform&&os.platform();let Xn=class{constructor(r){this.editor=r.editor,this.rawCommands=this.editor.extensionManager.commands,this.customState=r.state}get hasCustomState(){return!!this.customState}get state(){return this.customState||this.editor.state}get commands(){const{rawCommands:r,editor:e,state:t}=this,{view:n}=e,{tr:o}=t,i=this.buildProps(o);return Object.fromEntries(Object.entries(r).map(([s,l])=>[s,(...a)=>{const c=l(...a)(i);return o.getMeta("preventDispatch")||this.hasCustomState||n.dispatch(o),c}]))}get chain(){return()=>this.createChain()}get can(){return()=>this.createCan()}createChain(r,e=!0){const{rawCommands:t,editor:n,state:o}=this,{view:i}=n,s=[],l=!!r,a=r||o.tr,c={...Object.fromEntries(Object.entries(t).map(([d,h])=>[d,(...p)=>{const f=this.buildProps(a,e),u=h(...p)(f);return s.push(u),c}])),run:()=>(l||!e||a.getMeta("preventDispatch")||this.hasCustomState||i.dispatch(a),s.every(d=>d===!0))};return c}createCan(r){const{rawCommands:e,state:t}=this,n=!1,o=r||t.tr,i=this.buildProps(o,n);return{...Object.fromEntries(Object.entries(e).map(([l,a])=>[l,(...c)=>a(...c)({...i,dispatch:void 0})])),chain:()=>this.createChain(o,n)}}buildProps(r,e=!0){const{rawCommands:t,editor:n,state:o}=this,{view:i}=n,s={tr:r,editor:n,view:i,state:Yn({state:o,transaction:r}),dispatch:e?()=>{}:void 0,chain:()=>this.createChain(r,e),can:()=>this.createCan(r),get commands(){return Object.fromEntries(Object.entries(t).map(([l,a])=>[l,(...c)=>a(...c)(s)]))}};return s}};class oc{constructor(){this.callbacks={}}on(e,t){return this.callbacks[e]||(this.callbacks[e]=[]),this.callbacks[e].push(t),this}emit(e,...t){const n=this.callbacks[e];return n&&n.forEach(o=>o.apply(this,t)),this}off(e,t){const n=this.callbacks[e];return n&&(t?this.callbacks[e]=n.filter(o=>o!==t):delete this.callbacks[e]),this}removeAllListeners(){this.callbacks={}}}function $(r,e,t){return r.config[e]===void 0&&r.parent?$(r.parent,e,t):typeof r.config[e]=="function"?r.config[e].bind({...t,parent:r.parent?$(r.parent,e,t):null}):r.config[e]}function Fn(r){return{baseExtensions:r.filter(e=>e.type==="extension"),nodeExtensions:r.filter(e=>e.type==="node"),markExtensions:r.filter(e=>e.type==="mark")}}function Oi(r){const e=[],{nodeExtensions:t,markExtensions:n}=Fn(r),o=[...t,...n],i={default:null,rendered:!0,renderHTML:null,parseHTML:null,keepOnSplit:!0,isRequired:!1};return r.forEach(s=>{const l=$(s,"addGlobalAttributes",{name:s.name,options:s.options,storage:s.storage});l&&l().forEach(a=>{a.types.forEach(c=>{Object.entries(a.attributes).forEach(([d,h])=>{e.push({type:c,name:d,attribute:{...i,...h}})})})})}),o.forEach(s=>{const l={name:s.name,options:s.options,storage:s.storage},a=$(s,"addAttributes",l);if(!a)return;const c=a();Object.entries(c).forEach(([d,h])=>{const p={...i,...h};typeof(p==null?void 0:p.default)=="function"&&(p.default=p.default()),p!=null&&p.isRequired&&(p==null?void 0:p.default)===void 0&&delete p.default,e.push({type:s.name,name:d,attribute:p})})}),e}function oe(r,e){if(typeof r=="string"){if(!e.nodes[r])throw Error(`There is no node type named '${r}'. Maybe you forgot to add the extension?`);return e.nodes[r]}return r}function xt(...r){return r.filter(e=>!!e).reduce((e,t)=>{const n={...e};return Object.entries(t).forEach(([o,i])=>{if(n[o])if(o==="class"){const s=i?i.split(" "):[],l=n[o]?n[o].split(" "):[],a=s.filter(c=>!l.includes(c));n[o]=[...l,...a].join(" ")}else n[o]=o==="style"?[n[o],i].join("; "):i;else n[o]=i}),n},{})}function xo(r,e){return e.filter(t=>t.attribute.rendered).map(t=>t.attribute.renderHTML?t.attribute.renderHTML(r.attrs)||{}:{[t.name]:r.attrs[t.name]}).reduce((t,n)=>xt(t,n),{})}function pl(r){return typeof r=="function"}function F(r,e=void 0,...t){return pl(r)?e?r.bind(e)(...t):r(...t):r}function Ti(r,e){return r.style?r:{...r,getAttrs:t=>{const n=r.getAttrs?r.getAttrs(t):r.attrs;if(n===!1)return!1;const o=e.reduce((i,s)=>{const l=s.attribute.parseHTML?s.attribute.parseHTML(t):function(a){return typeof a!="string"?a:a.match(/^[+-]?(?:\d*\.)?\d+$/)?Number(a):a==="true"||a!=="false"&&a}(t.getAttribute(s.name));return l==null?i:{...i,[s.name]:l}},{});return{...n,...o}}}}function Ei(r){return Object.fromEntries(Object.entries(r).filter(([e,t])=>(e!=="attrs"||!function(n={}){return Object.keys(n).length===0&&n.constructor===Object}(t))&&t!=null))}function bo(r,e){return e.nodes[r]||e.marks[r]||null}function Ni(r,e){return Array.isArray(e)?e.some(t=>(typeof t=="string"?t:t.name)===r.name):e}const rc=(r,e=500)=>{let t="";const n=r.parentOffset;return r.parent.nodesBetween(Math.max(0,n-e),n,(o,i,s,l)=>{var a,c;const d=((c=(a=o.type.spec).toText)===null||c===void 0?void 0:c.call(a,{node:o,pos:i,parent:s,index:l}))||o.textContent||"%leaf%";t+=d.slice(0,Math.max(0,n-i))}),t};function ir(r){return Object.prototype.toString.call(r)==="[object RegExp]"}class ic{constructor(e){this.find=e.find,this.handler=e.handler}}const sc=(r,e)=>{if(ir(e))return e.exec(r);const t=e(r);if(!t)return null;const n=[t.text];return n.index=t.index,n.input=r,n.data=t.data,t.replaceWith&&(t.text.includes(t.replaceWith)||console.warn('[tiptap warn]: "inputRuleMatch.replaceWith" must be part of "inputRuleMatch.text".'),n.push(t.replaceWith)),n};function On(r){var e;const{editor:t,from:n,to:o,text:i,rules:s,plugin:l}=r,{view:a}=t;if(a.composing)return!1;const c=a.state.doc.resolve(n);if(c.parent.type.spec.code||!((e=c.nodeBefore||c.nodeAfter)===null||e===void 0)&&e.marks.find(p=>p.type.spec.code))return!1;let d=!1;const h=rc(c)+i;return s.forEach(p=>{if(d)return;const f=sc(h,p.find);if(!f)return;const u=a.state.tr,m=Yn({state:a.state,transaction:u}),g={from:n-(f[0].length-i.length),to:o},{commands:C,chain:w,can:v}=new Xn({editor:t,state:m});p.handler({state:m,range:g,match:f,commands:C,chain:w,can:v})!==null&&u.steps.length&&(u.setMeta(l,{transform:u,from:n,to:o,text:i}),a.dispatch(u),d=!0)}),d}function lc(r){const{editor:e,rules:t}=r,n=new Te({state:{init:()=>null,apply(o,i){const s=o.getMeta(n);if(s)return s;const l=o.getMeta("applyInputRules");return l&&setTimeout(()=>{const{from:a,text:c}=l,d=a+c.length;On({editor:e,from:a,to:d,text:c,rules:t,plugin:n})}),o.selectionSet||o.docChanged?null:i}},props:{handleTextInput:(o,i,s,l)=>On({editor:e,from:i,to:s,text:l,rules:t,plugin:n}),handleDOMEvents:{compositionend:o=>(setTimeout(()=>{const{$cursor:i}=o.state.selection;i&&On({editor:e,from:i.pos,to:i.pos,text:"",rules:t,plugin:n})}),!1)},handleKeyDown(o,i){if(i.key!=="Enter")return!1;const{$cursor:s}=o.state.selection;return!!s&&On({editor:e,from:s.pos,to:s.pos,text:`
`,rules:t,plugin:n})}},isInputRules:!0});return n}const ac=(r,e,t)=>{if(ir(e))return[...r.matchAll(e)];const n=e(r,t);return n?n.map(o=>{const i=[o.text];return i.index=o.index,i.input=r,i.data=o.data,o.replaceWith&&(o.text.includes(o.replaceWith)||console.warn('[tiptap warn]: "pasteRuleMatch.replaceWith" must be part of "pasteRuleMatch.text".'),i.push(o.replaceWith)),i}):[]};function cc(r){const{editor:e,rules:t}=r;let n=null,o=!1,i=!1,s=typeof ClipboardEvent<"u"?new ClipboardEvent("paste"):null,l=typeof DragEvent<"u"?new DragEvent("drop"):null;const a=({state:d,from:h,to:p,rule:f,pasteEvt:u})=>{const m=d.tr,g=Yn({state:d,transaction:m});if(function(w){const{editor:v,state:b,from:k,to:N,rule:L,pasteEvent:O,dropEvent:M}=w,{commands:A,chain:j,can:B}=new Xn({editor:v,state:b}),ee=[];return b.doc.nodesBetween(k,N,(P,q)=>{if(!P.isTextblock||P.type.spec.code)return;const K=Math.max(k,q),G=Math.min(N,q+P.content.size),ke=P.textBetween(K-q,G-q,void 0,"￼");ac(ke,L.find,O).forEach(H=>{if(H.index===void 0)return;const It=K+H.index+1,Pt=It+H[0].length,ft={from:b.tr.mapping.map(It),to:b.tr.mapping.map(Pt)},bn=L.handler({state:b,range:ft,match:H,commands:A,chain:j,can:B,pasteEvent:O,dropEvent:M});ee.push(bn)})}),ee.every(P=>P!==null)}({editor:e,state:g,from:Math.max(h-1,0),to:p.b-1,rule:f,pasteEvent:u,dropEvent:l})&&m.steps.length)return l=typeof DragEvent<"u"?new DragEvent("drop"):null,s=typeof ClipboardEvent<"u"?new ClipboardEvent("paste"):null,m};return t.map(d=>new Te({view(h){const p=f=>{var u;n=!((u=h.dom.parentElement)===null||u===void 0)&&u.contains(f.target)?h.dom.parentElement:null};return window.addEventListener("dragstart",p),{destroy(){window.removeEventListener("dragstart",p)}}},props:{handleDOMEvents:{drop:(h,p)=>(i=n===h.dom.parentElement,l=p,!1),paste:(h,p)=>{var f;const u=(f=p.clipboardData)===null||f===void 0?void 0:f.getData("text/html");return s=p,o=!!(u!=null&&u.includes("data-pm-slice")),!1}}},appendTransaction:(h,p,f)=>{const u=h[0],m=u.getMeta("uiEvent")==="paste"&&!o,g=u.getMeta("uiEvent")==="drop"&&!i,C=u.getMeta("applyPasteRules"),w=!!C;if(!m&&!g&&!w)return;if(w){const{from:k,text:N}=C,L=k+N.length,O=(M=>{var A;const j=new ClipboardEvent("paste",{clipboardData:new DataTransfer});return(A=j.clipboardData)===null||A===void 0||A.setData("text/html",M),j})(N);return a({rule:d,state:f,from:k,to:{b:L},pasteEvt:O})}const v=p.doc.content.findDiffStart(f.doc.content),b=p.doc.content.findDiffEnd(f.doc.content);return typeof v=="number"&&b&&v!==b.b?a({rule:d,state:f,from:v,to:b,pasteEvt:s}):void 0}}))}class Wt{constructor(e,t){this.splittableMarks=[],this.editor=t,this.extensions=Wt.resolve(e),this.schema=function(n,o){var i;const s=Oi(n),{nodeExtensions:l,markExtensions:a}=Fn(n),c=(i=l.find(p=>$(p,"topNode")))===null||i===void 0?void 0:i.name,d=Object.fromEntries(l.map(p=>{const f=s.filter(v=>v.type===p.name),u={name:p.name,options:p.options,storage:p.storage,editor:o},m=Ei({...n.reduce((v,b)=>{const k=$(b,"extendNodeSchema",u);return{...v,...k?k(p):{}}},{}),content:F($(p,"content",u)),marks:F($(p,"marks",u)),group:F($(p,"group",u)),inline:F($(p,"inline",u)),atom:F($(p,"atom",u)),selectable:F($(p,"selectable",u)),draggable:F($(p,"draggable",u)),code:F($(p,"code",u)),defining:F($(p,"defining",u)),isolating:F($(p,"isolating",u)),attrs:Object.fromEntries(f.map(v=>{var b;return[v.name,{default:(b=v==null?void 0:v.attribute)===null||b===void 0?void 0:b.default}]}))}),g=F($(p,"parseHTML",u));g&&(m.parseDOM=g.map(v=>Ti(v,f)));const C=$(p,"renderHTML",u);C&&(m.toDOM=v=>C({node:v,HTMLAttributes:xo(v,f)}));const w=$(p,"renderText",u);return w&&(m.toText=w),[p.name,m]})),h=Object.fromEntries(a.map(p=>{const f=s.filter(w=>w.type===p.name),u={name:p.name,options:p.options,storage:p.storage,editor:o},m=Ei({...n.reduce((w,v)=>{const b=$(v,"extendMarkSchema",u);return{...w,...b?b(p):{}}},{}),inclusive:F($(p,"inclusive",u)),excludes:F($(p,"excludes",u)),group:F($(p,"group",u)),spanning:F($(p,"spanning",u)),code:F($(p,"code",u)),attrs:Object.fromEntries(f.map(w=>{var v;return[w.name,{default:(v=w==null?void 0:w.attribute)===null||v===void 0?void 0:v.default}]}))}),g=F($(p,"parseHTML",u));g&&(m.parseDOM=g.map(w=>Ti(w,f)));const C=$(p,"renderHTML",u);return C&&(m.toDOM=w=>C({mark:w,HTMLAttributes:xo(w,f)})),[p.name,m]}));return new ra({topNode:c,nodes:d,marks:h})}(this.extensions,t),this.setupExtensions()}static resolve(e){const t=Wt.sort(Wt.flatten(e)),n=function(o){const i=o.filter((s,l)=>o.indexOf(s)!==l);return[...new Set(i)]}(t.map(o=>o.name));return n.length&&console.warn(`[tiptap warn]: Duplicate extension names found: [${n.map(o=>`'${o}'`).join(", ")}]. This can lead to issues.`),t}static flatten(e){return e.map(t=>{const n=$(t,"addExtensions",{name:t.name,options:t.options,storage:t.storage});return n?[t,...this.flatten(n())]:t}).flat(10)}static sort(e){return e.sort((t,n)=>{const o=$(t,"priority")||100,i=$(n,"priority")||100;return o>i?-1:o<i?1:0})}get commands(){return this.extensions.reduce((e,t)=>{const n=$(t,"addCommands",{name:t.name,options:t.options,storage:t.storage,editor:this.editor,type:bo(t.name,this.schema)});return n?{...e,...n()}:e},{})}get plugins(){const{editor:e}=this,t=Wt.sort([...this.extensions].reverse()),n=[],o=[],i=t.map(s=>{const l={name:s.name,options:s.options,storage:s.storage,editor:e,type:bo(s.name,this.schema)},a=[],c=$(s,"addKeyboardShortcuts",l);let d={};if(s.type==="mark"&&s.config.exitable&&(d.ArrowRight=()=>Jn.handleExit({editor:e,mark:s})),c){const m=Object.fromEntries(Object.entries(c()).map(([g,C])=>[g,()=>C({editor:e})]));d={...d,...m}}const h=new Te({props:{handleKeyDown:dl(d)}});a.push(h);const p=$(s,"addInputRules",l);Ni(s,e.options.enableInputRules)&&p&&n.push(...p());const f=$(s,"addPasteRules",l);Ni(s,e.options.enablePasteRules)&&f&&o.push(...f());const u=$(s,"addProseMirrorPlugins",l);if(u){const m=u();a.push(...m)}return a}).flat();return[lc({editor:e,rules:n}),...cc({editor:e,rules:o}),...i]}get attributes(){return Oi(this.extensions)}get nodeViews(){const{editor:e}=this,{nodeExtensions:t}=Fn(this.extensions);return Object.fromEntries(t.filter(n=>!!$(n,"addNodeView")).map(n=>{const o=this.attributes.filter(l=>l.type===n.name),i={name:n.name,options:n.options,storage:n.storage,editor:e,type:oe(n.name,this.schema)},s=$(n,"addNodeView",i);return s?[n.name,(l,a,c,d)=>{const h=xo(l,o);return s()({editor:e,node:l,getPos:c,decorations:d,HTMLAttributes:h,extension:n})}]:[]}))}setupExtensions(){this.extensions.forEach(e=>{var t;this.editor.extensionStorage[e.name]=e.storage;const n={name:e.name,options:e.options,storage:e.storage,editor:this.editor,type:bo(e.name,this.schema)};e.type==="mark"&&((t=F($(e,"keepOnSplit",n)))===null||t===void 0||t)&&this.splittableMarks.push(e.name);const o=$(e,"onBeforeCreate",n),i=$(e,"onCreate",n),s=$(e,"onUpdate",n),l=$(e,"onSelectionUpdate",n),a=$(e,"onTransaction",n),c=$(e,"onFocus",n),d=$(e,"onBlur",n),h=$(e,"onDestroy",n);o&&this.editor.on("beforeCreate",o),i&&this.editor.on("create",i),s&&this.editor.on("update",s),l&&this.editor.on("selectionUpdate",l),a&&this.editor.on("transaction",a),c&&this.editor.on("focus",c),d&&this.editor.on("blur",d),h&&this.editor.on("destroy",h)})}}function ko(r){return function(e){return Object.prototype.toString.call(e).slice(8,-1)}(r)==="Object"&&r.constructor===Object&&Object.getPrototypeOf(r)===Object.prototype}function Qn(r,e){const t={...r};return ko(r)&&ko(e)&&Object.keys(e).forEach(n=>{ko(e[n])?n in r?t[n]=Qn(r[n],e[n]):Object.assign(t,{[n]:e[n]}):Object.assign(t,{[n]:e[n]})}),t}class Ae{constructor(e={}){this.type="extension",this.name="extension",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...e},this.name=this.config.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=F($(this,"addOptions",{name:this.name}))),this.storage=F($(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(e={}){return new Ae(e)}configure(e={}){const t=this.extend();return t.parent=this.parent,t.options=Qn(this.options,e),t.storage=F($(t,"addStorage",{name:t.name,options:t.options})),t}extend(e={}){const t=new Ae({...this.config,...e});return t.parent=this,this.child=t,t.name=e.name?e.name:t.parent.name,e.defaultOptions&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${t.name}".`),t.options=F($(t,"addOptions",{name:t.name})),t.storage=F($(t,"addStorage",{name:t.name,options:t.options})),t}}function ul(r,e,t){const{from:n,to:o}=e,{blockSeparator:i=`

`,textSerializers:s={}}=t||{};let l="";return r.nodesBetween(n,o,(a,c,d,h)=>{var p;a.isBlock&&c>n&&(l+=i);const f=s==null?void 0:s[a.type.name];if(f)return d&&(l+=f({node:a,pos:c,parent:d,index:h,range:e})),!1;a.isText&&(l+=(p=a==null?void 0:a.text)===null||p===void 0?void 0:p.slice(Math.max(n,c)-c,o-c))}),l}function fl(r){return Object.fromEntries(Object.entries(r.nodes).filter(([,e])=>e.spec.toText).map(([e,t])=>[e,t.spec.toText]))}const dc=Ae.create({name:"clipboardTextSerializer",addOptions:()=>({blockSeparator:void 0}),addProseMirrorPlugins(){return[new Te({key:new Oe("clipboardTextSerializer"),props:{clipboardTextSerializer:()=>{const{editor:r}=this,{state:e,schema:t}=r,{doc:n,selection:o}=e,{ranges:i}=o,s=Math.min(...i.map(c=>c.$from.pos)),l=Math.max(...i.map(c=>c.$to.pos)),a=fl(t);return ul(n,{from:s,to:l},{...this.options.blockSeparator!==void 0?{blockSeparator:this.options.blockSeparator}:{},textSerializers:a})}}})]}});function Hn(r,e,t={strict:!0}){const n=Object.keys(e);return!n.length||n.every(o=>t.strict?e[o]===r[o]:ir(e[o])?e[o].test(r[o]):e[o]===r[o])}function Jo(r,e,t={}){return r.find(n=>n.type===e&&Hn(n.attrs,t))}function hc(r,e,t={}){return!!Jo(r,e,t)}function $i(r,e,t={}){if(!r||!e)return;let n=r.parent.childAfter(r.parentOffset);if(r.parentOffset===n.offset&&n.offset!==0&&(n=r.parent.childBefore(r.parentOffset)),!n.node)return;const o=Jo([...n.node.marks],e,t);if(!o)return;let i=n.index,s=r.start()+n.offset,l=i+1,a=s+n.node.nodeSize;for(Jo([...n.node.marks],e,t);i>0&&o.isInSet(r.parent.child(i-1).marks);)i-=1,s-=r.parent.child(i).nodeSize;for(;l<r.parent.childCount&&hc([...r.parent.child(l).marks],e,t);)a+=r.parent.child(l).nodeSize,l+=1;return{from:s,to:a}}function nt(r,e){if(typeof r=="string"){if(!e.marks[r])throw Error(`There is no mark type named '${r}'. Maybe you forgot to add the extension?`);return e.marks[r]}return r}function Di(r){return r instanceof _}function bt(r=0,e=0,t=0){return Math.min(Math.max(r,e),t)}function ml(r,e=null){if(!e)return null;const t=V.atStart(r),n=V.atEnd(r);if(e==="start"||e===!0)return t;if(e==="end")return n;const o=t.from,i=n.to;return e==="all"?_.create(r,bt(0,o,i),bt(r.content.size,o,i)):_.create(r,bt(e,o,i),bt(e,o,i))}function Ko(){return["iPad Simulator","iPhone Simulator","iPod Simulator","iPad","iPhone","iPod"].includes(navigator.platform)||navigator.userAgent.includes("Mac")&&"ontouchend"in document}const gl=r=>{const e=r.childNodes;for(let t=e.length-1;t>=0;t-=1){const n=e[t];n.nodeType===3&&n.nodeValue&&/^(\n\s\s|\n)$/.test(n.nodeValue)?r.removeChild(n):n.nodeType===1&&gl(n)}return r};function Ai(r){const e=`<body>${r}</body>`,t=new window.DOMParser().parseFromString(e,"text/html").body;return gl(t)}function jn(r,e,t){t={slice:!0,parseOptions:{},...t};const n=typeof r=="string";if(typeof r=="object"&&r!==null)try{return Array.isArray(r)&&r.length>0?S.fromArray(r.map(o=>e.nodeFromJSON(o))):e.nodeFromJSON(r)}catch(o){return console.warn("[tiptap warn]: Invalid content.","Passed value:",r,"Error:",o),jn("",e,t)}if(n){const o=Qt.fromSchema(e);return t.slice?o.parseSlice(Ai(r),t.parseOptions).content:o.parse(Ai(r),t.parseOptions)}return jn("",e,t)}function Cl(){return typeof navigator<"u"&&/Mac/.test(navigator.platform)}function un(r,e,t={}){const{from:n,to:o,empty:i}=r.selection,s=e?oe(e,r.schema):null,l=[];r.doc.nodesBetween(n,o,(d,h)=>{if(d.isText)return;const p=Math.max(n,h),f=Math.min(o,h+d.nodeSize);l.push({node:d,from:p,to:f})});const a=o-n,c=l.filter(d=>!s||s.name===d.node.type.name).filter(d=>Hn(d.node.attrs,t,{strict:!1}));return i?!!c.length:c.reduce((d,h)=>d+h.to-h.from,0)>=a}function qn(r,e){return e.nodes[r]?"node":e.marks[r]?"mark":null}function Li(r,e){const t=typeof e=="string"?[e]:e;return Object.keys(r).reduce((n,o)=>(t.includes(o)||(n[o]=r[o]),n),{})}function yl(r,e,t={}){return jn(r,e,{slice:!1,parseOptions:t})}function wl(r,e){const t=nt(e,r.schema),{from:n,to:o,empty:i}=r.selection,s=[];i?(r.storedMarks&&s.push(...r.storedMarks),s.push(...r.selection.$head.marks())):r.doc.nodesBetween(n,o,a=>{s.push(...a.marks)});const l=s.find(a=>a.type.name===t.name);return l?{...l.attrs}:{}}function sr(r){return e=>function(t,n){for(let o=t.depth;o>0;o-=1){const i=t.node(o);if(n(i))return{pos:o>0?t.before(o):0,start:t.start(o),depth:o,node:i}}}(e.$from,r)}function pc(r,e){const t=qn(typeof e=="string"?e:e.name,r.schema);return t==="node"?function(n,o){const i=oe(o,n.schema),{from:s,to:l}=n.selection,a=[];n.doc.nodesBetween(s,l,d=>{a.push(d)});const c=a.reverse().find(d=>d.type.name===i.name);return c?{...c.attrs}:{}}(r,e):t==="mark"?wl(r,e):{}}function Tn(r,e,t){return Object.fromEntries(Object.entries(t).filter(([n])=>{const o=r.find(i=>i.type===e&&i.name===n);return!!o&&o.attribute.keepOnSplit}))}function Wo(r,e,t={}){const{empty:n,ranges:o}=r.selection,i=e?nt(e,r.schema):null;if(n)return!!(r.storedMarks||r.selection.$from.marks()).filter(d=>!i||i.name===d.type.name).find(d=>Hn(d.attrs,t,{strict:!1}));let s=0;const l=[];if(o.forEach(({$from:d,$to:h})=>{const p=d.pos,f=h.pos;r.doc.nodesBetween(p,f,(u,m)=>{if(!u.isText&&!u.marks.length)return;const g=Math.max(p,m),C=Math.min(f,m+u.nodeSize);s+=C-g,l.push(...u.marks.map(w=>({mark:w,from:g,to:C})))})}),s===0)return!1;const a=l.filter(d=>!i||i.name===d.mark.type.name).filter(d=>Hn(d.mark.attrs,t,{strict:!1})).reduce((d,h)=>d+h.to-h.from,0),c=l.filter(d=>!i||d.mark.type!==i&&d.mark.type.excludes(i)).reduce((d,h)=>d+h.to-h.from,0);return(a>0?a+c:a)>=s}function Ii(r,e){const{nodeExtensions:t}=Fn(e),n=t.find(i=>i.name===r);if(!n)return!1;const o=F($(n,"group",{name:n.name,options:n.options,storage:n.storage}));return typeof o=="string"&&o.split(" ").includes("list")}function Pi(r,e){const t=r.storedMarks||r.selection.$to.parentOffset&&r.selection.$from.marks();if(t){const n=t.filter(o=>e==null?void 0:e.includes(o.type.name));r.tr.ensureMarks(n)}}const Mo=(r,e)=>{const t=sr(i=>i.type===e)(r.selection);if(!t)return!0;const n=r.doc.resolve(Math.max(0,t.pos-1)).before(t.depth);if(n===void 0)return!0;const o=r.doc.nodeAt(n);return t.node.type!==(o==null?void 0:o.type)||!$t(r.doc,t.pos)||(r.join(t.pos),!0)},So=(r,e)=>{const t=sr(i=>i.type===e)(r.selection);if(!t)return!0;const n=r.doc.resolve(t.start).after(t.depth);if(n===void 0)return!0;const o=r.doc.nodeAt(n);return t.node.type!==(o==null?void 0:o.type)||!$t(r.doc,n)||(r.join(n),!0)};var uc=Object.freeze({__proto__:null,blur:()=>({editor:r,view:e})=>(requestAnimationFrame(()=>{var t;r.isDestroyed||(e.dom.blur(),(t=window==null?void 0:window.getSelection())===null||t===void 0||t.removeAllRanges())}),!0),clearContent:(r=!1)=>({commands:e})=>e.setContent("",r),clearNodes:()=>({state:r,tr:e,dispatch:t})=>{const{selection:n}=e,{ranges:o}=n;return!t||(o.forEach(({$from:i,$to:s})=>{r.doc.nodesBetween(i.pos,s.pos,(l,a)=>{if(l.type.isText)return;const{doc:c,mapping:d}=e,h=c.resolve(d.map(a)),p=c.resolve(d.map(a+l.nodeSize)),f=h.blockRange(p);if(!f)return;const u=qt(f);if(l.type.isTextblock){const{defaultType:m}=h.parent.contentMatchAt(h.index());e.setNodeMarkup(f.start,m)}(u||u===0)&&e.lift(f,u)})}),!0)},command:r=>e=>r(e),createParagraphNear:()=>({state:r,dispatch:e})=>((t,n)=>{let o=t.selection,{$from:i,$to:s}=o;if(o instanceof Re||i.parent.inlineContent||s.parent.inlineContent)return!1;let l=ki(s.parent.contentMatchAt(s.indexAfter()));if(!l||!l.isTextblock)return!1;if(n){let a=(!i.parentOffset&&s.index()<s.parent.childCount?i:s).pos,c=t.tr.insert(a,l.createAndFill());c.setSelection(_.create(c.doc,a+1)),n(c.scrollIntoView())}return!0})(r,e),cut:(r,e)=>({editor:t,tr:n})=>{const{state:o}=t,i=o.doc.slice(r.from,r.to);n.deleteRange(r.from,r.to);const s=n.mapping.map(e);return n.insert(s,i.content),n.setSelection(new _(n.doc.resolve(s-1))),!0},deleteCurrentNode:()=>({tr:r,dispatch:e})=>{const{selection:t}=r,n=t.$anchor.node();if(n.content.size>0)return!1;const o=r.selection.$anchor;for(let i=o.depth;i>0;i-=1)if(o.node(i).type===n.type){if(e){const s=o.before(i),l=o.after(i);r.delete(s,l).scrollIntoView()}return!0}return!1},deleteNode:r=>({tr:e,state:t,dispatch:n})=>{const o=oe(r,t.schema),i=e.selection.$anchor;for(let s=i.depth;s>0;s-=1)if(i.node(s).type===o){if(n){const l=i.before(s),a=i.after(s);e.delete(l,a).scrollIntoView()}return!0}return!1},deleteRange:r=>({tr:e,dispatch:t})=>{const{from:n,to:o}=r;return t&&e.delete(n,o),!0},deleteSelection:()=>({state:r,dispatch:e})=>((t,n)=>!t.selection.empty&&(n&&n(t.tr.deleteSelection().scrollIntoView()),!0))(r,e),enter:()=>({commands:r})=>r.keyboardShortcut("Enter"),exitCode:()=>({state:r,dispatch:e})=>((t,n)=>{let{$head:o,$anchor:i}=t.selection;if(!o.parent.type.spec.code||!o.sameParent(i))return!1;let s=o.node(-1),l=o.indexAfter(-1),a=ki(s.contentMatchAt(l));if(!a||!s.canReplaceWith(l,l,a))return!1;if(n){let c=o.after(),d=t.tr.replaceWith(c,c,a.createAndFill());d.setSelection(V.near(d.doc.resolve(c),1)),n(d.scrollIntoView())}return!0})(r,e),extendMarkRange:(r,e={})=>({tr:t,state:n,dispatch:o})=>{const i=nt(r,n.schema),{doc:s,selection:l}=t,{$from:a,from:c,to:d}=l;if(o){const h=$i(a,i,e);if(h&&h.from<=c&&h.to>=d){const p=_.create(s,h.from,h.to);t.setSelection(p)}}return!0},first:r=>e=>{const t=typeof r=="function"?r(e):r;for(let n=0;n<t.length;n+=1)if(t[n](e))return!0;return!1},focus:(r=null,e={})=>({editor:t,view:n,tr:o,dispatch:i})=>{e={scrollIntoView:!0,...e};const s=()=>{Ko()&&n.dom.focus(),requestAnimationFrame(()=>{t.isDestroyed||(n.focus(),e!=null&&e.scrollIntoView&&t.commands.scrollIntoView())})};if(n.hasFocus()&&r===null||r===!1)return!0;if(i&&r===null&&!Di(t.state.selection))return s(),!0;const l=ml(o.doc,r)||t.state.selection,a=t.state.selection.eq(l);return i&&(a||o.setSelection(l),a&&o.storedMarks&&o.setStoredMarks(o.storedMarks),s()),!0},forEach:(r,e)=>t=>r.every((n,o)=>e(n,{...t,index:o})),insertContent:(r,e)=>({tr:t,commands:n})=>n.insertContentAt({from:t.selection.from,to:t.selection.to},r,e),insertContentAt:(r,e,t)=>({tr:n,dispatch:o,editor:i})=>{if(o){t={parseOptions:{},updateSelection:!0,applyInputRules:!1,applyPasteRules:!1,...t};const s=jn(e,i.schema,{parseOptions:{preserveWhitespace:"full",...t.parseOptions}});if(s.toString()==="<>")return!0;let{from:l,to:a}=typeof r=="number"?{from:r,to:r}:{from:r.from,to:r.to},c=!0,d=!0;if((s.toString().startsWith("<")?s:[s]).forEach(p=>{p.check(),c=!!c&&p.isText&&p.marks.length===0,d=!!d&&p.isBlock}),l===a&&d){const{parent:p}=n.doc.resolve(l);p.isTextblock&&!p.type.spec.code&&!p.childCount&&(l-=1,a+=1)}let h;c?(h=Array.isArray(e)?e.map(p=>p.text||"").join(""):typeof e=="object"&&e&&e.text?e.text:e,n.insertText(h,l,a)):(h=s,n.replaceWith(l,a,h)),t.updateSelection&&function(p,f,u){const m=p.steps.length-1;if(m<f)return;const g=p.steps[m];if(!(g instanceof ie||g instanceof se))return;const C=p.mapping.maps[m];let w=0;C.forEach((v,b,k,N)=>{w===0&&(w=N)}),p.setSelection(V.near(p.doc.resolve(w),u))}(n,n.steps.length-1,-1),t.applyInputRules&&n.setMeta("applyInputRules",{from:l,text:h}),t.applyPasteRules&&n.setMeta("applyPasteRules",{from:l,text:h})}return!0},joinUp:()=>({state:r,dispatch:e})=>((t,n)=>{let o,i=t.selection,s=i instanceof R;if(s){if(i.node.isTextblock||!$t(t.doc,i.from))return!1;o=i.from}else if(o=Mn(t.doc,i.from,-1),o==null)return!1;if(n){let l=t.tr.join(o);s&&l.setSelection(R.create(l.doc,o-t.doc.resolve(o).nodeBefore.nodeSize)),n(l.scrollIntoView())}return!0})(r,e),joinDown:()=>({state:r,dispatch:e})=>((t,n)=>{let o,i=t.selection;if(i instanceof R){if(i.node.isTextblock||!$t(t.doc,i.to))return!1;o=i.to}else if(o=Mn(t.doc,i.to,1),o==null)return!1;return n&&n(t.tr.join(o).scrollIntoView()),!0})(r,e),joinBackward:()=>({state:r,dispatch:e})=>((t,n,o)=>{let i=vi(t,o);if(!i)return!1;let s=wo(i);if(!s){let a=i.blockRange(),c=a&&qt(a);return c!=null&&(n&&n(t.tr.lift(a,c).scrollIntoView()),!0)}let l=s.nodeBefore;if(!l.type.spec.isolating&&Mi(t,s,n))return!0;if(i.parent.content.size==0&&(Kt(l,"end")||R.isSelectable(l))){let a=Rn(t.doc,i.before(),i.after(),T.empty);if(a&&a.slice.size<a.to-a.from){if(n){let c=t.tr.step(a);c.setSelection(Kt(l,"end")?V.findFrom(c.doc.resolve(c.mapping.map(s.pos,-1)),-1):R.create(c.doc,s.pos-l.nodeSize)),n(c.scrollIntoView())}return!0}}return!(!l.isAtom||s.depth!=i.depth-1||(n&&n(t.tr.delete(s.pos-l.nodeSize,s.pos).scrollIntoView()),0))})(r,e),joinForward:()=>({state:r,dispatch:e})=>((t,n,o)=>{let i=bi(t,o);if(!i)return!1;let s=vo(i);if(!s)return!1;let l=s.nodeAfter;if(Mi(t,s,n))return!0;if(i.parent.content.size==0&&(Kt(l,"start")||R.isSelectable(l))){let a=Rn(t.doc,i.before(),i.after(),T.empty);if(a&&a.slice.size<a.to-a.from){if(n){let c=t.tr.step(a);c.setSelection(Kt(l,"start")?V.findFrom(c.doc.resolve(c.mapping.map(s.pos)),1):R.create(c.doc,c.mapping.map(s.pos))),n(c.scrollIntoView())}return!0}}return!(!l.isAtom||s.depth!=i.depth-1||(n&&n(t.tr.delete(s.pos,s.pos+l.nodeSize).scrollIntoView()),0))})(r,e),joinItemBackward:()=>({tr:r,state:e,dispatch:t})=>{try{const n=Mn(e.doc,e.selection.$from.pos,-1);return n!=null&&(r.join(n,2),t&&t(r),!0)}catch{return!1}},joinItemForward:()=>({state:r,dispatch:e,tr:t})=>{try{const n=Mn(r.doc,r.selection.$from.pos,1);return n!=null&&(t.join(n,2),e&&e(t),!0)}catch{return!1}},joinTextblockBackward:()=>({state:r,dispatch:e})=>((t,n,o)=>{let i=vi(t,o);if(!i)return!1;let s=wo(i);return!!s&&xi(t,s,n)})(r,e),joinTextblockForward:()=>({state:r,dispatch:e})=>((t,n,o)=>{let i=bi(t,o);if(!i)return!1;let s=vo(i);return!!s&&xi(t,s,n)})(r,e),keyboardShortcut:r=>({editor:e,view:t,tr:n,dispatch:o})=>{const i=function(c){const d=c.split(/-(?!$)/);let h,p,f,u,m=d[d.length-1];m==="Space"&&(m=" ");for(let g=0;g<d.length-1;g+=1){const C=d[g];if(/^(cmd|meta|m)$/i.test(C))u=!0;else if(/^a(lt)?$/i.test(C))h=!0;else if(/^(c|ctrl|control)$/i.test(C))p=!0;else if(/^s(hift)?$/i.test(C))f=!0;else{if(!/^mod$/i.test(C))throw new Error(`Unrecognized modifier name: ${C}`);Ko()||Cl()?u=!0:p=!0}}return h&&(m=`Alt-${m}`),p&&(m=`Ctrl-${m}`),u&&(m=`Meta-${m}`),f&&(m=`Shift-${m}`),m}(r).split(/-(?!$)/),s=i.find(c=>!["Alt","Ctrl","Meta","Shift"].includes(c)),l=new KeyboardEvent("keydown",{key:s==="Space"?" ":s,altKey:i.includes("Alt"),ctrlKey:i.includes("Ctrl"),metaKey:i.includes("Meta"),shiftKey:i.includes("Shift"),bubbles:!0,cancelable:!0}),a=e.captureTransaction(()=>{t.someProp("handleKeyDown",c=>c(t,l))});return a==null||a.steps.forEach(c=>{const d=c.map(n.mapping);d&&o&&n.maybeStep(d)}),!0},lift:(r,e={})=>({state:t,dispatch:n})=>!!un(t,oe(r,t.schema),e)&&((o,i)=>{let{$from:s,$to:l}=o.selection,a=s.blockRange(l),c=a&&qt(a);return c!=null&&(i&&i(o.tr.lift(a,c).scrollIntoView()),!0)})(t,n),liftEmptyBlock:()=>({state:r,dispatch:e})=>((t,n)=>{let{$cursor:o}=t.selection;if(!o||o.parent.content.size)return!1;if(o.depth>1&&o.after()!=o.end(-1)){let l=o.before();if(_t(t.doc,l))return n&&n(t.tr.split(l).scrollIntoView()),!0}let i=o.blockRange(),s=i&&qt(i);return s!=null&&(n&&n(t.tr.lift(i,s).scrollIntoView()),!0)})(r,e),liftListItem:r=>({state:e,dispatch:t})=>nc(oe(r,e.schema))(e,t),newlineInCode:()=>({state:r,dispatch:e})=>((t,n)=>{let{$head:o,$anchor:i}=t.selection;return!(!o.parent.type.spec.code||!o.sameParent(i)||(n&&n(t.tr.insertText(`
`).scrollIntoView()),0))})(r,e),resetAttributes:(r,e)=>({tr:t,state:n,dispatch:o})=>{let i=null,s=null;const l=qn(typeof r=="string"?r:r.name,n.schema);return!!l&&(l==="node"&&(i=oe(r,n.schema)),l==="mark"&&(s=nt(r,n.schema)),o&&t.selection.ranges.forEach(a=>{n.doc.nodesBetween(a.$from.pos,a.$to.pos,(c,d)=>{i&&i===c.type&&t.setNodeMarkup(d,void 0,Li(c.attrs,e)),s&&c.marks.length&&c.marks.forEach(h=>{s===h.type&&t.addMark(d,d+c.nodeSize,s.create(Li(h.attrs,e)))})})}),!0)},scrollIntoView:()=>({tr:r,dispatch:e})=>(e&&r.scrollIntoView(),!0),selectAll:()=>({tr:r,commands:e})=>e.setTextSelection({from:0,to:r.doc.content.size}),selectNodeBackward:()=>({state:r,dispatch:e})=>((t,n,o)=>{let{$head:i,empty:s}=t.selection,l=i;if(!s)return!1;if(i.parent.isTextblock){if(o?!o.endOfTextblock("backward",t):i.parentOffset>0)return!1;l=wo(i)}let a=l&&l.nodeBefore;return!(!a||!R.isSelectable(a)||(n&&n(t.tr.setSelection(R.create(t.doc,l.pos-a.nodeSize)).scrollIntoView()),0))})(r,e),selectNodeForward:()=>({state:r,dispatch:e})=>((t,n,o)=>{let{$head:i,empty:s}=t.selection,l=i;if(!s)return!1;if(i.parent.isTextblock){if(o?!o.endOfTextblock("forward",t):i.parentOffset<i.parent.content.size)return!1;l=vo(i)}let a=l&&l.nodeAfter;return!(!a||!R.isSelectable(a)||(n&&n(t.tr.setSelection(R.create(t.doc,l.pos)).scrollIntoView()),0))})(r,e),selectParentNode:()=>({state:r,dispatch:e})=>((t,n)=>{let o,{$from:i,to:s}=t.selection,l=i.sharedDepth(s);return l!=0&&(o=i.before(l),n&&n(t.tr.setSelection(R.create(t.doc,o))),!0)})(r,e),selectTextblockEnd:()=>({state:r,dispatch:e})=>ec(r,e),selectTextblockStart:()=>({state:r,dispatch:e})=>Qa(r,e),setContent:(r,e=!1,t={})=>({tr:n,editor:o,dispatch:i})=>{const{doc:s}=n,l=yl(r,o.schema,t);return i&&n.replaceWith(0,s.content.size,l).setMeta("preventUpdate",!e),!0},setMark:(r,e={})=>({tr:t,state:n,dispatch:o})=>{const{selection:i}=t,{empty:s,ranges:l}=i,a=nt(r,n.schema);if(o)if(s){const c=wl(n,a);t.addStoredMark(a.create({...c,...e}))}else l.forEach(c=>{const d=c.$from.pos,h=c.$to.pos;n.doc.nodesBetween(d,h,(p,f)=>{const u=Math.max(f,d),m=Math.min(f+p.nodeSize,h);p.marks.find(g=>g.type===a)?p.marks.forEach(g=>{a===g.type&&t.addMark(u,m,a.create({...g.attrs,...e}))}):t.addMark(u,m,a.create(e))})});return function(c,d,h){var p;const{selection:f}=d;let u=null;if(Di(f)&&(u=f.$cursor),u){const g=(p=c.storedMarks)!==null&&p!==void 0?p:u.marks();return!!h.isInSet(g)||!g.some(C=>C.type.excludes(h))}const{ranges:m}=f;return m.some(({$from:g,$to:C})=>{let w=g.depth===0&&c.doc.inlineContent&&c.doc.type.allowsMarkType(h);return c.doc.nodesBetween(g.pos,C.pos,(v,b,k)=>{if(w)return!1;if(v.isInline){const N=!k||k.type.allowsMarkType(h),L=!!h.isInSet(v.marks)||!v.marks.some(O=>O.type.excludes(h));w=N&&L}return!w}),w})}(n,t,a)},setMeta:(r,e)=>({tr:t})=>(t.setMeta(r,e),!0),setNode:(r,e={})=>({state:t,dispatch:n,chain:o})=>{const i=oe(r,t.schema);return i.isTextblock?o().command(({commands:s})=>!!Si(i,e)(t)||s.clearNodes()).command(({state:s})=>Si(i,e)(s,n)).run():(console.warn('[tiptap warn]: Currently "setNode()" only supports text block nodes.'),!1)},setNodeSelection:r=>({tr:e,dispatch:t})=>{if(t){const{doc:n}=e,o=bt(r,0,n.content.size),i=R.create(n,o);e.setSelection(i)}return!0},setTextSelection:r=>({tr:e,dispatch:t})=>{if(t){const{doc:n}=e,{from:o,to:i}=typeof r=="number"?{from:r,to:r}:r,s=_.atStart(n).from,l=_.atEnd(n).to,a=bt(o,s,l),c=bt(i,s,l),d=_.create(n,a,c);e.setSelection(d)}return!0},sinkListItem:r=>({state:e,dispatch:t})=>{return(o=oe(r,e.schema),function(i,s){let{$from:l,$to:a}=i.selection,c=l.blockRange(a,f=>f.childCount>0&&f.firstChild.type==o);if(!c)return!1;let d=c.startIndex;if(d==0)return!1;let h=c.parent,p=h.child(d-1);if(p.type!=o)return!1;if(s){let f=p.lastChild&&p.lastChild.type==h.type,u=S.from(f?o.create():null),m=new T(S.from(o.create(null,S.from(h.type.create(null,u)))),f?3:1,0),g=c.start,C=c.end;s(i.tr.step(new se(g-(f?3:1),C,g,C,m,1,!0)).scrollIntoView())}return!0})(e,t);var o},splitBlock:({keepMarks:r=!0}={})=>({tr:e,state:t,dispatch:n,editor:o})=>{const{selection:i,doc:s}=e,{$from:l,$to:a}=i,c=Tn(o.extensionManager.attributes,l.node().type.name,l.node().attrs);if(i instanceof R&&i.node.isBlock)return!(!l.parentOffset||!_t(s,l.pos))&&(n&&(r&&Pi(t,o.extensionManager.splittableMarks),e.split(l.pos).scrollIntoView()),!0);if(!l.parent.isBlock)return!1;if(n){const d=a.parentOffset===a.parent.content.size;i instanceof _&&e.deleteSelection();const h=l.depth===0?void 0:function(u){for(let m=0;m<u.edgeCount;m+=1){const{type:g}=u.edge(m);if(g.isTextblock&&!g.hasRequiredAttrs())return g}return null}(l.node(-1).contentMatchAt(l.indexAfter(-1)));let p=d&&h?[{type:h,attrs:c}]:void 0,f=_t(e.doc,e.mapping.map(l.pos),1,p);if(p||f||!_t(e.doc,e.mapping.map(l.pos),1,h?[{type:h}]:void 0)||(f=!0,p=h?[{type:h,attrs:c}]:void 0),f&&(e.split(e.mapping.map(l.pos),1,p),h&&!d&&!l.parentOffset&&l.parent.type!==h)){const u=e.mapping.map(l.before()),m=e.doc.resolve(u);l.node(-1).canReplaceWith(m.index(),m.index()+1,h)&&e.setNodeMarkup(e.mapping.map(l.before()),h)}r&&Pi(t,o.extensionManager.splittableMarks),e.scrollIntoView()}return!0},splitListItem:r=>({tr:e,state:t,dispatch:n,editor:o})=>{var i;const s=oe(r,t.schema),{$from:l,$to:a}=t.selection,c=t.selection.node;if(c&&c.isBlock||l.depth<2||!l.sameParent(a))return!1;const d=l.node(-1);if(d.type!==s)return!1;const h=o.extensionManager.attributes;if(l.parent.content.size===0&&l.node(-1).childCount===l.indexAfter(-1)){if(l.depth===2||l.node(-3).type!==s||l.index(-2)!==l.node(-2).childCount-1)return!1;if(n){let g=S.empty;const C=l.index(-1)?1:l.index(-2)?2:3;for(let L=l.depth-C;L>=l.depth-3;L-=1)g=S.from(l.node(L).copy(g));const w=l.indexAfter(-1)<l.node(-2).childCount?1:l.indexAfter(-2)<l.node(-3).childCount?2:3,v=Tn(h,l.node().type.name,l.node().attrs),b=((i=s.contentMatch.defaultType)===null||i===void 0?void 0:i.createAndFill(v))||void 0;g=g.append(S.from(s.createAndFill(null,b)||void 0));const k=l.before(l.depth-(C-1));e.replace(k,l.after(-w),new T(g,4-C,0));let N=-1;e.doc.nodesBetween(k,e.doc.content.size,(L,O)=>{if(N>-1)return!1;L.isTextblock&&L.content.size===0&&(N=O+1)}),N>-1&&e.setSelection(_.near(e.doc.resolve(N))),e.scrollIntoView()}return!0}const p=a.pos===l.end()?d.contentMatchAt(0).defaultType:null,f=Tn(h,d.type.name,d.attrs),u=Tn(h,l.node().type.name,l.node().attrs);e.delete(l.pos,a.pos);const m=p?[{type:s,attrs:f},{type:p,attrs:u}]:[{type:s,attrs:f}];if(!_t(e.doc,l.pos,2))return!1;if(n){const{selection:g,storedMarks:C}=t,{splittableMarks:w}=o.extensionManager,v=C||g.$to.parentOffset&&g.$from.marks();if(e.split(l.pos,2,m).scrollIntoView(),!v||!n)return!0;const b=v.filter(k=>w.includes(k.type.name));e.ensureMarks(b)}return!0},toggleList:(r,e,t,n={})=>({editor:o,tr:i,state:s,dispatch:l,chain:a,commands:c,can:d})=>{const{extensions:h,splittableMarks:p}=o.extensionManager,f=oe(r,s.schema),u=oe(e,s.schema),{selection:m,storedMarks:g}=s,{$from:C,$to:w}=m,v=C.blockRange(w),b=g||m.$to.parentOffset&&m.$from.marks();if(!v)return!1;const k=sr(N=>Ii(N.type.name,h))(m);if(v.depth>=1&&k&&v.depth-k.depth<=1){if(k.node.type===f)return c.liftListItem(u);if(Ii(k.node.type.name,h)&&f.validContent(k.node.content)&&l)return a().command(()=>(i.setNodeMarkup(k.pos,f),!0)).command(()=>Mo(i,f)).command(()=>So(i,f)).run()}return t&&b&&l?a().command(()=>{const N=d().wrapInList(f,n),L=b.filter(O=>p.includes(O.type.name));return i.ensureMarks(L),!!N||c.clearNodes()}).wrapInList(f,n).command(()=>Mo(i,f)).command(()=>So(i,f)).run():a().command(()=>!!d().wrapInList(f,n)||c.clearNodes()).wrapInList(f,n).command(()=>Mo(i,f)).command(()=>So(i,f)).run()},toggleMark:(r,e={},t={})=>({state:n,commands:o})=>{const{extendEmptyMarkRange:i=!1}=t,s=nt(r,n.schema);return Wo(n,s,e)?o.unsetMark(s,{extendEmptyMarkRange:i}):o.setMark(s,e)},toggleNode:(r,e,t={})=>({state:n,commands:o})=>{const i=oe(r,n.schema),s=oe(e,n.schema);return un(n,i,t)?o.setNode(s):o.setNode(i,t)},toggleWrap:(r,e={})=>({state:t,commands:n})=>{const o=oe(r,t.schema);return un(t,o,e)?n.lift(o):n.wrapIn(o,e)},undoInputRule:()=>({state:r,dispatch:e})=>{const t=r.plugins;for(let n=0;n<t.length;n+=1){const o=t[n];let i;if(o.spec.isInputRules&&(i=o.getState(r))){if(e){const s=r.tr,l=i.transform;for(let a=l.steps.length-1;a>=0;a-=1)s.step(l.steps[a].invert(l.docs[a]));if(i.text){const a=s.doc.resolve(i.from).marks();s.replaceWith(i.from,i.to,r.schema.text(i.text,a))}else s.delete(i.from,i.to)}return!0}}return!1},unsetAllMarks:()=>({tr:r,dispatch:e})=>{const{selection:t}=r,{empty:n,ranges:o}=t;return n||e&&o.forEach(i=>{r.removeMark(i.$from.pos,i.$to.pos)}),!0},unsetMark:(r,e={})=>({tr:t,state:n,dispatch:o})=>{var i;const{extendEmptyMarkRange:s=!1}=e,{selection:l}=t,a=nt(r,n.schema),{$from:c,empty:d,ranges:h}=l;if(!o)return!0;if(d&&s){let{from:p,to:f}=l;const u=(i=c.marks().find(g=>g.type===a))===null||i===void 0?void 0:i.attrs,m=$i(c,a,u);m&&(p=m.from,f=m.to),t.removeMark(p,f,a)}else h.forEach(p=>{t.removeMark(p.$from.pos,p.$to.pos,a)});return t.removeStoredMark(a),!0},updateAttributes:(r,e={})=>({tr:t,state:n,dispatch:o})=>{let i=null,s=null;const l=qn(typeof r=="string"?r:r.name,n.schema);return!!l&&(l==="node"&&(i=oe(r,n.schema)),l==="mark"&&(s=nt(r,n.schema)),o&&t.selection.ranges.forEach(a=>{const c=a.$from.pos,d=a.$to.pos;n.doc.nodesBetween(c,d,(h,p)=>{i&&i===h.type&&t.setNodeMarkup(p,void 0,{...h.attrs,...e}),s&&h.marks.length&&h.marks.forEach(f=>{if(s===f.type){const u=Math.max(p,c),m=Math.min(p+h.nodeSize,d);t.addMark(u,m,s.create({...f.attrs,...e}))}})})}),!0)},wrapIn:(r,e={})=>({state:t,dispatch:n})=>function(o,i=null){return function(s,l){let{$from:a,$to:c}=s.selection,d=a.blockRange(c),h=d&&As(d,o,i);return!!h&&(l&&l(s.tr.wrap(d,h).scrollIntoView()),!0)}}(oe(r,t.schema),e)(t,n),wrapInList:(r,e={})=>({state:t,dispatch:n})=>tc(oe(r,t.schema),e)(t,n)});const fc=Ae.create({name:"commands",addCommands:()=>({...uc})}),mc=Ae.create({name:"editable",addProseMirrorPlugins(){return[new Te({key:new Oe("editable"),props:{editable:()=>this.editor.options.editable}})]}}),gc=Ae.create({name:"focusEvents",addProseMirrorPlugins(){const{editor:r}=this;return[new Te({key:new Oe("focusEvents"),props:{handleDOMEvents:{focus:(e,t)=>{r.isFocused=!0;const n=r.state.tr.setMeta("focus",{event:t}).setMeta("addToHistory",!1);return e.dispatch(n),!1},blur:(e,t)=>{r.isFocused=!1;const n=r.state.tr.setMeta("blur",{event:t}).setMeta("addToHistory",!1);return e.dispatch(n),!1}}}})]}}),Cc=Ae.create({name:"keymap",addKeyboardShortcuts(){const r=()=>this.editor.commands.first(({commands:i})=>[()=>i.undoInputRule(),()=>i.command(({tr:s})=>{const{selection:l,doc:a}=s,{empty:c,$anchor:d}=l,{pos:h,parent:p}=d,f=d.parent.isTextblock&&h>0?s.doc.resolve(h-1):d,u=f.parent.type.spec.isolating,m=d.pos-d.parentOffset,g=u&&f.parent.childCount===1?m===d.pos:V.atStart(a).from===h;return!(!c||!p.type.isTextblock||p.textContent.length||!g||g&&d.parent.type.name==="paragraph")&&i.clearNodes()}),()=>i.deleteSelection(),()=>i.joinBackward(),()=>i.selectNodeBackward()]),e=()=>this.editor.commands.first(({commands:i})=>[()=>i.deleteSelection(),()=>i.deleteCurrentNode(),()=>i.joinForward(),()=>i.selectNodeForward()]),t={Enter:()=>this.editor.commands.first(({commands:i})=>[()=>i.newlineInCode(),()=>i.createParagraphNear(),()=>i.liftEmptyBlock(),()=>i.splitBlock()]),"Mod-Enter":()=>this.editor.commands.exitCode(),Backspace:r,"Mod-Backspace":r,"Shift-Backspace":r,Delete:e,"Mod-Delete":e,"Mod-a":()=>this.editor.commands.selectAll()},n={...t},o={...t,"Ctrl-h":r,"Alt-Backspace":r,"Ctrl-d":e,"Ctrl-Alt-Backspace":e,"Alt-Delete":e,"Alt-d":e,"Ctrl-a":()=>this.editor.commands.selectTextblockStart(),"Ctrl-e":()=>this.editor.commands.selectTextblockEnd()};return Ko()||Cl()?o:n},addProseMirrorPlugins(){return[new Te({key:new Oe("clearDocument"),appendTransaction:(r,e,t)=>{if(!(r.some(h=>h.docChanged)&&!e.doc.eq(t.doc)))return;const{empty:n,from:o,to:i}=e.selection,s=V.atStart(e.doc).from,l=V.atEnd(e.doc).to;if(n||!(o===s&&i===l)||t.doc.textBetween(0,t.doc.content.size," "," ").length!==0)return;const a=t.tr,c=Yn({state:t,transaction:a}),{commands:d}=new Xn({editor:this.editor,state:c});return d.clearNodes(),a.steps.length?a:void 0}})]}}),yc=Ae.create({name:"tabindex",addProseMirrorPlugins(){return[new Te({key:new Oe("tabindex"),props:{attributes:this.editor.isEditable?{tabindex:"0"}:{}}})]}});class Ct{constructor(e,t,n=!1,o=null){this.currentNode=null,this.actualDepth=null,this.isBlock=n,this.resolvedPos=e,this.editor=t,this.currentNode=o}get name(){return this.node.type.name}get node(){return this.currentNode||this.resolvedPos.node()}get element(){return this.editor.view.domAtPos(this.pos).node}get depth(){var e;return(e=this.actualDepth)!==null&&e!==void 0?e:this.resolvedPos.depth}get pos(){return this.resolvedPos.pos}get content(){return this.node.content}set content(e){let t=this.from,n=this.to;if(this.isBlock){if(this.content.size===0)return void console.error(`You can’t set content on a block node. Tried to set content on ${this.name} at ${this.pos}`);t=this.from+1,n=this.to-1}this.editor.commands.insertContentAt({from:t,to:n},e)}get attributes(){return this.node.attrs}get textContent(){return this.node.textContent}get size(){return this.node.nodeSize}get from(){return this.isBlock?this.pos:this.resolvedPos.start(this.resolvedPos.depth)}get range(){return{from:this.from,to:this.to}}get to(){return this.isBlock?this.pos+this.size:this.resolvedPos.end(this.resolvedPos.depth)+(this.node.isText?0:1)}get parent(){if(this.depth===0)return null;const e=this.resolvedPos.start(this.resolvedPos.depth-1),t=this.resolvedPos.doc.resolve(e);return new Ct(t,this.editor)}get before(){let e=this.resolvedPos.doc.resolve(this.from-(this.isBlock?1:2));return e.depth!==this.depth&&(e=this.resolvedPos.doc.resolve(this.from-3)),new Ct(e,this.editor)}get after(){let e=this.resolvedPos.doc.resolve(this.to+(this.isBlock?2:1));return e.depth!==this.depth&&(e=this.resolvedPos.doc.resolve(this.to+3)),new Ct(e,this.editor)}get children(){const e=[];return this.node.content.forEach((t,n)=>{const o=t.isBlock&&!t.isTextblock,i=this.pos+n+1,s=this.resolvedPos.doc.resolve(i);if(!o&&s.depth<=this.depth)return;const l=new Ct(s,this.editor,o,o?t:null);o&&(l.actualDepth=this.depth+1),e.push(new Ct(s,this.editor,o,o?t:null))}),e}get firstChild(){return this.children[0]||null}get lastChild(){const e=this.children;return e[e.length-1]||null}closest(e,t={}){let n=null,o=this.parent;for(;o&&!n;){if(o.node.type.name===e)if(Object.keys(t).length>0){const i=o.node.attrs,s=Object.keys(t);for(let l=0;l<s.length;l+=1){const a=s[l];if(i[a]!==t[a])break}}else n=o;o=o.parent}return n}querySelector(e,t={}){return this.querySelectorAll(e,t,!0)[0]||null}querySelectorAll(e,t={},n=!1){let o=[];if(!this.children||this.children.length===0)return o;const i=Object.keys(t);return this.children.forEach(s=>{n&&o.length>0||(s.node.type.name===e&&i.every(l=>t[l]===s.node.attrs[l])&&o.push(s),n&&o.length>0||(o=o.concat(s.querySelectorAll(e,t,n))))}),o}setAttribute(e){const t=this.editor.state.selection;this.editor.chain().setTextSelection(this.from).updateAttributes(this.node.type.name,e).setTextSelection(t.from).run()}}class wc extends oc{constructor(e={}){super(),this.isFocused=!1,this.extensionStorage={},this.options={element:document.createElement("div"),content:"",injectCSS:!0,injectNonce:void 0,extensions:[],autofocus:!1,editable:!0,editorProps:{},parseOptions:{},coreExtensionOptions:{},enableInputRules:!0,enablePasteRules:!0,enableCoreExtensions:!0,onBeforeCreate:()=>null,onCreate:()=>null,onUpdate:()=>null,onSelectionUpdate:()=>null,onTransaction:()=>null,onFocus:()=>null,onBlur:()=>null,onDestroy:()=>null},this.isCapturingTransaction=!1,this.capturedTransaction=null,this.setOptions(e),this.createExtensionManager(),this.createCommandManager(),this.createSchema(),this.on("beforeCreate",this.options.onBeforeCreate),this.emit("beforeCreate",{editor:this}),this.createView(),this.injectCSS(),this.on("create",this.options.onCreate),this.on("update",this.options.onUpdate),this.on("selectionUpdate",this.options.onSelectionUpdate),this.on("transaction",this.options.onTransaction),this.on("focus",this.options.onFocus),this.on("blur",this.options.onBlur),this.on("destroy",this.options.onDestroy),window.setTimeout(()=>{this.isDestroyed||(this.commands.focus(this.options.autofocus),this.emit("create",{editor:this}))},0)}get storage(){return this.extensionStorage}get commands(){return this.commandManager.commands}chain(){return this.commandManager.chain()}can(){return this.commandManager.can()}injectCSS(){this.options.injectCSS&&document&&(this.css=function(e,t,n){const o=document.querySelector("style[data-tiptap-style]");if(o!==null)return o;const i=document.createElement("style");return t&&i.setAttribute("nonce",t),i.setAttribute("data-tiptap-style",""),i.innerHTML=e,document.getElementsByTagName("head")[0].appendChild(i),i}(`.ProseMirror {
  position: relative;
}

.ProseMirror {
  word-wrap: break-word;
  white-space: pre-wrap;
  white-space: break-spaces;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  font-feature-settings: "liga" 0; /* the above doesn't seem to work in Edge */
}

.ProseMirror [contenteditable="false"] {
  white-space: normal;
}

.ProseMirror [contenteditable="false"] [contenteditable="true"] {
  white-space: pre-wrap;
}

.ProseMirror pre {
  white-space: pre-wrap;
}

img.ProseMirror-separator {
  display: inline !important;
  border: none !important;
  margin: 0 !important;
  width: 1px !important;
  height: 1px !important;
}

.ProseMirror-gapcursor {
  display: none;
  pointer-events: none;
  position: absolute;
  margin: 0;
}

.ProseMirror-gapcursor:after {
  content: "";
  display: block;
  position: absolute;
  top: -2px;
  width: 20px;
  border-top: 1px solid black;
  animation: ProseMirror-cursor-blink 1.1s steps(2, start) infinite;
}

@keyframes ProseMirror-cursor-blink {
  to {
    visibility: hidden;
  }
}

.ProseMirror-hideselection *::selection {
  background: transparent;
}

.ProseMirror-hideselection *::-moz-selection {
  background: transparent;
}

.ProseMirror-hideselection * {
  caret-color: transparent;
}

.ProseMirror-focused .ProseMirror-gapcursor {
  display: block;
}

.tippy-box[data-animation=fade][data-state=hidden] {
  opacity: 0
}`,this.options.injectNonce))}setOptions(e={}){this.options={...this.options,...e},this.view&&this.state&&!this.isDestroyed&&(this.options.editorProps&&this.view.setProps(this.options.editorProps),this.view.updateState(this.state))}setEditable(e,t=!0){this.setOptions({editable:e}),t&&this.emit("update",{editor:this,transaction:this.state.tr})}get isEditable(){return this.options.editable&&this.view&&this.view.editable}get state(){return this.view.state}registerPlugin(e,t){const n=pl(t)?t(e,[...this.state.plugins]):[...this.state.plugins,e],o=this.state.reconfigure({plugins:n});this.view.updateState(o)}unregisterPlugin(e){if(this.isDestroyed)return;const t=typeof e=="string"?`${e}$`:e.key,n=this.state.reconfigure({plugins:this.state.plugins.filter(o=>!o.key.startsWith(t))});this.view.updateState(n)}createExtensionManager(){var e,t;const n=[...this.options.enableCoreExtensions?[mc,dc.configure({blockSeparator:(t=(e=this.options.coreExtensionOptions)===null||e===void 0?void 0:e.clipboardTextSerializer)===null||t===void 0?void 0:t.blockSeparator}),fc,gc,Cc,yc]:[],...this.options.extensions].filter(o=>["extension","node","mark"].includes(o==null?void 0:o.type));this.extensionManager=new Wt(n,this)}createCommandManager(){this.commandManager=new Xn({editor:this})}createSchema(){this.schema=this.extensionManager.schema}createView(){const e=yl(this.options.content,this.schema,this.options.parseOptions),t=ml(e,this.options.autofocus);this.view=new Ua(this.options.element,{...this.options.editorProps,dispatchTransaction:this.dispatchTransaction.bind(this),state:Jt.create({doc:e,selection:t||void 0})});const n=this.state.reconfigure({plugins:this.extensionManager.plugins});this.view.updateState(n),this.createNodeViews(),this.prependClass(),this.view.dom.editor=this}createNodeViews(){this.view.setProps({nodeViews:this.extensionManager.nodeViews})}prependClass(){this.view.dom.className=`tiptap ${this.view.dom.className}`}captureTransaction(e){this.isCapturingTransaction=!0,e(),this.isCapturingTransaction=!1;const t=this.capturedTransaction;return this.capturedTransaction=null,t}dispatchTransaction(e){if(this.view.isDestroyed)return;if(this.isCapturingTransaction)return this.capturedTransaction?void e.steps.forEach(s=>{var l;return(l=this.capturedTransaction)===null||l===void 0?void 0:l.step(s)}):void(this.capturedTransaction=e);const t=this.state.apply(e),n=!this.state.selection.eq(t.selection);this.view.updateState(t),this.emit("transaction",{editor:this,transaction:e}),n&&this.emit("selectionUpdate",{editor:this,transaction:e});const o=e.getMeta("focus"),i=e.getMeta("blur");o&&this.emit("focus",{editor:this,event:o.event,transaction:e}),i&&this.emit("blur",{editor:this,event:i.event,transaction:e}),e.docChanged&&!e.getMeta("preventUpdate")&&this.emit("update",{editor:this,transaction:e})}getAttributes(e){return pc(this.state,e)}isActive(e,t){const n=typeof e=="string"?e:null,o=typeof e=="string"?t:e;return function(i,s,l={}){if(!s)return un(i,null,l)||Wo(i,null,l);const a=qn(s,i.schema);return a==="node"?un(i,s,l):a==="mark"&&Wo(i,s,l)}(this.state,n,o)}getJSON(){return this.state.doc.toJSON()}getHTML(){return function(e,t){const n=Lt.fromSchema(t).serializeFragment(e),o=document.implementation.createHTMLDocument().createElement("div");return o.appendChild(n),o.innerHTML}(this.state.doc.content,this.schema)}getText(e){const{blockSeparator:t=`

`,textSerializers:n={}}=e||{};return function(o,i){return ul(o,{from:0,to:o.content.size},i)}(this.state.doc,{blockSeparator:t,textSerializers:{...fl(this.schema),...n}})}get isEmpty(){return function(e){var t;const n=(t=e.type.createAndFill())===null||t===void 0?void 0:t.toJSON(),o=e.toJSON();return JSON.stringify(n)===JSON.stringify(o)}(this.state.doc)}getCharacterCount(){return console.warn('[tiptap warn]: "editor.getCharacterCount()" is deprecated. Please use "editor.storage.characterCount.characters()" instead.'),this.state.doc.content.size-2}destroy(){this.emit("destroy"),this.view&&this.view.destroy(),this.removeAllListeners()}get isDestroyed(){var e;return!(!((e=this.view)===null||e===void 0)&&e.docView)}$node(e,t){var n;return((n=this.$doc)===null||n===void 0?void 0:n.querySelector(e,t))||null}$nodes(e,t){var n;return((n=this.$doc)===null||n===void 0?void 0:n.querySelectorAll(e,t))||null}$pos(e){const t=this.state.doc.resolve(e);return new Ct(t,this)}get $doc(){return this.$pos(0)}}function Ud(r){return new ic({find:r.find,handler:({state:e,range:t,match:n})=>{const o=F(r.getAttributes,void 0,n)||{},{tr:i}=e,s=t.from;let l=t.to;const a=r.type.create(o);if(n[1]){let c=s+n[0].lastIndexOf(n[1]);c>l?c=l:l=c+n[1].length;const d=n[0][n[0].length-1];i.insertText(d,s+n[0].length-1),i.replaceWith(c,l,a)}else n[0]&&i.insert(s-1,r.type.create(o)).delete(i.mapping.map(s),i.mapping.map(l));i.scrollIntoView()}})}class Jn{constructor(e={}){this.type="mark",this.name="mark",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...e},this.name=this.config.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=F($(this,"addOptions",{name:this.name}))),this.storage=F($(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(e={}){return new Jn(e)}configure(e={}){const t=this.extend();return t.options=Qn(this.options,e),t.storage=F($(t,"addStorage",{name:t.name,options:t.options})),t}extend(e={}){const t=new Jn({...this.config,...e});return t.parent=this,this.child=t,t.name=e.name?e.name:t.parent.name,e.defaultOptions&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${t.name}".`),t.options=F($(t,"addOptions",{name:t.name})),t.storage=F($(t,"addStorage",{name:t.name,options:t.options})),t}static handleExit({editor:e,mark:t}){const{tr:n}=e.state,o=e.state.selection.$from;if(o.pos===o.end()){const i=o.marks();if(!i.find(l=>(l==null?void 0:l.type.name)===t.name))return!1;const s=i.find(l=>(l==null?void 0:l.type.name)===t.name);return s&&n.removeStoredMark(s),n.insertText(" ",o.pos),e.view.dispatch(n),!0}return!1}}class pt{constructor(e={}){this.type="node",this.name="node",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...e},this.name=this.config.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=F($(this,"addOptions",{name:this.name}))),this.storage=F($(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(e={}){return new pt(e)}configure(e={}){const t=this.extend();return t.options=Qn(this.options,e),t.storage=F($(t,"addStorage",{name:t.name,options:t.options})),t}extend(e={}){const t=new pt({...this.config,...e});return t.parent=this,this.child=t,t.name=e.name?e.name:t.parent.name,e.defaultOptions&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${t.name}".`),t.options=F($(t,"addOptions",{name:t.name})),t.storage=F($(t,"addStorage",{name:t.name,options:t.options})),t}}const vc=pt.create({name:"paragraph",priority:1e3,addOptions:()=>({HTMLAttributes:{}}),group:"block",content:"inline*",parseHTML:()=>[{tag:"p"}],renderHTML({HTMLAttributes:r}){return["p",xt(this.options.HTMLAttributes,r),0]},addCommands(){return{setParagraph:()=>({commands:r})=>r.setNode(this.name)}},addKeyboardShortcuts(){return{"Mod-Alt-0":()=>this.editor.commands.setParagraph()}}}),xc=pt.create({name:"hardBreak",addOptions:()=>({keepMarks:!0,HTMLAttributes:{}}),inline:!0,group:"inline",selectable:!1,parseHTML:()=>[{tag:"br"}],renderHTML({HTMLAttributes:r}){return["br",xt(this.options.HTMLAttributes,r)]},renderText:()=>`
`,addCommands(){return{setHardBreak:()=>({commands:r,chain:e,state:t,editor:n})=>r.first([()=>r.exitCode(),()=>r.command(()=>{const{selection:o,storedMarks:i}=t;if(o.$from.parent.type.spec.isolating)return!1;const{keepMarks:s}=this.options,{splittableMarks:l}=n.extensionManager,a=i||o.$to.parentOffset&&o.$from.marks();return e().insertContent({type:this.name}).command(({tr:c,dispatch:d})=>{if(d&&a&&s){const h=a.filter(p=>l.includes(p.type.name));c.ensureMarks(h)}return!0}).run()})])}},addKeyboardShortcuts(){return{"Mod-Enter":()=>this.editor.commands.setHardBreak(),"Shift-Enter":()=>this.editor.commands.setHardBreak()}}}),bc=pt.create({name:"text",group:"inline"}),kc=pt.create({name:"doc",topNode:!0,content:"block+"});var Kn=200,re=function(){};re.prototype.append=function(r){return r.length?(r=re.from(r),!this.length&&r||r.length<Kn&&this.leafAppend(r)||this.length<Kn&&r.leafPrepend(this)||this.appendInner(r)):this},re.prototype.prepend=function(r){return r.length?re.from(r).append(this):this},re.prototype.appendInner=function(r){return new Mc(this,r)},re.prototype.slice=function(r,e){return r===void 0&&(r=0),e===void 0&&(e=this.length),r>=e?re.empty:this.sliceInner(Math.max(0,r),Math.min(this.length,e))},re.prototype.get=function(r){if(!(r<0||r>=this.length))return this.getInner(r)},re.prototype.forEach=function(r,e,t){e===void 0&&(e=0),t===void 0&&(t=this.length),e<=t?this.forEachInner(r,e,t,0):this.forEachInvertedInner(r,e,t,0)},re.prototype.map=function(r,e,t){e===void 0&&(e=0),t===void 0&&(t=this.length);var n=[];return this.forEach(function(o,i){return n.push(r(o,i))},e,t),n},re.from=function(r){return r instanceof re?r:r&&r.length?new vl(r):re.empty};var vl=function(r){function e(n){r.call(this),this.values=n}r&&(e.__proto__=r),e.prototype=Object.create(r&&r.prototype),e.prototype.constructor=e;var t={length:{configurable:!0},depth:{configurable:!0}};return e.prototype.flatten=function(){return this.values},e.prototype.sliceInner=function(n,o){return n==0&&o==this.length?this:new e(this.values.slice(n,o))},e.prototype.getInner=function(n){return this.values[n]},e.prototype.forEachInner=function(n,o,i,s){for(var l=o;l<i;l++)if(n(this.values[l],s+l)===!1)return!1},e.prototype.forEachInvertedInner=function(n,o,i,s){for(var l=o-1;l>=i;l--)if(n(this.values[l],s+l)===!1)return!1},e.prototype.leafAppend=function(n){if(this.length+n.length<=Kn)return new e(this.values.concat(n.flatten()))},e.prototype.leafPrepend=function(n){if(this.length+n.length<=Kn)return new e(n.flatten().concat(this.values))},t.length.get=function(){return this.values.length},t.depth.get=function(){return 0},Object.defineProperties(e.prototype,t),e}(re);re.empty=new vl([]);var Mc=function(r){function e(t,n){r.call(this),this.left=t,this.right=n,this.length=t.length+n.length,this.depth=Math.max(t.depth,n.depth)+1}return r&&(e.__proto__=r),e.prototype=Object.create(r&&r.prototype),e.prototype.constructor=e,e.prototype.flatten=function(){return this.left.flatten().concat(this.right.flatten())},e.prototype.getInner=function(t){return t<this.left.length?this.left.get(t):this.right.get(t-this.left.length)},e.prototype.forEachInner=function(t,n,o,i){var s=this.left.length;return!(n<s&&this.left.forEachInner(t,n,Math.min(o,s),i)===!1)&&!(o>s&&this.right.forEachInner(t,Math.max(n-s,0),Math.min(this.length,o)-s,i+s)===!1)&&void 0},e.prototype.forEachInvertedInner=function(t,n,o,i){var s=this.left.length;return!(n>s&&this.right.forEachInvertedInner(t,n-s,Math.max(o,s)-s,i+s)===!1)&&!(o<s&&this.left.forEachInvertedInner(t,Math.min(n,s),o,i)===!1)&&void 0},e.prototype.sliceInner=function(t,n){if(t==0&&n==this.length)return this;var o=this.left.length;return n<=o?this.left.slice(t,n):t>=o?this.right.slice(t-o,n-o):this.left.slice(t,o).append(this.right.slice(0,n-o))},e.prototype.leafAppend=function(t){var n=this.right.leafAppend(t);if(n)return new e(this.left,n)},e.prototype.leafPrepend=function(t){var n=this.left.leafPrepend(t);if(n)return new e(n,this.right)},e.prototype.appendInner=function(t){return this.left.depth>=Math.max(this.right.depth,t.depth)+1?new e(this.left,new e(this.right,t)):new e(this,t)},e}(re);class Ie{constructor(e,t){this.items=e,this.eventCount=t}popEvent(e,t){if(this.eventCount==0)return null;let n,o,i=this.items.length;for(;;i--)if(this.items.get(i-1).selection){--i;break}t&&(n=this.remapping(i,this.items.length),o=n.maps.length);let s,l,a=e.tr,c=[],d=[];return this.items.forEach((h,p)=>{if(!h.step)return n||(n=this.remapping(i,p+1),o=n.maps.length),o--,void d.push(h);if(n){d.push(new _e(h.map));let f,u=h.step.map(n.slice(o));u&&a.maybeStep(u).doc&&(f=a.mapping.maps[a.mapping.maps.length-1],c.push(new _e(f,void 0,void 0,c.length+d.length))),o--,f&&n.appendMap(f,o)}else a.maybeStep(h.step);return h.selection?(s=n?h.selection.map(n.slice(o)):h.selection,l=new Ie(this.items.slice(0,i).append(d.reverse().concat(c)),this.eventCount-1),!1):void 0},this.items.length,0),{remaining:l,transform:a,selection:s}}addTransform(e,t,n,o){let i=[],s=this.eventCount,l=this.items,a=!o&&l.length?l.get(l.length-1):null;for(let d=0;d<e.steps.length;d++){let h,p=e.steps[d].invert(e.docs[d]),f=new _e(e.mapping.maps[d],p,t);(h=a&&a.merge(f))&&(f=h,d?i.pop():l=l.slice(0,l.length-1)),i.push(f),t&&(s++,t=void 0),o||(a=f)}let c=s-n.depth;return c>Sc&&(l=function(d,h){let p;return d.forEach((f,u)=>{if(f.selection&&h--==0)return p=u,!1}),d.slice(p)}(l,c),s-=c),new Ie(l.append(i),s)}remapping(e,t){let n=new Zt;return this.items.forEach((o,i)=>{let s=o.mirrorOffset!=null&&i-o.mirrorOffset>=e?n.maps.length-o.mirrorOffset:void 0;n.appendMap(o.map,s)},e,t),n}addMaps(e){return this.eventCount==0?this:new Ie(this.items.append(e.map(t=>new _e(t))),this.eventCount)}rebased(e,t){if(!this.eventCount)return this;let n=[],o=Math.max(0,this.items.length-t),i=e.mapping,s=e.steps.length,l=this.eventCount;this.items.forEach(p=>{p.selection&&l--},o);let a=t;this.items.forEach(p=>{let f=i.getMirror(--a);if(f==null)return;s=Math.min(s,f);let u=i.maps[f];if(p.step){let m=e.steps[f].invert(e.docs[f]),g=p.selection&&p.selection.map(i.slice(a+1,f));g&&l++,n.push(new _e(u,m,g))}else n.push(new _e(u))},o);let c=[];for(let p=t;p<s;p++)c.push(new _e(i.maps[p]));let d=this.items.slice(0,o).append(c).append(n),h=new Ie(d,l);return h.emptyItemCount()>500&&(h=h.compress(this.items.length-n.length)),h}emptyItemCount(){let e=0;return this.items.forEach(t=>{t.step||e++}),e}compress(e=this.items.length){let t=this.remapping(0,e),n=t.maps.length,o=[],i=0;return this.items.forEach((s,l)=>{if(l>=e)o.push(s),s.selection&&i++;else if(s.step){let a=s.step.map(t.slice(n)),c=a&&a.getMap();if(n--,c&&t.appendMap(c,n),a){let d=s.selection&&s.selection.map(t.slice(n));d&&i++;let h,p=new _e(c.invert(),a,d),f=o.length-1;(h=o.length&&o[f].merge(p))?o[f]=h:o.push(p)}}else s.map&&n--},this.items.length,0),new Ie(re.from(o.reverse()),i)}}Ie.empty=new Ie(re.empty,0);class _e{constructor(e,t,n,o){this.map=e,this.step=t,this.selection=n,this.mirrorOffset=o}merge(e){if(this.step&&e.step&&!e.selection){let t=e.step.merge(this.step);if(t)return new _e(t.getMap().invert(),t,this.selection)}}}class Qe{constructor(e,t,n,o,i){this.done=e,this.undone=t,this.prevRanges=n,this.prevTime=o,this.prevComposition=i}}const Sc=20;function Ri(r){let e=[];return r.forEach((t,n,o,i)=>e.push(o,i)),e}function Oo(r,e){if(!r)return null;let t=[];for(let n=0;n<r.length;n+=2){let o=e.map(r[n],1),i=e.map(r[n+1],-1);o<=i&&t.push(o,i)}return t}let To=!1,zi=null;function $n(r){let e=r.plugins;if(zi!=e){To=!1,zi=e;for(let t=0;t<e.length;t++)if(e[t].spec.historyPreserveItems){To=!0;break}}return To}const kt=new Oe("history"),Oc=new Oe("closeHistory");function Tc(r={}){return r={depth:r.depth||100,newGroupDelay:r.newGroupDelay||500},new Te({key:kt,state:{init:()=>new Qe(Ie.empty,Ie.empty,null,0,-1),apply:(e,t,n)=>function(o,i,s,l){let a,c=s.getMeta(kt);if(c)return c.historyState;s.getMeta(Oc)&&(o=new Qe(o.done,o.undone,null,0,-1));let d=s.getMeta("appendedTransaction");if(s.steps.length==0)return o;if(d&&d.getMeta(kt))return d.getMeta(kt).redo?new Qe(o.done.addTransform(s,void 0,l,$n(i)),o.undone,Ri(s.mapping.maps[s.steps.length-1]),o.prevTime,o.prevComposition):new Qe(o.done,o.undone.addTransform(s,void 0,l,$n(i)),null,o.prevTime,o.prevComposition);if(s.getMeta("addToHistory")===!1||d&&d.getMeta("addToHistory")===!1)return(a=s.getMeta("rebased"))?new Qe(o.done.rebased(s,a),o.undone.rebased(s,a),Oo(o.prevRanges,s.mapping),o.prevTime,o.prevComposition):new Qe(o.done.addMaps(s.mapping.maps),o.undone.addMaps(s.mapping.maps),Oo(o.prevRanges,s.mapping),o.prevTime,o.prevComposition);{let h=s.getMeta("composition"),p=o.prevTime==0||!d&&o.prevComposition!=h&&(o.prevTime<(s.time||0)-l.newGroupDelay||!function(u,m){if(!m)return!1;if(!u.docChanged)return!0;let g=!1;return u.mapping.maps[0].forEach((C,w)=>{for(let v=0;v<m.length;v+=2)C<=m[v+1]&&w>=m[v]&&(g=!0)}),g}(s,o.prevRanges)),f=d?Oo(o.prevRanges,s.mapping):Ri(s.mapping.maps[s.steps.length-1]);return new Qe(o.done.addTransform(s,p?i.selection.getBookmark():void 0,l,$n(i)),Ie.empty,f,s.time,h??o.prevComposition)}}(t,n,e,r)},config:r,props:{handleDOMEvents:{beforeinput(e,t){let n=t.inputType,o=n=="historyUndo"?bl:n=="historyRedo"?kl:null;return!!o&&(t.preventDefault(),o(e.state,e.dispatch))}}}})}function xl(r,e){return(t,n)=>{let o=kt.getState(t);if(!o||(r?o.undone:o.done).eventCount==0)return!1;if(n){let i=function(s,l,a){let c=$n(l),d=kt.get(l).spec.config,h=(a?s.undone:s.done).popEvent(l,c);if(!h)return null;let p=h.selection.resolve(h.transform.doc),f=(a?s.done:s.undone).addTransform(h.transform,l.selection.getBookmark(),d,c),u=new Qe(a?f:h.remaining,a?h.remaining:f,null,0,-1);return h.transform.setSelection(p).setMeta(kt,{redo:a,historyState:u})}(o,t,r);i&&n(e?i.scrollIntoView():i)}return!0}}const bl=xl(!1,!0),kl=xl(!0,!0),Ec=Ae.create({name:"history",addOptions:()=>({depth:100,newGroupDelay:500}),addCommands:()=>({undo:()=>({state:r,dispatch:e})=>bl(r,e),redo:()=>({state:r,dispatch:e})=>kl(r,e)}),addProseMirrorPlugins(){return[Tc(this.options)]},addKeyboardShortcuts(){return{"Mod-z":()=>this.editor.commands.undo(),"Shift-Mod-z":()=>this.editor.commands.redo(),"Mod-y":()=>this.editor.commands.redo(),"Mod-я":()=>this.editor.commands.undo(),"Shift-Mod-я":()=>this.editor.commands.redo()}}});class Nc{constructor(e){x(this,"_registeredPlugins",lt([]));x(this,"_defaultPlugins");x(this,"_allPlugins");x(this,"_getDefaultPlugins",e=>[kc.extend({addKeyboardShortcuts:()=>({...e.keyboardShortcuts})}),vc,bc,xc,Ec.configure({depth:100,newGroupDelay:750})].map(t=>({tipTapExtension:t})));x(this,"registerPlugin",e=>(this._registeredPlugins.update(t=>[...t,e]),()=>{this._registeredPlugins.update(t=>t.filter(n=>n!==e))}));x(this,"onPluginsChanged",e=>this._allPlugins.subscribe(e));this._opts=e,this._defaultPlugins=et(this._opts,this._getDefaultPlugins),this._allPlugins=et([this._defaultPlugins,this._registeredPlugins],([t,n])=>[...t,...n])}get tipTapExtensions(){return he(this._allPlugins).map(e=>e.tipTapExtension)}}function Bi(r){return r.length===0?[]:[{type:"text",text:r}]}function _i(r){if(r!==void 0){if(typeof r=="string"){const e=r.split(`
`).map((t,n)=>n===0?Bi(t):[{type:"hardBreak"},...Bi(t)]).flat();return e.length>0?e:void 0}return Array.isArray(r)?r.length>0?r:void 0:r}}class $c{constructor(e){x(this,"_setupFns",[]);x(this,"_editor");x(this,"registerEditor",e=>{this._editor=e,this._runSetupFns()});x(this,"unregisterEditor",()=>{this._editor=void 0});x(this,"can",()=>{var e;return(e=this._editor)==null?void 0:e.can()});x(this,"chain",()=>{var e;return(e=this._editor)==null?void 0:e.chain()});x(this,"_queueOrRun",e=>{this._editor?e():this._setupFns.push(e)});x(this,"_runSetupFns",()=>{this._setupFns.forEach(e=>{e()}),this._setupFns=[]});x(this,"hide",()=>this._queueOrRun(this._hide));x(this,"show",()=>this._queueOrRun(this._show));x(this,"focus",()=>this._queueOrRun(this._focus));x(this,"requestFocus",()=>this._queueOrRun(this._requestFocus));x(this,"blur",()=>this._queueOrRun(this._blur));x(this,"scrollToCursor",()=>this._queueOrRun(this._scrollToCursor));x(this,"setEditable",e=>this._queueOrRun(()=>this._setEditable(e)));x(this,"clearContent",()=>this._queueOrRun(this._clearContent));x(this,"setContent",(e,t)=>this._queueOrRun(()=>this._setContent(e,t)));x(this,"insertContent",e=>this._queueOrRun(()=>this._insertContent(e)));x(this,"_hide",()=>{this._editor&&(this._editor.view.dom.style.display="none")});x(this,"_show",()=>{this._editor&&this._editor.view.dom.style.removeProperty("display")});x(this,"_focus",()=>{var e;return(e=this._editor)==null?void 0:e.commands.focus()});x(this,"_requestFocus",async()=>{await Pl(),this._editor&&(document.activeElement&&document.activeElement!==document.body&&!this._editor.view.dom.contains(document.activeElement)||this._editor.commands.focus())});x(this,"_blur",()=>{var e;return(e=this._editor)==null?void 0:e.commands.blur()});x(this,"_scrollToCursor",()=>{var e;return(e=this._editor)==null?void 0:e.commands.scrollIntoView()});x(this,"_setEditable",e=>{var t,n;(t=this._editor)==null||t.setEditable(e),e&&((n=this._editor)==null||n.commands.setTextSelection(this._editor.state.doc.content.size))});x(this,"_clearContent",()=>{var t;const e=he(this._opts.content);e&&e.rawText!==""&&((t=this._editor)==null||t.commands.clearContent(!0))});x(this,"_setContent",(e,t)=>{var o;const n=he(this._opts.content);e!==(n==null?void 0:n.rawText)&&e!==(n==null?void 0:n.richTextJsonRepr)&&((e=_i(e??[]))!==void 0?(o=this._editor)==null||o.chain().setContent(e,!0,{preserveWhitespace:!0}).setTextSelection((t==null?void 0:t.cursorPosition)==="start"?0:1e20).run():this._clearContent())});x(this,"_insertContent",e=>{var n;const t=_i(e);t!==void 0&&((n=this._editor)==null||n.commands.insertContent(t,{parseOptions:{preserveWhitespace:!0}}))});this._opts=e}get commands(){var e;return(e=this._editor)==null?void 0:e.commands}}class Dc{constructor(){x(this,"_isFocused",lt(!1));x(this,"_isEditable",lt(!1));x(this,"_content",lt(void 0));x(this,"_disposers",[]);x(this,"registerEditor",e=>{this._isFocused.set(e.isFocused),this._isEditable.set(e.isEditable),this._content.set({richTextJsonRepr:e.getJSON(),rawText:e.getText()});const t=()=>this._isFocused.set(!0),n=()=>this._isFocused.set(!1),o=()=>this._isEditable.set(e.isEditable),i=()=>{this._content.set({richTextJsonRepr:e.getJSON(),rawText:e.getText()})};e.on("focus",t),e.on("blur",n),e.on("update",o),e.on("update",i),this._disposers.push(()=>e.off("focus",t),()=>e.off("blur",n),()=>e.off("update",o),()=>e.off("update",i))});x(this,"unregisterEditor",()=>{this._isFocused.set(!1),this._isEditable.set(!1),this._disposers.forEach(e=>e()),this._disposers=[]});x(this,"onFocus",e=>this.onFocusChanged(t=>{t&&e()}));x(this,"onBlur",e=>this.onFocusChanged(t=>{!t&&e()}));x(this,"onFocusChanged",e=>this._isFocused.subscribe(e));x(this,"onEditableChanged",e=>this._isEditable.subscribe(e));x(this,"onContentChanged",e=>this._content.subscribe(t=>t&&e(t)))}get isFocused(){return this._isFocused}get isEditable(){return this._isEditable}get content(){return this._content}}const yt=class yt{constructor(){x(this,"_opts",lt({}));x(this,"_editor");x(this,"_rootNode");x(this,"pluginManager",new Nc(this._opts));x(this,"eventManager",new Dc);x(this,"commandManager",new $c({content:this.eventManager.content}));x(this,"instanceId",`augment-rich-text-editor-${yt._getNextInstanceIdx()}`);x(this,"registerRoot",(e,t)=>(this._destroyEditor(),this._rootNode=e,this._opts.set(t),this._initializeEditor(),{update:n=>{this._opts.set(n)},destroy:()=>{this._destroyEditor()}}));x(this,"_registerEditorWithManagers",e=>{this.eventManager.registerEditor(e),this.commandManager.registerEditor(e)});x(this,"_unregisterEditorFromManagers",()=>{this.commandManager.unregisterEditor(),this.eventManager.unregisterEditor()});x(this,"_reinitializeEditor",()=>{const e=he(this.eventManager.content);this._destroyEditor(),this._initializeEditor(),e!==void 0&&this.commandManager.setContent(e.richTextJsonRepr)});x(this,"_initializeEditor",()=>{if(this._rootNode===void 0||this._editor!==void 0)return;const e=he(this._opts),t={element:document.createElement("div"),editable:e.editable??!0,injectCSS:!0,extensions:this.pluginManager.tipTapExtensions,onCreate:({editor:n})=>{this._registerEditorWithManagers(n),he(this._opts).focusOnInit&&this.commandManager.focus();const o=this._attachCopyHandler();n.on("destroy",o);const i=he(this._opts).onFocus;i&&n.on("focus",i);const s=he(this._opts).onBlur;s&&n.on("blur",s)},onDestroy:()=>{this._unregisterEditorFromManagers()},onSelectionUpdate:()=>{(he(this._opts).editable??1)&&this.commandManager.scrollToCursor()},editorProps:{handlePaste:(n,o)=>{var s;if(this._isEventFromRichTextEditor(o))return!1;const i=(s=o.clipboardData)==null?void 0:s.getData("text/plain");return!!i&&(this.commandManager.insertContent(i),!0)},attributes:{style:"min-height: 100%; outline: none;","data-testid":"design-system-rich-text-editor-tiptap"}}};return this._editor=new wc(t),this._rootNode.appendChild(this._editor.view.dom),this._editor});x(this,"_attachCopyHandler",()=>{var e,t;return(e=this._rootNode)==null||e.addEventListener("copy",this._copyHandler),(t=this._rootNode)==null||t.addEventListener("cut",this._copyHandler),()=>{var n,o;(n=this._rootNode)==null||n.removeEventListener("copy",this._copyHandler),(o=this._rootNode)==null||o.removeEventListener("cut",this._copyHandler)}});x(this,"_copyHandler",e=>{var t;(t=e.clipboardData)==null||t.setData("application/x-augment/rich-text","true")});x(this,"_isEventFromRichTextEditor",e=>{var t;return((t=e.clipboardData)==null?void 0:t.getData("application/x-augment/rich-text"))==="true"});x(this,"_destroyEditor",()=>{var e,t;this._unregisterEditorFromManagers(),(e=this._editor)==null||e.view.dom.remove(),(t=this._editor)==null||t.destroy(),this._editor=void 0});this.pluginManager.onPluginsChanged(this._reinitializeEditor)}get rootNode(){var e;return(e=this._editor)==null?void 0:e.view.dom}};x(yt,"CONTEXT_KEY","augment-rich-text-editor"),x(yt,"INSTANCE_IDX",0),x(yt,"_getNextInstanceIdx",()=>yt.INSTANCE_IDX++);let Be=yt;function Zd(){const r=Ke(Be.CONTEXT_KEY);if(!r)throw new Error(`No editor context '${Be.CONTEXT_KEY}' found.`);return r}function Ac(r,e,t){let{onContentChanged:n=()=>{}}=e,{content:o=""}=e;const i=Ke(Be.CONTEXT_KEY);let s;return r.$$set=l=>{"onContentChanged"in l&&t(0,n=l.onContentChanged),"content"in l&&t(1,o=l.content)},r.$$.update=()=>{5&r.$$.dirty&&(s==null||s(),t(2,s=i.eventManager.onContentChanged(n))),2&r.$$.dirty&&i.commandManager.setContent(o)},[n,o,s]}const Lc=r=>({}),Vi=r=>({}),Ic=r=>({}),Fi=r=>({}),Pc=r=>({}),Hi=r=>({});function ji(r){let e,t;const n=r[10].banner,o=Ce(n,r,r[13],Hi);return{c(){e=be("div"),o&&o.c(),y(e,"class","c-rich-text-editor-augment__banner svelte-bpjcmz")},m(i,s){Z(i,e,s),o&&o.m(e,null),t=!0},p(i,s){o&&o.p&&(!t||8192&s)&&ye(o,n,i,i[13],t?ve(n,i[13],s,Pc):we(i[13]),Hi)},i(i){t||(I(o,i),t=!0)},o(i){z(o,i),t=!1},d(i){i&&U(e),o&&o.d(i)}}}function Rc(r){let e,t,n,o,i,s,l,a,c,d=r[7].banner&&ji(r);const h=r[10].header,p=Ce(h,r,r[13],Fi),f=r[10].default,u=Ce(f,r,r[13],null),m=r[10].footer,g=Ce(m,r,r[13],Vi);return{c(){d&&d.c(),e=Pe(),t=be("div"),p&&p.c(),n=Pe(),o=be("div"),u&&u.c(),s=Pe(),g&&g.c(),y(o,"class","c-rich-text-editor-augment__editor svelte-bpjcmz"),y(t,"class","l-rich-text-editor-augment svelte-bpjcmz"),y(t,"role","button"),y(t,"tabindex","-1")},m(C,w){d&&d.m(C,w),Z(C,e,w),Z(C,t,w),p&&p.m(t,null),E(t,n),E(t,o),u&&u.m(o,null),E(t,s),g&&g.m(t,null),l=!0,a||(c=[Rl(i=r[6].registerRoot(o,{editable:r[0],focusOnInit:r[1],onFocus:r[2],onBlur:r[3]})),Ht(t,"keydown",r[5]),Ht(t,"click",r[5]),Ht(t,"click",r[11]),Ht(t,"dblclick",r[12])],a=!0)},p(C,w){C[7].banner?d?(d.p(C,w),128&w&&I(d,1)):(d=ji(C),d.c(),I(d,1),d.m(e.parentNode,e)):d&&(He(),z(d,1,1,()=>{d=null}),je()),p&&p.p&&(!l||8192&w)&&ye(p,h,C,C[13],l?ve(h,C[13],w,Ic):we(C[13]),Fi),u&&u.p&&(!l||8192&w)&&ye(u,f,C,C[13],l?ve(f,C[13],w,null):we(C[13]),null),i&&zl(i.update)&&15&w&&i.update.call(null,{editable:C[0],focusOnInit:C[1],onFocus:C[2],onBlur:C[3]}),g&&g.p&&(!l||8192&w)&&ye(g,m,C,C[13],l?ve(m,C[13],w,Lc):we(C[13]),Vi)},i(C){l||(I(d),I(p,C),I(u,C),I(g,C),l=!0)},o(C){z(d),z(p,C),z(u,C),z(g,C),l=!1},d(C){C&&(U(e),U(t)),d&&d.d(C),p&&p.d(C),u&&u.d(C),g&&g.d(C),a=!1,ps(c)}}}function zc(r){let e,t;return e=new Bl({props:{class:"c-rich-text-editor-augment",insetContent:!0,size:r[4],$$slots:{default:[Rc]},$$scope:{ctx:r}}}),{c(){Ne(e.$$.fragment)},m(n,o){$e(e,n,o),t=!0},p(n,[o]){const i={};16&o&&(i.size=n[4]),8335&o&&(i.$$scope={dirty:o,ctx:n}),e.$set(i)},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){z(e.$$.fragment,n),t=!1},d(n){De(e,n)}}}function Bc(r,e,t){let{$$slots:n={},$$scope:o}=e;const i=Zo(n);let{editable:s}=e,{focusOnInit:l}=e,{onFocus:a}=e,{onBlur:c}=e,{size:d=1}=e;const h=new Be;return hs(Be.CONTEXT_KEY,h),l&&h.commandManager.requestFocus(),r.$$set=p=>{"editable"in p&&t(0,s=p.editable),"focusOnInit"in p&&t(1,l=p.focusOnInit),"onFocus"in p&&t(2,a=p.onFocus),"onBlur"in p&&t(3,c=p.onBlur),"size"in p&&t(4,d=p.size),"$$scope"in p&&t(13,o=p.$$scope)},[s,l,a,c,d,()=>h.commandManager.focus(),h,i,()=>h.commandManager.requestFocus(),()=>h.commandManager.blur(),n,function(p){cr.call(this,r,p)},function(p){cr.call(this,r,p)},o]}const Gd={Content:class extends Y{constructor(r){super(),X(this,r,Ac,null,Q,{onContentChanged:0,content:1})}},Root:class extends Y{constructor(r){super(),X(this,r,Bc,zc,Q,{editable:0,focusOnInit:1,onFocus:2,onBlur:3,size:4,requestFocus:8,forceFocus:5,blur:9})}get requestFocus(){return this.$$.ctx[8]}get forceFocus(){return this.$$.ctx[5]}get blur(){return this.$$.ctx[9]}}};function _c(r){var e;const{char:t,allowSpaces:n,allowedPrefixes:o,startOfLine:i,$position:s}=r,l=t.replace(/[-/\\^$*+?.()|[\]{}]/g,"\\$&"),a=new RegExp(`\\s${l}$`),c=i?"^":"",d=n?new RegExp(`${c}${l}.*?(?=\\s${l}|$)`,"gm"):new RegExp(`${c}(?:^)?${l}[^\\s${l}]*`,"gm"),h=((e=s.nodeBefore)===null||e===void 0?void 0:e.isText)&&s.nodeBefore.text;if(!h)return null;const p=s.pos-h.length,f=Array.from(h.matchAll(d)).pop();if(!f||f.input===void 0||f.index===void 0)return null;const u=f.input.slice(Math.max(0,f.index-1),f.index),m=new RegExp(`^[${o==null?void 0:o.join("")}\0]?$`).test(u);if(o!==null&&!m)return null;const g=p+f.index;let C=g+f[0].length;return n&&a.test(h.slice(C-1,C+1))&&(f[0]+=" ",C+=1),g<s.pos&&C>=s.pos?{range:{from:g,to:C},query:f[0].slice(t.length),text:f[0]}:null}const Vc=new Oe("suggestion");function Fc({pluginKey:r=Vc,editor:e,char:t="@",allowSpaces:n=!1,allowedPrefixes:o=[" "],startOfLine:i=!1,decorationTag:s="span",decorationClass:l="suggestion",command:a=()=>null,items:c=()=>[],render:d=()=>({}),allow:h=()=>!0,findSuggestionMatch:p=_c}){let f;const u=d==null?void 0:d(),m=new Te({key:r,view(){return{update:async(g,C)=>{var w,v,b,k,N,L,O;const M=(w=this.key)===null||w===void 0?void 0:w.getState(C),A=(v=this.key)===null||v===void 0?void 0:v.getState(g.state),j=M.active&&A.active&&M.range.from!==A.range.from,B=!M.active&&A.active,ee=M.active&&!A.active,P=B||j,q=!B&&!ee&&M.query!==A.query&&!j,K=ee||j;if(!P&&!q&&!K)return;const G=K&&!P?M:A,ke=g.dom.querySelector(`[data-decoration-id="${G.decorationId}"]`);f={editor:e,range:G.range,query:G.query,text:G.text,items:[],command:H=>a({editor:e,range:G.range,props:H}),decorationNode:ke,clientRect:ke?()=>{var H;const{decorationId:It}=(H=this.key)===null||H===void 0?void 0:H.getState(e.state),Pt=g.dom.querySelector(`[data-decoration-id="${It}"]`);return(Pt==null?void 0:Pt.getBoundingClientRect())||null}:null},P&&((b=u==null?void 0:u.onBeforeStart)===null||b===void 0||b.call(u,f)),q&&((k=u==null?void 0:u.onBeforeUpdate)===null||k===void 0||k.call(u,f)),(q||P)&&(f.items=await c({editor:e,query:G.query})),K&&((N=u==null?void 0:u.onExit)===null||N===void 0||N.call(u,f)),q&&((L=u==null?void 0:u.onUpdate)===null||L===void 0||L.call(u,f)),P&&((O=u==null?void 0:u.onStart)===null||O===void 0||O.call(u,f))},destroy:()=>{var g;f&&((g=u==null?void 0:u.onExit)===null||g===void 0||g.call(u,f))}}},state:{init:()=>({active:!1,range:{from:0,to:0},query:null,text:null,composing:!1}),apply(g,C,w,v){const{isEditable:b}=e,{composing:k}=e.view,{selection:N}=g,{empty:L,from:O}=N,M={...C};if(M.composing=k,b&&(L||e.view.composing)){!(O<C.range.from||O>C.range.to)||k||C.composing||(M.active=!1);const A=p({char:t,allowSpaces:n,allowedPrefixes:o,startOfLine:i,$position:N.$from}),j=`id_${Math.floor(4294967295*Math.random())}`;A&&h({editor:e,state:v,range:A.range})?(M.active=!0,M.decorationId=C.decorationId?C.decorationId:j,M.range=A.range,M.query=A.query,M.text=A.text):M.active=!1}else M.active=!1;return M.active||(M.decorationId=null,M.range={from:0,to:0},M.query=null,M.text=null),M}},props:{handleKeyDown(g,C){var w;const{active:v,range:b}=m.getState(g.state);return v&&((w=u==null?void 0:u.onKeyDown)===null||w===void 0?void 0:w.call(u,{view:g,event:C,range:b}))||!1},decorations(g){const{active:C,range:w,decorationId:v}=m.getState(g);return C?te.create(g.doc,[Se.inline(w.from,w.to,{nodeName:s,class:l,"data-decoration-id":v})]):null}}});return m}const Hc=new Oe("mention"),jc=pt.create({name:"mention",addOptions(){return{HTMLAttributes:{},renderText({options:r,node:e}){var t;return`${r.suggestion.char}${(t=e.attrs.label)!==null&&t!==void 0?t:e.attrs.id}`},deleteTriggerWithBackspace:!1,renderHTML({options:r,node:e}){var t;return["span",xt(this.HTMLAttributes,r.HTMLAttributes),`${r.suggestion.char}${(t=e.attrs.label)!==null&&t!==void 0?t:e.attrs.id}`]},suggestion:{char:"@",pluginKey:Hc,command:({editor:r,range:e,props:t})=>{var n,o;const i=r.view.state.selection.$to.nodeAfter;!((n=i==null?void 0:i.text)===null||n===void 0)&&n.startsWith(" ")&&(e.to+=1),r.chain().focus().insertContentAt(e,[{type:this.name,attrs:t},{type:"text",text:" "}]).run(),(o=window.getSelection())===null||o===void 0||o.collapseToEnd()},allow:({state:r,range:e})=>{const t=r.doc.resolve(e.from),n=r.schema.nodes[this.name];return!!t.parent.type.contentMatch.matchType(n)}}}},group:"inline",inline:!0,selectable:!1,atom:!0,addAttributes:()=>({id:{default:null,parseHTML:r=>r.getAttribute("data-id"),renderHTML:r=>r.id?{"data-id":r.id}:{}},label:{default:null,parseHTML:r=>r.getAttribute("data-label"),renderHTML:r=>r.label?{"data-label":r.label}:{}}}),parseHTML(){return[{tag:`span[data-type="${this.name}"]`}]},renderHTML({node:r,HTMLAttributes:e}){if(this.options.renderLabel!==void 0)return console.warn("renderLabel is deprecated use renderText and renderHTML instead"),["span",xt({"data-type":this.name},this.options.HTMLAttributes,e),this.options.renderLabel({options:this.options,node:r})];const t={...this.options};t.HTMLAttributes=xt({"data-type":this.name},this.options.HTMLAttributes,e);const n=this.options.renderHTML({options:t,node:r});return typeof n=="string"?["span",xt({"data-type":this.name},this.options.HTMLAttributes,e),n]:n},renderText({node:r}){return this.options.renderLabel!==void 0?(console.warn("renderLabel is deprecated use renderText and renderHTML instead"),this.options.renderLabel({options:this.options,node:r})):this.options.renderText({options:this.options,node:r})},addKeyboardShortcuts(){return{Backspace:()=>this.editor.commands.command(({tr:r,state:e})=>{let t=!1;const{selection:n}=e,{empty:o,anchor:i}=n;return!!o&&(e.doc.nodesBetween(i-1,i,(s,l)=>{if(s.type.name===this.name)return t=!0,r.insertText(this.options.deleteTriggerWithBackspace?"":this.options.suggestion.char||"",l,l+s.nodeSize),!1}),t)})}},addProseMirrorPlugins(){return[Fc({editor:this.editor,...this.options.suggestion})]}});function qi(r,e){const t=[];return e.$doc.content.descendants(n=>{n.type.name===r&&t.push(n)}),t}function Ji(r,e,t,n={}){const o=[],i=(s,l)=>{var a;(a=n.onNewMention)==null||a.call(n,s,l),o.push(s)};return e.descendants((s,l)=>{if(s.type.name!==r)return;if(t===null)return void i(s,l);const a=Math.abs(e.nodeSize-t.nodeSize),c=t.nodeSize-2,d=a+s.nodeSize+1,h=Math.max(0,l-d),p=Math.min(c,l+d);let f=!1;t.nodesBetween(h,p,u=>{var m;if(u.type.name===r&&u.attrs.id===s.attrs.id)return f=!0,(m=n.onExistingMention)==null||m.call(n,u,l),!1}),f||i(s,l)}),o}function Ml(r,e){const t=new MutationObserver(n=>{for(const o of n)if(e.shouldTrigger(o))return e.callback(),void t.disconnect()});return t.observe(r,{childList:!0,subtree:!0,...e.mutationObserverInit}),e.timeout&&setTimeout(()=>t.disconnect(),e.timeout),()=>t.disconnect()}function Ki(r,e,t,n){return Ml(r,{shouldTrigger:o=>new Set(o.addedNodes).has(e),callback:t,timeout:n})}class Ut{constructor(e){x(this,"_disposers",[]);x(this,"_editor");x(this,"_tooltipData",lt(void 0));x(this,"onCreate",e=>{this._editor=e});x(this,"onDispose",()=>{this._editor=void 0,this._disposers.forEach(e=>e()),this._disposers=[]});x(this,"createMentionChip",({options:e,node:t})=>{var i,s;if(t.type.name!==this._mentionPluginId)throw new Error("Expected a mention node");const n=document.createElement("span");n.innerText=`${((i=e.suggestion)==null?void 0:i.char)??"@"}${t.attrs.label}`;for(const[l,a]of Object.entries(e.HTMLAttributes))typeof a=="string"?n.setAttribute(l,a.toString()):console.warn(`Unexpected HTML attribute value type: [${l}] = ${a}`);const o=(s=this._editor)==null?void 0:s.view.dom;return o&&this._setupMountUnmountCycle(o,n,()=>{this._tooltipData.set({data:t.attrs.data,anchorElement:n})}),n});x(this,"hideTooltip",()=>{this._tooltipData.set(void 0)});this._mentionPluginId=e}get tooltipData(){return this._tooltipData}_attachChipEventListeners(e,t){e.addEventListener("mouseenter",t),e.addEventListener("mouseover",t),e.addEventListener("mouseleave",this.hideTooltip)}_detachChipEventListeners(e,t){e.removeEventListener("mouseenter",t),e.removeEventListener("mouseover",t),e.removeEventListener("mouseleave",this.hideTooltip)}_removeFromDisposers(e){e&&(this._disposers=this._disposers.filter(t=>t!==e))}_setupMountUnmountCycle(e,t,n){let o,i;const s=()=>{this._removeFromDisposers(o),this._detachChipEventListeners(t,n),i=Ki(e,t,l),this._disposers.push(i)},l=()=>{var a,c;this._removeFromDisposers(i),this._attachChipEventListeners(t,n),a=t,o=Ml(e,{shouldTrigger:d=>new Set(d.removedNodes).has(a),callback:s,timeout:c}),this._disposers.push(o)};Ki(e,t,l)}}x(Ut,"CHIP_CLASS_NAME","c-mention-chip"),x(Ut,"CHIP_DATA_ATTR_KEY","data-augment-mention-chip-tooltip");class qc{constructor(e={}){x(this,"_menuData",lt(void 0));x(this,"_mentionables",lt([]));x(this,"query",et(this._menuData,e=>e==null?void 0:e.tiptapExtensionProps.query));x(this,"activeIdx",et([this._mentionables,this._menuData],Kc));x(this,"activeItem",et([this._mentionables,this.activeIdx],Jc));x(this,"referenceClientRect",et(this._menuData,e=>e==null?void 0:e.referenceClientRect));x(this,"tiptapExtensionProps",et(this._menuData,e=>e==null?void 0:e.tiptapExtensionProps));x(this,"isMenuActive",et(this._menuData,e=>e!==void 0));x(this,"mentionables",this._mentionables);x(this,"updateOptions",e=>{this._opts={...this._opts,...e}});x(this,"updateMentionables",e=>{this._mentionables.set(e)});x(this,"replaceQueryWithMentionNode",e=>{var o;const t=he(this.isMenuActive),n=(o=he(this.tiptapExtensionProps))==null?void 0:o.command;return!(!t||!n)&&(n(e),!0)});x(this,"selectItem",e=>{var n,o;return!(he(this._mentionables).findIndex(i=>i.id===e.id)===-1||!this._opts.onSelectMentionable)&&((o=(n=this._opts).onSelectMentionable)==null||o.call(n,e),!0)});x(this,"_incrementActiveIdx",()=>{const e=he(this.activeIdx)??0;this._setActiveIdx(e+1)});x(this,"_decrementActiveIdx",()=>{const e=he(this.activeIdx)??0;this._setActiveIdx(e-1)});x(this,"_setActiveIdx",e=>{this._menuData.update(t=>t&&{...t,activeIdx:e})});x(this,"onUpdateSuggestion",e=>{var n;const t=(n=e.clientRect)==null?void 0:n.call(e);t&&this._menuData.update(o=>({referenceClientRect:t,tiptapExtensionProps:e,activeIdx:(o==null?void 0:o.activeIdx)??0}))});x(this,"exitMenu",()=>{this._menuData.set(void 0),this._mentionables.set([])});x(this,"_handleKeyIfActive",e=>()=>!!he(this.isMenuActive)&&(e(),!0));x(this,"onArrowUp",this._handleKeyIfActive(this._decrementActiveIdx));x(this,"onArrowDown",this._handleKeyIfActive(this._incrementActiveIdx));x(this,"selectActiveItem",this._handleKeyIfActive(()=>{const e=he(this.activeItem);return!!e&&this.selectItem(e)}));this._opts=e}}function Jc([r,e]){if(e!==void 0&&r.length!==0)return r[e]}function Kc([r,e]){if(!((e==null?void 0:e.activeIdx)===void 0||r.length===0))return(e.activeIdx%r.length+r.length)%r.length%r.length}const Ge=class Ge{constructor(e){x(this,"_triggerCharacter");x(this,"_editor");x(this,"_mention");x(this,"_chipController");x(this,"_mentionableMenuContext",new qc);x(this,"insertMentionNode",e=>!!this._editor&&(this._mentionableMenuContext.replaceQueryWithMentionNode(e.data)||this._editor.commands.insertContent({type:this._mentionPluginId,attrs:e}),!0));x(this,"_onCreate",e=>{var n,o;const t=qi(this._mentionPluginId,e).map(i=>i.attrs.data);(o=(n=this._options).onMentionItemsUpdated)==null||o.call(n,{added:t,removed:[],current:t}),this._editor=e,this._chipController.onCreate(e)});x(this,"_onDestroy",()=>{var e,t;if(this._editor){const n=qi(this._mentionPluginId,this._editor).map(o=>o.attrs.data);(t=(e=this._options).onMentionItemsUpdated)==null||t.call(e,{added:[],removed:n,current:[]}),this._editor=void 0,this._chipController.onDispose()}this._chipController.hideTooltip(),this._mentionableMenuContext.exitMenu()});x(this,"_onProseMirrorUpdate",(e,t)=>{var l,a;if(e===t)return;const n=c=>c.attrs.data,o=[],i=Ji(this._mentionPluginId,e,t).map(n),s=Ji(this._mentionPluginId,t,e,{onNewMention:c=>o.push(n(c)),onExistingMention:c=>o.push(n(c))}).map(n);(a=(l=this._options).onMentionItemsUpdated)==null||a.call(l,{added:s,removed:i,current:o})});this._options=e;const t=e.triggerCharacter??"@";this._triggerCharacter=t;const n=new Oe(this._mentionListenerPluginId),o=new Oe(this._mentionPluginId),i=e.allowedPrefixes??[" ","	",`
`],s=e.renderText??(g=>`${t}${g.name??g.id}`);this._chipController=new Ut(this._mentionPluginId);const l=this._onCreate.bind(this),a=this._onProseMirrorUpdate.bind(this),c=this._onDestroy.bind(this),d=this._chipController.createMentionChip,h=this._mentionableMenuContext.onUpdateSuggestion,p=this._mentionableMenuContext.exitMenu,f=this._mentionableMenuContext.onArrowUp,u=this._mentionableMenuContext.onArrowDown,m=this._mentionableMenuContext.selectActiveItem;this._mention=jc.extend({name:this._mentionPluginId,onCreate(){l(this.editor)},onDestroy(){c()},addKeyboardShortcuts:()=>({ArrowUp:f,ArrowDown:u,Enter:m,Tab:m}),addAttributes(){var g;return{...(g=this.parent)==null?void 0:g.call(this),data:{default:null,keepOnSplit:!1,parseHTML:C=>{const w=C.getAttribute(Ut.CHIP_DATA_ATTR_KEY);return w?JSON.parse(w):null},renderHTML:C=>C.data?{[Ut.CHIP_DATA_ATTR_KEY]:JSON.stringify(C.data)}:{}}}},addProseMirrorPlugins(){var g;return[...((g=this.parent)==null?void 0:g.call(this))??[],new Te({key:n,view:()=>({update:(C,w)=>{a(w.doc,C.state.doc)}})})]},addOptions(){var C;const g=(C=this.parent)==null?void 0:C.call(this);return{...g,HTMLAttributes:{class:Ut.CHIP_CLASS_NAME},renderHTML:d,renderText:({node:w})=>{const v=w.attrs.data;return s(v)},suggestion:{...g==null?void 0:g.suggestion,pluginKey:o,char:t,allowedPrefixes:i,command:({editor:w,range:v,props:b})=>{var k,N;w&&v&&((N=(k=g.suggestion)==null?void 0:k.command)==null||N.call(k,{editor:w,range:v,props:{id:b.id,name:b.name??b.id,label:b.label,data:b}}))},render:()=>({onStart:h,onUpdate:h,onExit:p})}}}})}get chipController(){return this._chipController}get mentionableMenuContext(){return this._mentionableMenuContext}get _mentionPluginId(){return this._options.pluginId?this._options.pluginId:this._triggerCharacter==="@"?Ge.DEFAULT_MENTION_PLUGIN_ID:Ge.MENTION_PLUGIN_ID_BASE.replace("{}",this._triggerCharacter)}get _mentionListenerPluginId(){return Ge.MENTION_LISTENER_PLUGIN_ID_BASE.replace("{}",this._mentionPluginId)}get tipTapExtension(){return this._mention}};x(Ge,"CONTEXT_KEY","augment-svelte-mention-plugin"),x(Ge,"MENTION_LISTENER_PLUGIN_ID_BASE","{}-listener"),x(Ge,"MENTION_PLUGIN_ID_BASE","augment-prosemirror-mention-{}"),x(Ge,"DEFAULT_MENTION_PLUGIN_ID","mention");let At=Ge;const Wc=r=>({mentionable:2&r}),Wi=r=>({mentionable:r[1].data}),Uc=r=>({mentionable:2&r}),Ui=r=>({mentionable:r[1].data});function Zi(r){let e;const t=r[4].mentionable,n=Ce(t,r,r[6],Ui);return{c(){n&&n.c()},m(o,i){n&&n.m(o,i),e=!0},p(o,i){n&&n.p&&(!e||66&i)&&ye(n,t,o,o[6],e?ve(t,o[6],i,Uc):we(o[6]),Ui)},i(o){e||(I(n,o),e=!0)},o(o){z(n,o),e=!1},d(o){n&&n.d(o)}}}function Gi(r){let e;const t=r[4].default,n=Ce(t,r,r[6],Wi);return{c(){n&&n.c()},m(o,i){n&&n.m(o,i),e=!0},p(o,i){n&&n.p&&(!e||66&i)&&ye(n,t,o,o[6],e?ve(t,o[6],i,Wc):we(o[6]),Wi)},i(o){e||(I(n,o),e=!0)},o(o){z(n,o),e=!1},d(o){n&&n.d(o)}}}function Zc(r){let e,t,n,o=r[1]&&r[3].mentionable&&Zi(r),i=r[1]&&r[3].default&&Gi(r);return{c(){o&&o.c(),e=Pe(),i&&i.c(),t=us()},m(s,l){o&&o.m(s,l),Z(s,e,l),i&&i.m(s,l),Z(s,t,l),n=!0},p(s,l){s[1]&&s[3].mentionable?o?(o.p(s,l),10&l&&I(o,1)):(o=Zi(s),o.c(),I(o,1),o.m(e.parentNode,e)):o&&(He(),z(o,1,1,()=>{o=null}),je()),s[1]&&s[3].default?i?(i.p(s,l),10&l&&I(i,1)):(i=Gi(s),i.c(),I(i,1),i.m(t.parentNode,t)):i&&(He(),z(i,1,1,()=>{i=null}),je())},i(s){n||(I(o),I(i),n=!0)},o(s){z(o),z(i),n=!1},d(s){s&&(U(e),U(t)),o&&o.d(s),i&&i.d(s)}}}function Gc(r){var o;let e,t,n={triggerOn:[],referenceClientRect:(o=r[1])==null?void 0:o.anchorElement.getBoundingClientRect(),$$slots:{content:[Zc]},$$scope:{ctx:r}};return e=new _l({props:n}),r[5](e),{c(){Ne(e.$$.fragment)},m(i,s){$e(e,i,s),t=!0},p(i,[s]){var a;const l={};2&s&&(l.referenceClientRect=(a=i[1])==null?void 0:a.anchorElement.getBoundingClientRect()),74&s&&(l.$$scope={dirty:s,ctx:i}),e.$set(l)},i(i){t||(I(e.$$.fragment,i),t=!0)},o(i){z(e.$$.fragment,i),t=!1},d(i){r[5](null),De(e,i)}}}function Yc(r,e,t){let n,{$$slots:o={},$$scope:i}=e;const s=Zo(o),l=Ke(At.CONTEXT_KEY).chipController.tooltipData;let a;return jt(r,l,c=>t(1,n=c)),r.$$set=c=>{"$$scope"in c&&t(6,i=c.$$scope)},r.$$.update=()=>{3&r.$$.dirty&&(n===void 0?a==null||a.requestClose():a==null||a.requestOpen())},[a,n,l,s,o,function(c){fs[c?"unshift":"push"](()=>{a=c,t(0,a)})},i]}function Xc(r){let e;const t=r[7].default,n=Ce(t,r,r[6],null);return{c(){n&&n.c()},m(o,i){n&&n.m(o,i),e=!0},p(o,[i]){n&&n.p&&(!e||64&i)&&ye(n,t,o,o[6],e?ve(t,o[6],i,null):we(o[6]),null)},i(o){e||(I(n,o),e=!0)},o(o){z(n,o),e=!1},d(o){n&&n.d(o)}}}function Qc(r,e,t){let{$$slots:n={},$$scope:o}=e,{onMentionItemsUpdated:i}=e,{triggerCharacter:s}=e,{allowedPrefixes:l}=e,{pluginId:a}=e,{renderText:c}=e;const d=new At({onMentionItemsUpdated:p=>i==null?void 0:i(p),triggerCharacter:s,allowedPrefixes:l,pluginId:a,renderText:c});hs(At.CONTEXT_KEY,d);const h=Ke(Be.CONTEXT_KEY).pluginManager.registerPlugin(d);return Go(h),r.$$set=p=>{"onMentionItemsUpdated"in p&&t(0,i=p.onMentionItemsUpdated),"triggerCharacter"in p&&t(1,s=p.triggerCharacter),"allowedPrefixes"in p&&t(2,l=p.allowedPrefixes),"pluginId"in p&&t(3,a=p.pluginId),"renderText"in p&&t(4,c=p.renderText),"$$scope"in p&&t(6,o=p.$$scope)},[i,s,l,a,c,p=>{d.insertMentionNode({id:p.id,label:p.label,data:p})},o,n]}const ed=r=>({activeItem:16&r,query:1&r}),Yi=r=>({activeItem:r[4],query:r[0]});function td(r){let e;const t=r[17].default,n=Ce(t,r,r[19],Yi);return{c(){n&&n.c()},m(o,i){n&&n.m(o,i),e=!0},p(o,i){n&&n.p&&(!e||524305&i)&&ye(n,t,o,o[19],e?ve(t,o[19],i,ed):we(o[19]),Yi)},i(o){e||(I(n,o),e=!0)},o(o){z(n,o),e=!1},d(o){n&&n.d(o)}}}function nd(r){let e,t,n,o;return e=new fn.Trigger({props:{referenceClientRect:r[3]}}),n=new fn.Content({props:{side:"top",align:"start",size:1,onClickOutside:r[9],onEscapeKeyDown:r[9],$$slots:{default:[td]},$$scope:{ctx:r}}}),{c(){Ne(e.$$.fragment),t=Pe(),Ne(n.$$.fragment)},m(i,s){$e(e,i,s),Z(i,t,s),$e(n,i,s),o=!0},p(i,s){const l={};8&s&&(l.referenceClientRect=i[3]),e.$set(l);const a={};524305&s&&(a.$$scope={dirty:s,ctx:i}),n.$set(a)},i(i){o||(I(e.$$.fragment,i),I(n.$$.fragment,i),o=!0)},o(i){z(e.$$.fragment,i),z(n.$$.fragment,i),o=!1},d(i){i&&U(t),De(e,i),De(n,i)}}}function od(r){let e,t,n={open:r[2],onOpenChange:r[10],$$slots:{default:[nd]},$$scope:{ctx:r}};return e=new fn.Root({props:n}),r[18](e),{c(){Ne(e.$$.fragment)},m(o,i){$e(e,o,i),t=!0},p(o,[i]){const s={};4&i&&(s.open=o[2]),524313&i&&(s.$$scope={dirty:i,ctx:o}),e.$set(s)},i(o){t||(I(e.$$.fragment,o),t=!0)},o(o){z(e.$$.fragment,o),t=!1},d(o){r[18](null),De(e,o)}}}function rd(r,e,t){let n,o,i,s,l,{$$slots:a={},$$scope:c}=e,{mentionables:d}=e,{onQueryUpdate:h}=e,{onSelectMentionable:p=k=>(u.insertMentionNode({...k,data:k}),!0)}=e;const f=Ke(Be.CONTEXT_KEY),u=Ke(At.CONTEXT_KEY),m=u.mentionableMenuContext,{referenceClientRect:g,query:C,activeItem:w,isMenuActive:v,exitMenu:b}=m;return jt(r,g,k=>t(3,i=k)),jt(r,C,k=>t(0,n=k)),jt(r,w,k=>t(4,s=k)),jt(r,v,k=>t(2,o=k)),r.$$set=k=>{"mentionables"in k&&t(11,d=k.mentionables),"onQueryUpdate"in k&&t(12,h=k.onQueryUpdate),"onSelectMentionable"in k&&t(13,p=k.onSelectMentionable),"$$scope"in k&&t(19,c=k.$$scope)},r.$$.update=()=>{4097&r.$$.dirty&&h(n),8192&r.$$.dirty&&m.updateOptions({onSelectMentionable:p}),2048&r.$$.dirty&&m.updateMentionables(d)},[n,l,o,i,s,g,C,w,v,b,async k=>{k||f.commandManager.requestFocus()},d,h,p,()=>l==null?void 0:l.requestOpen(),()=>l==null?void 0:l.requestClose(),k=>l==null?void 0:l.focusIdx(k),a,function(k){fs[k?"unshift":"push"](()=>{l=k,t(1,l)})},c]}function id(r){let e;const t=r[4].default,n=Ce(t,r,r[6],null);return{c(){n&&n.c()},m(o,i){n&&n.m(o,i),e=!0},p(o,i){n&&n.p&&(!e||64&i)&&ye(n,t,o,o[6],e?ve(t,o[6],i,null):we(o[6]),null)},i(o){e||(I(n,o),e=!0)},o(o){z(n,o),e=!1},d(o){n&&n.d(o)}}}function sd(r){let e,t;return e=new Vl({props:{onSelect:r[5],highlight:r[1]===r[0],$$slots:{default:[id]},$$scope:{ctx:r}}}),{c(){Ne(e.$$.fragment)},m(n,o){$e(e,n,o),t=!0},p(n,[o]){const i={};1&o&&(i.onSelect=n[5]),3&o&&(i.highlight=n[1]===n[0]),64&o&&(i.$$scope={dirty:o,ctx:n}),e.$set(i)},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){z(e.$$.fragment,n),t=!1},d(n){De(e,n)}}}function ld(r,e,t){let n,{$$slots:o={},$$scope:i}=e;const s=Ke(At.CONTEXT_KEY),{replaceQueryWithMentionNode:l,activeItem:a}=s.mentionableMenuContext;jt(r,a,d=>t(1,n=d));let{mentionable:c}=e;return r.$$set=d=>{"mentionable"in d&&t(0,c=d.mentionable),"$$scope"in d&&t(6,i=d.$$scope)},[c,n,l,a,o,()=>l(c),i]}const Yd={ChipTooltip:class extends Y{constructor(r){super(),X(this,r,Yc,Gc,Q,{})}},Root:class extends Y{constructor(r){super(),X(this,r,Qc,Xc,Q,{onMentionItemsUpdated:0,triggerCharacter:1,allowedPrefixes:2,pluginId:3,renderText:4,insertMention:5})}get insertMention(){return this.$$.ctx[5]}},Menu:{Root:class extends Y{constructor(r){super(),X(this,r,rd,od,Q,{mentionables:11,onQueryUpdate:12,onSelectMentionable:13,requestOpen:14,requestClose:15,focusIdx:16})}get requestOpen(){return this.$$.ctx[14]}get requestClose(){return this.$$.ctx[15]}get focusIdx(){return this.$$.ctx[16]}},Item:class extends Y{constructor(r){super(),X(this,r,ld,sd,Q,{mentionable:0})}},Separator:fn.Separator,Label:fn.Label}};function ad(r){let e,t,n,o,i,s,l,a,c,d,h,p,f;return{c(){e=D("svg"),t=D("g"),n=D("path"),o=D("path"),i=D("path"),s=D("path"),l=D("path"),a=D("path"),c=D("path"),d=D("path"),h=D("path"),p=D("path"),f=D("path"),y(n,"d","M2.08,107.33 C3.82,110.98 6.43,114.11 9.73,115.85 C13.73,118.11 17.73,118.28 20.16,118.28 C20.86,118.28 21.38,118.28 22.08,118.28 C22.6,118.28 22.95,118.28 23.12,118.28 L90.23,118.28 C90.58,118.28 90.92,118.28 91.27,118.28 C91.97,118.28 92.49,118.28 93.18,118.28 C95.62,118.28 99.62,118.11 103.61,115.85 C106.92,113.93 109.53,110.98 111.26,107.33 M111.26,107.33 C112.66,104.02 113.35,100.55 113.35,96.9 L113.35,87.33 C115.09,86.29 116.65,85.07 117.87,83.16 C119.96,80.38 121,76.9 121,73.08 C121,69.25 119.78,65.43 117.52,62.47 C116.31,60.91 114.92,59.86 113.35,59 L113.35,46.13 C113.35,40.22 111.79,34.66 107.96,30.48 C104.31,26.31 99.44,24.57 94.23,24.57 L92.49,24.57 L92.31,24.57 C92.66,24.57 91.27,15.18 91.1,14.31 C90.4,11.01 89.18,7.71 87.45,4.93 C81.19,-5.85 67.97,-4.98 56.5,-4.98 C45.03,-4.98 31.81,-5.68 25.55,4.93 C23.82,7.88 22.77,11.01 21.9,14.31 C21.73,15.18 20.16,24.57 20.69,24.57 L20.51,24.57 L18.77,24.57 C13.56,24.57 8.86,26.31 5.04,30.48 C1.39,34.66 -0.35,40.22 -0.35,46.13 L-0.35,59 C-1.92,59.86 -3.31,61.08 -4.52,62.47 C-6.78,65.43 -8,69.08 -8,73.08 C-8,76.73 -6.96,80.21 -4.87,83.16 C-3.65,84.9 -2.09,86.29 -0.35,87.33 L-0.35,96.9 C-0.35,100.55 0.35,104.02 1.74,107.33"),y(n,"id","Shape"),y(n,"fill-rule","nonzero"),y(o,"d","M27.29,66.65 L36.86,66.65 C40.86,66.65 43.98,69.78 43.98,73.78 C43.98,77.77 40.86,80.9 36.86,80.9 L27.29,80.9 C23.3,80.9 20.17,77.77 20.17,73.78 C20.17,69.95 23.47,66.65 27.29,66.65 Z"),y(o,"id","Path"),y(o,"fill-rule","nonzero"),y(i,"d","M75.45,66.65 L85.01,66.65 C89.01,66.65 92.14,69.78 92.14,73.78 C92.14,77.77 89.01,80.9 85.01,80.9 L75.45,80.9 C71.45,80.9 68.32,77.77 68.32,73.78 C68.32,69.95 71.63,66.65 75.45,66.65 Z"),y(i,"id","Path"),y(i,"fill-rule","nonzero"),y(s,"d","M17.38,110.98 C15.64,110.98 14.08,110.63 12.86,110.11 C11.65,109.41 10.78,108.54 10.08,107.33 C9.39,106.11 9.21,104.55 9.21,102.63 L9.21,84.03 C9.21,81.77 8.69,80.03 7.82,78.99 C6.95,77.95 5.39,77.42 2.95,77.42 C2.26,77.42 1.74,77.08 1.21,76.56 C0.69,76.03 0.52,75.34 0.52,74.64 C0.52,73.95 0.69,73.25 1.21,72.73 C1.74,72.21 2.26,72.03 2.95,71.86 C5.21,71.86 6.78,71.34 7.82,70.3 C8.69,69.25 9.21,67.69 9.21,65.43 L9.21,46.65 C9.21,43.87 9.91,41.78 11.3,40.39 C12.69,39 14.78,38.31 17.56,38.31 L39.46,38.31 C40.33,38.31 41.03,38.48 41.55,39 C42.07,39.52 42.42,40.05 42.42,40.91 C42.42,41.61 42.24,42.31 41.72,42.83 C41.2,43.35 40.68,43.7 39.81,43.7 L19.47,43.7 C18.25,43.7 17.38,44.04 16.86,44.57 C16.34,45.09 15.99,46.13 15.99,47.35 L15.99,66.12 C15.99,67.69 15.64,69.25 14.95,70.64 C14.25,72.03 13.38,73.08 12.34,73.77 C11.3,74.47 9.91,74.99 8.52,74.99 L8.52,74.47 C10.08,74.47 11.3,74.82 12.34,75.69 C13.38,76.38 14.25,77.42 14.95,78.82 C15.64,80.21 15.99,81.6 15.99,83.34 L15.99,102.11 C15.99,103.33 16.34,104.37 16.86,104.89 C17.38,105.59 18.43,105.76 19.47,105.76 L39.81,105.76 C40.51,105.76 41.2,106.11 41.72,106.63 C42.24,107.15 42.42,107.85 42.42,108.54 C42.42,109.24 42.07,109.94 41.55,110.46 C41.03,110.98 40.33,111.33 39.46,111.33 L17.38,110.98 Z"),y(s,"id","Path"),y(s,"fill","currentColor"),y(s,"fill-rule","nonzero"),y(l,"d","M74.06,110.98 C73.19,110.98 72.49,110.63 71.97,110.11 C71.45,109.59 71.1,108.89 71.1,108.2 C71.1,107.5 71.28,106.81 71.8,106.29 C72.32,105.76 72.84,105.42 73.71,105.42 L94.05,105.42 C95.27,105.42 96.14,105.07 96.66,104.55 C97.18,104.03 97.53,102.98 97.53,101.77 L97.53,83.16 C97.53,81.6 97.88,80.03 98.57,78.64 C99.27,77.25 100.14,76.21 101.18,75.51 C102.22,74.82 103.61,74.3 105.01,74.3 L105.01,74.64 C103.44,74.64 102.22,74.3 101.18,73.43 C100.14,72.73 99.27,71.51 98.57,70.3 C97.88,68.91 97.53,67.52 97.53,65.78 L97.53,47 C97.53,45.78 97.18,44.74 96.66,44.22 C95.96,43.7 95.1,43.35 94.05,43.35 L73.71,43.35 C73.02,43.35 72.32,43 71.8,42.48 C71.28,41.96 71.1,41.26 71.1,40.57 C71.1,39.87 71.45,39.18 71.97,38.66 C72.49,38.13 73.19,37.96 74.06,37.96 L95.96,37.96 C98.57,37.96 100.66,38.66 102.22,40.05 C103.61,41.44 104.31,43.52 104.31,46.31 L104.31,64.91 C104.31,67.17 104.83,68.73 105.7,69.78 C106.57,70.82 108.13,71.34 110.57,71.34 C111.26,71.34 111.79,71.69 112.31,72.21 C112.83,72.73 113,73.25 113,74.12 C113,74.82 112.83,75.51 112.31,76.04 C111.96,76.56 111.26,76.9 110.57,76.9 C108.31,76.9 106.74,77.43 105.7,78.47 C104.83,79.51 104.31,81.25 104.31,83.51 L104.31,102.11 C104.31,103.85 103.96,105.42 103.44,106.81 C102.75,108.02 101.88,109.07 100.66,109.59 C99.44,110.28 97.88,110.46 96.14,110.46 L74.06,110.98 Z"),y(l,"id","Path"),y(l,"fill","currentColor"),y(l,"fill-rule","nonzero"),y(a,"d","M77.88,64.04 L75.1,67.69 C73.19,70.12 72.15,70.82 70.24,70.82 C69.19,70.82 68.32,70.3 68.32,69.43 C68.32,68.73 68.67,68.21 69.02,67.52 L73.36,61.43 C74.41,59.87 75.62,59 77.54,59 L78.06,59 C79.97,59 81.19,59.87 82.23,61.43 L86.58,67.52 C87.1,68.21 87.27,68.91 87.27,69.43 C87.27,70.3 86.4,70.82 85.36,70.82 C83.45,70.82 82.4,70.12 80.49,67.69 L77.88,64.04 Z"),y(a,"id","Path"),y(a,"fill","currentColor"),y(a,"fill-rule","nonzero"),y(c,"d","M37.2,64.04 L34.42,67.69 C32.51,70.12 31.46,70.82 29.55,70.82 C28.51,70.82 27.64,70.3 27.64,69.43 C27.64,68.73 27.99,68.21 28.34,67.52 L32.68,61.43 C33.73,59.87 34.94,59 36.85,59 L37.38,59 C39.29,59 40.51,59.87 41.55,61.43 L45.89,67.52 C46.42,68.21 46.59,68.91 46.59,69.43 C46.59,70.3 45.72,70.82 44.68,70.82 C42.77,70.82 41.72,70.12 39.81,67.69 L37.2,64.04 Z"),y(c,"id","Path"),y(c,"fill","currentColor"),y(c,"fill-rule","nonzero"),y(d,"d","M81.88,6.84 L75.8,23.01 C75.45,23.88 74.41,24.05 72.67,23.53 C70.93,23.01 70.24,22.14 70.58,21.27 C71.8,17.1 72.67,13.97 73.36,11.71 C74.06,9.45 74.58,7.71 74.93,6.67 C75.28,5.62 75.45,5.1 75.45,4.93 C75.45,4.75 75.62,4.58 75.62,4.58 C75.97,3.36 77.36,3.19 79.45,3.89 C81.36,4.75 82.23,5.62 81.88,6.84 Z"),y(d,"id","Path"),y(d,"fill","currentColor"),y(d,"fill-rule","nonzero"),y(h,"d","M30.07,6.84 L36.16,23.01 C36.51,23.88 37.55,24.05 39.29,23.53 C41.03,23.01 41.72,22.14 41.37,21.27 C40.16,17.1 39.29,13.97 38.59,11.71 C37.9,9.45 37.38,7.71 37.03,6.67 C36.68,5.62 36.51,5.1 36.51,4.93 C36.51,4.75 36.33,4.58 36.33,4.58 C35.98,3.36 34.59,3.19 32.51,3.89 C30.42,4.75 29.55,5.62 30.07,6.84 Z"),y(h,"id","Path"),y(h,"fill","currentColor"),y(h,"fill-rule","nonzero"),y(p,"d","M59.46,3.01 L58.76,20.4 C58.76,21.44 57.89,21.79 55.98,21.79 C54.07,21.79 53.2,21.27 53.2,20.4 C53.02,16.05 52.85,12.75 52.85,10.32 C52.85,7.88 52.68,6.14 52.68,5.1 C52.68,4.06 52.68,3.54 52.68,3.19 C52.68,2.84 52.68,2.84 52.68,2.84 C52.68,1.62 53.89,0.93 56.15,0.93 C58.41,1.1 59.46,1.62 59.46,3.01 Z"),y(p,"id","Path"),y(p,"fill","currentColor"),y(p,"fill-rule","nonzero"),y(f,"d","M42.07,79.6 C41.55,79.08 41.03,78.73 39.99,78.73 C39.29,78.73 38.77,78.91 38.25,79.25 C37.73,79.6 37.55,80.12 37.55,80.64 C37.55,80.99 37.55,81.17 37.73,81.34 C37.73,81.51 37.9,81.69 37.9,81.86 C42.42,88.29 48.51,91.77 56.85,91.77 C65.02,91.77 71.28,88.29 75.8,81.86 C75.97,81.69 75.97,81.51 75.97,81.34 C75.97,81.17 76.15,80.82 76.15,80.64 C76.15,80.12 75.8,79.6 75.45,79.25 C74.93,78.91 74.41,78.73 73.71,78.73 C72.84,78.73 72.15,79.08 71.63,79.6 C69.72,82.04 67.46,83.77 65.02,84.99 C62.59,86.21 59.98,86.73 56.68,86.73 C53.55,86.73 50.77,86.21 48.33,84.99 C45.9,83.77 43.98,82.04 42.07,79.6 Z"),y(f,"id","Path"),y(f,"stroke","currentColor"),y(f,"stroke-width","0.452891"),y(f,"fill","currentColor"),y(f,"fill-rule","nonzero"),y(t,"id","Page-1"),y(t,"stroke","none"),y(t,"stroke-width","1"),y(t,"fill","none"),y(t,"fill-rule","evenodd"),y(e,"width","113"),y(e,"height","112"),y(e,"viewBox","0 0 113 112"),y(e,"fill","none"),y(e,"xmlns","http://www.w3.org/2000/svg")},m(u,m){Z(u,e,m),E(e,t),E(t,n),E(t,o),E(t,i),E(t,s),E(t,l),E(t,a),E(t,c),E(t,d),E(t,h),E(t,p),E(t,f)},p:J,i:J,o:J,d(u){u&&U(e)}}}class Xd extends Y{constructor(e){super(),X(this,e,null,ad,Q,{})}}function cd(r){let e,t,n,o,i,s,l,a,c,d,h,p;return{c(){e=D("svg"),t=D("path"),n=D("path"),o=D("path"),i=D("path"),s=D("path"),l=D("path"),a=D("path"),c=D("path"),d=D("path"),h=D("path"),p=D("path"),y(t,"d","M18.0699 114.34C16.1299 114.34 14.5 114.004 13.17 113.331C11.85 112.659 10.8501 111.637 10.1801 110.286C9.50005 108.934 9.16003 107.284 9.16003 105.34V85.3291C9.16003 82.8631 8.67996 81.0691 7.70996 79.9481C6.73996 78.8331 5.02005 78.2421 2.55005 78.1861C1.79005 78.1861 1.16995 77.8931 0.699951 77.3011C0.229951 76.7161 0 76.0371 0 75.2771C0 74.4551 0.229951 73.7761 0.699951 73.2471C1.16995 72.7171 1.79005 72.4241 2.55005 72.3681C5.02005 72.3061 6.73996 71.7211 7.70996 70.6061C8.67996 69.4911 9.16003 67.7221 9.16003 65.3121V45.3011C9.16003 42.3611 9.92995 40.1251 11.45 38.5991C12.98 37.0731 15.1799 36.3071 18.0699 36.3071H41.59C42.48 36.3071 43.2 36.5751 43.8 37.0981C44.38 37.6281 44.6801 38.3061 44.6801 39.1221C44.6801 39.8881 44.4301 40.5491 43.9301 41.1091C43.4401 41.6701 42.78 41.9501 41.95 41.9501H20.1C18.87 41.9501 17.92 42.2741 17.28 42.9221C16.64 43.5691 16.3101 44.5661 16.3101 45.9241V66.0281C16.3101 67.7911 15.96 69.3911 15.25 70.8301C14.55 72.2751 13.6101 73.4021 12.4301 74.2311C11.2501 75.0531 9.88004 75.4641 8.29004 75.4641V75.1091C9.88004 75.1091 11.2501 75.5201 12.4301 76.3421C13.6101 77.1641 14.55 78.2981 15.25 79.7431C15.96 81.1811 16.3101 82.7821 16.3101 84.5451V104.736C16.3101 106.094 16.64 107.091 17.28 107.738C17.93 108.392 18.87 108.71 20.1 108.71H41.95C42.77 108.71 43.4301 108.99 43.9301 109.551C44.4401 110.111 44.6801 110.771 44.6801 111.538C44.6801 112.304 44.39 112.958 43.8 113.518C43.2 114.079 42.48 114.359 41.59 114.359H18.0699V114.34Z"),y(t,"fill","currentColor"),y(n,"d","M78.84 114.339C77.95 114.339 77.22 114.059 76.63 113.499C76.04 112.938 75.75 112.278 75.75 111.518C75.75 110.758 76 110.092 76.5 109.531C76.99 108.971 77.65 108.69 78.48 108.69H100.32C101.56 108.69 102.5 108.373 103.15 107.719C103.79 107.071 104.12 106.075 104.12 104.717V84.5251C104.12 82.7621 104.47 81.1621 105.18 79.7231C105.88 78.2781 106.82 77.1511 108 76.3231C109.18 75.5001 110.55 75.0891 112.14 75.0891V75.444C110.55 75.444 109.18 75.0331 108 74.2111C106.82 73.3891 105.88 72.256 105.18 70.811C104.47 69.372 104.12 67.771 104.12 66.009V45.9041C104.12 44.5531 103.79 43.556 103.15 42.902C102.5 42.254 101.56 41.9301 100.32 41.9301H78.48C77.66 41.9301 77 41.6501 76.5 41.0901C76 40.5351 75.75 39.8691 75.75 39.1031C75.75 38.2811 76.04 37.608 76.63 37.079C77.22 36.549 77.95 36.2881 78.84 36.2881H102.36C105.25 36.2881 107.44 37.0541 108.98 38.5801C110.51 40.1061 111.27 42.3421 111.27 45.2811V65.2921C111.27 67.7031 111.75 69.4721 112.72 70.5861C113.69 71.7011 115.41 72.2931 117.88 72.3491C118.64 72.4051 119.26 72.6981 119.73 73.2271C120.2 73.7571 120.43 74.4351 120.43 75.2581C120.43 76.0241 120.2 76.696 119.73 77.282C119.26 77.867 118.64 78.1661 117.88 78.1661C115.41 78.2221 113.69 78.8141 112.72 79.9291C111.75 81.0441 111.27 82.8371 111.27 85.3101V105.321C111.27 107.264 110.93 108.908 110.25 110.266C109.57 111.624 108.58 112.633 107.26 113.312C105.93 113.984 104.3 114.321 102.36 114.321H78.84V114.339Z"),y(n,"fill","currentColor"),y(o,"d","M79.92 76.025C83.88 76.025 87.09 72.8159 87.09 68.8569C87.09 64.8979 83.88 61.689 79.92 61.689C75.96 61.689 72.75 64.8979 72.75 68.8569C72.75 72.8159 75.96 76.025 79.92 76.025Z"),y(o,"fill","currentColor"),y(i,"d","M39.8301 76.025C43.7901 76.025 46.9901 72.8159 46.9901 68.8569C46.9901 64.8979 43.7901 61.689 39.8301 61.689C35.8701 61.689 32.6602 64.8979 32.6602 68.8569C32.6602 72.8159 35.8701 76.025 39.8301 76.025Z"),y(i,"fill","currentColor"),y(s,"d","M59.8701 94.9769C59.7201 94.9769 59.5701 94.9769 59.4301 94.9769C59.1501 94.9769 58.8701 94.9609 58.5901 94.9459H58.5101C57.4801 94.8899 56.45 94.7669 55.45 94.5819L55.0901 94.515C54.9501 94.49 54.8001 94.4589 54.6601 94.4279C49.1801 93.2799 44.1001 90.3729 40.3601 86.2509C39.8101 85.6359 39.85 84.877 40.07 84.369C40.32 83.81 40.8101 83.4669 41.3401 83.4669C41.4701 83.4669 41.5901 83.4869 41.7201 83.5179C47.4801 85.1129 53.4301 86.764 59.3101 86.835H60.4301C66.3101 86.764 72.2501 85.1179 78.0001 83.5229C78.1501 83.4819 78.2801 83.4619 78.4101 83.4619C78.9401 83.4619 79.4301 83.81 79.6801 84.364C79.9001 84.872 79.9401 85.6309 79.3901 86.2459C75.6501 90.3679 70.5701 93.2749 65.0901 94.4229C64.9301 94.4589 64.7601 94.4899 64.6001 94.5259L64.3001 94.5819C63.2901 94.7669 62.2601 94.8839 61.2401 94.9409H61.1601C60.8801 94.9609 60.61 94.9669 60.33 94.9719C60.18 94.9719 60.0301 94.9719 59.8801 94.9719L59.8701 94.9769Z"),y(s,"fill","currentColor"),y(l,"d","M70.1802 51.6431L70.8801 42.201L62.9901 47.5721C62.4701 47.9181 62.1301 48.0911 61.6101 48.0911C60.6501 48.0911 59.8701 47.225 59.8701 46.272C59.8701 45.493 60.4801 44.8871 61.0901 44.6271L70.0101 40.3821L61.0901 36.1381C60.3901 35.7911 59.8701 35.2721 59.8701 34.4921C59.8701 33.5391 60.6501 32.76 61.6101 32.673C62.2101 32.673 62.4701 32.8461 62.9901 33.1931L70.8801 38.563L70.1802 29.121C70.1002 28.082 70.8802 27.2161 71.9202 27.2161C72.9502 27.2161 73.7301 28.082 73.6501 29.121L72.9501 38.563L80.8401 33.1931C81.3601 32.8461 81.7001 32.673 82.2201 32.673C83.1801 32.673 83.9601 33.5391 83.9601 34.4921C83.9601 35.4451 83.3501 35.8781 82.7401 36.1381L73.8201 40.3821L82.7401 44.6271C83.3501 44.8871 83.9601 45.32 83.9601 46.272C83.9601 47.225 83.1801 48.0911 82.2201 48.0911C81.7001 48.0911 81.3601 47.9181 80.8401 47.5721L72.9501 42.201L73.6501 51.6431C73.7301 52.6821 72.9502 53.5491 71.9202 53.5491C70.8802 53.5491 70.1002 52.6821 70.1802 51.6431Z"),y(l,"fill","currentColor"),y(a,"fill-rule","evenodd"),y(a,"clip-rule","evenodd"),y(a,"d","M73.65 51.6429C73.73 52.6819 72.95 53.5489 71.92 53.5489C70.88 53.5489 70.1001 52.6819 70.1801 51.6429L70.88 42.2009L62.99 47.5719C62.47 47.9179 62.13 48.0909 61.61 48.0909C60.65 48.0909 59.87 47.2249 59.87 46.2719C59.87 45.4929 60.48 44.8869 61.09 44.6269L70.01 40.3819L61.09 36.1379C60.39 35.7909 59.87 35.2719 59.87 34.4919C59.87 33.5389 60.65 32.7599 61.61 32.6729C62.21 32.6729 62.47 32.8459 62.99 33.1929L70.88 38.5629L70.1801 29.1208C70.1001 28.0818 70.88 27.2159 71.92 27.2159C72.95 27.2159 73.73 28.0818 73.65 29.1208L72.95 38.5629L80.84 33.1929C81.36 32.8459 81.7 32.6729 82.22 32.6729C83.18 32.6729 83.96 33.5389 83.96 34.4919C83.96 35.4449 83.35 35.8779 82.74 36.1379L73.8199 40.3819L82.74 44.6269C83.35 44.8869 83.96 45.3199 83.96 46.2719C83.96 47.2249 83.18 48.0909 82.22 48.0909C81.7 48.0909 81.36 47.9179 80.84 47.5719L72.95 42.2009L73.65 51.6429ZM75.04 45.8169L75.46 51.5029C75.62 53.5679 74.04 55.3629 71.92 55.3629C69.79 55.3629 68.21 53.5679 68.37 51.5029L68.79 45.8169L64 49.0809C63.39 49.4879 62.67 49.9059 61.61 49.9059C59.54 49.9059 58.0601 48.1109 58.0601 46.2719C58.0601 44.5309 59.34 43.4159 60.33 42.9759L65.79 40.3819L60.28 37.7609C59.31 37.2759 58.0601 36.2689 58.0601 34.4919C58.0601 32.5089 59.6499 31.0289 61.4399 30.8659L61.52 30.8589H61.61C62.11 30.8589 62.57 30.9339 63.04 31.1309C63.41 31.2859 63.73 31.5049 63.95 31.6519C63.97 31.6629 63.98 31.6729 64 31.6829L64.01 31.6929L68.79 34.9469L68.37 29.2619C68.21 27.1959 69.79 25.4009 71.92 25.4009C74.04 25.4009 75.62 27.1959 75.46 29.2619L75.04 34.9469L79.83 31.6829C80.44 31.2759 81.16 30.8589 82.22 30.8589C84.29 30.8589 85.77 32.6529 85.77 34.4919C85.77 35.3979 85.4699 36.1709 84.9399 36.7699C84.4699 37.3069 83.91 37.6099 83.49 37.7899L78.04 40.3819L83.49 42.9739C83.91 43.1549 84.4699 43.4579 84.9399 43.9949C85.4699 44.5929 85.77 45.3669 85.77 46.2719C85.77 48.1109 84.29 49.9059 82.22 49.9059C81.16 49.9059 80.44 49.4879 79.83 49.0809L79.8199 49.0709L75.04 45.8169Z"),y(a,"fill","currentColor"),y(c,"d","M41.1499 37.1279L41.8499 27.6859L33.9598 33.0569C33.4398 33.4029 33.0998 33.5759 32.5798 33.5759C31.6198 33.5759 30.8398 32.71 30.8398 31.757C30.8398 30.978 31.4499 30.3709 32.0599 30.1119L40.9799 25.8669L32.0599 21.6229C31.3599 21.2759 30.8398 20.7559 30.8398 19.9769C30.8398 19.0239 31.6198 18.244 32.5798 18.158C33.1798 18.158 33.4398 18.3309 33.9598 18.6779L41.8499 24.0479L41.1499 14.606C41.0699 13.567 41.8499 12.7009 42.8799 12.7009C43.9199 12.7009 44.6999 13.567 44.6199 14.606L43.9199 24.0479L51.8099 18.6779C52.3299 18.3309 52.6698 18.158 53.1898 18.158C54.1498 18.158 54.9299 19.0239 54.9299 19.9769C54.9299 20.9299 54.3198 21.3629 53.7098 21.6229L44.7899 25.8669L53.7098 30.1119C54.3198 30.3709 54.9299 30.805 54.9299 31.757C54.9299 32.71 54.1498 33.5759 53.1898 33.5759C52.6698 33.5759 52.3299 33.4029 51.8099 33.0569L43.9199 27.6859L44.6199 37.1279C44.6999 38.1669 43.9199 39.0339 42.8799 39.0339C41.8499 39.0339 41.0699 38.1669 41.1499 37.1279Z"),y(c,"fill","currentColor"),y(d,"fill-rule","evenodd"),y(d,"clip-rule","evenodd"),y(d,"d","M44.62 37.128C44.7 38.167 43.92 39.034 42.88 39.034C41.85 39.034 41.07 38.167 41.15 37.128L41.85 27.686L33.96 33.057C33.44 33.403 33.1 33.576 32.58 33.576C31.62 33.576 30.84 32.71 30.84 31.757C30.84 30.978 31.4501 30.371 32.0601 30.112L40.98 25.867L32.0601 21.623C31.3601 21.276 30.84 20.756 30.84 19.977C30.84 19.024 31.62 18.244 32.58 18.158C33.18 18.158 33.44 18.331 33.96 18.678L41.85 24.048L41.15 14.606C41.07 13.567 41.85 12.701 42.88 12.701C43.92 12.701 44.7 13.567 44.62 14.606L43.92 24.048L51.8101 18.678C52.3301 18.331 52.6699 18.158 53.1899 18.158C54.1499 18.158 54.9301 19.024 54.9301 19.977C54.9301 20.93 54.32 21.363 53.71 21.623L44.79 25.867L53.71 30.112C54.32 30.371 54.9301 30.805 54.9301 31.757C54.9301 32.71 54.1499 33.576 53.1899 33.576C52.6699 33.576 52.3301 33.403 51.8101 33.057L43.92 27.686L44.62 37.128ZM46.01 31.302L46.4301 36.988C46.5901 39.053 45.01 40.848 42.88 40.848C40.76 40.848 39.18 39.053 39.34 36.988L39.76 31.302L34.97 34.566C34.36 34.973 33.64 35.391 32.58 35.391C30.51 35.391 29.03 33.596 29.03 31.757C29.03 30.016 30.31 28.9 31.3 28.461L36.76 25.867L31.25 23.246C30.28 22.761 29.03 21.754 29.03 19.977C29.03 17.994 30.62 16.514 32.41 16.351L32.49 16.343H32.58C33.08 16.343 33.54 16.419 34.01 16.616C34.38 16.771 34.7 16.99 34.92 17.137C34.94 17.148 34.95 17.158 34.97 17.168L34.98 17.178L39.76 20.432L39.34 14.747C39.18 12.681 40.76 10.886 42.88 10.886C45.01 10.886 46.5901 12.681 46.4301 14.747L46.01 20.432L50.8 17.168C51.41 16.761 52.1299 16.343 53.1899 16.343C55.2599 16.343 56.74 18.138 56.74 19.977C56.74 20.882 56.44 21.656 55.91 22.254C55.44 22.791 54.88 23.095 54.46 23.275L49.01 25.867L54.46 28.459C54.88 28.639 55.44 28.943 55.91 29.48C56.44 30.078 56.74 30.852 56.74 31.757C56.74 33.596 55.2599 35.391 53.1899 35.391C52.1299 35.391 51.41 34.973 50.8 34.566L50.79 34.556L46.01 31.302Z"),y(d,"fill","currentColor"),y(h,"d","M84.7001 26.2419L85.3901 16.7999L77.5101 22.1699C76.9901 22.5169 76.6401 22.6899 76.1201 22.6899C75.1701 22.6899 74.3901 21.8239 74.3901 20.8709C74.3901 20.0909 75.0001 19.4849 75.6001 19.2249L84.5201 14.981L75.6001 10.736C74.9101 10.39 74.3901 9.86994 74.3901 9.09094C74.3901 8.13794 75.1701 7.35791 76.1201 7.27191C76.7301 7.27191 76.9901 7.44495 77.5101 7.79095L85.3901 13.1619L84.7001 3.71991C84.6101 2.68091 85.3902 1.81396 86.4302 1.81396C87.4702 1.81396 88.2502 2.68091 88.1602 3.71991L87.4701 13.1619L95.3501 7.79095C95.8701 7.44495 96.2201 7.27191 96.7401 7.27191C97.6901 7.27191 98.4701 8.13794 98.4701 9.09094C98.4701 10.0429 97.8601 10.476 97.2601 10.736L88.3401 14.981L97.2601 19.2249C97.8601 19.4849 98.4701 19.9179 98.4701 20.8709C98.4701 21.8239 97.6901 22.6899 96.7401 22.6899C96.2201 22.6899 95.8701 22.5169 95.3501 22.1699L87.4701 16.7999L88.1602 26.2419C88.2502 27.2809 87.4702 28.1469 86.4302 28.1469C85.3902 28.1469 84.6101 27.2809 84.7001 26.2419Z"),y(h,"fill","currentColor"),y(p,"fill-rule","evenodd"),y(p,"clip-rule","evenodd"),y(p,"d","M88.1602 26.242C88.2502 27.281 87.4702 28.147 86.4302 28.147C85.3902 28.147 84.6101 27.281 84.7001 26.242L85.3901 16.8L77.5101 22.17C76.9901 22.517 76.6401 22.69 76.1201 22.69C75.1701 22.69 74.3901 21.824 74.3901 20.871C74.3901 20.091 75.0001 19.485 75.6001 19.225L84.5201 14.981L75.6001 10.736C74.9101 10.39 74.3901 9.87 74.3901 9.091C74.3901 8.138 75.1701 7.35797 76.1201 7.27197C76.7301 7.27197 76.9901 7.44502 77.5101 7.79102L85.3901 13.162L84.7001 3.71997C84.6101 2.68097 85.3902 1.81403 86.4302 1.81403C87.4702 1.81403 88.2502 2.68097 88.1602 3.71997L87.4701 13.162L95.3501 7.79102C95.8701 7.44502 96.2201 7.27197 96.7401 7.27197C97.6901 7.27197 98.4701 8.138 98.4701 9.091C98.4701 10.043 97.8601 10.476 97.2601 10.736L88.3401 14.981L97.2601 19.225C97.8601 19.485 98.4701 19.918 98.4701 20.871C98.4701 21.824 97.6901 22.69 96.7401 22.69C96.2201 22.69 95.8701 22.517 95.3501 22.17L87.4701 16.8L88.1602 26.242ZM89.5502 20.416L89.9701 26.101C90.1401 28.167 88.5502 29.962 86.4302 29.962C84.3102 29.962 82.7201 28.167 82.8901 26.101L83.3102 20.416L78.5101 23.68C77.9001 24.087 77.1901 24.504 76.1201 24.504C74.0601 24.504 72.5801 22.71 72.5801 20.871C72.5801 19.13 73.8601 18.014 74.8501 17.574L80.3002 14.981L74.7902 12.359C73.8202 11.874 72.5801 10.867 72.5801 9.091C72.5801 7.108 74.1601 5.62803 75.9601 5.46503L76.0402 5.45697H76.1201C76.6201 5.45697 77.0902 5.533 77.5502 5.729C77.9202 5.885 78.2501 6.10398 78.4701 6.25098C78.4801 6.26198 78.5001 6.27198 78.5101 6.28198L78.5302 6.29199L83.3102 9.54602L82.8901 3.85999C82.7201 1.79399 84.3102 0 86.4302 0C88.5502 0 90.1401 1.79399 89.9701 3.85999L89.5502 9.54602L94.3501 6.28198C94.9601 5.87498 95.6701 5.45697 96.7401 5.45697C98.8101 5.45697 100.28 7.252 100.28 9.091C100.28 9.996 99.9801 10.77 99.4601 11.368C98.9901 11.905 98.4201 12.208 98.0101 12.389L92.5602 14.981L98.0101 17.573C98.4201 17.753 98.9901 18.056 99.4601 18.593C99.9801 19.192 100.28 19.965 100.28 20.871C100.28 22.71 98.8101 24.504 96.7401 24.504C95.6701 24.504 94.9601 24.087 94.3501 23.68L94.3301 23.67L89.5502 20.416Z"),y(p,"fill","currentColor"),y(e,"width","121"),y(e,"height","115"),y(e,"viewBox","0 0 121 115"),y(e,"fill","none"),y(e,"xmlns","http://www.w3.org/2000/svg")},m(f,u){Z(f,e,u),E(e,t),E(e,n),E(e,o),E(e,i),E(e,s),E(e,l),E(e,a),E(e,c),E(e,d),E(e,h),E(e,p)},p:J,i:J,o:J,d(f){f&&U(e)}}}class Qd extends Y{constructor(e){super(),X(this,e,null,cd,Q,{})}}function dd(r){let e,t,n,o,i,s,l,a,c,d,h,p;return{c(){e=D("svg"),t=D("g"),n=D("path"),o=D("path"),i=D("path"),s=D("path"),l=D("path"),a=D("path"),c=D("path"),d=D("path"),h=D("path"),p=D("path"),y(n,"d","M18.068,99.825 C16.131,99.825 14.499,99.489 13.173,98.816 C11.852,98.143 10.85,97.122 10.177,95.77 C9.504,94.419 9.162,92.768 9.162,90.825 L9.162,70.814 C9.162,68.348 8.676,66.554 7.711,65.433 C6.739,64.318 5.02,63.726 2.554,63.67 C1.787,63.67 1.171,63.378 0.704,62.786 C0.23,62.2 0,61.522 0,60.762 C0,59.94 0.23,59.261 0.704,58.731 C1.171,58.202 1.794,57.909 2.554,57.853 C5.02,57.791 6.739,57.205 7.711,56.091 C8.682,54.976 9.162,53.207 9.162,50.797 L9.162,30.785 C9.162,27.846 9.928,25.61 11.454,24.084 C12.98,22.558 15.184,21.792 18.068,21.792 L41.592,21.792 C42.476,21.792 43.205,22.06 43.797,22.583 C44.382,23.112 44.681,23.791 44.681,24.607 C44.681,25.373 44.432,26.033 43.934,26.594 C43.435,27.154 42.775,27.435 41.947,27.435 L20.105,27.435 C18.865,27.435 17.925,27.758 17.283,28.406 C16.636,29.054 16.312,30.05 16.312,31.408 L16.312,51.513 C16.312,53.275 15.957,54.876 15.253,56.315 C14.549,57.76 13.609,58.887 12.431,59.715 C11.254,60.537 9.878,60.949 8.29,60.949 L8.29,60.594 C9.878,60.594 11.254,61.005 12.431,61.827 C13.609,62.649 14.549,63.782 15.253,65.227 C15.957,66.666 16.312,68.267 16.312,70.029 L16.312,90.221 C16.312,91.579 16.636,92.575 17.283,93.223 C17.931,93.877 18.871,94.195 20.105,94.195 L41.947,94.195 C42.769,94.195 43.429,94.475 43.934,95.035 C44.438,95.596 44.681,96.256 44.681,97.022 C44.681,97.788 44.388,98.442 43.797,99.003 C43.205,99.563 42.476,99.844 41.592,99.844 L18.068,99.844 L18.068,99.825 Z"),y(n,"id","Path"),y(n,"fill","currentColor"),y(n,"fill-rule","nonzero"),y(o,"d","M78.838,99.825 C77.953,99.825 77.224,99.545 76.633,98.984 C76.041,98.424 75.748,97.764 75.748,97.004 C75.748,96.244 75.997,95.578 76.496,95.017 C76.994,94.457 77.654,94.176 78.483,94.176 L100.325,94.176 C101.564,94.176 102.505,93.859 103.146,93.205 C103.794,92.557 104.118,91.56 104.118,90.203 L104.118,70.011 C104.118,68.248 104.473,66.648 105.177,65.209 C105.88,63.764 106.821,62.637 107.998,61.808 C109.175,60.986 110.551,60.575 112.14,60.575 L112.14,60.93 C110.551,60.93 109.175,60.519 107.998,59.697 C106.821,58.875 105.88,57.741 105.177,56.296 C104.473,54.858 104.118,53.257 104.118,51.494 L104.118,31.39 C104.118,30.038 103.794,29.042 103.146,28.388 C102.498,27.74 101.558,27.416 100.325,27.416 L78.483,27.416 C77.66,27.416 77,27.136 76.496,26.575 C75.997,26.021 75.748,25.355 75.748,24.589 C75.748,23.767 76.041,23.094 76.633,22.564 C77.218,22.035 77.953,21.773 78.838,21.773 L102.361,21.773 C105.245,21.773 107.444,22.54 108.976,24.065 C110.508,25.591 111.268,27.827 111.268,30.767 L111.268,50.778 C111.268,53.189 111.754,54.957 112.719,56.072 C113.69,57.187 115.409,57.779 117.876,57.835 C118.642,57.891 119.258,58.184 119.726,58.713 C120.199,59.242 120.429,59.921 120.429,60.743 C120.429,61.509 120.199,62.182 119.726,62.767 C119.258,63.353 118.636,63.652 117.876,63.652 C115.409,63.708 113.69,64.3 112.719,65.414 C111.747,66.529 111.268,68.323 111.268,70.796 L111.268,90.807 C111.268,92.75 110.931,94.394 110.253,95.752 C109.574,97.11 108.577,98.119 107.257,98.798 C105.93,99.47 104.298,99.807 102.361,99.807 L78.838,99.807 L78.838,99.825 Z"),y(o,"id","Path"),y(o,"fill","currentColor"),y(o,"fill-rule","nonzero"),y(i,"d","M39.827,61.511 C43.785,61.511 46.995,58.301 46.995,54.343 C46.995,50.384 43.785,47.175 39.827,47.175 C35.868,47.175 32.659,50.384 32.659,54.343 C32.659,58.301 35.868,61.511 39.827,61.511 Z"),y(i,"id","Path"),y(i,"fill","currentColor"),y(i,"fill-rule","nonzero"),y(s,"d","M61.599,115.942 C65.558,115.942 68.767,112.733 68.767,108.774 C68.767,104.816 65.558,101.607 61.599,101.607 C57.641,101.607 54.432,104.816 54.432,108.774 C54.432,112.733 57.641,115.942 61.599,115.942 Z"),y(s,"id","Path"),y(s,"fill","currentColor"),y(s,"fill-rule","nonzero"),y(l,"d","M59.872,80.462 C59.724,80.462 59.575,80.462 59.426,80.462 C59.149,80.462 58.872,80.447 58.59,80.431 L58.514,80.431 C57.483,80.375 56.453,80.252 55.453,80.067 L55.089,80.001 C54.945,79.975 54.802,79.944 54.658,79.914 C49.178,78.765 44.097,75.858 40.365,71.736 C39.806,71.121 39.847,70.362 40.072,69.855 C40.318,69.296 40.805,68.952 41.344,68.952 C41.467,68.952 41.595,68.973 41.723,69.004 C47.481,70.598 53.433,72.249 59.313,72.321 L60.431,72.321 C66.312,72.249 72.254,70.603 78.001,69.009 C78.149,68.968 78.278,68.947 78.406,68.947 C78.944,68.947 79.431,69.296 79.677,69.85 C79.903,70.357 79.944,71.116 79.385,71.731 C75.648,75.853 70.572,78.76 65.091,79.908 C64.927,79.944 64.763,79.975 64.599,80.011 L64.297,80.067 C63.292,80.252 62.261,80.37 61.236,80.426 L61.159,80.426 C60.882,80.447 60.605,80.452 60.329,80.457 C60.18,80.457 60.031,80.457 59.882,80.457 L59.872,80.462 Z"),y(l,"id","Path"),y(l,"fill","currentColor"),y(l,"fill-rule","nonzero"),y(a,"d","M50.388,15.193 C50.342,15.193 50.25,15.171 50.112,15.125 C49.975,15.033 49.654,14.895 49.149,14.712 L37.044,8.934 C36.54,8.659 36.288,8.201 36.288,7.559 C36.288,6.963 36.471,6.298 36.838,5.564 C37.205,4.831 37.617,4.212 38.076,3.707 C38.58,3.157 39.039,2.882 39.451,2.882 C39.818,2.882 40.116,2.974 40.346,3.157 L51.763,10.998 C52.222,11.273 52.451,11.617 52.451,12.03 C52.451,12.58 52.199,13.245 51.694,14.024 C51.19,14.804 50.754,15.193 50.388,15.193 Z"),y(a,"id","Path"),y(a,"fill","currentColor"),y(a,"fill-rule","nonzero"),y(c,"d","M73.439,12.312 C73.393,12.312 73.302,12.289 73.164,12.243 C73.027,12.151 72.706,12.014 72.201,11.83 L60.096,6.053 C59.592,5.778 59.339,5.319 59.339,4.677 C59.339,4.081 59.523,3.416 59.89,2.682 C60.256,1.949 60.669,1.33 61.128,0.825 C61.632,0.275 62.091,-1.13686838e-13 62.503,-1.13686838e-13 C62.87,-1.13686838e-13 63.168,0.092 63.397,0.275 L74.815,8.116 C75.273,8.391 75.503,8.735 75.503,9.148 C75.503,9.698 75.25,10.363 74.746,11.142 C74.242,11.922 73.806,12.312 73.439,12.312 Z"),y(c,"id","Path"),y(c,"fill","currentColor"),y(c,"fill-rule","nonzero"),y(d,"d","M52.207,112.646 C52.157,112.676 52.043,112.71 51.864,112.748 C51.657,112.737 51.22,112.794 50.556,112.918 L33.73,114.413 C33.007,114.438 32.439,114.102 32.028,113.406 C31.646,112.76 31.418,111.922 31.346,110.891 C31.273,109.861 31.324,108.925 31.498,108.085 C31.692,107.165 32.013,106.573 32.46,106.308 C32.858,106.073 33.24,105.981 33.606,106.033 L51.009,107.216 C51.683,107.221 52.152,107.447 52.416,107.894 C52.769,108.491 52.921,109.373 52.874,110.541 C52.827,111.71 52.604,112.411 52.207,112.646 Z"),y(d,"id","Path"),y(d,"fill","currentColor"),y(d,"fill-rule","nonzero"),y(h,"d","M71.184,112.646 C71.234,112.676 71.348,112.71 71.526,112.748 C71.734,112.737 72.17,112.794 72.835,112.918 L89.661,114.413 C90.384,114.438 90.951,114.102 91.363,113.406 C91.745,112.76 91.972,111.922 92.045,110.891 C92.117,109.861 92.067,108.925 91.893,108.085 C91.699,107.165 91.378,106.573 90.93,106.308 C90.533,106.073 90.151,105.981 89.785,106.033 L72.381,107.216 C71.708,107.221 71.239,107.447 70.975,107.894 C70.622,108.491 70.469,109.373 70.516,110.541 C70.564,111.71 70.786,112.411 71.184,112.646 Z"),y(h,"id","Path"),y(h,"fill","currentColor"),y(h,"fill-rule","nonzero"),y(p,"d","M79.214,52.635 L76.187,56.552 C74.11,59.223 72.983,59.876 70.965,59.876 C69.837,59.876 68.947,59.341 68.947,58.451 C68.947,57.798 69.244,57.086 69.778,56.315 L74.466,49.727 C75.653,48.065 76.9,47.175 78.977,47.175 L79.511,47.175 C81.588,47.175 82.834,48.065 84.021,49.727 L88.71,56.315 C89.244,57.086 89.541,57.798 89.541,58.451 C89.541,59.341 88.651,59.876 87.523,59.876 C85.386,59.876 84.318,59.223 82.241,56.552 L79.214,52.635 Z"),y(p,"id","Path"),y(p,"fill","currentColor"),y(p,"fill-rule","nonzero"),y(t,"id","Page-1"),y(t,"stroke","none"),y(t,"stroke-width","1"),y(t,"fill","none"),y(t,"fill-rule","evenodd"),y(e,"width","121px"),y(e,"height","116px"),y(e,"viewBox","0 0 121 116"),y(e,"version","1.1"),y(e,"xmlns","http://www.w3.org/2000/svg")},m(f,u){Z(f,e,u),E(e,t),E(t,n),E(t,o),E(t,i),E(t,s),E(t,l),E(t,a),E(t,c),E(t,d),E(t,h),E(t,p)},p:J,i:J,o:J,d(f){f&&U(e)}}}class eh extends Y{constructor(e){super(),X(this,e,null,dd,Q,{})}}function hd(r){let e,t,n,o,i,s,l,a,c,d,h;return{c(){e=D("svg"),t=D("path"),n=D("path"),o=D("path"),i=D("path"),s=D("path"),l=D("path"),a=D("path"),c=D("path"),d=D("path"),h=D("path"),y(t,"d","M18.068 110.71C16.131 110.71 14.499 110.38 13.173 109.7C11.852 109.03 10.85 108.01 10.177 106.66C9.504 105.31 9.16199 103.66 9.16199 101.71V81.7001C9.16199 79.2301 8.676 77.4401 7.711 76.3201C6.739 75.2101 5.02002 74.6102 2.55402 74.5602C1.78702 74.5602 1.17098 74.2602 0.703979 73.6702C0.229979 73.0902 0 72.4102 0 71.6502C0 70.8302 0.229979 70.1502 0.703979 69.6202C1.17098 69.0902 1.79402 68.8002 2.55402 68.7402C5.02002 68.6802 6.739 68.0902 7.711 66.9802C8.682 65.8602 9.16199 64.0902 9.16199 61.6802V41.6702C9.16199 38.7302 9.92798 36.5002 11.454 34.9702C12.98 33.4502 15.184 32.6802 18.068 32.6802H41.592C42.476 32.6802 43.205 32.9502 43.797 33.4702C44.382 34.0002 44.681 34.6802 44.681 35.4902C44.681 36.2602 44.432 36.9202 43.934 37.4802C43.435 38.0402 42.775 38.3202 41.947 38.3202H20.105C18.865 38.3202 17.925 38.6502 17.283 39.2902C16.636 39.9402 16.312 40.9402 16.312 42.3002V62.4002C16.312 64.1602 15.957 65.7601 15.253 67.2001C14.549 68.6501 13.609 69.7702 12.431 70.6002C11.254 71.4202 9.87798 71.8401 8.28998 71.8401V71.4802C9.87798 71.4802 11.254 71.8901 12.431 72.7101C13.609 73.5401 14.549 74.6702 15.253 76.1102C15.957 77.5502 16.312 79.1502 16.312 80.9202V101.11C16.312 102.47 16.636 103.46 17.283 104.11C17.931 104.76 18.871 105.08 20.105 105.08H41.947C42.769 105.08 43.429 105.36 43.934 105.92C44.438 106.48 44.681 107.14 44.681 107.91C44.681 108.68 44.388 109.33 43.797 109.89C43.205 110.45 42.476 110.73 41.592 110.73H18.068V110.71Z"),y(t,"fill","currentColor"),y(n,"d","M78.838 110.71C77.953 110.71 77.225 110.43 76.633 109.87C76.041 109.31 75.749 108.65 75.749 107.89C75.749 107.13 75.998 106.46 76.496 105.9C76.994 105.34 77.654 105.06 78.483 105.06H100.325C101.564 105.06 102.505 104.74 103.146 104.09C103.794 103.44 104.118 102.45 104.118 101.09V80.9002C104.118 79.1302 104.473 77.5301 105.177 76.0901C105.881 74.6501 106.821 73.5201 107.998 72.6901C109.175 71.8701 110.552 71.4601 112.14 71.4601V71.8201C110.552 71.8201 109.175 71.4101 107.998 70.5801C106.821 69.7601 105.881 68.6302 105.177 67.1802C104.473 65.7402 104.118 64.1402 104.118 62.3802V42.2802C104.118 40.9202 103.794 39.9302 103.146 39.2702C102.499 38.6302 101.558 38.3002 100.325 38.3002H78.483C77.661 38.3002 77 38.0202 76.496 37.4602C75.998 36.9102 75.749 36.2402 75.749 35.4702C75.749 34.6502 76.041 33.9802 76.633 33.4502C77.218 32.9202 77.953 32.6602 78.838 32.6602H102.362C105.245 32.6602 107.444 33.4302 108.976 34.9502C110.508 36.4802 111.268 38.7102 111.268 41.6502V61.6602C111.268 64.0702 111.754 65.8401 112.719 66.9601C113.691 68.0701 115.41 68.6602 117.876 68.7202C118.642 68.7802 119.259 69.0702 119.726 69.6002C120.199 70.1302 120.43 70.8102 120.43 71.6302C120.43 72.4002 120.199 73.0702 119.726 73.6502C119.259 74.2402 118.636 74.5402 117.876 74.5402C115.41 74.5902 113.691 75.1902 112.719 76.3002C111.747 77.4202 111.268 79.2102 111.268 81.6802V101.69C111.268 103.64 110.932 105.28 110.253 106.64C109.574 108 108.577 109 107.257 109.68C105.93 110.36 104.299 110.69 102.362 110.69H78.838V110.71Z"),y(n,"fill","currentColor"),y(o,"d","M39.827 76.03C43.786 76.03 46.995 72.82 46.995 68.86C46.995 64.9 43.786 61.6899 39.827 61.6899C35.868 61.6899 32.659 64.9 32.659 68.86C32.659 72.82 35.868 76.03 39.827 76.03Z"),y(o,"fill","currentColor"),y(i,"d","M28.903 57.47L28.908 57.46H28.913C29.272 57.09 29.484 56.58 29.484 55.91C29.484 55.44 29.335 54.98 29.096 54.63C28.866 54.29 28.492 53.98 28.016 53.98C27.811 53.98 27.62 54.02 27.465 54.08C27.332 54.13 27.184 54.2 27.074 54.3C23.331 57.37 21.319 61.57 21.319 67.13C21.319 72.7 23.331 76.89 27.074 79.96C27.184 80.06 27.332 80.1399 27.465 80.1899C27.62 80.2399 27.811 80.29 28.016 80.29C28.492 80.29 28.866 79.97 29.096 79.63C29.335 79.28 29.484 78.83 29.484 78.36C29.484 77.68 29.272 77.1701 28.913 76.8101L28.908 76.8H28.903C26.193 74.21 24.895 71.29 24.895 67.13C24.895 62.98 26.193 60.06 28.903 57.47Z"),y(i,"fill","currentColor"),y(i,"stroke","currentColor"),y(i,"stroke-width","0.907194"),y(s,"d","M50.931 76.8H50.925L50.92 76.8101C50.562 77.1701 50.349 77.68 50.349 78.36C50.349 78.83 50.498 79.28 50.737 79.63C50.967 79.97 51.342 80.29 51.818 80.29C52.022 80.29 52.213 80.2499 52.368 80.1899C52.501 80.1399 52.649 80.06 52.759 79.96C56.502 76.89 58.514 72.7 58.514 67.13C58.514 61.57 56.502 57.37 52.759 54.3C52.649 54.2 52.501 54.13 52.368 54.08C52.213 54.02 52.022 53.98 51.818 53.98C51.342 53.98 50.967 54.29 50.737 54.63C50.498 54.98 50.349 55.44 50.349 55.91C50.349 56.58 50.562 57.09 50.92 57.46H50.925L50.931 57.47C53.64 60.06 54.938 62.98 54.938 67.13C54.938 71.29 53.64 74.21 50.931 76.8Z"),y(s,"fill","currentColor"),y(s,"stroke","currentColor"),y(s,"stroke-width","0.907194"),y(l,"d","M68.819 57.47L68.825 57.46H68.83C69.188 57.09 69.4 56.58 69.4 55.91C69.4 55.44 69.251 54.98 69.013 54.63C68.782 54.29 68.408 53.98 67.932 53.98C67.728 53.98 67.537 54.02 67.382 54.08C67.248 54.13 67.1 54.2 66.991 54.3C63.248 57.37 61.236 61.57 61.236 67.13C61.236 72.7 63.248 76.89 66.991 79.96C67.1 80.06 67.248 80.1399 67.382 80.1899C67.537 80.2399 67.728 80.29 67.932 80.29C68.408 80.29 68.782 79.97 69.013 79.63C69.251 79.28 69.4 78.83 69.4 78.36C69.4 77.68 69.188 77.1701 68.83 76.8101L68.825 76.8H68.819C66.109 74.21 64.812 71.29 64.812 67.13C64.812 62.98 66.109 60.06 68.819 57.47Z"),y(l,"fill","currentColor"),y(l,"stroke","currentColor"),y(l,"stroke-width","0.907194"),y(a,"d","M90.847 76.8H90.842L90.836 76.8101C90.478 77.1701 90.266 77.68 90.266 78.36C90.266 78.83 90.415 79.28 90.654 79.63C90.884 79.97 91.258 80.29 91.734 80.29C91.938 80.29 92.129 80.2499 92.284 80.1899C92.418 80.1399 92.566 80.06 92.676 79.96C96.418 76.89 98.431 72.7 98.431 67.13C98.431 61.57 96.418 57.37 92.676 54.3C92.566 54.2 92.418 54.13 92.284 54.08C92.129 54.02 91.938 53.98 91.734 53.98C91.258 53.98 90.884 54.29 90.654 54.63C90.415 54.98 90.266 55.44 90.266 55.91C90.266 56.58 90.478 57.09 90.836 57.46H90.842L90.847 57.47C93.557 60.06 94.854 62.98 94.854 67.13C94.854 71.29 93.557 74.21 90.847 76.8Z"),y(a,"fill","currentColor"),y(a,"stroke","currentColor"),y(a,"stroke-width","0.907194"),y(c,"d","M44.445 84.1503L44.44 84.1403C43.96 83.5903 43.268 83.2402 42.287 83.2402C41.627 83.2402 40.986 83.4802 40.507 83.8602C40.03 84.2402 39.69 84.7802 39.69 85.3702C39.69 85.6602 39.743 85.9403 39.815 86.1703C39.883 86.3903 39.976 86.5902 40.076 86.7002C44.904 93.5802 51.5 97.3003 60.328 97.3003C69.157 97.3003 75.753 93.5802 80.581 86.7002C80.681 86.5902 80.773 86.3903 80.842 86.1703C80.914 85.9403 80.967 85.6602 80.967 85.3702C80.967 84.7802 80.626 84.2402 80.15 83.8602C79.671 83.4802 79.029 83.2402 78.37 83.2402C77.389 83.2402 76.697 83.5903 76.216 84.1403L76.212 84.1503C74.075 86.7503 71.802 88.6702 69.219 89.9402C66.637 91.2102 63.734 91.8402 60.328 91.8402C56.923 91.8402 54.02 91.2102 51.438 89.9402C48.855 88.6702 46.582 86.7503 44.445 84.1503Z"),y(c,"fill","currentColor"),y(c,"stroke","currentColor"),y(c,"stroke-width","0.453597"),y(d,"d","M54.456 28.9769L55.988 10.851C56 10.336 56.719 10.095 58.143 10.129C58.748 10.143 59.264 10.2259 59.693 10.3759C60.078 10.5259 60.268 10.718 60.262 10.952C60.338 13.202 60.416 15.4059 60.494 17.5619C60.572 19.7419 60.655 21.6999 60.743 23.4349C60.788 25.1929 60.841 26.587 60.903 27.619C60.922 28.65 60.931 29.166 60.931 29.166C60.923 29.517 60.593 29.7549 59.942 29.8799C59.248 30.0269 58.49 30.091 57.67 30.072C56.763 30.05 56.01 29.9509 55.409 29.7719C54.765 29.6169 54.447 29.3509 54.456 28.9769ZM55.486 4.69092L55.557 0.852965C55.564 0.548965 55.828 0.343951 56.349 0.238951C56.871 0.110951 57.52 0.0559499 58.297 0.0749499C59.117 0.0939499 59.785 0.179945 60.3 0.332945C60.815 0.462945 61.069 0.678946 61.062 0.983947L60.991 4.82092C60.983 5.14892 60.719 5.36491 60.198 5.46991C59.677 5.57491 59.028 5.61791 58.251 5.59991C57.431 5.57991 56.763 5.50591 56.248 5.37591C55.732 5.24691 55.478 5.01792 55.486 4.69092Z"),y(d,"fill","currentColor"),y(h,"d","M80.121 67.15L77.095 71.0699C75.017 73.7399 73.89 74.39 71.872 74.39C70.744 74.39 69.854 73.86 69.854 72.97C69.854 72.31 70.151 71.6 70.685 70.83L75.373 64.24C76.56 62.58 77.807 61.6899 79.884 61.6899H80.418C82.495 61.6899 83.742 62.58 84.929 64.24L89.617 70.83C90.151 71.6 90.448 72.31 90.448 72.97C90.448 73.86 89.558 74.39 88.43 74.39C86.294 74.39 85.225 73.7399 83.148 71.0699L80.121 67.15Z"),y(h,"fill","currentColor"),y(e,"width","121"),y(e,"height","111"),y(e,"viewBox","0 0 121 111"),y(e,"fill","none"),y(e,"xmlns","http://www.w3.org/2000/svg")},m(p,f){Z(p,e,f),E(e,t),E(e,n),E(e,o),E(e,i),E(e,s),E(e,l),E(e,a),E(e,c),E(e,d),E(e,h)},p:J,i:J,o:J,d(p){p&&U(e)}}}class th extends Y{constructor(e){super(),X(this,e,null,hd,Q,{})}}const pd=r=>({}),Xi=r=>({}),ud=r=>({}),Qi=r=>({}),fd=r=>({}),es=r=>({}),md=r=>({}),ts=r=>({});function ns(r){let e;const t=r[6].leftIcon,n=Ce(t,r,r[7],ts);return{c(){n&&n.c()},m(o,i){n&&n.m(o,i),e=!0},p(o,i){n&&n.p&&(!e||128&i)&&ye(n,t,o,o[7],e?ve(t,o[7],i,md):we(o[7]),ts)},i(o){e||(I(n,o),e=!0)},o(o){z(n,o),e=!1},d(o){n&&n.d(o)}}}function rs(r){let e,t;const n=r[6].text,o=Ce(n,r,r[7],es);return{c(){e=be("span"),o&&o.c(),y(e,"class","c-text-combo__text svelte-1pddlam")},m(i,s){Z(i,e,s),o&&o.m(e,null),t=!0},p(i,s){o&&o.p&&(!t||128&s)&&ye(o,n,i,i[7],t?ve(n,i[7],s,fd):we(i[7]),es)},i(i){t||(I(o,i),t=!0)},o(i){z(o,i),t=!1},d(i){i&&U(e),o&&o.d(i)}}}function is(r){let e,t,n;const o=r[6].grayText,i=Ce(o,r,r[7],Qi);return{c(){e=be("div"),t=be("div"),i&&i.c(),y(t,"class","c-text-combo__gray-text svelte-1pddlam"),y(e,"class","c-text-combo__gray svelte-1pddlam"),qe(e,"c-text-combo--gray-truncate-left",r[3]==="left"),qe(e,"c-text-combo--gray-truncate-right",r[3]==="right")},m(s,l){Z(s,e,l),E(e,t),i&&i.m(t,null),n=!0},p(s,l){i&&i.p&&(!n||128&l)&&ye(i,o,s,s[7],n?ve(o,s[7],l,ud):we(s[7]),Qi),(!n||8&l)&&qe(e,"c-text-combo--gray-truncate-left",s[3]==="left"),(!n||8&l)&&qe(e,"c-text-combo--gray-truncate-right",s[3]==="right")},i(s){n||(I(i,s),n=!0)},o(s){z(i,s),n=!1},d(s){s&&U(e),i&&i.d(s)}}}function ss(r){let e;const t=r[6].rightIcon,n=Ce(t,r,r[7],Xi);return{c(){n&&n.c()},m(o,i){n&&n.m(o,i),e=!0},p(o,i){n&&n.p&&(!e||128&i)&&ye(n,t,o,o[7],e?ve(t,o[7],i,pd):we(o[7]),Xi)},i(o){e||(I(n,o),e=!0)},o(o){z(n,o),e=!1},d(o){n&&n.d(o)}}}function gd(r){let e,t,n,o,i,s,l=r[5].leftIcon&&ns(r),a=r[5].text&&rs(r),c=r[5].grayText&&is(r),d=r[5].rightIcon&&ss(r);return{c(){e=be("div"),l&&l.c(),t=Pe(),a&&a.c(),n=Pe(),c&&c.c(),o=Pe(),d&&d.c(),y(e,"class",i=Xt(`c-text-combo ${r[0]}`)+" svelte-1pddlam"),qe(e,"c-text-combo--align-right",r[2]==="right"),qe(e,"c-text-combo--shrink",r[4])},m(h,p){Z(h,e,p),l&&l.m(e,null),E(e,t),a&&a.m(e,null),E(e,n),c&&c.m(e,null),E(e,o),d&&d.m(e,null),s=!0},p(h,p){h[5].leftIcon?l?(l.p(h,p),32&p&&I(l,1)):(l=ns(h),l.c(),I(l,1),l.m(e,t)):l&&(He(),z(l,1,1,()=>{l=null}),je()),h[5].text?a?(a.p(h,p),32&p&&I(a,1)):(a=rs(h),a.c(),I(a,1),a.m(e,n)):a&&(He(),z(a,1,1,()=>{a=null}),je()),h[5].grayText?c?(c.p(h,p),32&p&&I(c,1)):(c=is(h),c.c(),I(c,1),c.m(e,o)):c&&(He(),z(c,1,1,()=>{c=null}),je()),h[5].rightIcon?d?(d.p(h,p),32&p&&I(d,1)):(d=ss(h),d.c(),I(d,1),d.m(e,null)):d&&(He(),z(d,1,1,()=>{d=null}),je()),(!s||1&p&&i!==(i=Xt(`c-text-combo ${h[0]}`)+" svelte-1pddlam"))&&y(e,"class",i),(!s||5&p)&&qe(e,"c-text-combo--align-right",h[2]==="right"),(!s||17&p)&&qe(e,"c-text-combo--shrink",h[4])},i(h){s||(I(l),I(a),I(c),I(d),s=!0)},o(h){z(l),z(a),z(c),z(d),s=!1},d(h){h&&U(e),l&&l.d(),a&&a.d(),c&&c.d(),d&&d.d()}}}function Cd(r){let e,t;return e=new ms({props:{size:r[1],$$slots:{default:[gd]},$$scope:{ctx:r}}}),{c(){Ne(e.$$.fragment)},m(n,o){$e(e,n,o),t=!0},p(n,[o]){const i={};2&o&&(i.size=n[1]),189&o&&(i.$$scope={dirty:o,ctx:n}),e.$set(i)},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){z(e.$$.fragment,n),t=!1},d(n){De(e,n)}}}function yd(r,e,t){let{$$slots:n={},$$scope:o}=e;const i=Zo(n);let{class:s=""}=e,{size:l=1}=e,{align:a="left"}=e,{greyTextTruncateDirection:c="right"}=e,{shrink:d=!1}=e;return r.$$set=h=>{"class"in h&&t(0,s=h.class),"size"in h&&t(1,l=h.size),"align"in h&&t(2,a=h.align),"greyTextTruncateDirection"in h&&t(3,c=h.greyTextTruncateDirection),"shrink"in h&&t(4,d=h.shrink),"$$scope"in h&&t(7,o=h.$$scope)},[s,l,a,c,d,i,n,o]}class nh extends Y{constructor(e){super(),X(this,e,yd,Cd,Q,{class:0,size:1,align:2,greyTextTruncateDirection:3,shrink:4})}}function wd(r){let e,t;return{c(){e=D("svg"),t=D("path"),y(t,"fill-rule","evenodd"),y(t,"clip-rule","evenodd"),y(t,"d","M4.5 1C4.22386 1 4 1.22386 4 1.5C4 1.77614 4.22386 2 4.5 2C5.42215 2 6.0399 2.23054 6.42075 2.56379C6.79286 2.88939 7 3.36626 7 4V7H5.75C5.47386 7 5.25 7.22386 5.25 7.5C5.25 7.77614 5.47386 8 5.75 8H7V11C7 11.6337 6.79286 12.1106 6.42075 12.4362C6.0399 12.7695 5.42215 13 4.5 13C4.22386 13 4 13.2239 4 13.5C4 13.7761 4.22386 14 4.5 14C5.57785 14 6.4601 13.7305 7.07925 13.1888C7.24168 13.0467 7.38169 12.8896 7.5 12.7198C7.61832 12.8896 7.75832 13.0467 7.92075 13.1888C8.5399 13.7305 9.42215 14 10.5 14C10.7761 14 11 13.7761 11 13.5C11 13.2239 10.7761 13 10.5 13C9.57785 13 8.9601 12.7695 8.57925 12.4362C8.20714 12.1106 8 11.6337 8 11V8H9.25C9.52614 8 9.75 7.77614 9.75 7.5C9.75 7.22386 9.52614 7 9.25 7H8V4C8 3.36626 8.20714 2.88939 8.57925 2.56379C8.9601 2.23054 9.57785 2 10.5 2C10.7761 2 11 1.77614 11 1.5C11 1.22386 10.7761 1 10.5 1C9.42215 1 8.5399 1.26946 7.92075 1.81121C7.75832 1.95333 7.61832 2.11043 7.5 2.28023C7.38168 2.11043 7.24168 1.95333 7.07925 1.81121C6.4601 1.26946 5.57785 1 4.5 1Z"),y(t,"fill","currentColor"),y(e,"width","15"),y(e,"height","15"),y(e,"viewBox","0 0 15 15"),y(e,"fill","none"),y(e,"xmlns","http://www.w3.org/2000/svg")},m(n,o){Z(n,e,o),E(e,t)},p:J,i:J,o:J,d(n){n&&U(e)}}}class oh extends Y{constructor(e){super(),X(this,e,null,wd,Q,{})}}function ls(r){let e,t;return e=new ms({props:{size:1,$$slots:{default:[bd]},$$scope:{ctx:r}}}),{c(){Ne(e.$$.fragment)},m(n,o){$e(e,n,o),t=!0},p(n,o){const i={};39&o&&(i.$$scope={dirty:o,ctx:n}),e.$set(i)},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){z(e.$$.fragment,n),t=!1},d(n){De(e,n)}}}function vd(r){let e,t;return e=new Fl({props:{slot:"leftIcon",class:"c-guidelines-filespan-warning-icon"}}),{c(){Ne(e.$$.fragment)},m(n,o){$e(e,n,o),t=!0},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){z(e.$$.fragment,n),t=!1},d(n){De(e,n)}}}function xd(r){let e,t;return e=new Hl({}),{c(){Ne(e.$$.fragment)},m(n,o){$e(e,n,o),t=!0},i(n){t||(I(e.$$.fragment,n),t=!0)},o(n){z(e.$$.fragment,n),t=!1},d(n){De(e,n)}}}function bd(r){let e,t,n,o,i,s,l,a,c,d;const h=[xd,vd],p=[];function f(u,m){return u[1].guidelinesOverLimit?1:0}return t=f(r),n=p[t]=h[t](r),{c(){e=be("div"),n.c(),o=Pe(),i=be("span"),s=cs(r[2]),y(i,"class","c-guidelines-filespan__text svelte-1jd2qvj"),y(i,"role","button"),y(i,"tabindex","0"),y(e,"class",l=Xt(`c-guidelines-filespan ${r[0]}`)+" svelte-1jd2qvj")},m(u,m){Z(u,e,m),p[t].m(e,null),E(e,o),E(e,i),E(i,s),a=!0,c||(d=[Ht(i,"click",r[3]),Ht(i,"keydown",Md)],c=!0)},p(u,m){let g=t;t=f(u),t!==g&&(He(),z(p[g],1,1,()=>{p[g]=null}),je(),n=p[t],n||(n=p[t]=h[t](u),n.c()),I(n,1),n.m(e,o)),(!a||4&m)&&ds(s,u[2]),(!a||1&m&&l!==(l=Xt(`c-guidelines-filespan ${u[0]}`)+" svelte-1jd2qvj"))&&y(e,"class",l)},i(u){a||(I(n),a=!0)},o(u){z(n),a=!1},d(u){u&&U(e),p[t].d(),c=!1,ps(d)}}}function kd(r){let e,t,n=r[1].guidelinesEnabled&&ls(r);return{c(){n&&n.c(),e=us()},m(o,i){n&&n.m(o,i),Z(o,e,i),t=!0},p(o,[i]){o[1].guidelinesEnabled?n?(n.p(o,i),2&i&&I(n,1)):(n=ls(o),n.c(),I(n,1),n.m(e.parentNode,e)):n&&(He(),z(n,1,1,()=>{n=null}),je())},i(o){t||(I(n),t=!0)},o(o){z(n),t=!1},d(o){o&&U(e),n&&n.d(o)}}}const Md=()=>{};function Sd(r,e,t){let n,{class:o=""}=e,{sourceFolder:i}=e;const s=Ke("chatModel");return r.$$set=l=>{"class"in l&&t(0,o=l.class),"sourceFolder"in l&&t(1,i=l.sourceFolder)},r.$$.update=()=>{2&r.$$.dirty&&t(2,n=i.guidelinesEnabled&&!i.guidelinesOverLimit?"Edit workspace guidelines":`Workspace guidelines exceeded ${i.guidelinesLengthLimit} character limit`)},[o,i,n,()=>{s.extensionClient.openGuidelines(i.folderRoot)}]}class rh extends Y{constructor(e){super(),X(this,e,Sd,kd,Q,{class:0,sourceFolder:1})}}function Od(r){let e,t;return{c(){e=D("svg"),t=D("ellipse"),y(t,"cx","51"),y(t,"cy","4.5"),y(t,"rx","51"),y(t,"ry","4.5"),y(t,"fill","currentColor"),y(e,"width","102"),y(e,"height","9"),y(e,"viewBox","0 0 102 9"),y(e,"fill","none"),y(e,"xmlns","http://www.w3.org/2000/svg")},m(n,o){Z(n,e,o),E(e,t)},p:J,i:J,o:J,d(n){n&&U(e)}}}class Td extends Y{constructor(e){super(),X(this,e,null,Od,Q,{})}}function as(r){let e,t,n;return t=new Td({}),{c(){e=be("div"),Ne(t.$$.fragment),y(e,"class","c-augment-logo-animated__shadow svelte-hwxi20")},m(o,i){Z(o,e,i),$e(t,e,null),n=!0},i(o){n||(I(t.$$.fragment,o),n=!0)},o(o){z(t.$$.fragment,o),n=!1},d(o){o&&U(e),De(t)}}}function Ed(r){let e,t,n,o;const i=r[8].default,s=Ce(i,r,r[7],null),l=s||function(c){let d,h;return d=new jl({}),{c(){Ne(d.$$.fragment)},m(p,f){$e(d,p,f),h=!0},i(p){h||(I(d.$$.fragment,p),h=!0)},o(p){z(d.$$.fragment,p),h=!1},d(p){De(d,p)}}}();let a=r[0]&&as();return{c(){e=be("div"),t=be("div"),l&&l.c(),n=Pe(),a&&a.c(),y(t,"class","c-augment-logo-animated__icon svelte-hwxi20"),y(e,"class","c-augment-logo-animated svelte-hwxi20"),y(e,"style",r[2]),qe(e,"c-augment-logo-animated--animated",r[1])},m(c,d){Z(c,e,d),E(e,t),l&&l.m(t,null),E(e,n),a&&a.m(e,null),o=!0},p(c,[d]){s&&s.p&&(!o||128&d)&&ye(s,i,c,c[7],o?ve(i,c[7],d,null):we(c[7]),null),c[0]?a?1&d&&I(a,1):(a=as(),a.c(),I(a,1),a.m(e,null)):a&&(He(),z(a,1,1,()=>{a=null}),je()),(!o||4&d)&&y(e,"style",c[2]),(!o||2&d)&&qe(e,"c-augment-logo-animated--animated",c[1])},i(c){o||(I(l,c),I(a),o=!0)},o(c){z(l,c),z(a),o=!1},d(c){c&&U(e),l&&l.d(c),a&&a.d()}}}function Nd(r,e,t){let n,o,{$$slots:i={},$$scope:s}=e,{heightPx:l=160}=e,{floatHeight:a=20}=e,{animationDuration:c=3}=e,{showShadow:d=!0}=e,{animated:h=!0}=e;return r.$$set=p=>{"heightPx"in p&&t(3,l=p.heightPx),"floatHeight"in p&&t(4,a=p.floatHeight),"animationDuration"in p&&t(5,c=p.animationDuration),"showShadow"in p&&t(0,d=p.showShadow),"animated"in p&&t(1,h=p.animated),"$$scope"in p&&t(7,s=p.$$scope)},r.$$.update=()=>{8&r.$$.dirty&&t(6,n=Math.round(.875*l)),120&r.$$.dirty&&t(2,o=`
    --augment-logo-height: ${l}px;
    --augment-logo-icon-size: ${n}px;
    --augment-logo-float-height: ${a}px;
    --animation-duration: ${c}s;
  `)},[d,h,o,l,a,c,n,s,i]}class ih extends Y{constructor(e){super(),X(this,e,Nd,Ed,Q,{heightPx:3,floatHeight:4,animationDuration:5,showShadow:0,animated:1})}}function $d(r){let e,t;return{c(){e=D("svg"),t=D("path"),y(t,"fill-rule","evenodd"),y(t,"clip-rule","evenodd"),y(t,"d","M14.5 2H9L8.65002 2.15002L8 2.79004L7.34998 2.15002L7 2H1.5L1 2.5V12.5L1.5 13H6.78998L7.65002 13.85H8.34998L9.21002 13H14.5L15 12.5V2.5L14.5 2ZM7.5 12.3199L7.32001 12.15L7 12H2V3H6.78998L7.53003 3.73999L7.5 12.3199ZM14 12H9L8.65002 12.15L8.51001 12.28V3.69995L9.21002 3H14V12ZM6 5H3V6H6V5ZM6 9H3V10H6V9ZM3 7H6V8H3V7ZM13 5H10V6H13V5ZM10 7H13V8H10V7ZM10 9H13V10H10V9Z"),y(t,"fill","currentColor"),y(e,"width","16"),y(e,"height","16"),y(e,"viewBox","0 0 16 16"),y(e,"fill","none"),y(e,"xmlns","http://www.w3.org/2000/svg")},m(n,o){Z(n,e,o),E(e,t)},p:J,i:J,o:J,d(n){n&&U(e)}}}class sh extends Y{constructor(e){super(),X(this,e,null,$d,Q,{})}}function Dd(r){let e,t,n,o;return{c(){e=D("svg"),t=D("path"),n=D("path"),o=D("path"),y(t,"fill-rule","evenodd"),y(t,"clip-rule","evenodd"),y(t,"d","M7.70996 3H14.5L15.01 3.5V9V13.5L14.51 14H8.74284C8.99647 13.6929 9.21739 13.3578 9.40029 13H13.99V11.49L14 7.48999V5.98999H7.68994L7.67296 6.00697C7.39684 5.81162 7.10191 5.64108 6.79144 5.4986L7.14001 5.15002L7.48999 5H13.99L14 4.01001H7.5L7.14001 3.85999L6.29004 3.01001H2V5.59971C1.6461 5.78062 1.31438 5.99874 1.01001 6.24892V2.5L1.51001 2H6.51001L6.85999 2.15002L7.70996 3Z"),y(t,"fill","currentColor"),y(n,"d","M6 10.5C6 11.3284 5.32843 12 4.5 12C3.67157 12 3 11.3284 3 10.5C3 9.67157 3.67157 9 4.5 9C5.32843 9 6 9.67157 6 10.5Z"),y(n,"fill","currentColor"),y(o,"fill-rule","evenodd"),y(o,"clip-rule","evenodd"),y(o,"d","M8 10.5C8 12.433 6.433 14 4.5 14C2.567 14 1 12.433 1 10.5C1 8.567 2.567 7 4.5 7C6.433 7 8 8.567 8 10.5ZM4.5 13C5.88071 13 7 11.8807 7 10.5C7 9.11929 5.88071 8 4.5 8C3.11929 8 2 9.11929 2 10.5C2 11.8807 3.11929 13 4.5 13Z"),y(o,"fill","currentColor"),y(e,"width","16"),y(e,"height","16"),y(e,"viewBox","0 0 16 16"),y(e,"fill","none"),y(e,"xmlns","http://www.w3.org/2000/svg")},m(i,s){Z(i,e,s),E(e,t),E(e,n),E(e,o)},p:J,i:J,o:J,d(i){i&&U(e)}}}class lh extends Y{constructor(e){super(),X(this,e,null,Dd,Q,{})}}const Ad=Ae.create({name:"placeholder",addOptions:()=>({emptyEditorClass:"is-editor-empty",emptyNodeClass:"is-empty",placeholder:"Write something …",showOnlyWhenEditable:!0,considerAnyAsEmpty:!1,showOnlyCurrent:!0,includeChildren:!1}),addProseMirrorPlugins(){return[new Te({key:new Oe("placeholder"),props:{decorations:({doc:r,selection:e})=>{var t;const n=this.editor.isEditable||!this.options.showOnlyWhenEditable,{anchor:o}=e,i=[];if(!n)return null;const{firstChild:s}=r.content,l=s&&s.type.isLeaf,a=s&&s.isAtom,c=!!this.options.considerAnyAsEmpty||s&&s.type.name===((t=r.type.contentMatch.defaultType)===null||t===void 0?void 0:t.name),d=r.content.childCount<=1&&s&&c&&s.nodeSize<=2&&(!l||!a);return r.descendants((h,p)=>{const f=o>=p&&o<=p+h.nodeSize,u=!h.isLeaf&&!h.childCount;if((f||!this.options.showOnlyCurrent)&&u){const m=[this.options.emptyNodeClass];d&&m.push(this.options.emptyEditorClass);const g=Se.node(p,p+h.nodeSize,{class:m.join(" "),"data-placeholder":typeof this.options.placeholder=="function"?this.options.placeholder({editor:this.editor,node:h,pos:p,hasAnchor:f}):this.options.placeholder});i.push(g)}return this.options.includeChildren}),te.create(r,i)}}})]}});class Ld{constructor(e){x(this,"_placeholderExtension");x(this,"_editor");x(this,"setPlaceholder",e=>{var t;this._placeholderExtension.options.placeholder=e,(t=this._editor)==null||t.view.updateState(this._editor.view.state)});x(this,"_onUpdate",e=>{this._editor=e});x(this,"_onDestroy",()=>{this._editor=void 0});const t=this._onUpdate.bind(this),n=this._onDestroy.bind(this);this._placeholderExtension=Ad.extend({placeholder:e,onUpdate(){var o;(o=this.parent)==null||o.call(this),t(this.editor)},onDestroy(){var o;(o=this.parent)==null||o.call(this),n()}})}get tipTapExtension(){return this._placeholderExtension}}function Id(r,e,t){let{placeholder:n="Type something..."}=e;const o=new Ld(n),i=Ke(Be.CONTEXT_KEY);return Go(i.pluginManager.registerPlugin(o)),r.$$set=s=>{"placeholder"in s&&t(0,n=s.placeholder)},r.$$.update=()=>{1&r.$$.dirty&&o.setPlaceholder(n)},[n]}class ah extends Y{constructor(e){super(),X(this,e,Id,null,Q,{placeholder:0})}}const Ve=class Ve{constructor(e){x(this,"_tipTapExtension");x(this,"_keydownHandler",()=>!1);x(this,"updateOptions",e=>{this._options={...this._options,...e},this._keydownHandler=dl(this._options.shortcuts)});x(this,"_handleKeyDown",(e,t)=>this._keydownHandler(e,t));this._options=e,this.updateOptions(this._options);const t=this._handleKeyDown,n=Ve._getNextPluginId(),o=new Oe(n);this._tipTapExtension=Ae.create({name:n,addProseMirrorPlugins:()=>[new Te({key:o,props:{handleKeyDown:t}})]})}get tipTapExtension(){return this._tipTapExtension}};x(Ve,"_sequenceId",0),x(Ve,"KEYBINDINGS_PLUGIN_KEY_BASE","augment-keybindings-plugin-{}"),x(Ve,"_getSequenceId",()=>Ve._sequenceId++),x(Ve,"_getNextPluginId",()=>{const e=Ve._getSequenceId().toString();return Ve.KEYBINDINGS_PLUGIN_KEY_BASE.replace("{}",e)});let Uo=Ve;function Pd(r,e,t){let{shortcuts:n={}}=e;const o=Ke(Be.CONTEXT_KEY),i=new Uo({shortcuts:n}),s=o.pluginManager.registerPlugin(i);return Go(s),r.$$set=l=>{"shortcuts"in l&&t(0,n=l.shortcuts)},r.$$.update=()=>{1&r.$$.dirty&&i.updateOptions({shortcuts:n})},[n]}class ch extends Y{constructor(e){super(),X(this,e,Pd,null,Q,{shortcuts:0})}}export{Xd as A,Qd as B,Kd as C,Ae as E,rh as G,Jd as K,Yd as M,pt as N,eh as P,th as R,nh as T,ih as a,oh as b,lh as c,sh as d,Gd as e,Wd as f,ch as g,ah as h,Be as i,_ as j,Oe as k,Te as l,xt as m,Ud as n,Zd as o};
