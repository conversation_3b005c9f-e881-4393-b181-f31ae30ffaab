var se=Object.defineProperty;var re=(r,e,n)=>e in r?se(r,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):r[e]=n;var V=(r,e,n)=>re(r,typeof e!="symbol"?e+"":e,n);import{S as U,i as X,s as ee,Q as D,R as W,V as x,W as w,Y,u as f,t as d,Z as y,$ as P,J as L,T as B,c as E,e as v,f as M,h as b,n as N,af as oe,a0 as O,D as ae,q as k,r as J,ae as le,a1 as te,P as ce,ad as T,a2 as ie,ah as j}from"./SpinnerAugment-BUJasFTo.js";import"./design-system-init-BKdwvVur.js";import{h as S,W as Z}from"./BaseButton-ci_067e0.js";import{e as _}from"./each-DUdYBCJG.js";import{O as $e}from"./OpenFileButton-Cgk3MGWq.js";import{C as pe,E as me}from"./chat-flags-model-pSBfdnEi.js";import{M as ne}from"./TextTooltipAugment-UDQF2J4S.js";import{D as I}from"./index-yg8vr2DA.js";import{C as fe}from"./chevron-down-BPcCn3Z6.js";import{M as ue}from"./MarkdownEditor-CL85cpd_.js";import"./open-in-new-window-eiueNVFd.js";import"./types-CF53Ux0u.js";import"./file-base64-RhZyEMB8.js";import"./file-paths-BcSg4gks.js";import"./types-e72Yl75f.js";import"./Content-CSmc2GUv.js";import"./globals-D0QH3NT1.js";import"./ButtonAugment-DbAwCSeR.js";import"./IconButtonAugment-DFy7vWkh.js";import"./CardAugment-DvO45c5p.js";import"./BaseTextInput-BYcZ2XaJ.js";import"./TextAreaAugment-FdvYFnJr.js";import"./lodash-BHrlUNHT.js";class q{static hasFrontmatter(e){return this.frontmatterRegex.test(e)}static extractFrontmatter(e){const n=e.match(this.frontmatterRegex);return n&&n[1]?n[1]:null}static extractContent(e){return e.replace(this.frontmatterRegex,"")}static parseBoolean(e,n,t=!0){const o=this.extractFrontmatter(e);if(o){const s=new RegExp(`${n}\\s*:\\s*(true|false)`,"i"),a=o.match(s);if(a&&a[1])return a[1].toLowerCase()==="true"}return t}static parseString(e,n,t=""){const o=this.extractFrontmatter(e);if(o){const s=new RegExp(`${n}\\s*:\\s*["']?([^"'
]*)["']?`,"i"),a=o.match(s);if(a&&a[1])return a[1].trim()}return t}static updateFrontmatter(e,n,t){const o=e.match(this.frontmatterRegex),s=typeof t!="string"||/^(true|false)$/.test(t.toLowerCase())?t:`"${t}"`;if(o){const a=o[1],c=new RegExp(`(${n}\\s*:\\s*)([^\\n]*)`,"i");if(a.match(c)){const l=a.replace(c,`$1${s}`);return e.replace(this.frontmatterRegex,`---
${l}---
`)}{const l=`${a.endsWith(`
`)?a:a+`
`}${n}: ${s}
`;return e.replace(this.frontmatterRegex,`---
${l}---
`)}}return`---
${n}: ${s}
---

${e}`}static createFrontmatter(e,n){let t=e;this.hasFrontmatter(t)&&(t=this.extractContent(t));for(const[o,s]of Object.entries(n))t=this.updateFrontmatter(t,o,s);return t}}V(q,"frontmatterRegex",/^---\s*\n([\s\S]*?)\n---\s*\n/);function G(r,e,n){const t=r.slice();return t[21]=e[n],t}function de(r){let e;return{c(){e=O("Rules Trigger Mode:")},m(n,t){v(n,e,t)},d(n){n&&b(e)}}}function ge(r){let e,n=r[4].label+"";return{c(){e=O(n)},m(t,o){v(t,e,o)},p(t,o){16&o&&n!==(n=t[4].label+"")&&te(e,n)},d(t){t&&b(e)}}}function he(r){let e,n,t,o,s,a;return n=new P({props:{size:1,$$slots:{default:[ge]},$$scope:{ctx:r}}}),s=new fe({}),{c(){e=L("div"),x(n.$$.fragment),t=B(),o=L("div"),x(s.$$.fragment),E(o,"class","c-dropdown-icon svelte-93gnsz"),E(e,"class","c-dropdown-trigger svelte-93gnsz")},m(c,l){v(c,e,l),w(n,e,null),M(e,t),M(e,o),w(s,o,null),a=!0},p(c,l){const $={};16777232&l&&($.$$scope={dirty:l,ctx:c}),n.$set($)},i(c){a||(f(n.$$.fragment,c),f(s.$$.fragment,c),a=!0)},o(c){d(n.$$.fragment,c),d(s.$$.fragment,c),a=!1},d(c){c&&b(e),y(n),y(s)}}}function xe(r){let e,n=r[21].label+"";return{c(){e=O(n)},m(t,o){v(t,e,o)},p:N,d(t){t&&b(e)}}}function H(r){let e,n;return e=new I.Item({props:{onSelect:function(){return r[11](r[21])},highlight:r[4].label===r[21].label,$$slots:{default:[xe]},$$scope:{ctx:r}}}),{c(){x(e.$$.fragment)},m(t,o){w(e,t,o),n=!0},p(t,o){r=t;const s={};16&o&&(s.highlight=r[4].label===r[21].label),16777216&o&&(s.$$scope={dirty:o,ctx:r}),e.$set(s)},i(t){n||(f(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){y(e,t)}}}function K(r){let e,n,t,o;return e=new I.Separator({}),t=new I.Label({props:{$$slots:{default:[we]},$$scope:{ctx:r}}}),{c(){x(e.$$.fragment),n=B(),x(t.$$.fragment)},m(s,a){w(e,s,a),v(s,n,a),w(t,s,a),o=!0},p(s,a){const c={};16777264&a&&(c.$$scope={dirty:a,ctx:s}),t.$set(c)},i(s){o||(f(e.$$.fragment,s),f(t.$$.fragment,s),o=!0)},o(s){d(e.$$.fragment,s),d(t.$$.fragment,s),o=!1},d(s){s&&b(n),y(e,s),y(t,s)}}}function we(r){let e,n=(r[5]!==void 0?r[7][r[5]].description:r[4].description)+"";return{c(){e=O(n)},m(t,o){v(t,e,o)},p(t,o){48&o&&n!==(n=(t[5]!==void 0?t[7][t[5]].description:t[4].description)+"")&&te(e,n)},d(t){t&&b(e)}}}function ye(r){let e,n,t,o=_(r[7]),s=[];for(let l=0;l<o.length;l+=1)s[l]=H(G(r,o,l));const a=l=>d(s[l],1,1,()=>{s[l]=null});let c=(r[5]!==void 0||r[4])&&K(r);return{c(){for(let l=0;l<s.length;l+=1)s[l].c();e=B(),c&&c.c(),n=ae()},m(l,$){for(let i=0;i<s.length;i+=1)s[i]&&s[i].m(l,$);v(l,e,$),c&&c.m(l,$),v(l,n,$),t=!0},p(l,$){if(400&$){let i;for(o=_(l[7]),i=0;i<o.length;i+=1){const m=G(l,o,i);s[i]?(s[i].p(m,$),f(s[i],1)):(s[i]=H(m),s[i].c(),f(s[i],1),s[i].m(e.parentNode,e))}for(k(),i=o.length;i<s.length;i+=1)a(i);J()}l[5]!==void 0||l[4]?c?(c.p(l,$),48&$&&f(c,1)):(c=K(l),c.c(),f(c,1),c.m(n.parentNode,n)):c&&(k(),d(c,1,1,()=>{c=null}),J())},i(l){if(!t){for(let $=0;$<o.length;$+=1)f(s[$]);f(c),t=!0}},o(l){s=s.filter(Boolean);for(let $=0;$<s.length;$+=1)d(s[$]);d(c),t=!1},d(l){l&&(b(e),b(n)),le(s,l),c&&c.d(l)}}}function ve(r){let e,n,t,o;return e=new I.Trigger({props:{$$slots:{default:[he]},$$scope:{ctx:r}}}),t=new I.Content({props:{side:"bottom",align:"start",$$slots:{default:[ye]},$$scope:{ctx:r}}}),{c(){x(e.$$.fragment),n=B(),x(t.$$.fragment)},m(s,a){w(e,s,a),v(s,n,a),w(t,s,a),o=!0},p(s,a){const c={};16777232&a&&(c.$$scope={dirty:a,ctx:s}),e.$set(c);const l={};16777264&a&&(l.$$scope={dirty:a,ctx:s}),t.$set(l)},i(s){o||(f(e.$$.fragment,s),f(t.$$.fragment,s),o=!0)},o(s){d(e.$$.fragment,s),d(t.$$.fragment,s),o=!1},d(s){s&&b(n),y(e,s),y(t,s)}}}function be(r){let e;return{c(){e=O("Open file")},m(n,t){v(n,e,t)},d(n){n&&b(e)}}}function Fe(r){let e,n;return e=new P({props:{slot:"text",size:1,$$slots:{default:[be]},$$scope:{ctx:r}}}),{c(){x(e.$$.fragment)},m(t,o){w(e,t,o),n=!0},p(t,o){const s={};16777216&o&&(s.$$scope={dirty:o,ctx:t}),e.$set(s)},i(t){n||(f(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){y(e,t)}}}function Re(r){let e,n,t,o,s,a,c,l,$,i,m;function g(p){r[12](p)}function z(p){r[13](p)}o=new P({props:{size:1,$$slots:{default:[de]},$$scope:{ctx:r}}});let F={$$slots:{default:[ve]},$$scope:{ctx:r}};return r[3]!==void 0&&(F.requestClose=r[3]),r[2]!==void 0&&(F.focusedIndex=r[2]),a=new I.Root({props:F}),D.push(()=>W(a,"requestClose",g)),D.push(()=>W(a,"focusedIndex",z)),i=new $e({props:{size:1,path:r[1],onOpenLocalFile:r[14],$$slots:{text:[Fe]},$$scope:{ctx:r}}}),{c(){e=L("div"),n=L("div"),t=L("div"),x(o.$$.fragment),s=B(),x(a.$$.fragment),$=B(),x(i.$$.fragment),E(t,"class","c-dropdown-with-label svelte-93gnsz"),E(n,"class","l-file-controls-left svelte-93gnsz"),E(e,"class","l-file-controls svelte-93gnsz"),E(e,"slot","header")},m(p,h){v(p,e,h),M(e,n),M(n,t),w(o,t,null),M(t,s),w(a,t,null),M(e,$),w(i,e,null),m=!0},p(p,h){const C={};16777216&h&&(C.$$scope={dirty:h,ctx:p}),o.$set(C);const A={};16777264&h&&(A.$$scope={dirty:h,ctx:p}),!c&&8&h&&(c=!0,A.requestClose=p[3],Y(()=>c=!1)),!l&&4&h&&(l=!0,A.focusedIndex=p[2],Y(()=>l=!1)),a.$set(A);const R={};2&h&&(R.path=p[1]),2&h&&(R.onOpenLocalFile=p[14]),16777216&h&&(R.$$scope={dirty:h,ctx:p}),i.$set(R)},i(p){m||(f(o.$$.fragment,p),f(a.$$.fragment,p),f(i.$$.fragment,p),m=!0)},o(p){d(o.$$.fragment,p),d(a.$$.fragment,p),d(i.$$.fragment,p),m=!1},d(p){p&&b(e),y(o),y(a),y(i)}}}function Ce(r){let e,n,t;function o(a){r[15](a)}let s={saveFunction:r[9],variant:"surface",size:2,resize:"vertical",class:"markdown-editor",$$slots:{header:[Re]},$$scope:{ctx:r}};return r[0]!==void 0&&(s.value=r[0]),e=new ue({props:s}),D.push(()=>W(e,"value",o)),{c(){x(e.$$.fragment)},m(a,c){w(e,a,c),t=!0},p(a,[c]){const l={};16777278&c&&(l.$$scope={dirty:c,ctx:a}),!n&&1&c&&(n=!0,l.value=a[0],Y(()=>n=!1)),e.$set(l)},i(a){t||(f(e.$$.fragment,a),t=!0)},o(a){d(e.$$.fragment,a),t=!1},d(a){y(e,a)}}}function ze(r,e,n){let t,o,s=N,a=()=>(s(),s=oe(C,u=>n(5,o=u)),C);r.$$.on_destroy.push(()=>s());let{text:c}=e,{path:l}=e;const $=new ne(S),i=new pe,m=new me(S,$,i);let{alwaysApply:g}=e;const z={label:"Always",description:"Rules will always be followed, though individual context will still be respected."},F={label:"Manual",description:"Rules will only be followed when manually triggered. You can trigger them from the prompt by @ tagging the active file."},p=[z,F];function h(u){n(10,g=u.label==="Always"),A(),R()}let C;a();const A=async()=>{const u=(Q=g,q.updateFrontmatter(c,"alwaysApply",Q));var Q;m.saveFile({repoRoot:"",pathName:l,content:u})};let R=()=>{};return r.$$set=u=>{"text"in u&&n(0,c=u.text),"path"in u&&n(1,l=u.path),"alwaysApply"in u&&n(10,g=u.alwaysApply)},r.$$.update=()=>{1024&r.$$.dirty&&n(4,t=g?z:F)},[c,l,C,R,t,o,m,p,h,A,g,u=>h(u),function(u){R=u,n(3,R)},function(u){C=u,a(n(2,C))},async()=>(m.openFile({repoRoot:"",pathName:l}),"success"),function(u){c=u,n(0,c)}]}class Ae extends U{constructor(e){super(),X(this,e,ze,Ce,ee,{text:0,path:1,alwaysApply:10})}}function Ee(r){let e;return{c(){e=L("div"),e.textContent="Loading..."},m(n,t){v(n,e,t)},p:N,i:N,o:N,d(n){n&&b(e)}}}function Le(r){let e,n;return e=new Ae({props:{text:r[0],path:r[1],alwaysApply:r[2]}}),{c(){x(e.$$.fragment)},m(t,o){w(e,t,o),n=!0},p(t,o){const s={};1&o&&(s.text=t[0]),2&o&&(s.path=t[1]),4&o&&(s.alwaysApply=t[2]),e.$set(s)},i(t){n||(f(e.$$.fragment,t),n=!0)},o(t){d(e.$$.fragment,t),n=!1},d(t){y(e,t)}}}function Me(r){let e,n,t,o,s,a;const c=[Le,Ee],l=[];function $(i,m){return i[0]!==null&&i[1]!==null?0:1}return n=$(r),t=l[n]=c[n](r),{c(){e=L("div"),t.c(),E(e,"class","c-rules-container svelte-1vbu0zh")},m(i,m){v(i,e,m),l[n].m(e,null),o=!0,s||(a=ce(window,"message",r[3].onMessageFromExtension),s=!0)},p(i,[m]){let g=n;n=$(i),n===g?l[n].p(i,m):(k(),d(l[g],1,1,()=>{l[g]=null}),J(),t=l[n],t?t.p(i,m):(t=l[n]=c[n](i),t.c()),f(t,1),t.m(e,null))},i(i){o||(f(t),o=!0)},o(i){d(t),o=!1},d(i){i&&b(e),l[n].d(),s=!1,a()}}}function Be(r,e,n){let t,o,s;const a=new ne(S),c=j(null);T(r,c,m=>n(0,t=m));const l=j("");T(r,l,m=>n(1,o=m));const $=j(!0);T(r,$,m=>n(2,s=m));const i={handleMessageFromExtension(m){const g=m.data;if(g&&g.type===Z.loadFile&&g){const z=g.data.content;if(z!==void 0){const F=z.replace(/^\n+/,""),p=q.parseBoolean(F,"alwaysApply",!0),h=q.extractContent(F);$.set(p),c.set(h)}l.set(g.data.pathName)}return!0}};return ie(()=>{a.registerConsumer(i),S.postMessage({type:Z.rulesLoaded})}),[t,o,s,a,c,l,$]}new class extends U{constructor(r){super(),X(this,r,Be,Me,ee,{})}}({target:document.getElementById("app")});
