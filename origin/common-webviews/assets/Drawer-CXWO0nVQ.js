import{S as K,i as U,s as Y,I as J,J as _,T as D,c as W,a8 as B,L as g,e as V,f as x,P as q,a4 as ee,M as N,N as R,O as S,u as y,q as te,t as L,r as ae,a9 as se,h as Z,A as de,a2 as oe,V as C,W as F,aa as ne,ab as X,Z as G,Q as j}from"./SpinnerAugment-BUJasFTo.js";import{I as le}from"./IconButtonAugment-DFy7vWkh.js";import{f as A}from"./index-DlpZFSR-.js";import{r as re}from"./resize-observer-DdAtcrRr.js";import{E as ie}from"./ellipsis-CRdQranZ.js";const ce=a=>({}),O=a=>({}),ue=a=>({}),P=a=>({});function Q(a){let t,e,d,u;return e=new le({props:{variant:"solid",color:"accent",size:2,radius:"full",title:"Show panel",$$slots:{default:[me]},$$scope:{ctx:a}}}),e.$on("click",a[12]),{c(){t=_("div"),C(e.$$.fragment),W(t,"class","c-drawer__hidden-indicator svelte-18f0m3o")},m(l,$){V(l,t,$),F(e,t,null),u=!0},p(l,$){const p={};16777216&$&&(p.$$scope={dirty:$,ctx:l}),e.$set(p)},i(l){u||(y(e.$$.fragment,l),l&&ne(()=>{u&&(d||(d=X(t,A,{y:0,x:0,duration:200},!0)),d.run(1))}),u=!0)},o(l){L(e.$$.fragment,l),l&&(d||(d=X(t,A,{y:0,x:0,duration:200},!1)),d.run(0)),u=!1},d(l){l&&Z(t),G(e),l&&d&&d.end()}}}function me(a){let t,e;return t=new ie({}),{c(){C(t.$$.fragment)},m(d,u){F(t,d,u),e=!0},i(d){e||(y(t.$$.fragment,d),e=!0)},o(d){L(t.$$.fragment,d),e=!1},d(d){G(t,d)}}}function he(a){let t,e,d,u,l,$,p,T,v,f,i,m,k;const M=a[20].left,h=J(M,a,a[24],P),z=a[20].right,r=J(z,a,a[24],O);let n=a[0]&&a[3]&&Q(a);return{c(){t=_("div"),e=_("div"),d=_("div"),h&&h.c(),u=D(),l=_("div"),$=D(),p=_("div"),r&&r.c(),T=D(),n&&n.c(),W(d,"class","c-drawer__left-content svelte-18f0m3o"),d.inert=a[7],B(d,"width","var(--augment-drawer-width)"),B(d,"min-width","var(--augment-drawer-width)"),B(d,"max-width","var(--augment-drawer-width)"),W(e,"class","c-drawer__left svelte-18f0m3o"),B(e,"--augment-drawer-width",a[8]+"px"),W(l,"aria-hidden","true"),W(l,"class","c-drawer__handle svelte-18f0m3o"),g(l,"is-locked",a[4]),W(p,"class","c-drawer__right svelte-18f0m3o"),W(t,"class",v="c-drawer "+a[2]+" svelte-18f0m3o"),g(t,"is-dragging",a[7]),g(t,"is-hidden",!a[8]),g(t,"is-column",a[4])},m(s,c){V(s,t,c),x(t,e),x(e,d),h&&h.m(d,null),a[21](e),x(t,u),x(t,l),x(t,$),x(t,p),r&&r.m(p,null),x(t,T),n&&n.m(t,null),a[22](t),i=!0,m||(k=[q(window,"mousemove",a[10]),q(window,"mouseup",a[11]),q(l,"mousedown",a[9]),q(l,"dblclick",a[12]),ee(f=re.call(null,t,{onResize:a[23]}))],m=!0)},p(s,[c]){h&&h.p&&(!i||16777216&c)&&N(h,M,s,s[24],i?S(M,s[24],c,ue):R(s[24]),P),(!i||128&c)&&(d.inert=s[7]),(!i||256&c)&&B(e,"--augment-drawer-width",s[8]+"px"),(!i||16&c)&&g(l,"is-locked",s[4]),r&&r.p&&(!i||16777216&c)&&N(r,z,s,s[24],i?S(z,s[24],c,ce):R(s[24]),O),s[0]&&s[3]?n?(n.p(s,c),9&c&&y(n,1)):(n=Q(s),n.c(),y(n,1),n.m(t,null)):n&&(te(),L(n,1,1,()=>{n=null}),ae()),(!i||4&c&&v!==(v="c-drawer "+s[2]+" svelte-18f0m3o"))&&W(t,"class",v),f&&se(f.update)&&2&c&&f.update.call(null,{onResize:s[23]}),(!i||132&c)&&g(t,"is-dragging",s[7]),(!i||260&c)&&g(t,"is-hidden",!s[8]),(!i||20&c)&&g(t,"is-column",s[4])},i(s){i||(y(h,s),y(r,s),y(n),i=!0)},o(s){L(h,s),L(r,s),L(n),i=!1},d(s){s&&Z(t),h&&h.d(s),a[21](null),r&&r.d(s),n&&n.d(),a[22](null),m=!1,de(k)}}}function fe(a,t,e){let d,u,l,$,{$$slots:p={},$$scope:T}=t,{initialWidth:v=300}=t,{expandedMinWidth:f=50}=t,{minimizedWidth:i=0}=t,{minimized:m=!1}=t,{class:k=""}=t,{showButton:M=!0}=t,{deadzone:h=0}=t,{columnLayoutThreshold:z=600}=t,{layoutMode:r}=t,n=!1,s=v,c=v,w=!1;function b(){if(u){if(r!==void 0)return e(4,w=r==="column"),void(w&&e(7,n=!1));e(4,w=u.clientWidth<z),w&&e(7,n=!1)}}return oe(b),a.$$set=o=>{"initialWidth"in o&&e(14,v=o.initialWidth),"expandedMinWidth"in o&&e(15,f=o.expandedMinWidth),"minimizedWidth"in o&&e(16,i=o.minimizedWidth),"minimized"in o&&e(0,m=o.minimized),"class"in o&&e(2,k=o.class),"showButton"in o&&e(3,M=o.showButton),"deadzone"in o&&e(17,h=o.deadzone),"columnLayoutThreshold"in o&&e(18,z=o.columnLayoutThreshold),"layoutMode"in o&&e(1,r=o.layoutMode),"$$scope"in o&&e(24,T=o.$$scope)},a.$$.update=()=>{3&a.$$.dirty&&(m?(e(1,r="row"),e(4,w=!1)):r!=="row"||m||(e(1,r=void 0),b())),18&a.$$.dirty&&r!==void 0&&(e(4,w=r==="column"),w&&e(7,n=!1)),589825&a.$$.dirty&&e(8,c=m?i:s)},[m,r,k,M,w,d,u,n,c,function(o){w||(e(7,n=!0),l=o.clientX,$=d.offsetWidth,o.preventDefault())},function(o){if(!n||!d||w)return;const H=o.clientX-l,E=u.clientWidth-200,I=$+H;I<f?I<f-h?e(0,m=!0):(e(19,s=f),e(0,m=!1)):I>E?(e(19,s=E),e(0,m=!1)):(e(19,s=I),e(0,m=!1))},function(){e(7,n=!1),e(19,s=Math.max(s,f))},function(){e(0,m=!m)},b,v,f,i,h,z,s,p,function(o){j[o?"unshift":"push"](()=>{d=o,e(5,d)})},function(o){j[o?"unshift":"push"](()=>{u=o,e(6,u)})},()=>r===void 0&&b(),T]}class We extends K{constructor(t){super(),U(this,t,fe,he,Y,{initialWidth:14,expandedMinWidth:15,minimizedWidth:16,minimized:0,class:2,showButton:3,deadzone:17,columnLayoutThreshold:18,layoutMode:1})}}export{We as D};
