import{S as H,i as J,s as Q,Q as oo,R as no,J as W,V as F,c as P,e as R,W as T,Y as eo,u as m,t as h,h as D,Z as N,E as _,a as j,j as co,D as X,q as Y,r as Z,G as d,g as K,X as U,I as O,M as L,N as z,O as b,T as I,a6 as so,ac as io,n as to}from"./SpinnerAugment-BUJasFTo.js";import{l as lo,R as ao,O as ro}from"./open-in-new-window-eiueNVFd.js";import"./BaseButton-ci_067e0.js";import{T as uo}from"./Content-CSmc2GUv.js";import{B as po}from"./ButtonAugment-DbAwCSeR.js";import{I as $o}from"./IconButtonAugment-DFy7vWkh.js";import{T as fo}from"./TextTooltipAugment-UDQF2J4S.js";const mo=e=>({}),M=e=>({slot:"iconLeft"}),ho=e=>({}),E=e=>({slot:"iconRight"}),Co=e=>({}),S=e=>({}),go=e=>({}),A=e=>({});function ko(e){let o,c;const n=[e[5],{color:e[1]},{variant:e[4]}];let s={$$slots:{iconRight:[wo],iconLeft:[xo],default:[yo]},$$scope:{ctx:e}};for(let i=0;i<n.length;i+=1)s=j(s,n[i]);return o=new po({props:s}),o.$on("click",e[7]),o.$on("keyup",e[25]),o.$on("keydown",e[26]),o.$on("mousedown",e[27]),o.$on("mouseover",e[28]),o.$on("focus",e[29]),o.$on("mouseleave",e[30]),o.$on("blur",e[31]),o.$on("contextmenu",e[32]),{c(){F(o.$$.fragment)},m(i,a){T(o,i,a),c=!0},p(i,a){const t=50&a[0]?K(n,[32&a[0]&&U(i[5]),2&a[0]&&{color:i[1]},16&a[0]&&{variant:i[4]}]):{};8&a[1]&&(t.$$scope={dirty:a,ctx:i}),o.$set(t)},i(i){c||(m(o.$$.fragment,i),c=!0)},o(i){h(o.$$.fragment,i),c=!1},d(i){N(o,i)}}}function vo(e){let o,c;const n=[e[5],{color:e[1]},{variant:e[4]}];let s={$$slots:{default:[Oo]},$$scope:{ctx:e}};for(let i=0;i<n.length;i+=1)s=j(s,n[i]);return o=new $o({props:s}),o.$on("click",e[7]),o.$on("keyup",e[17]),o.$on("keydown",e[18]),o.$on("mousedown",e[19]),o.$on("mouseover",e[20]),o.$on("focus",e[21]),o.$on("mouseleave",e[22]),o.$on("blur",e[23]),o.$on("contextmenu",e[24]),{c(){F(o.$$.fragment)},m(i,a){T(o,i,a),c=!0},p(i,a){const t=50&a[0]?K(n,[32&a[0]&&U(i[5]),2&a[0]&&{color:i[1]},16&a[0]&&{variant:i[4]}]):{};8&a[1]&&(t.$$scope={dirty:a,ctx:i}),o.$set(t)},i(i){c||(m(o.$$.fragment,i),c=!0)},o(i){h(o.$$.fragment,i),c=!1},d(i){N(o,i)}}}function yo(e){let o;const c=e[16].default,n=O(c,e,e[34],null);return{c(){n&&n.c()},m(s,i){n&&n.m(s,i),o=!0},p(s,i){n&&n.p&&(!o||8&i[1])&&L(n,c,s,s[34],o?b(c,s[34],i,null):z(s[34]),null)},i(s){o||(m(n,s),o=!0)},o(s){h(n,s),o=!1},d(s){n&&n.d(s)}}}function xo(e){let o;const c=e[16].iconLeft,n=O(c,e,e[34],M);return{c(){n&&n.c()},m(s,i){n&&n.m(s,i),o=!0},p(s,i){n&&n.p&&(!o||8&i[1])&&L(n,c,s,s[34],o?b(c,s[34],i,mo):z(s[34]),M)},i(s){o||(m(n,s),o=!0)},o(s){h(n,s),o=!1},d(s){n&&n.d(s)}}}function wo(e){let o;const c=e[16].iconRight,n=O(c,e,e[34],E);return{c(){n&&n.c()},m(s,i){n&&n.m(s,i),o=!0},p(s,i){n&&n.p&&(!o||8&i[1])&&L(n,c,s,s[34],o?b(c,s[34],i,ho):z(s[34]),E)},i(s){o||(m(n,s),o=!0)},o(s){h(n,s),o=!1},d(s){n&&n.d(s)}}}function Oo(e){let o,c,n;const s=e[16].iconLeft,i=O(s,e,e[34],A),a=e[16].default,t=O(a,e,e[34],null),u=e[16].iconRight,$=O(u,e,e[34],S);return{c(){i&&i.c(),o=I(),t&&t.c(),c=I(),$&&$.c()},m(r,f){i&&i.m(r,f),R(r,o,f),t&&t.m(r,f),R(r,c,f),$&&$.m(r,f),n=!0},p(r,f){i&&i.p&&(!n||8&f[1])&&L(i,s,r,r[34],n?b(s,r[34],f,go):z(r[34]),A),t&&t.p&&(!n||8&f[1])&&L(t,a,r,r[34],n?b(a,r[34],f,null):z(r[34]),null),$&&$.p&&(!n||8&f[1])&&L($,u,r,r[34],n?b(u,r[34],f,Co):z(r[34]),S)},i(r){n||(m(i,r),m(t,r),m($,r),n=!0)},o(r){h(i,r),h(t,r),h($,r),n=!1},d(r){r&&(D(o),D(c)),i&&i.d(r),t&&t.d(r),$&&$.d(r)}}}function Lo(e){let o,c,n,s;const i=[vo,ko],a=[];function t(u,$){return u[0]?0:1}return o=t(e),c=a[o]=i[o](e),{c(){c.c(),n=X()},m(u,$){a[o].m(u,$),R(u,n,$),s=!0},p(u,$){let r=o;o=t(u),o===r?a[o].p(u,$):(Y(),h(a[r],1,1,()=>{a[r]=null}),Z(),c=a[o],c?c.p(u,$):(c=a[o]=i[o](u),c.c()),m(c,1),c.m(n.parentNode,n))},i(u){s||(m(c),s=!0)},o(u){h(c),s=!1},d(u){u&&D(n),a[o].d(u)}}}function zo(e){let o,c,n,s;function i(t){e[33](t)}let a={onOpenChange:e[6],content:e[3],triggerOn:[uo.Hover],$$slots:{default:[Lo]},$$scope:{ctx:e}};return e[2]!==void 0&&(a.requestClose=e[2]),c=new fo({props:a}),oo.push(()=>no(c,"requestClose",i)),{c(){o=W("div"),F(c.$$.fragment),P(o,"class","c-successful-button svelte-1dvyzw2")},m(t,u){R(t,o,u),T(c,o,null),s=!0},p(t,u){const $={};8&u[0]&&($.content=t[3]),51&u[0]|8&u[1]&&($.$$scope={dirty:u,ctx:t}),!n&&4&u[0]&&(n=!0,$.requestClose=t[2],eo(()=>n=!1)),c.$set($)},i(t){s||(m(c.$$.fragment,t),s=!0)},o(t){h(c.$$.fragment,t),s=!1},d(t){t&&D(o),N(c)}}}function bo(e,o,c){let n,s,i;const a=["defaultColor","tooltip","stateVariant","onClick","tooltipDuration","icon","stickyColor"];let t,u,$=_(o,a),{$$slots:r={},$$scope:f}=o,{defaultColor:x}=o,{tooltip:C}=o,{stateVariant:y}=o,{onClick:v}=o,{tooltipDuration:w=1500}=o,{icon:p=!1}=o,{stickyColor:k=!0}=o,g="neutral",V=x,q=C==null?void 0:C.neutral;return e.$$set=l=>{o=j(j({},o),co(l)),c(36,$=_(o,a)),"defaultColor"in l&&c(8,x=l.defaultColor),"tooltip"in l&&c(9,C=l.tooltip),"stateVariant"in l&&c(10,y=l.stateVariant),"onClick"in l&&c(11,v=l.onClick),"tooltipDuration"in l&&c(12,w=l.tooltipDuration),"icon"in l&&c(0,p=l.icon),"stickyColor"in l&&c(13,k=l.stickyColor),"$$scope"in l&&c(34,f=l.$$scope)},e.$$.update=()=>{c(15,{variant:n,...s}=$,n,(c(5,s),c(36,$))),50176&e.$$.dirty[0]&&c(4,i=(y==null?void 0:y[g])??n),16640&e.$$.dirty[0]&&c(1,V=g==="success"?"success":g==="failure"?"error":x)},[p,V,t,q,i,s,function(l){l||(clearTimeout(u),u=void 0,c(3,q=C==null?void 0:C.neutral),k||c(14,g="neutral"))},async function(l){try{c(14,g=await v(l)??"neutral")}catch{c(14,g="failure")}c(3,q=C==null?void 0:C[g]),clearTimeout(u),u=setTimeout(()=>{t==null||t(),k||c(14,g="neutral")},w)},x,C,y,v,w,k,g,n,r,function(l){d.call(this,e,l)},function(l){d.call(this,e,l)},function(l){d.call(this,e,l)},function(l){d.call(this,e,l)},function(l){d.call(this,e,l)},function(l){d.call(this,e,l)},function(l){d.call(this,e,l)},function(l){d.call(this,e,l)},function(l){d.call(this,e,l)},function(l){d.call(this,e,l)},function(l){d.call(this,e,l)},function(l){d.call(this,e,l)},function(l){d.call(this,e,l)},function(l){d.call(this,e,l)},function(l){d.call(this,e,l)},function(l){d.call(this,e,l)},function(l){t=l,c(2,t)},f]}class Ro extends H{constructor(o){super(),J(this,o,bo,zo,Q,{defaultColor:8,tooltip:9,stateVariant:10,onClick:11,tooltipDuration:12,icon:0,stickyColor:13},null,[-1,-1])}}const Do=e=>({}),B=e=>({});function G(e){let o,c,n,s;return c=new Ro({props:{defaultColor:e[1],stickyColor:e[3],size:e[0],variant:e[2],tooltip:{neutral:"Open File In Editor",success:"Opening file..."},stateVariant:{success:"soft"},onClick:e[4],icon:!e[6].text,$$slots:{iconLeft:[Fo],default:[Vo]},$$scope:{ctx:e}}}),{c(){o=W("span"),F(c.$$.fragment),P(o,"class",n="c-open-file-button-container c-open-file-button__size--"+e[0]+" svelte-pdfhuj")},m(i,a){R(i,o,a),T(c,o,null),s=!0},p(i,a){const t={};2&a&&(t.defaultColor=i[1]),8&a&&(t.stickyColor=i[3]),1&a&&(t.size=i[0]),4&a&&(t.variant=i[2]),16&a&&(t.onClick=i[4]),64&a&&(t.icon=!i[6].text),2048&a&&(t.$$scope={dirty:a,ctx:i}),c.$set(t),(!s||1&a&&n!==(n="c-open-file-button-container c-open-file-button__size--"+i[0]+" svelte-pdfhuj"))&&P(o,"class",n)},i(i){s||(m(c.$$.fragment,i),s=!0)},o(i){h(c.$$.fragment,i),s=!1},d(i){i&&D(o),N(c)}}}function Vo(e){let o;const c=e[10].text,n=O(c,e,e[11],B);return{c(){n&&n.c()},m(s,i){n&&n.m(s,i),o=!0},p(s,i){n&&n.p&&(!o||2048&i)&&L(n,c,s,s[11],o?b(c,s[11],i,Do):z(s[11]),B)},i(s){o||(m(n,s),o=!0)},o(s){h(n,s),o=!1},d(s){n&&n.d(s)}}}function Fo(e){let o,c;return o=new ro({props:{slot:"iconLeft"}}),{c(){F(o.$$.fragment)},m(n,s){T(o,n,s),c=!0},p:to,i(n){c||(m(o.$$.fragment,n),c=!0)},o(n){h(o.$$.fragment,n),c=!1},d(n){N(o,n)}}}function To(e){let o,c,n=!e[5]&&G(e);return{c(){n&&n.c(),o=X()},m(s,i){n&&n.m(s,i),R(s,o,i),c=!0},p(s,[i]){s[5]?n&&(Y(),h(n,1,1,()=>{n=null}),Z()):n?(n.p(s,i),32&i&&m(n,1)):(n=G(s),n.c(),m(n,1),n.m(o.parentNode,o))},i(s){c||(m(n),c=!0)},o(s){h(n),c=!1},d(s){s&&D(o),n&&n.d(s)}}}function No(e,o,c){let n,{$$slots:s={},$$scope:i}=o;const a=so(s);let{path:t}=o,{start:u=0}=o,{stop:$=0}=o,{size:r=0}=o,{color:f="neutral"}=o,{variant:x="ghost-block"}=o,{stickyColor:C=!1}=o,{onOpenLocalFile:y=async function(p){var g,V;if((g=p==null?void 0:p.stopPropagation)==null||g.call(p),(V=p==null?void 0:p.preventDefault)==null||V.call(p),!t)return;const k=await(v==null?void 0:v.extensionClient.resolvePath({rootPath:"",relPath:t}));return v==null||v.extensionClient.openFile({repoRoot:(k==null?void 0:k.repoRoot)??"",pathName:(k==null?void 0:k.pathName)??"",range:{start:Math.max(u,0),stop:Math.max($,0)}}),"success"}}=o;const v=lo(),w=io(ao.key);return e.$$set=p=>{"path"in p&&c(7,t=p.path),"start"in p&&c(8,u=p.start),"stop"in p&&c(9,$=p.stop),"size"in p&&c(0,r=p.size),"color"in p&&c(1,f=p.color),"variant"in p&&c(2,x=p.variant),"stickyColor"in p&&c(3,C=p.stickyColor),"onOpenLocalFile"in p&&c(4,y=p.onOpenLocalFile),"$$scope"in p&&c(11,i=p.$$scope)},c(5,n=!!(w!=null&&w.isActive)),[r,f,x,C,y,n,a,t,u,$,s,i]}class Ao extends H{constructor(o){super(),J(this,o,No,To,Q,{path:7,start:8,stop:9,size:0,color:1,variant:2,stickyColor:3,onOpenLocalFile:4})}}export{Ao as O,Ro as S};
