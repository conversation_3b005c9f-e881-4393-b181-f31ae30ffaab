import{U as S,ao as A,ap as W,aq as j,S as B,i as N,s as H,F as E,ar as I,a as k,J as F,K as D,L as b,e as C,P as f,q as _,t as w,r as J,u as x,g as K,h as y,A as Z,E as G,j as Q,G as h,I as O,M as z,N as T,O as U,al as X,V as Y,T as ee,c as L,W as te,Z as se}from"./SpinnerAugment-BUJasFTo.js";var ne=(e=>(e.asyncWrapper="async-wrapper",e.historyLoaded="history-loaded",e.historyInitialize="history-initialize",e.completionRating="completion-rating",e.completionRatingDone="completion-rating-done",e.nextEditRating="next-edit-rating",e.nextEditRatingDone="next-edit-rating-done",e.completions="completions",e.historyConfig="history-config",e.copyRequestID="copy-request-id-to-clipboard",e.openFile="open-file",e.openDiffInBuffer="open-diff-in-buffer",e.saveFile="save-file",e.loadFile="load-file",e.openMemoriesFile="open-memories-file",e.openAndEditFile="open-and-edit-file",e.diffViewNotifyReinit="diff-view-notify-reinit",e.diffViewLoaded="diff-view-loaded",e.diffViewInitialize="diff-view-initialize",e.diffViewResolveChunk="diff-view-resolve-chunk",e.diffViewFetchPendingStream="diff-view-fetch-pending-stream",e.diffViewDiffStreamStarted="diff-view-diff-stream-started",e.diffViewDiffStreamChunk="diff-view-diff-stream-chunk",e.diffViewDiffStreamEnded="diff-view-diff-stream-ended",e.diffViewAcceptAllChunks="diff-view-accept-all-chunks",e.diffViewAcceptFocusedChunk="diff-view-accept-selected-chunk",e.diffViewRejectFocusedChunk="diff-view-reject-focused-chunk",e.diffViewFocusPrevChunk="diff-view-focus-prev-chunk",e.diffViewFocusNextChunk="diff-view-focus-next-chunk",e.diffViewWindowFocusChange="diff-view-window-focus-change",e.diffViewFileFocus="diff-view-file-focus",e.disposeDiffView="dispose-diff-view",e.chatAutofixExecuteCommandRequest="chat-autofix-execute-command-request",e.chatAutofixExecuteCommandResult="chat-autofix-execute-command-result",e.autofixPanelExecuteCommandPartialOutput="autofix-panel-execute-command-partial-output",e.reportWebviewClientMetric="report-webview-client-metric",e.reportError="report-error",e.openConfirmationModal="open-confirmation-modal",e.confirmationModalResponse="confirmation-modal-response",e.clientTools="client-tools",e.currentlyOpenFiles="currently-open-files",e.findFileRequest="find-file-request",e.resolveFileRequest="resolve-file-request",e.findFileResponse="find-file-response",e.resolveFileResponse="resolve-file-response",e.findRecentlyOpenedFilesRequest="find-recently-opened-files",e.findRecentlyOpenedFilesResponse="find-recently-opened-files-response",e.findFolderRequest="find-folder-request",e.findFolderResponse="find-folder-response",e.findExternalSourcesRequest="find-external-sources-request",e.findExternalSourcesResponse="find-external-sources-response",e.findSymbolRequest="find-symbol-request",e.findSymbolRegexRequest="find-symbol-regex-request",e.findSymbolResponse="find-symbol-response",e.fileRangesSelected="file-ranges-selected",e.getDiagnosticsRequest="get-diagnostics-request",e.getDiagnosticsResponse="get-diagnostics-response",e.resolveWorkspaceFileChunkRequest="resolve-workspace-file-chunk",e.resolveWorkspaceFileChunkResponse="resolve-workspace-file-chunk-response",e.sourceFoldersUpdated="source-folders-updated",e.sourceFoldersSyncStatus="source-folders-sync-status",e.syncEnabledState="sync-enabled-state",e.shouldShowSummary="should-show-summary",e.showAugmentPanel="show-augment-panel",e.updateGuidelinesState="update-guidelines-state",e.openGuidelines="open-guidelines",e.updateWorkspaceGuidelines="update-workspace-guidelines",e.updateUserGuidelines="update-user-guidelines",e.chatAutofixStateUpdate="chat-autofix-state-update",e.chatAutofixPlanRequest="chat-autofix-plan-request",e.chatAutofixPlanResponse="chat-autofix-plan-response",e.chatAutofixStateUpdateRequest="chat-autofix-state-update-request",e.chatAutofixSuggestionsApplied="chat-autofix-suggestions-applied",e.chatLaunchAutofixPanel="chat-launch-autofix-panel",e.chatAgentEditListHasUpdates="chat-agent-edit-list-has-updates",e.chatMemoryHasUpdates="chat-memory-has-updates",e.getAgentEditContentsByRequestId="getAgentEditContentsByRequestId",e.chatModeChanged="chat-mode-changed",e.chatClearMetadata="chat-clear-metadata",e.chatLoaded="chat-loaded",e.chatInitialize="chat-initialize",e.chatGetStreamRequest="chat-get-stream-request",e.chatUserMessage="chat-user-message",e.generateCommitMessage="generate-commit-message",e.chatUserCancel="chat-user-cancel",e.chatModelReply="chat-model-reply",e.chatInstructionMessage="chat-instruction-message",e.chatInstructionModelReply="chat-instruction-model-reply",e.chatCreateFile="chat-create-file",e.chatSmartPaste="chat-smart-paste",e.chatRating="chat-rating",e.chatRatingDone="chat-rating-done",e.chatStreamDone="chat-stream-done",e.runSlashCommand="run-slash-command",e.callTool="call-tool",e.callToolResponse="call-tool-response",e.cancelToolRun="cancel-tool-run",e.cancelToolRunResponse="cancel-tool-run-response",e.toolCheckSafe="check-safe",e.toolCheckSafeResponse="check-safe-response",e.checkToolExists="checkToolExists",e.checkToolExistsResponse="checkToolExistsResponse",e.getToolCallCheckpoint="get-tool-call-checkpoint",e.getToolCallCheckpointResponse="get-tool-call-checkpoint-response",e.updateAditionalChatModels="update-additional-chat-models",e.saveChat="save-chat",e.saveChatDone="save-chat-done",e.newThread="new-thread",e.chatSaveImageRequest="chat-save-image-request",e.chatSaveImageResponse="chat-save-image-response",e.chatLoadImageRequest="chat-load-image-request",e.chatLoadImageResponse="chat-load-image-response",e.chatDeleteImageRequest="chat-delete-image-request",e.chatDeleteImageResponse="chat-delete-image-response",e.instructions="instructions",e.nextEditDismiss="next-edit-dismiss",e.nextEditLoaded="next-edit-loaded",e.nextEditSuggestions="next-edit-suggestions",e.nextEditSuggestionsAction="next-edit-suggestions-action",e.nextEditRefreshStarted="next-edit-refresh-started",e.nextEditRefreshFinished="next-edit-refresh-finished",e.nextEditCancel="next-edit-cancel",e.nextEditPreviewActive="next-edit-preview-active",e.nextEditSuggestionsChanged="next-edit-suggestions-changed",e.nextEditNextSuggestionChanged="next-edit-next-suggestion-changed",e.nextEditOpenSuggestion="next-edit-open-suggestion",e.nextEditToggleSuggestionTree="next-edit-toggle-suggestion-tree",e.nextEditActiveSuggestionChanged="next-edit-active-suggestion",e.nextEditPanelFocus="next-edit-panel-focus",e.onboardingLoaded="onboarding-loaded",e.onboardingUpdateState="onboarding-update-state",e.usedChat="used-chat",e.preferencePanelLoaded="preference-panel-loaded",e.preferenceInit="preference-init",e.preferenceResultMessage="preference-result-message",e.preferenceNotify="preference-notify",e.openSettingsPage="open-settings-page",e.settingsPanelLoaded="settings-panel-loaded",e.navigateToSettingsSection="navigate-to-settings-section",e.autofixPanelStateUpdate="autofix-panel-state-update",e.autofixPanelDetailsInitRequest="autofix-panel-details-init-request",e.autofixPanelOpenSpecificStage="autofix-panel-open-specific-stage",e.autofixPanelApplyAndRetestRequest="autofix-panel-apply-and-retest-request",e.mainPanelDisplayApp="main-panel-display-app",e.mainPanelLoaded="main-panel-loaded",e.mainPanelActions="main-panel-actions",e.mainPanelPerformAction="main-panel-perform-action",e.mainPanelCreateProject="main-panel-create-project",e.usedSlashAction="used-slash-action",e.signInLoaded="sign-in-loaded",e.signInLoadedResponse="sign-in-loaded-response",e.awaitingSyncingPermissionLoaded="awaiting-syncing-permission-loaded",e.awaitingSyncingPermissionInitialize="awaiting-syncing-permission-initialize",e.readFileRequest="read-file-request",e.readFileResponse="read-file-response",e.wsContextGetChildrenRequest="ws-context-get-children-request",e.wsContextGetChildrenResponse="ws-context-get-children-response",e.wsContextGetSourceFoldersRequest="ws-context-get-source-folders-request",e.wsContextGetSourceFoldersResponse="ws-context-get-source-folders-response",e.wsContextAddMoreSourceFolders="ws-context-add-more-source-folders",e.wsContextRemoveSourceFolder="ws-context-remove-source-folder",e.wsContextSourceFoldersChanged="ws-context-source-folders-changed",e.wsContextFolderContentsChanged="ws-context-folder-contents-changed",e.wsContextUserRequestedRefresh="ws-context-user-requested-refresh",e.augmentLink="augment-link",e.resetAgentOnboarding="reset-agent-onboarding",e.empty="empty",e.chatGetAgentOnboardingPromptRequest="chat-get-agent-onboarding-prompt-request",e.chatGetAgentOnboardingPromptResponse="chat-get-agent-onboarding-prompt-response",e.getWorkspaceInfoRequest="get-workspace-info-request",e.getWorkspaceInfoResponse="get-workspace-info-response",e.getRemoteAgentOverviewsRequest="get-remote-agent-overviews-request",e.getRemoteAgentOverviewsResponse="get-remote-agent-overviews-response",e.getRemoteAgentChatHistoryRequest="get-remote-agent-chat-history-request",e.getRemoteAgentChatHistoryResponse="get-remote-agent-chat-history-response",e.createRemoteAgentRequest="create-remote-agent-request",e.createRemoteAgentResponse="create-remote-agent-response",e.deleteRemoteAgentRequest="delete-remote-agent-request",e.deleteRemoteAgentResponse="delete-remote-agent-response",e.remoteAgentChatRequest="remote-agent-chat-request",e.remoteAgentChatResponse="remote-agent-chat-response",e.remoteAgentDeleteRequest="remote-agent-delete-request",e.remoteAgentDeleteResponse="remote-agent-delete-response",e.remoteAgentInterruptRequest="remote-agent-interrupt-request",e.remoteAgentInterruptResponse="remote-agent-interrupt-response",e.listSetupScriptsRequest="list-setup-scripts-request",e.listSetupScriptsResponse="list-setup-scripts-response",e.saveSetupScriptRequest="save-setup-script-request",e.saveSetupScriptResponse="save-setup-script-response",e.deleteSetupScriptRequest="delete-setup-script-request",e.deleteSetupScriptResponse="delete-setup-script-response",e.renameSetupScriptRequest="rename-setup-script-request",e.renameSetupScriptResponse="rename-setup-script-response",e.remoteAgentSshRequest="remote-agent-ssh-request",e.remoteAgentSshResponse="remote-agent-ssh-response",e.setRemoteAgentNotificationEnabled="set-remote-agent-notification-enabled",e.getRemoteAgentNotificationEnabledRequest="get-remote-agent-notification-enabled-request",e.getRemoteAgentNotificationEnabledResponse="get-remote-agent-notification-enabled-response",e.deleteRemoteAgentNotificationEnabled="delete-remote-agent-notification-enabled",e.remoteAgentNotifyReady="remote-agent-notify-ready",e.remoteAgentSelectAgentId="remote-agent-select-agent-id",e.remoteAgentWorkspaceLogsRequest="remote-agent-workspace-logs-request",e.remoteAgentWorkspaceLogsResponse="remote-agent-workspace-logs-response",e.updateSharedWebviewState="update-shared-webview-state",e.getSharedWebviewState="get-shared-webview-state",e.getSharedWebviewStateResponse="get-shared-webview-state-response",e.getGitBranchesRequest="get-git-branches-request",e.getGitBranchesResponse="get-git-branches-response",e.gitFetchRequest="git-fetch-request",e.gitFetchResponse="git-fetch-response",e.isGitRepositoryRequest="is-git-repository-request",e.isGitRepositoryResponse="is-git-repository-response",e.getWorkspaceDiffRequest="get-workspace-diff-request",e.getWorkspaceDiffResponse="get-workspace-diff-response",e.getRemoteUrlRequest="get-remote-url-request",e.getRemoteUrlResponse="get-remote-url-response",e.diffExplanationRequest="get-diff-explanation-request",e.diffExplanationResponse="get-diff-explanation-response",e.diffGroupChangesRequest="get-diff-group-changes-request",e.diffGroupChangesResponse="get-diff-group-changes-response",e.diffDescriptionsRequest="get-diff-descriptions-request",e.diffDescriptionsResponse="get-diff-descriptions-response",e.applyChangesRequest="apply-changes-request",e.applyChangesResponse="apply-changes-response",e.isGithubAuthenticatedRequest="is-github-authenticated-request",e.isGithubAuthenticatedResponse="is-github-authenticated-response",e.authenticateGithubRequest="authenticate-github-request",e.authenticateGithubResponse="authenticate-github-response",e.revokeGithubAccessRequest="revoke-github-access-request",e.revokeGithubAccessResponse="revoke-github-access-response",e.listGithubReposForAuthenticatedUserRequest="list-github-repos-for-authenticated-user-request",e.listGithubReposForAuthenticatedUserResponse="list-github-repos-for-authenticated-user-response",e.listGithubRepoBranchesRequest="list-github-repo-branches-request",e.listGithubRepoBranchesResponse="list-github-repo-branches-response",e.getGithubRepoRequest="get-github-repo-request",e.getGithubRepoResponse="get-github-repo-response",e.getCurrentLocalBranchRequest="get-current-local-branch-request",e.getCurrentLocalBranchResponse="get-current-local-branch-response",e.remoteAgentDiffPanelLoaded="remote-agent-diff-panel-loaded",e.remoteAgentDiffPanelSetOpts="remote-agent-diff-panel-set-opts",e.showRemoteAgentDiffPanel="show-remote-agent-diff-panel",e.closeRemoteAgentDiffPanel="close-remote-agent-diff-panel",e.remoteAgentHomePanelLoaded="remote-agent-home-panel-loaded",e.showRemoteAgentHomePanel="show-remote-agent-home-panel",e.closeRemoteAgentHomePanel="close-remote-agent-home-panel",e.triggerInitialOrientation="trigger-initial-orientation",e.executeInitialOrientation="execute-initial-orientation",e.orientationStatusUpdate="orientation-status-update",e.getOrientationStatus="get-orientation-status",e.checkAgentAutoModeApproval="check-agent-auto-mode-approval",e.checkAgentAutoModeApprovalResponse="check-agent-auto-mode-approval-response",e.setAgentAutoModeApproved="set-agent-auto-mode-approved",e.toolConfigLoaded="tool-config-loaded",e.toolConfigInitialize="tool-config-initialize",e.toolConfigSave="tool-config-save",e.toolConfigGetDefinitions="tool-config-get-definitions",e.toolConfigDefinitionsResponse="tool-config-definitions-response",e.toolConfigStartOAuth="tool-config-start-oauth",e.toolConfigStartOAuthResponse="tool-config-start-oauth-response",e.toolConfigRevokeAccess="tool-config-revoke-access",e.getStoredMCPServers="get-stored-mcp-servers",e.setStoredMCPServers="set-stored-mcp-servers",e.getStoredMCPServersResponse="get-stored-mcp-servers-response",e.getChatRequestIdeStateRequest="get-ide-state-node-request",e.getChatRequestIdeStateResponse="get-ide-state-node-response",e.executeCommand="execute-command",e.toggleCollapseUnchangedRegions="toggle-collapse-unchanged-regions",e.openScratchFileRequest="open-scratch-file-request",e.getTerminalSettings="get-terminal-settings",e.terminalSettingsResponse="terminal-settings-response",e.updateTerminalSettings="update-terminal-settings",e.getRemoteAgentStatus="get-remote-agent-status",e.remoteAgentStatusResponse="remote-agent-status-response",e.saveLastRemoteAgentSetupRequest="save-last-remote-agent-setup-request",e.getLastRemoteAgentSetupRequest="get-last-remote-agent-setup-request",e.getLastRemoteAgentSetupResponse="get-last-remote-agent-setup-response",e.rulesLoaded="rules-loaded",e.memoriesLoaded="memories-loaded",e))(ne||{}),oe=(e=>(e.off="off",e.visibleHover="visible-hover",e.visible="visible",e.on="on",e))(oe||{}),ae=(e=>(e.accept="accept",e.reject="reject",e))(ae||{}),ie=(e=>(e.signIn="sign-in",e.chat="chat",e.workspaceContext="workspace-context",e.awaitingSyncingPermission="awaiting-syncing-permission",e.folderSelection="folder-selection",e))(ie||{}),re=(e=>(e.idle="idle",e.inProgress="in-progress",e.succeeded="succeeded",e.failed="failed",e.aborted="aborted",e))(re||{}),ce=(e=>(e.included="included",e.excluded="excluded",e.partial="partial",e))(ce||{});const P="data-vscode-theme-kind";function le(){return self.acquireVsCodeApi!==void 0}function $(){W(function(){const e=document.body.getAttribute(P);if(e)return ue[e]}()),j(function(){const e=document.body.getAttribute(P);if(e)return ge[e]}())}function de(){if(self.acquireVsCodeApi===void 0)throw new Error("acquireVsCodeAPI not available");return function(){new MutationObserver($).observe(document.body,{attributeFilter:[P],attributes:!0}),$()}(),self.acquireVsCodeApi()}const ue={"vscode-dark":S.dark,"vscode-high-contrast":S.dark,"vscode-light":S.light,"vscode-high-contrast-light":S.light},ge={"vscode-dark":A.regular,"vscode-light":A.regular,"vscode-high-contrast":A.highContrast,"vscode-high-contrast-light":A.highContrast};var V;const qe=((V=window==null?void 0:window.augment)!=null&&V.host||(window.augment=window.augment||{},window.augment.host=function(){var e;if(le())return de();if(!((e=window.augment)!=null&&e.host))throw new Error("Augment host not available");return window.augment.host}()),window.augment.host);function pe(e){let t;const s=e[11].default,o=O(s,e,e[10],null);return{c(){o&&o.c()},m(i,c){o&&o.m(i,c),t=!0},p(i,c){o&&o.p&&(!t||1024&c)&&z(o,s,i,i[10],t?U(s,i[10],c,null):T(i[10]),null)},i(i){t||(x(o,i),t=!0)},o(i){w(o,i),t=!1},d(i){o&&o.d(i)}}}function fe(e){let t,s,o,i,c;s=new X({props:{size:M(e[0])}});const d=e[11].default,l=O(d,e,e[10],null);return{c(){t=F("div"),Y(s.$$.fragment),o=ee(),i=F("span"),l&&l.c(),L(t,"class","c-base-btn__loading svelte-q8rj1a"),L(i,"class","c-base-btn__hidden-content svelte-q8rj1a")},m(r,g){C(r,t,g),te(s,t,null),C(r,o,g),C(r,i,g),l&&l.m(i,null),c=!0},p(r,g){const u={};1&g&&(u.size=M(r[0])),s.$set(u),l&&l.p&&(!c||1024&g)&&z(l,d,r,r[10],c?U(d,r[10],g,null):T(r[10]),null)},i(r){c||(x(s.$$.fragment,r),x(l,r),c=!0)},o(r){w(s.$$.fragment,r),w(l,r),c=!1},d(r){r&&(y(t),y(o),y(i)),se(s),l&&l.d(r)}}}function he(e){let t,s,o,i,c,d,l,r;const g=[fe,pe],u=[];function v(a,p){return a[5]?0:1}s=v(e),o=u[s]=g[s](e);let R=[E(e[2]),I(e[7]),{class:i=`c-base-btn c-base-btn--size-${e[0]} c-base-btn--${e[1]} c-base-btn--${e[2]} ${e[9]} c-base-btn--alignment-${e[6]}`},{disabled:c=e[3]||e[5]},e[8]],m={};for(let a=0;a<R.length;a+=1)m=k(m,R[a]);return{c(){t=F("button"),o.c(),D(t,m),b(t,"c-base-btn--highContrast",e[4]),b(t,"c-base-btn--loading",e[5]),b(t,"svelte-q8rj1a",!0)},m(a,p){C(a,t,p),u[s].m(t,null),t.autofocus&&t.focus(),d=!0,l||(r=[f(t,"click",e[12]),f(t,"keyup",e[13]),f(t,"keydown",e[14]),f(t,"mousedown",e[15]),f(t,"mouseover",e[16]),f(t,"focus",e[17]),f(t,"mouseleave",e[18]),f(t,"blur",e[19]),f(t,"contextmenu",e[20])],l=!0)},p(a,[p]){let q=s;s=v(a),s===q?u[s].p(a,p):(_(),w(u[q],1,1,()=>{u[q]=null}),J(),o=u[s],o?o.p(a,p):(o=u[s]=g[s](a),o.c()),x(o,1),o.m(t,null)),D(t,m=K(R,[4&p&&E(a[2]),128&p&&I(a[7]),(!d||583&p&&i!==(i=`c-base-btn c-base-btn--size-${a[0]} c-base-btn--${a[1]} c-base-btn--${a[2]} ${a[9]} c-base-btn--alignment-${a[6]}`))&&{class:i},(!d||40&p&&c!==(c=a[3]||a[5]))&&{disabled:c},256&p&&a[8]])),b(t,"c-base-btn--highContrast",a[4]),b(t,"c-base-btn--loading",a[5]),b(t,"svelte-q8rj1a",!0)},i(a){d||(x(o),d=!0)},o(a){w(o),d=!1},d(a){a&&y(t),u[s].d(),l=!1,Z(r)}}}function M(e){switch(e){case 1:return 1;case 2:case 3:return 2;case 4:return 3}}function me(e,t,s){let o,i;const c=["size","variant","color","disabled","highContrast","loading","alignment","radius"];let d=G(t,c),{$$slots:l={},$$scope:r}=t,{size:g=2}=t,{variant:u="solid"}=t,{color:v="accent"}=t,{disabled:R=!1}=t,{highContrast:m=!1}=t,{loading:a=!1}=t,{alignment:p="center"}=t,{radius:q="medium"}=t;return e.$$set=n=>{t=k(k({},t),Q(n)),s(21,d=G(t,c)),"size"in n&&s(0,g=n.size),"variant"in n&&s(1,u=n.variant),"color"in n&&s(2,v=n.color),"disabled"in n&&s(3,R=n.disabled),"highContrast"in n&&s(4,m=n.highContrast),"loading"in n&&s(5,a=n.loading),"alignment"in n&&s(6,p=n.alignment),"radius"in n&&s(7,q=n.radius),"$$scope"in n&&s(10,r=n.$$scope)},e.$$.update=()=>{s(9,{class:o,...i}=d,o,(s(8,i),s(21,d)))},[g,u,v,R,m,a,p,q,i,o,r,l,function(n){h.call(this,e,n)},function(n){h.call(this,e,n)},function(n){h.call(this,e,n)},function(n){h.call(this,e,n)},function(n){h.call(this,e,n)},function(n){h.call(this,e,n)},function(n){h.call(this,e,n)},function(n){h.call(this,e,n)},function(n){h.call(this,e,n)}]}class be extends B{constructor(t){super(),N(this,t,me,he,H,{size:0,variant:1,color:2,disabled:3,highContrast:4,loading:5,alignment:6,radius:7})}}export{be as B,ae as D,ie as M,re as O,oe as S,ne as W,ce as a,qe as h,le as i};
