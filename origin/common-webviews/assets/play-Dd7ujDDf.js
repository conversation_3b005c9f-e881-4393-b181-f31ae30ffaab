import{S as i,i as c,s as u,b as o,c as e,e as d,f as h,n,h as p}from"./SpinnerAugment-BUJasFTo.js";function C(r){let s,l;return{c(){s=o("svg"),l=o("path"),e(l,"fill-rule","evenodd"),e(l,"clip-rule","evenodd"),e(l,"d","M4.04896 3.25233C4.20904 3.1558 4.40798 3.15014 4.57329 3.23739L14.1733 8.30406C14.3482 8.39638 14.4577 8.57794 14.4577 8.77573C14.4577 8.97352 14.3482 9.15508 14.1733 9.2474L4.57329 14.3141C4.40798 14.4013 4.20904 14.3957 4.04896 14.2991C3.88888 14.2026 3.79102 14.0293 3.79102 13.8424V3.70906C3.79102 3.52214 3.88888 3.34885 4.04896 3.25233Z"),e(l,"fill","currentColor"),e(s,"width","16"),e(s,"height","16"),e(s,"viewBox","0 1 16 16"),e(s,"fill","none"),e(s,"xmlns","http://www.w3.org/2000/svg")},m(t,a){d(t,s,a),h(s,l)},p:n,i:n,o:n,d(t){t&&p(s)}}}class w extends i{constructor(s){super(),c(this,s,null,C,u,{})}}export{w as P};
