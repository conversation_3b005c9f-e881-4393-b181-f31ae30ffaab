import{S as i,i as c,s as u,b as o,c as e,e as d,f as h,n,h as p}from"./SpinnerAugment-BUJasFTo.js";function C(r){let s,l;return{c(){s=o("svg"),l=o("path"),e(l,"fill-rule","evenodd"),e(l,"clip-rule","evenodd"),e(l,"d","M3.13523 6.15803C3.3241 5.95657 3.64052 5.94637 3.84197 6.13523L7.5 9.56464L11.158 6.13523C11.3595 5.94637 11.6759 5.95657 11.8648 6.15803C12.0536 6.35949 12.0434 6.67591 11.842 6.86477L7.84197 10.6148C7.64964 10.7951 7.35036 10.7951 7.15803 10.6148L3.15803 6.86477C2.95657 6.67591 2.94637 6.35949 3.13523 6.15803Z"),e(l,"fill","currentColor"),e(s,"width","15"),e(s,"height","15"),e(s,"viewBox","0 0 15 15"),e(s,"fill","none"),e(s,"xmlns","http://www.w3.org/2000/svg")},m(t,a){d(t,s,a),h(s,l)},p:n,i:n,o:n,d(t){t&&p(s)}}}class w extends i{constructor(s){super(),c(this,s,null,C,u,{})}}export{w as C};
