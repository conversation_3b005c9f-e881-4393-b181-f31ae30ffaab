var G=Object.defineProperty;var W=(a,e,t)=>e in a?G(a,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[e]=t;var i=(a,e,t)=>W(a,typeof e!="symbol"?e+"":e,t);import{R as A}from"./types-CF53Ux0u.js";import{W as o}from"./BaseButton-ci_067e0.js";import{a as x,C as k,P as R,W as B,A as S,T as P,g as z,r as F,s as V,b as q}from"./file-base64-RhZyEMB8.js";import{ah as T,ac as j,an as J,S as X,i as Y,s as Z,b as L,c as w,e as K,f as Q,n as D,h as ee}from"./SpinnerAugment-BUJasFTo.js";import{n as te}from"./file-paths-BcSg4gks.js";import{p as se}from"./types-e72Yl75f.js";const Ee="augment-welcome";var y=(a=>(a.draft="draft",a.sent="sent",a.failed="failed",a.success="success",a.cancelled="cancelled",a))(y||{}),ae=(a=>(a.running="running",a.awaitingUserAction="awaiting-user-action",a.notRunning="not-running",a))(ae||{}),v=(a=>(a.seen="seen",a.unseen="unseen",a))(v||{}),ne=(a=>(a.signInWelcome="sign-in-welcome",a.generateCommitMessage="generate-commit-message",a.summaryResponse="summary-response",a.summaryTitle="summary-title",a.educateFeatures="educate-features",a.autofixMessage="autofix-message",a.autofixSteeringMessage="autofix-steering-message",a.autofixStage="autofix-stage",a.agentOnboarding="agent-onboarding",a.agenticTurnDelimiter="agentic-turn-delimiter",a.agenticRevertDelimiter="agentic-revert-delimiter",a.agenticCheckpointDelimiter="agentic-checkpoint-delimiter",a.exchange="exchange",a))(ne||{});function ie(a){return!!a&&(a.chatItemType===void 0||a.chatItemType==="agent-onboarding")}function xe(a){return ie(a)&&a.status==="success"}function Pe(a){return a.chatItemType==="autofix-message"}function Fe(a){return a.chatItemType==="autofix-steering-message"}function qe(a){return a.chatItemType==="autofix-stage"}function Te(a){return a.chatItemType==="sign-in-welcome"}function Le(a){return a.chatItemType==="generate-commit-message"}function De(a){return a.chatItemType==="summary-response"}function Oe(a){return a.chatItemType==="educate-features"}function ke(a){return a.chatItemType==="agent-onboarding"}function Ue(a){return a.chatItemType==="agentic-turn-delimiter"}function Ne(a){return a.chatItemType==="agentic-checkpoint-delimiter"}function He(a){return a.revertTarget!==void 0}function $e(a){var e;return((e=a.structured_output_nodes)==null?void 0:e.some(t=>t.type===k.TOOL_USE))??!1}function Ge(a){var e;return((e=a.structured_request_nodes)==null?void 0:e.some(t=>t.type===x.TOOL_RESULT))??!1}function We(a){return!(!a||typeof a!="object")&&(!("request_id"in a)||typeof a.request_id=="string")&&(!("seen_state"in a)||a.seen_state==="seen"||a.seen_state==="unseen")}class re{constructor(e,t,s,n=5,r=4e3,c){i(this,"_isCancelled",!1);this.requestId=e,this.chatMessage=t,this.startStreamFn=s,this.maxRetries=n,this.baseDelay=r,this.flags=c}cancel(){this._isCancelled=!0}async*getStream(){let e=0,t=!1;try{for(;!this._isCancelled;){const s=this.startStreamFn(this.chatMessage,this.flags?{flags:this.flags}:void 0);let n=!1,r="";for await(const g of s){if(g.status===y.failed){if(g.isRetriable!==!0||t)return yield g;n=!0,r=g.display_error_message||"Service is currently unavailable";break}t=!0,yield g}if(!n)return;if(this._isCancelled)return yield this.createCancelledStatus();if(e++,e>this.maxRetries)return void(yield{request_id:this.requestId,seen_state:v.unseen,status:y.failed,display_error_message:r,isRetriable:!1});const c=this.baseDelay*2**(e-1);yield{request_id:this.requestId,status:y.sent,display_error_message:`Service temporarily unavailable. Retrying in ${c/1e3} seconds... (Attempt ${e} of ${this.maxRetries})`,isRetriable:!0},await new Promise(g=>setTimeout(g,c)),yield{request_id:this.requestId,status:y.sent,display_error_message:"Generating response...",isRetriable:!0}}this._isCancelled&&(yield this.createCancelledStatus())}catch(s){yield{request_id:this.requestId,seen_state:v.unseen,status:y.failed,display_error_message:s instanceof Error?s.message:String(s)}}}createCancelledStatus(){return{request_id:this.requestId,seen_state:v.unseen,status:y.cancelled}}}function m(a,e){return e in a&&a[e]!==void 0}function oe(a){return m(a,"file")}function ce(a){return m(a,"recentFile")}function ge(a){return m(a,"folder")}function le(a){return m(a,"sourceFolder")}function Be(a){return m(a,"sourceFolderGroup")}function ze(a){return m(a,"selection")}function de(a){return m(a,"externalSource")}function Ve(a){return m(a,"allDefaultContext")}function je(a){return m(a,"clearContext")}function Je(a){return m(a,"userGuidelines")}function Xe(a){return m(a,"agentMemories")}function he(a){return m(a,"personality")}const Ye={allDefaultContext:!0,label:"Default Context",id:"allDefaultContext"},Ze={clearContext:!0,label:"Clear Context",id:"clearContext"},Ke={userGuidelines:{enabled:!1,overLimit:!1,contents:"",lengthLimit:2e3},label:"User Guidelines",id:"userGuidelines"},Qe={agentMemories:{},label:"Agent Memories",id:"agentMemories"},O=[{personality:{type:R.DEFAULT,description:"Expert software engineer - trusted coding agent, at your service!"},label:"Agent Auggie",name:"auggie-personality-agent-default",id:"auggie-personality-agent-default"},{personality:{type:R.PROTOTYPER,description:"Fast and loose - let's get it done, boss!"},label:"Prototyper Auggie",name:"auggie-personality-prototyper",id:"auggie-personality-prototyper"},{personality:{type:R.BRAINSTORM,description:"Thoughtful and creative - thinking through all possibilities..."},label:"Brainstorm Auggie",name:"auggie-personality-brainstorm",id:"auggie-personality-brainstorm"},{personality:{type:R.REVIEWER,description:"Code detective - finding issues and analyzing implications"},label:"Reviewer Auggie",name:"auggie-personality-reviewer",id:"auggie-personality-reviewer"}];function et(a){return m(a,"group")}function tt(a){const e=new Map;return a.forEach(t=>{oe(t)?e.set("file",[...e.get("file")??[],t]):ce(t)?e.set("recentFile",[...e.get("recentFile")??[],t]):ge(t)?e.set("folder",[...e.get("folder")??[],t]):de(t)?e.set("externalSource",[...e.get("externalSource")??[],t]):le(t)?e.set("sourceFolder",[...e.get("sourceFolder")??[],t]):he(t)&&e.set("personality",[...e.get("personality")??[],t])}),[{label:"Personalities",id:"personalities",group:{type:"personality",materialIcon:"person",items:e.get("personality")??[]}},{label:"Files",id:"files",group:{type:"file",materialIcon:"insert_drive_file",items:e.get("file")??[]}},{label:"Folders",id:"folders",group:{type:"folder",materialIcon:"folder",items:e.get("folder")??[]}},{label:"Source Folders",id:"sourceFolders",group:{type:"sourceFolder",materialIcon:"folder_managed",items:e.get("sourceFolder")??[]}},{label:"Recently Opened Files",id:"recentlyOpenedFiles",group:{type:"recentFile",materialIcon:"insert_drive_file",items:e.get("recentFile")??[]}},{label:"Documentation",id:"externalSources",group:{type:"externalSource",materialIcon:"link",items:e.get("externalSource")??[]}}].filter(t=>t.group.items.length>0)}function ue(a){const e={label:te(a.pathName).split("/").filter(t=>t.trim()!=="").pop()||"",name:a.pathName,id:se({rootPath:a.repoRoot,relPath:a.pathName})};if(a.fullRange){const t=`:L${a.fullRange.startLineNumber}-${a.fullRange.endLineNumber}`;e.label+=t,e.name+=t,e.id+=t}else if(a.range){const t=`:L${a.range.start}-${a.range.stop}`;e.label+=t,e.name+=t,e.id+=t}return e}class pe{constructor(e,t,s){i(this,"getChatInitData",async()=>(await this._asyncMsgSender.send({type:o.chatLoaded},3e4)).data);i(this,"reportWebviewClientEvent",e=>{this._asyncMsgSender.send({type:o.reportWebviewClientMetric,data:{webviewName:B.chat,client_metric:e,value:1}})});i(this,"reportAgentSessionEvent",e=>{this._asyncMsgSender.sendToSidecar({type:S.reportAgentSessionEvent,data:e})});i(this,"reportAgentRequestEvent",e=>{this._asyncMsgSender.sendToSidecar({type:S.reportAgentRequestEvent,data:e})});i(this,"getSuggestions",async(e,t=!1)=>{const s={rootPath:"",relPath:e},n=this.findFiles(s,6),r=this.findRecentlyOpenedFiles(s,6),c=this.findFolders(s,3),g=this.findExternalSources(e,t),[l,d,h,u]=await Promise.all([M(n,[]),M(r,[]),M(c,[]),M(g,[])]),f=(_,b)=>({...ue(_),[b]:_}),p=[...l.map(_=>f(_,"file")),...h.map(_=>f(_,"folder")),...d.map(_=>f(_,"recentFile")),...u.map(_=>({label:_.name,name:_.name,id:_.id,externalSource:_}))];if(this._flags.enablePersonalities){const _=this.getPersonalities(e);_.length>0&&p.push(..._)}return p});i(this,"getPersonalities",e=>{if(!this._flags.enablePersonalities)return[];if(e==="")return O;const t=e.toLowerCase();return O.filter(s=>{const n=s.personality.description.toLowerCase(),r=s.label.toLowerCase();return n.includes(t)||r.includes(t)})});i(this,"sendAction",e=>{this._host.postMessage({type:o.mainPanelPerformAction,data:e})});i(this,"showAugmentPanel",()=>{this._asyncMsgSender.send({type:o.showAugmentPanel})});i(this,"openConfirmationModal",async e=>(await this._asyncMsgSender.send({type:o.openConfirmationModal,data:e},1e9)).data.ok);i(this,"clearMetadataFor",e=>{this._host.postMessage({type:o.chatClearMetadata,data:e})});i(this,"resolvePath",async(e,t=void 0)=>{const s=await this._asyncMsgSender.send({type:o.resolveFileRequest,data:{...e,exactMatch:!0,maxResults:1,searchScope:t}},5e3);if(s.data)return s.data});i(this,"resolveSymbols",async(e,t)=>(await this._asyncMsgSender.send({type:o.findSymbolRequest,data:{query:e,searchScope:t}},3e4)).data);i(this,"getDiagnostics",async()=>(await this._asyncMsgSender.send({type:o.getDiagnosticsRequest},1e3)).data);i(this,"findFiles",async(e,t=12)=>(await this._asyncMsgSender.send({type:o.findFileRequest,data:{...e,maxResults:t}},5e3)).data);i(this,"findFolders",async(e,t=12)=>(await this._asyncMsgSender.send({type:o.findFolderRequest,data:{...e,maxResults:t}},5e3)).data);i(this,"findRecentlyOpenedFiles",async(e,t=12)=>(await this._asyncMsgSender.send({type:o.findRecentlyOpenedFilesRequest,data:{...e,maxResults:t}},5e3)).data);i(this,"findExternalSources",async(e,t=!1)=>this._flags.enableExternalSourcesInChat?t?[]:(await this._asyncMsgSender.send({type:o.findExternalSourcesRequest,data:{query:e,source_types:[]}},5e3)).data.sources??[]:[]);i(this,"openFile",e=>{this._host.postMessage({type:o.openFile,data:e})});i(this,"saveFile",e=>this._host.postMessage({type:o.saveFile,data:e}));i(this,"loadFile",e=>this._host.postMessage({type:o.loadFile,data:e}));i(this,"openMemoriesFile",()=>{this._host.postMessage({type:o.openMemoriesFile})});i(this,"createFile",(e,t)=>{this._host.postMessage({type:o.chatCreateFile,data:{code:e,relPath:t}})});i(this,"openScratchFile",async(e,t="shellscript")=>{await this._asyncMsgSender.send({type:o.openScratchFileRequest,data:{content:e,language:t}},1e4)});i(this,"resolveWorkspaceFileChunk",async e=>{try{return(await this._asyncMsgSender.send({type:o.resolveWorkspaceFileChunkRequest,data:e},5e3)).data}catch{return}});i(this,"smartPaste",e=>{this._host.postMessage({type:o.chatSmartPaste,data:e})});i(this,"saveChat",async(e,t,s)=>this._asyncMsgSender.send({type:o.saveChat,data:{conversationId:e,chatHistory:t,title:s}}));i(this,"launchAutofixPanel",async(e,t,s)=>this._asyncMsgSender.send({type:o.chatLaunchAutofixPanel,data:{conversationId:e,iterationId:t,stage:s}}));i(this,"updateUserGuidelines",e=>{this._host.postMessage({type:o.updateUserGuidelines,data:e})});i(this,"updateWorkspaceGuidelines",e=>{this._host.postMessage({type:o.updateWorkspaceGuidelines,data:e})});i(this,"openSettingsPage",e=>{this._host.postMessage({type:o.openSettingsPage,data:e})});i(this,"_activeRetryStreams",new Map);i(this,"cancelChatStream",async e=>{var t;(t=this._activeRetryStreams.get(e))==null||t.cancel(),await this._asyncMsgSender.send({type:o.chatUserCancel,data:{requestId:e}},1e4)});i(this,"sendUserRating",async(e,t,s,n="")=>{const r={requestId:e,rating:s,note:n,mode:t},c={type:o.chatRating,data:r};return(await this._asyncMsgSender.send(c,3e4)).data});i(this,"triggerUsedChatMetric",()=>{this._host.postMessage({type:o.usedChat})});i(this,"createProject",e=>{this._host.postMessage({type:o.mainPanelCreateProject,data:{name:e}})});i(this,"openProjectFolder",()=>{this._host.postMessage({type:o.mainPanelPerformAction,data:"open-folder"})});i(this,"closeProjectFolder",()=>{this._host.postMessage({type:o.mainPanelPerformAction,data:"close-folder"})});i(this,"cloneRepository",()=>{this._host.postMessage({type:o.mainPanelPerformAction,data:"clone-repository"})});i(this,"grantSyncPermission",()=>{this._host.postMessage({type:o.mainPanelPerformAction,data:"grant-sync-permission"})});i(this,"callTool",async(e,t,s,n,r,c)=>{const g={type:o.callTool,data:{chatRequestId:e,toolUseId:t,name:s,input:n,chatHistory:r,conversationId:c}};return(await this._asyncMsgSender.send(g,0)).data});i(this,"cancelToolRun",async(e,t)=>{const s={type:o.cancelToolRun,data:{requestId:e,toolUseId:t}};await this._asyncMsgSender.send(s,0)});i(this,"checkSafe",async(e,t)=>{const s={type:o.toolCheckSafe,data:{name:e,input:t}};return(await this._asyncMsgSender.send(s,0)).data.isSafe});i(this,"closeAllToolProcesses",async()=>{await this._asyncMsgSender.sendToSidecar({type:P.closeAllToolProcesses},0)});i(this,"getToolIdentifier",async e=>{const t={type:P.getToolIdentifierRequest,data:{toolName:e}};return(await this._asyncMsgSender.sendToSidecar(t,0)).data});i(this,"executeCommand",async(e,t,s)=>{try{const n=await this._asyncMsgSender.send({type:o.chatAutofixExecuteCommandRequest,data:{iterationId:e,command:t,args:s}},6e5);return{output:n.data.output,returnCode:n.data.returnCode}}catch(n){throw console.error("[ExtensionClient] Execute command failed:",n),n}});i(this,"sendAutofixStateUpdate",async e=>{await this._asyncMsgSender.send({type:o.chatAutofixStateUpdate,data:e})});i(this,"autofixPlan",async(e,t)=>(await this._asyncMsgSender.send({type:o.chatAutofixPlanRequest,data:{command:e,steeringHistory:t}},6e4)).data.plan);i(this,"setChatMode",e=>{this._asyncMsgSender.send({type:o.chatModeChanged,data:{mode:e}})});i(this,"getAgentEditList",async(e,t)=>{const s={type:S.getEditListRequest,data:{fromTimestamp:e,toTimestamp:t}};return(await this._asyncMsgSender.sendToSidecar(s,3e4)).data});i(this,"hasChangesSince",async e=>{const t={type:S.getEditListRequest,data:{fromTimestamp:e,toTimestamp:Number.MAX_SAFE_INTEGER}};return(await this._asyncMsgSender.sendToSidecar(t,3e4)).data.edits.filter(s=>{var n,r;return((n=s.changesSummary)==null?void 0:n.totalAddedLines)||((r=s.changesSummary)==null?void 0:r.totalRemovedLines)}).length>0});i(this,"getToolCallCheckpoint",async e=>{const t={type:o.getToolCallCheckpoint,data:{requestId:e}};return(await this._asyncMsgSender.send(t,3e4)).data.checkpointNumber});i(this,"setCurrentConversation",e=>{this._asyncMsgSender.sendToSidecar({type:S.setCurrentConversation,data:{conversationId:e}})});i(this,"showAgentReview",(e,t,s,n=!0)=>{this._asyncMsgSender.sendToSidecar({type:S.chatReviewAgentFile,data:{qualifiedPathName:e,fromTimestamp:t,toTimestamp:s,retainFocus:n}})});i(this,"acceptAllAgentEdits",async()=>(await this._asyncMsgSender.sendToSidecar({type:S.chatAgentEditAcceptAll}),!0));i(this,"revertToTimestamp",async(e,t)=>(await this._asyncMsgSender.sendToSidecar({type:S.revertToTimestamp,data:{timestamp:e,qualifiedPathNames:t}}),!0));i(this,"getAgentOnboardingPrompt",async()=>(await this._asyncMsgSender.send({type:o.chatGetAgentOnboardingPromptRequest,data:{}},3e4)).data.prompt);i(this,"getAgentEditChangesByRequestId",async e=>{const t={type:S.getEditChangesByRequestIdRequest,data:{requestId:e}};return(await this._asyncMsgSender.sendToSidecar(t,3e4)).data});i(this,"getAgentEditContentsByRequestId",async e=>{const t={type:S.getAgentEditContentsByRequestId,data:{requestId:e}};return(await this._asyncMsgSender.sendToSidecar(t,3e4)).data});i(this,"triggerInitialOrientation",()=>{this._host.postMessage({type:o.triggerInitialOrientation})});i(this,"getWorkspaceInfo",async()=>{try{return(await this._asyncMsgSender.send({type:o.getWorkspaceInfoRequest},5e3)).data}catch(e){return console.error("Error getting workspace info:",e),{}}});i(this,"getRemoteAgentStatus",async()=>{try{return(await this._asyncMsgSender.send({type:o.getRemoteAgentStatus},5e3)).data}catch(e){return console.error("Error getting remote agent status:",e),{isRemoteAgentWindow:!1}}});i(this,"toggleCollapseUnchangedRegions",()=>{this._host.postMessage({type:o.toggleCollapseUnchangedRegions})});i(this,"checkAgentAutoModeApproval",async()=>(await this._asyncMsgSender.send({type:o.checkAgentAutoModeApproval},5e3)).data);i(this,"setAgentAutoModeApproved",async e=>{await this._asyncMsgSender.send({type:o.setAgentAutoModeApproved,data:e},5e3)});i(this,"checkHasEverUsedAgent",async()=>(await this._asyncMsgSender.sendToSidecar({type:S.checkHasEverUsedAgent},5e3)).data);i(this,"setHasEverUsedAgent",async e=>{await this._asyncMsgSender.sendToSidecar({type:S.setHasEverUsedAgent,data:e},5e3)});i(this,"getChatRequestIdeState",async()=>{const e={type:o.getChatRequestIdeStateRequest};return(await this._asyncMsgSender.send(e,3e4)).data});i(this,"reportError",e=>{this._host.postMessage({type:o.reportError,data:e})});this._host=e,this._asyncMsgSender=t,this._flags=s}async*generateCommitMessage(){const e={type:o.generateCommitMessage},t=this._asyncMsgSender.stream(e,3e4,6e4);yield*I(t)}async*sendInstructionMessage(e,t){const s={instruction:e.request_message??"",selectedCodeDetails:t,requestId:e.request_id},n={type:o.chatInstructionMessage,data:s},r=this._asyncMsgSender.stream(n,3e4,6e4);yield*async function*(c){let g;try{for await(const l of c)g=l.data.requestId,yield{request_id:g,response_text:l.data.text,seen_state:v.unseen,status:y.sent};yield{request_id:g,seen_state:v.unseen,status:y.success}}catch{yield{request_id:g,seen_state:v.unseen,status:y.failed}}}(r)}async openGuidelines(e){this._host.postMessage({type:o.openGuidelines,data:e})}async*getExistingChatStream(e,t){if(!e.request_id)return;const s=t==null?void 0:t.flags.enablePreferenceCollection,n=s?1e9:6e4,r=s?1e9:3e5,c={type:o.chatGetStreamRequest,data:{requestId:e.request_id}},g=this._asyncMsgSender.stream(c,n,r);yield*I(g,this.reportError)}async*startChatStream(e,t){const s=t==null?void 0:t.flags.enablePreferenceCollection,n=s?1e9:6e4,r=s?1e9:3e5,c={type:o.chatUserMessage,data:e},g=this._asyncMsgSender.stream(c,n,r);yield*I(g,this.reportError)}async checkToolExists(e){return(await this._asyncMsgSender.send({type:o.checkToolExists,toolName:e},0)).exists}async saveImage(e,t){const s=z(await F(e)),n=t??`${await V(await q(s))}.${e.name.split(".").at(-1)}`;return(await this._asyncMsgSender.send({type:o.chatSaveImageRequest,data:{filename:n,data:s}},1e4)).data}async loadImage(e){const t=await this._asyncMsgSender.send({type:o.chatLoadImageRequest,data:e},1e4),s=t.data?await q(t.data):void 0;if(!s)return;let n="application/octet-stream";const r=e.split(".").at(-1);r==="png"?n="image/png":r!=="jpg"&&r!=="jpeg"||(n="image/jpeg");const c=new File([s],e,{type:n});return await F(c)}async deleteImage(e){await this._asyncMsgSender.send({type:o.chatDeleteImageRequest,data:e},1e4)}async*startChatStreamWithRetry(e,t,s){const n=new re(e,t,(r,c)=>this.startChatStream(r,c),(s==null?void 0:s.maxRetries)??5,4e3,s==null?void 0:s.flags);this._activeRetryStreams.set(e,n);try{yield*n.getStream()}finally{this._activeRetryStreams.delete(e)}}}async function*I(a,e=()=>{}){let t;try{for await(const s of a){if(t=s.data.requestId,s.data.error)return yield{request_id:t,seen_state:v.unseen,status:y.failed,display_error_message:s.data.error.displayErrorMessage,isRetriable:s.data.error.isRetriable};yield{request_id:t,response_text:s.data.text,workspace_file_chunks:s.data.workspaceFileChunks,structured_output_nodes:_e(s.data.nodes),seen_state:v.unseen,status:y.sent}}yield{request_id:t,seen_state:v.unseen,status:y.success}}catch(s){e({originalRequestId:t||"",sanitizedMessage:s instanceof Error?s.message:String(s),stackTrace:s instanceof Error&&s.stack||"",diagnostics:[{key:"error_class",value:"Extension-WebView Error"}]}),yield{request_id:t,seen_state:v.unseen,status:y.failed}}}async function M(a,e){try{return await a}catch(t){return console.warn(`Error while resolving promise: ${t}`),e}}function _e(a){if(!a)return a;let e=!1;return a.filter(t=>t.type!==k.TOOL_USE||!e&&(e=!0,!0))}var C=(a=>(a[a.unknown=0]="unknown",a[a.new=1]="new",a[a.checkingSafety=2]="checkingSafety",a[a.runnable=3]="runnable",a[a.running=4]="running",a[a.completed=5]="completed",a[a.error=6]="error",a[a.cancelling=7]="cancelling",a[a.cancelled=8]="cancelled",a))(C||{});function st(a){return a.requestId+";"+a.toolUseId}function at(a){const[e,t]=a.split(";");return{requestId:e,toolUseId:t}}function nt(a,e){return a==null?e:typeof a=="string"?a:e}function it(a){try{const e=new Date(a);if(isNaN(e.getTime()))return"Unknown time";const t=new Date().getTime()-e.getTime(),s=Math.floor(t/1e3),n=Math.floor(s/60),r=Math.floor(n/60),c=Math.floor(r/24);return s<60?`${s}s ago`:n<60?`${n}m ago`:r<24?`${r}h ago`:c<30?`${c}d ago`:e.toLocaleDateString()}catch(e){return console.error("Error formatting date:",e),"Unknown time"}}function rt(a){switch(a){case A.agentStarting:return"Starting";case A.agentRunning:return"Running";case A.agentIdle:return"Idle";case A.agentFailed:return"Failed";default:return"Unknown"}}const U=a=>{let e={};for(const t of a){const s=e[t.new_path];e[t.new_path]=s?{...s,new_contents:t.new_contents,new_path:t.new_path}:t}return Object.values(e).filter(t=>!t.old_path||!t.new_path||t.old_path!==t.new_path||t.old_contents!==t.new_contents)},fe=a=>{const e=a.flatMap(t=>t.changed_files);return U(e)},ot=(a,e)=>{const t=a.slice(0,e).findLastIndex(n=>n.exchange.request_message),s=a.slice(t+1,e+1).flatMap(n=>n.changed_files);return U(s)},ct=(a,e)=>{var s;const t=a.slice(0,e).findLastIndex(n=>n.exchange.request_message);return((s=a[t])==null?void 0:s.exchange.request_message)??""};function gt(a,e){a.length&&e&&a.forEach(t=>{e(t.path,t.originalCode,t.newCode)})}function N(a){return a.sort((e,t)=>{const s=new Date(e.updated_at||e.started_at);return new Date(t.updated_at||t.started_at).getTime()-s.getTime()})}class H{constructor(e){i(this,"_applyingFilePaths",T([]));i(this,"_appliedFilePaths",T([]));this._asyncMsgSender=e}get applyingFilePaths(){let e=[];return this._applyingFilePaths.subscribe(t=>{e=t})(),e}get appliedFilePaths(){let e=[];return this._appliedFilePaths.subscribe(t=>{e=t})(),e}async getDiffExplanation(e,t,s=3e4){try{return(await this._asyncMsgSender.send({type:o.diffExplanationRequest,data:{changedFiles:e,apikey:t}},s)).data.explanation}catch(n){return console.error("Failed to get diff explanation:",n),[]}}async groupChanges(e,t=!1,s){try{return(await this._asyncMsgSender.send({type:o.diffGroupChangesRequest,data:{changedFiles:e,changesById:t,apikey:s}})).data.groupedChanges}catch(n){return console.error("Failed to group changes:",n),[]}}async getDescriptions(e,t){try{return(await this._asyncMsgSender.send({type:o.diffDescriptionsRequest,data:{groupedChanges:e,apikey:t}})).data.explanation}catch(s){return console.error("Failed to get descriptions:",s),[]}}async applyChanges(e,t,s){this._applyingFilePaths.update(n=>[...n.filter(r=>r!==e),e]);try{(await this._asyncMsgSender.send({type:o.applyChangesRequest,data:{path:e,originalCode:t,newCode:s}})).data.success&&this._appliedFilePaths.update(n=>[...n.filter(r=>r!==e),e])}catch(n){console.error("applyChanges error",n)}finally{this._applyingFilePaths.update(n=>n.filter(r=>r!==e))}}handleMessageFromExtension(e){return!1}}i(H,"key","remoteAgentsDiffOpsModel");class ${constructor(e){i(this,"_asyncMsgSender");this._asyncMsgSender=e}async sshToRemoteAgent(e){const t=await this._asyncMsgSender.send({type:o.remoteAgentSshRequest,data:{agentId:e}},1e4);return!!t.data.success||(console.error("Failed to connect to remote agent:",t.data.error),!1)}async deleteRemoteAgent(e){return(await this._asyncMsgSender.send({type:o.deleteRemoteAgentRequest,data:{agentId:e}},1e4)).data.success}async showRemoteAgentHomePanel(){await this._asyncMsgSender.send({type:o.showRemoteAgentHomePanel})}async closeRemoteAgentHomePanel(){await this._asyncMsgSender.send({type:o.closeRemoteAgentHomePanel})}async getRemoteAgentNotificationEnabled(e){return(await this._asyncMsgSender.send({type:o.getRemoteAgentNotificationEnabledRequest,data:{agentIds:e}})).data}async setRemoteAgentNotificationEnabled(e,t){await this._asyncMsgSender.send({type:o.setRemoteAgentNotificationEnabled,data:{agentId:e,enabled:t}})}async deleteRemoteAgentNotificationEnabled(e){await this._asyncMsgSender.send({type:o.deleteRemoteAgentNotificationEnabled,data:{agentId:e}})}async notifyRemoteAgentReady(e){await this._asyncMsgSender.send({type:o.remoteAgentNotifyReady,data:{agentId:e}})}async showRemoteAgentDiffPanel(e){await this._asyncMsgSender.send({type:o.showRemoteAgentDiffPanel,data:e})}async closeRemoteAgentDiffPanel(){await this._asyncMsgSender.send({type:o.closeRemoteAgentDiffPanel})}async getRemoteAgentChatHistory(e,t,s=1e4){return await this._asyncMsgSender.send({type:o.getRemoteAgentChatHistoryRequest,data:{agentId:e,lastProcessedSequenceId:t}},s)}async sendRemoteAgentChatRequest(e,t,s=1e4){await this._asyncMsgSender.send({type:o.remoteAgentChatRequest,data:{agentId:e,requestDetails:t}},s)}async interruptRemoteAgent(e,t=1e4){return await this._asyncMsgSender.send({type:o.remoteAgentInterruptRequest,data:{agentId:e}},t)}async createRemoteAgent(e,t,s,n,r=1e4){return await this._asyncMsgSender.send({type:o.createRemoteAgentRequest,data:{prompt:e,workspaceSetup:t,setupScript:s,isSetupScriptAgent:n}},r)}async getRemoteAgentOverviews(e=1e4){return await this._asyncMsgSender.send({type:o.getRemoteAgentOverviewsRequest},e)}async listSetupScripts(e=5e3){return await this._asyncMsgSender.send({type:o.listSetupScriptsRequest},e)}async saveSetupScript(e,t,s,n=5e3){return await this._asyncMsgSender.send({type:o.saveSetupScriptRequest,data:{name:e,content:t,location:s}},n)}async deleteSetupScript(e,t,s=5e3){return await this._asyncMsgSender.send({type:o.deleteSetupScriptRequest,data:{name:e,location:t}},s)}async renameSetupScript(e,t,s,n=5e3){return await this._asyncMsgSender.send({type:o.renameSetupScriptRequest,data:{oldName:e,newName:t,location:s}},n)}async getRemoteAgentWorkspaceLogs(e,t=1e4){return await this._asyncMsgSender.send({type:o.remoteAgentWorkspaceLogsRequest,data:{agentId:e}},t)}async saveLastRemoteAgentSetup(e,t,s){return await this._asyncMsgSender.send({type:o.saveLastRemoteAgentSetupRequest,data:{lastRemoteAgentGitRepoUrl:e,lastRemoteAgentGitBranch:t,lastRemoteAgentSetupScript:s}})}async getLastRemoteAgentSetup(){return await this._asyncMsgSender.send({type:o.getLastRemoteAgentSetupRequest})}async openDiffInBuffer(e,t,s){return await this._asyncMsgSender.send({type:o.openDiffInBuffer,data:{oldContents:e,newContents:t,filePath:s}})}}i($,"key","remoteAgentsClient");class E{constructor(e){i(this,"_pollingTimers",new Map);i(this,"_pollingInterval");i(this,"_failedAttempts",0);i(this,"_lastSuccessfulFetch",0);this._config=e,this._pollingInterval=e.defaultInterval}start(e){e&&this._pollingTimers.has(e)?this.stop(e):!e&&this._pollingTimers.has("global")&&this.stop("global"),this.refresh(e);const t=setInterval(()=>{this.refresh(e)},this._pollingInterval);e?this._pollingTimers.set(e,t):this._pollingTimers.set("global",t)}stop(e){if(e){const t=this._pollingTimers.get(e);t&&(clearInterval(t),this._pollingTimers.delete(e))}else for(const[t,s]of this._pollingTimers.entries())clearInterval(s),this._pollingTimers.delete(t)}async refresh(e){try{const t=await this._config.refreshFn(e);return this._failedAttempts=0,this._lastSuccessfulFetch=Date.now(),this._pollingInterval=this._config.defaultInterval,this._config.stopCondition&&e&&this._config.stopCondition(t,e)&&this.stop(e),t}catch{return this._failedAttempts++,this._failedAttempts>3?this._pollingInterval=1e4:this._pollingInterval=Math.min(1e3*Math.pow(2,this._failedAttempts),1e4),null}}isPolling(e){return e?this._pollingTimers.has(e):this._pollingTimers.size>0}get timeSinceLastSuccessfulFetch(){return Date.now()-this._lastSuccessfulFetch}get failedAttempts(){return this._failedAttempts}resetFailedAttempts(){this._failedAttempts=0}}class ye{constructor(e,t){i(this,"_state",{agentOverviews:[],agentConversations:new Map,agentLogs:new Map,maxRemoteAgents:0,overviewError:void 0,conversationError:void 0,logsError:void 0,isOverviewsLoading:!1,isConversationLoading:!1,isLogsLoading:!1,logPollFailedCount:0});i(this,"_loggingMaxRetries",8);i(this,"_overviewsPollingManager");i(this,"_conversationPollingManager");i(this,"_logsPollingManager");i(this,"_isInitialOverviewFetch",!0);i(this,"_stateUpdateSubscribers",new Set);this._flagsModel=e,this._remoteAgentsClient=t,this._overviewsPollingManager=new E({defaultInterval:5e3,refreshFn:async()=>this.refreshAgentOverviews()}),this._conversationPollingManager=new E({defaultInterval:1e3,refreshFn:async s=>{if(!s)throw new Error("Agent ID is required for conversation polling");await this.refreshCurrentAgent(s)}}),this._logsPollingManager=new E({defaultInterval:1e3,refreshFn:async s=>{if(!s)throw new Error("Agent ID is required for logs polling");return this.refreshAgentLogs(s)},stopCondition:(s,n)=>{const r=this._state.agentOverviews.find(g=>g.remote_agent_id===n);if(!r)return this._state.logPollFailedCount++,this._state.logPollFailedCount>this._loggingMaxRetries&&(this._state.logPollFailedCount=0,!0);const c=(r==null?void 0:r.status)!==A.agentStarting;return c&&(this._state.logPollFailedCount=0),c}}),this._flagsModel.subscribe(s=>{const n=this._overviewsPollingManager.isPolling()||this._conversationPollingManager.isPolling()||this._logsPollingManager.isPolling(),r=s.enableBackgroundAgents;r&&!n?this.startStateUpdates():!r&&n&&this.stopStateUpdates()})}get state(){return this._state}startStateUpdates(e){var t,s;this._flagsModel.enableBackgroundAgents&&(e?(e.overviews&&this._overviewsPollingManager.start(),(t=e.conversation)!=null&&t.agentId&&this._conversationPollingManager.start(e.conversation.agentId),(s=e.logs)!=null&&s.agentId&&(this._state.logPollFailedCount=0,this._logsPollingManager.start(e.logs.agentId))):this._overviewsPollingManager.start())}stopStateUpdates(e){var t,s;if(!e)return this._overviewsPollingManager.stop(),this._conversationPollingManager.stop(),void this._logsPollingManager.stop();e.overviews&&this._overviewsPollingManager.stop(),(t=e.conversation)!=null&&t.agentId&&this._conversationPollingManager.stop(e.conversation.agentId),(s=e.logs)!=null&&s.agentId&&this._logsPollingManager.stop(e.logs.agentId)}async refreshCurrentAgent(e){try{const t=this._state.agentConversations.get(e)||[],s=t[t.length-1],n=(s==null?void 0:s.sequence_id)??0,r=n===0?0:n-1,c=await this._remoteAgentsClient.getRemoteAgentChatHistory(e,r);if(c.data.error)return this._state.conversationError=c.data.error,void this.notifySubscribers({type:"conversation",agentId:e,data:t,error:c.data.error});const g=c.data.chatHistory.filter(d=>d.exchange!==null),l=t.length>0?function(d,h){if(d.length===0)return h;if(h.length===0)return d;const u=[];let f=0,p=0;for(;f<d.length&&p<h.length;){const _=d[f].sequence_id,b=h[p].sequence_id;b!==void 0?_!==void 0?_<b?(u.push(d[f]),f++):_>b?(u.push(h[p]),p++):(u.push(h[p]),f++,p++):(console.warn("Existing history has an exchange with an undefined sequence ID"),f++):(console.warn("New history has an exchange with an undefined sequence ID"),p++)}for(;f<d.length;)u.push(d[f]),f++;for(;p<h.length;)u.push(h[p]),p++;return u}(t,g):g;this._state.agentConversations.set(e,l),this._state.conversationError=void 0,this.notifySubscribers({type:"conversation",agentId:e,data:l})}catch(t){const s=t instanceof Error?t.message:String(t);this._state.conversationError=s,this.notifySubscribers({type:"conversation",agentId:e,data:this._state.agentConversations.get(e)||[],error:s})}}async refreshAgentOverviews(){this._isInitialOverviewFetch&&(this._state.overviewError=void 0,this._state.isOverviewsLoading=!0,this._isInitialOverviewFetch=!1);try{const e=await this._remoteAgentsClient.getRemoteAgentOverviews();if(e.data.error)throw new Error(e.data.error);e.data.maxRemoteAgents!==void 0&&(this._state.maxRemoteAgents=e.data.maxRemoteAgents);const t=N(e.data.overviews);return this._state.agentOverviews=t,this._state.overviewError=void 0,this._state.isOverviewsLoading=!1,this.notifySubscribers({type:"overviews",data:t}),t}catch(e){this._state.isOverviewsLoading=!1;const t=e instanceof Error?e.message:String(e);return this._isInitialOverviewFetch||this._state.agentOverviews.length===0?this._state.overviewError=t:(console.warn("Background refresh failed:",e),this._overviewsPollingManager.timeSinceLastSuccessfulFetch>3e4&&this._overviewsPollingManager.failedAttempts>1&&(this._state.overviewError||(this._state.overviewError=`Using cached data. Refresh failed: ${t}`))),this.notifySubscribers({type:"overviews",data:this._state.agentOverviews,error:this._state.overviewError}),this._state.agentOverviews}}async refreshAgentLogs(e){var s;try{const n=await this._remoteAgentsClient.getRemoteAgentWorkspaceLogs(e);if(!n.data.workspaceSetupStatus)return;const r=n.data.workspaceSetupStatus,c={steps:(t=r,(s=t==null?void 0:t.steps)!=null&&s.length?t.steps.reduce((g,l)=>{const d=g[g.length-1];return d&&d.step_number===l.step_number?(d.logs+=`
${l.logs}`,d.status=l.status):g.push(l),g},[]):[])};if(r){let g=!1;const l=this.state.agentLogs.get(e);if(l&&c.steps.length===l.steps.length){for(let d=0;d<c.steps.length;d++){if(c.steps[d].status!==l.steps[d].status){g=!0;break}if(c.steps[d].logs.length!==l.steps[d].logs.length){g=!0;break}}g||JSON.stringify(c)!==JSON.stringify(l)&&(g=!0)}else g=!0;if(!g)return l;g&&(this._state.agentLogs.set(e,c),this._state.logsError=void 0,this.notifySubscribers({type:"logs",agentId:e,data:c}))}return c}catch(n){const r=n instanceof Error?n.message:String(n);return this._state.logsError=r,this.notifySubscribers({type:"logs",agentId:e,data:this._state.agentLogs.get(e)||{steps:[]},error:r}),this._state.agentLogs.get(e)}var t}onStateUpdate(e){return this._stateUpdateSubscribers.add(e),e({type:"all",data:this._state}),()=>{this._stateUpdateSubscribers.delete(e)}}dispose(){this.stopStateUpdates(),this._stateUpdateSubscribers.clear()}notifySubscribers(e){this._stateUpdateSubscribers.forEach(t=>t(e))}}class me{constructor({asyncMsgSender:e,isActive:t,flagsModel:s,host:n,stateModel:r}){i(this,"_state",{isActive:!1,isPanelFocused:!1,currentAgentId:void 0,currentConversation:void 0,currentAgent:void 0,agentOverviews:[],isLoading:!1,isCurrentAgentDetailsLoading:!1,lastSuccessfulOverviewFetch:0,failedRefreshAttempts:0,maxRemoteAgents:0,isDiffPanelOpen:!1,diffPanelAgentId:void 0,focusedFilePath:null,isCreatingAgent:!1,error:void 0,agentThreadsError:void 0,remoteAgentCreationError:null,newAgentDraft:null,notificationSettings:{},setCurrentAgent:this.setCurrentAgent.bind(this),clearCurrentAgent:this.clearCurrentAgent.bind(this),sendMessage:this.sendMessage.bind(this),interruptAgent:this.interruptAgent.bind(this),createRemoteAgent:this.createRemoteAgent.bind(this),createRemoteAgentFromDraft:this.createRemoteAgentFromDraft.bind(this),deleteAgent:this.deleteAgent.bind(this),setNewAgentDraft:this.setNewAgentDraft.bind(this),setRemoteAgentCreationError:this.setRemoteAgentCreationError.bind(this),hasFetchedOnce:!1,showRemoteAgentDiffPanel:this.showRemoteAgentDiffPanel.bind(this),closeRemoteAgentDiffPanel:this.closeRemoteAgentDiffPanel.bind(this),setIsCreatingAgent:this.setIsCreatingAgent.bind(this)});i(this,"_agentConversations",new Map);i(this,"_initialPrompts",new Map);i(this,"_agentSetupLogsCache",new Map);i(this,"_preloadedDiffExplanations",new Map);i(this,"maxCacheEntries",10);i(this,"maxCacheSizeBytes",10485760);i(this,"_diffOpsModel");i(this,"subscribers",new Set);i(this,"agentSetupLogs");i(this,"_remoteAgentsClient");i(this,"_stateModel");i(this,"_extensionClient");i(this,"_flagsModel");i(this,"_cachedUrls",new Map);i(this,"_externalRefCount",0);i(this,"dispose",()=>{this._stateModel.dispose(),this._agentConversations.clear(),this._agentSetupLogsCache.clear(),this._preloadedDiffExplanations.clear(),this.subscribers.clear()});this._state.isActive=t,this._flagsModel=s,this._diffOpsModel=new H(e),this._remoteAgentsClient=new $(e),this._extensionClient=new pe(n,e,s),this._stateModel=r||new ye(this._flagsModel,this._remoteAgentsClient),this._stateModel.onStateUpdate(this.handleStateUpdate.bind(this)),t&&this._stateModel.startStateUpdates()}handleOverviewsUpdate(e){const t=e.data,s=this._state.agentOverviews,n=t;this._state.currentAgentId&&(this._state.currentAgent=this._state.agentOverviews.find(c=>c.remote_agent_id===this._state.currentAgentId)),this.maybeSendNotifications(n,s);const r=N(n);this._state.agentOverviews=r,this._state.hasFetchedOnce=!0,this._state.agentThreadsError=e.error,this._state.lastSuccessfulOverviewFetch=e.error?this._state.lastSuccessfulOverviewFetch:Date.now(),r.findIndex(c=>c.remote_agent_id===this._state.currentAgentId)===-1&&this.clearCurrentAgent()}handleConversationUpdate(e){if(e.agentId===this._state.currentAgentId){const t={exchanges:e.data,lastFetched:new Date};this._agentConversations.set(e.agentId,t),this._state.currentConversation=t,this._state.error=e.error,this._state.isCurrentAgentDetailsLoading=!1}}handleLogsUpdate(e){e.agentId===this._state.currentAgentId&&(this.agentSetupLogs=e.data,this._agentSetupLogsCache.set(e.agentId,e.data))}handleStateUpdate(e){switch(this._state.maxRemoteAgents=this._stateModel.state.maxRemoteAgents,e.type){case"overviews":this.handleOverviewsUpdate(e);break;case"conversation":this.handleConversationUpdate(e);break;case"logs":this.handleLogsUpdate(e);break;case"all":this.handleOverviewsUpdate({type:"overviews",data:e.data.agentOverviews,error:e.data.overviewError}),e.data.agentConversations.forEach((t,s)=>{this._agentConversations.set(s,{exchanges:t,lastFetched:new Date})}),e.data.agentLogs.forEach((t,s)=>{t&&(this._agentSetupLogsCache.set(s,t),s===this._state.currentAgentId&&(this.agentSetupLogs=t))}),this._state.hasFetchedOnce=!0,this._state.agentThreadsError=e.data.overviewError,this._state.error=e.data.conversationError||e.data.logsError}this.notifySubscribers()}subscribe(e){return this.subscribers.add(e),e(this),()=>{this.subscribers.delete(e)}}notifySubscribers(){this.subscribers.forEach(e=>e(this))}showRemoteAgentDiffPanel(e){const t=this._state.currentAgentId;if(t&&e.changedFiles.length>0&&e.turnIdx===-1&&e.isShowingAggregateChanges){const s=`${t}-${this.generateChangedFilesHash(e.changedFiles)}`,n=this._preloadedDiffExplanations.get(s);if(n)return n.lastAccessed=Date.now(),this._preloadedDiffExplanations.set(s,n),this._remoteAgentsClient.showRemoteAgentDiffPanel({...e,preloadedExplanation:n.explanation}),this._state.isDiffPanelOpen=!0,this._state.diffPanelAgentId=t,void this.notifySubscribers()}this._remoteAgentsClient.showRemoteAgentDiffPanel(e),this._state.isDiffPanelOpen=!0,this._state.diffPanelAgentId=t,this.notifySubscribers()}closeRemoteAgentDiffPanel(){this._remoteAgentsClient.closeRemoteAgentDiffPanel(),this._state.isDiffPanelOpen=!1,this._state.diffPanelAgentId=void 0,this.notifySubscribers()}get flagsModel(){return this._flagsModel}_getChatHistory(e){const t=this._agentConversations.get(e);if(!t)return[];const s=this.isAgentRunning(e);return t.exchanges.map(({exchange:n},r)=>{const c=n.request_id.startsWith("pending-");return{seen_state:v.seen,structured_request_nodes:n.request_nodes??[],status:c||r===t.exchanges.length-1&&s?y.sent:y.success,request_message:n.request_message,response_text:c?"":n.response_text,structured_output_nodes:n.response_nodes??[],request_id:n.request_id??`remote-agent-${r}`}})}getCurrentChatHistory(){const e=this.agentSetupLogs;return this.currentAgentId&&!e&&(this.agentSetupLogs={steps:[]},this._stateModel.startStateUpdates({logs:{agentId:this.currentAgentId}})),this._getChatHistory(this.currentAgentId??"")}getToolStates(){var g,l,d;const e=new Map,t=new Set,s=new Map;(g=this.currentConversation)==null||g.exchanges.forEach(h=>{var u,f;(u=h.exchange.response_nodes)==null||u.forEach(p=>{p.tool_use&&t.add(p.tool_use.tool_use_id)}),(f=h.exchange.request_nodes)==null||f.forEach(p=>{p.type===x.TOOL_RESULT&&p.tool_result_node&&s.set(p.tool_result_node.tool_use_id,p.tool_result_node)})});const n=(l=this.currentConversation)==null?void 0:l.exchanges[this.currentConversation.exchanges.length-1];let r=0,c=null;return(d=n==null?void 0:n.exchange.response_nodes)==null||d.forEach(h=>{var u;h.id>r&&(r=h.id,c=(u=h.tool_use)!=null&&u.tool_use_id?h.tool_use.tool_use_id:null)}),t.forEach(h=>{const u=s.get(h);if(u)e.set(h,{phase:u.is_error?C.error:C.completed,result:{isError:u.is_error,text:u.content},requestId:"",toolUseId:h});else{const f=this.isCurrentAgentRunning;h===c?e.set(h,{phase:f?C.running:C.cancelled,requestId:"",toolUseId:h}):e.set(h,{phase:C.cancelled,requestId:"",toolUseId:h})}}),e}getLastToolUseState(){const e=this.getToolStates(),t=[...e.keys()].pop();return e.get(t??"")??{phase:C.unknown}}getToolUseState(e){const t=e;return this.getToolStates().get(t)??{phase:C.completed,requestId:"",toolUseId:e}}async setCurrentAgent(e){this._state.currentAgentId&&this._stateModel.stopStateUpdates({conversation:{agentId:this._state.currentAgentId},logs:{agentId:this._state.currentAgentId}}),this._state.currentAgentId=e,this._state.isCurrentAgentDetailsLoading=!!e,e&&this._agentSetupLogsCache.has(e)?this.agentSetupLogs=this._agentSetupLogsCache.get(e):this.agentSetupLogs=void 0,this.notifySubscribers(),e&&(this._stateModel.startStateUpdates({conversation:{agentId:e},logs:{agentId:e}}),this.preloadDiffExplanation(e))}clearCurrentAgent(){this._state.currentAgentId&&this._stateModel.stopStateUpdates({conversation:{agentId:this._state.currentAgentId},logs:{agentId:this._state.currentAgentId}}),this._state.currentAgentId=void 0,this.agentSetupLogs=void 0,this.notifySubscribers()}async preloadDiffExplanation(e){const t=this._agentConversations.get(e);if(!t||t.exchanges.length===0)return;const s=fe(t.exchanges);if(s.length===0)return;const n=`${e}-${this.generateChangedFilesHash(s)}`;if(this._preloadedDiffExplanations.get(n)||s.length>12)return;let r=0;if(s.forEach(c=>{var g,l;r+=(((g=c.old_contents)==null?void 0:g.length)||0)+(((l=c.new_contents)==null?void 0:l.length)||0)}),!(r>512e3))try{const c=await this._diffOpsModel.getDiffExplanation(s,void 0,6e4);if(c&&c.length>0){const g=this.generateChangedFilesHash(s),l=`${e}-${g}`;this._preloadedDiffExplanations.set(l,{explanation:c,changedFiles:s,userPrompt:this.getUserMessagePrecedingTurn(t.exchanges,0),timestamp:Date.now(),lastAccessed:Date.now(),changedFilesHash:g,turnIdx:-1}),this.manageCacheSize()}}catch(c){console.error("Failed to preload diff explanation:",c)}}getUserMessagePrecedingTurn(e,t){return e.length===0||t<0||t>=e.length?"":e[t].exchange.request_message||""}generateChangedFilesHash(e){const t=e.map(s=>{var n,r;return{oldPath:s.old_path,newPath:s.new_path,oldSize:((n=s.old_contents)==null?void 0:n.length)||0,newSize:((r=s.new_contents)==null?void 0:r.length)||0,oldHash:this.simpleHash(s.old_contents||""),newHash:this.simpleHash(s.new_contents||"")}});return this.simpleHash(JSON.stringify(t))}simpleHash(e){let t=0;for(let s=0;s<e.length;s++)t=(t<<5)-t+e.charCodeAt(s),t|=0;return t.toString(36)}manageCacheSize(){if(this._preloadedDiffExplanations.size<=this.maxCacheEntries)return;const e=Array.from(this._preloadedDiffExplanations.entries()).map(([s,n])=>({key:s,value:n,accessTime:n.lastAccessed||n.timestamp})).sort((s,n)=>s.accessTime-n.accessTime);let t=0;for(e.forEach(s=>{const n=JSON.stringify(s.value.explanation).length,r=s.value.changedFiles.reduce((c,g)=>{var l,d;return c+(((l=g.old_contents)==null?void 0:l.length)||0)+(((d=g.new_contents)==null?void 0:d.length)||0)},0);t+=n+r});e.length>0&&(e.length>this.maxCacheEntries||t>this.maxCacheSizeBytes);){const s=e.shift();if(s){this._preloadedDiffExplanations.delete(s.key);const n=JSON.stringify(s.value.explanation).length,r=s.value.changedFiles.reduce((c,g)=>{var l,d;return c+(((l=g.old_contents)==null?void 0:l.length)||0)+(((d=g.new_contents)==null?void 0:d.length)||0)},0);t-=n+r}}}async sendMessage(e){const t=this._state.currentAgentId;if(!t)return this._state.error="No active remote agent",this.notifySubscribers(),!1;this._state.isLoading=!0,this._state.error=void 0,this.notifySubscribers();try{const s=this._agentConversations.get(t)||{exchanges:[],lastFetched:new Date},n=(this.getfinalSequenceId(t)||0)+1,r={exchange:{request_message:e,response_text:"",request_id:"pending-"+Date.now(),response_nodes:[],request_nodes:[]},changed_files:[],sequence_id:n};s.exchanges.push(r),this._agentConversations.set(t,s),this._state.currentConversation=s,this.notifySubscribers();const c={request_nodes:[{id:1,type:x.TEXT,text_node:{content:e}}]};return await this._remoteAgentsClient.sendRemoteAgentChatRequest(t,c),await this._stateModel.refreshCurrentAgent(t),await this._stateModel.refreshAgentOverviews(),this.preloadDiffExplanation(t),!0}catch(s){return this._state.error=s instanceof Error?s.message:String(s),this.notifySubscribers(),!1}finally{this._state.isLoading=!1,this.notifySubscribers()}}async interruptAgent(){const e=this._state.currentAgentId;if(!e)return this._state.error="No active remote agent",void this.notifySubscribers();this._state.isLoading=!0,this._state.error=void 0,this.notifySubscribers();try{await this._remoteAgentsClient.interruptRemoteAgent(e),await this._stateModel.refreshCurrentAgent(e),await this._stateModel.refreshAgentOverviews()}catch(t){this._state.error=t instanceof Error?t.message:String(t)}finally{this._state.isLoading=!1,this.notifySubscribers()}}async createRemoteAgent(e,t,s,n){var r;if(!e||!e.trim())return this._state.error="Cannot create a remote agent with an empty prompt",void this.notifySubscribers();this.agentSetupLogs=void 0,this._state.isLoading=!0,this._state.error=void 0,this.notifySubscribers();try{const c=await this._remoteAgentsClient.createRemoteAgent(e,t,s,n);if(c.data.agentId)return this._initialPrompts.set(c.data.agentId,e),await this.setNotificationEnabled(c.data.agentId,((r=this.newAgentDraft)==null?void 0:r.enableNotification)??!1),await this.setCurrentAgent(c.data.agentId),c.data.agentId;throw new Error("Failed to create remote agent: No agent ID returned")}catch(c){throw this._state.error=c instanceof Error?c.message:String(c),this.notifySubscribers(),c}finally{this._state.isLoading=!1,this.notifySubscribers()}}async createRemoteAgentFromDraft(e){var n,r;if(this.setRemoteAgentCreationError(null),this.agentSetupLogs=void 0,!e||!e.trim())return void this.setRemoteAgentCreationError("Cannot create a remote agent with an empty prompt");const t=this._state.newAgentDraft;if(!t)return void this.setRemoteAgentCreationError("No workspace selected. Please select a workspace first.");if(t.isDisabled)return void this.setRemoteAgentCreationError("Cannot create agent with current workspace selection. Please resolve the issues with your workspace selection.");const s={starting_files:t.commitRef};this._state.isLoading=!0,this.notifySubscribers();try{const c=t.isSetupScriptAgent||((n=t.setupScript)==null?void 0:n.isGenerateOption)===!0;let g=c||(r=t.setupScript)==null?void 0:r.content;if(t.setupScript&&!c){const l=(await this.listSetupScripts()).find(d=>d.path===t.setupScript.path);l&&(g=l.content)}try{return await this.createRemoteAgent(e,s,g,c)}catch(l){let d="Failed to create remote agent. Please try again.";return l instanceof Error&&(l.message.includes("too large")||l.message.includes("413")?d="Repository or selected files are too large. Please select a smaller repository or branch.":l.message.includes("timeout")||l.message.includes("504")?d="Request timed out. The repository might be too large or the server is busy.":l.message.includes("rate limit")||l.message.includes("429")?d="Rate limit exceeded. Please try again later.":l.message.includes("unauthorized")||l.message.includes("401")?d="Authentication failed. Please check your GitHub credentials.":l.message.includes("not found")||l.message.includes("404")?d="Repository or branch not found. Please check your selection.":l.message.includes("bad request")||l.message.includes("400")?d="Invalid request. Please check your workspace setup and try again.":l.message.length>0&&(d=`Failed to create remote agent: ${l.message}`)),void this.setRemoteAgentCreationError(d)}}finally{this._state.isLoading=!1,this.notifySubscribers()}}async deleteAgent(e){this._state.isLoading=!0,this._state.error=void 0,this.notifySubscribers();try{if(!await this._remoteAgentsClient.deleteRemoteAgent(e))return this._state.error="Failed to delete remote agent",void this.notifySubscribers();this._agentConversations.delete(e),this._agentSetupLogsCache.delete(e),this._state.agentOverviews=this._state.agentOverviews.filter(t=>t.remote_agent_id!==e),this.removeNotificationEnabled(e),this._state.currentAgentId===e&&this.clearCurrentAgent()}catch(t){this._state.error=t instanceof Error?t.message:String(t)}finally{this._state.isLoading=!1,this.notifySubscribers()}}async sshToRemoteAgent(e){this._state.isLoading=!0,this._state.error=void 0,this.notifySubscribers();try{return await this._remoteAgentsClient.sshToRemoteAgent(e.remote_agent_id)}catch(t){return this._state.error=t instanceof Error?t.message:String(t),this.notifySubscribers(),!1}finally{this._state.isLoading=!1,this.notifySubscribers()}}async maybeSendNotifications(e,t){const s=new Map(t.map(r=>[r.remote_agent_id,r])),n=await this._remoteAgentsClient.getRemoteAgentNotificationEnabled(e.map(r=>r.remote_agent_id));e.forEach(r=>{const c=s.get(r.remote_agent_id),g=n[r.remote_agent_id],l=(c==null?void 0:c.status)===A.agentRunning,d=r.status===A.agentIdle||r.status===A.agentFailed,h=r.remote_agent_id!==this._state.currentAgentId,u=this._state.isPanelFocused;g&&l&&d&&(h||!u)&&this._remoteAgentsClient.notifyRemoteAgentReady(r.remote_agent_id)})}async setNotificationEnabled(e,t){await this._remoteAgentsClient.setRemoteAgentNotificationEnabled(e,t),this._state={...this._state,notificationSettings:{...this._state.notificationSettings,[e]:t}},this.notifySubscribers()}async removeNotificationEnabled(e){await this._remoteAgentsClient.deleteRemoteAgentNotificationEnabled(e);const{[e]:t,...s}=this._state.notificationSettings;this._state={...this._state,notificationSettings:s},this.notifySubscribers()}get hasFetchedOnce(){return this._state.hasFetchedOnce}get focusedFilePath(){return this._state.focusedFilePath}setFocusedFilePath(e){this._state.focusedFilePath=e,this.notifySubscribers()}handleMessageFromExtension(e){const t=e.data.type===o.asyncWrapper?e.data.baseMsg.type:e.data.type;if(!e||!t)return!1;switch(t){case o.diffViewFileFocus:return this.setFocusedFilePath(e.data.baseMsg.data.filePath.replace(/^\/+/,"")),!0;case o.remoteAgentChatResponse:return this._state.currentAgentId&&setTimeout(()=>{this.preloadDiffExplanation(this._state.currentAgentId)},0),!0;case o.getRemoteAgentChatHistoryResponse:case o.remoteAgentInterruptResponse:case o.createRemoteAgentResponse:case o.getRemoteAgentOverviewsResponse:return!0;case o.showRemoteAgentDiffPanel:return this._state.isDiffPanelOpen=!0,!0;case o.closeRemoteAgentDiffPanel:return this._state.isDiffPanelOpen=!1,!0;default:return!1}}get currentAgentId(){return this._state.currentAgentId}get currentConversation(){return this._agentConversations.get(this._state.currentAgentId??"")??void 0}_getAgentExchanges(e){var t;return((t=this._agentConversations.get(e))==null?void 0:t.exchanges)||[]}get currentExchanges(){const e=this._state.currentAgentId;return e?this._getAgentExchanges(e):[]}get currentStatus(){var t;const e=this._state.currentAgentId;return e&&((t=this._state.agentOverviews.find(s=>s.remote_agent_id===e))==null?void 0:t.status)||A.agentIdle}get currentAgent(){const e=this._state.currentAgentId;return e?this._state.agentOverviews.find(t=>t.remote_agent_id===e):void 0}get agentOverviews(){return this._state.agentOverviews}get isLoading(){return this._state.isLoading}get isCurrentAgentDetailsLoading(){return this._state.isCurrentAgentDetailsLoading}get lastSuccessfulOverviewFetch(){return this._state.lastSuccessfulOverviewFetch}get error(){return this._state.error}get agentThreadsError(){return this._state.agentThreadsError}isAgentRunning(e){const t=this._state.agentOverviews.find(c=>c.remote_agent_id===e),s=!(!t||t.status!==A.agentRunning&&t.status!==A.agentStarting),n=this._getAgentExchanges(e),r=n.length>0&&n[n.length-1].exchange.request_id.startsWith("pending-");return s||r}get isCurrentAgentRunning(){return!!this._state.currentAgentId&&this.isAgentRunning(this._state.currentAgentId)}get maxRemoteAgents(){return this._state.maxRemoteAgents}getInitialPrompt(e){return this._initialPrompts.get(e)}clearInitialPrompt(e){this._initialPrompts.delete(e)}get notificationSettings(){return this._state.notificationSettings}getfinalSequenceId(e){var n;const t=this._agentConversations.get(e),s=t==null?void 0:t.exchanges;if(s)return((n=s[s.length-1])==null?void 0:n.sequence_id)??void 0}async listSetupScripts(){try{return(await this._remoteAgentsClient.listSetupScripts()).data.scripts}catch(e){return this._state.error=e instanceof Error?e.message:String(e),this.notifySubscribers(),[]}}async saveSetupScript(e,t,s){try{const n=await this._remoteAgentsClient.saveSetupScript(e,t,s);return n.data.success||(this._state.error=n.data.error||"Failed to save setup script",this.notifySubscribers()),n.data}catch(n){return this._state.error=n instanceof Error?n.message:String(n),this.notifySubscribers(),{success:!1,error:n instanceof Error?n.message:String(n)}}}async deleteSetupScript(e,t){try{const s=await this._remoteAgentsClient.deleteSetupScript(e,t);return s.data.success||(this._state.error=s.data.error||"Failed to delete setup script",this.notifySubscribers()),s.data}catch(s){return this._state.error=s instanceof Error?s.message:String(s),this.notifySubscribers(),{success:!1,error:s instanceof Error?s.message:String(s)}}}async renameSetupScript(e,t,s){try{const n=await this._remoteAgentsClient.renameSetupScript(e,t,s);return n.data.success||(this._state.error=n.data.error||"Failed to rename setup script",this.notifySubscribers()),n.data}catch(n){return this._state.error=n instanceof Error?n.message:String(n),this.notifySubscribers(),{success:!1,error:n instanceof Error?n.message:String(n)}}}get isActive(){return this._state.isActive}setIsActive(e){this._state.isActive=e,e?this._stateModel.startStateUpdates():this._externalRefCount===0?this._stateModel.stopStateUpdates():this._externalRefCount>0&&this._stateModel.startStateUpdates(),this.notifySubscribers()}setExternalRefCount(e){e!==this._externalRefCount&&(this._externalRefCount=e,e!==0||this._state.isActive?e>0&&!this._state.isActive&&this._stateModel.startStateUpdates():this._stateModel.stopStateUpdates())}get isPanelFocused(){return this._state.isPanelFocused}setIsPanelFocused(e){this._state.isPanelFocused=e,this.notifySubscribers()}setRemoteAgentCreationError(e){this._state.remoteAgentCreationError=e,this.notifySubscribers()}get isDiffPanelOpen(){return this._state.isDiffPanelOpen}get diffPanelAgentId(){return this._state.diffPanelAgentId}get remoteAgentCreationError(){return this._state.remoteAgentCreationError}setNewAgentDraft(e){this._state.newAgentDraft=e,this.notifySubscribers()}get newAgentDraft(){return this._state.newAgentDraft}async getAgentLogs(e){this._state.isLoading=!0,this._state.error=void 0,this.notifySubscribers();try{const t=await this._remoteAgentsClient.getRemoteAgentWorkspaceLogs(e);return t.data.workspaceSetupStatus?t.data.workspaceSetupStatus:void 0}catch(t){const s=t instanceof Error?t.message:String(t);return this._state.error=s,void this.notifySubscribers()}finally{this._state.isLoading=!1,this.notifySubscribers()}}setIsCreatingAgent(e){this._state.isCreatingAgent=e,this.notifySubscribers()}get isCreatingAgent(){return this._state.isCreatingAgent}async showRemoteAgentHomePanel(){await this._remoteAgentsClient.showRemoteAgentHomePanel()}async closeRemoteAgentHomePanel(){await this._remoteAgentsClient.closeRemoteAgentHomePanel()}async saveLastRemoteAgentSetup(e,t,s){try{await this._remoteAgentsClient.saveLastRemoteAgentSetup(e,t,s)}catch(n){console.error("Failed to save last remote agent setup:",n)}}async getLastRemoteAgentSetup(){try{return(await this._remoteAgentsClient.getLastRemoteAgentSetup()).data}catch(e){return console.error("Failed to get last remote agent setup:",e),{lastRemoteAgentGitRepoUrl:null,lastRemoteAgentGitBranch:null,lastRemoteAgentSetupScript:null}}}async getConversationUrl(e){var l;const t=this._cachedUrls.get(e),s=this._agentConversations.get(e),n=(s==null?void 0:s.exchanges.length)??0;if(t&&s&&t[0]===n)return t[1];const r=this._getChatHistory(e).map(d=>({...d,request_id:d.request_id||"",request_message:d.request_message,response_text:d.response_text||""}));if(r.length===0)throw new Error("No chat history to share");const c=await this._extensionClient.saveChat(e,r,`Remote Agent ${e}`);if(!c.data)throw new Error("Failed to create URL");const g=(l=c.data)==null?void 0:l.url;return g&&this._cachedUrls.set(e,[n,g]),g}async refreshAgentThreads(){this._state.agentThreadsError=void 0,this._state.isLoading=!0,this.notifySubscribers();try{await this._stateModel.refreshAgentOverviews()}catch(e){console.error("Failed to refresh agent threads:",e)}finally{this._state.isLoading=!1,this.notifySubscribers()}}async openDiffInBuffer(e,t,s){await this._remoteAgentsClient.openDiffInBuffer(e,t,s)}}i(me,"key","remoteAgentsModel");function lt(){const a=j("chatModel");return a||console.warn("ChatModel not found in context"),a}function dt(a){return J("chatModel",a),a}function Se(a){let e,t;return{c(){e=L("svg"),t=L("path"),w(t,"fill-rule","evenodd"),w(t,"clip-rule","evenodd"),w(t,"d","M12 13C12.5523 13 13 12.5523 13 12V3C13 2.44771 12.5523 2 12 2H3C2.44771 2 2 2.44771 2 3V6.5C2 6.77614 2.22386 7 2.5 7C2.77614 7 3 6.77614 3 6.5V3H12V12H8.5C8.22386 12 8 12.2239 8 12.5C8 12.7761 8.22386 13 8.5 13H12ZM9 6.5C9 6.5001 9 6.50021 9 6.50031V6.50035V9.5C9 9.77614 8.77614 10 8.5 10C8.22386 10 8 9.77614 8 9.5V7.70711L2.85355 12.8536C2.65829 13.0488 2.34171 13.0488 2.14645 12.8536C1.95118 12.6583 1.95118 12.3417 2.14645 12.1464L7.29289 7H5.5C5.22386 7 5 6.77614 5 6.5C5 6.22386 5.22386 6 5.5 6H8.5C8.56779 6 8.63244 6.01349 8.69139 6.03794C8.74949 6.06198 8.80398 6.09744 8.85143 6.14433C8.94251 6.23434 8.9992 6.35909 8.99999 6.49708L8.99999 6.49738"),w(t,"fill","currentColor"),w(e,"class",a[0]),w(e,"width","15"),w(e,"height","15"),w(e,"viewBox","0 0 15 15"),w(e,"fill","none"),w(e,"xmlns","http://www.w3.org/2000/svg")},m(s,n){K(s,e,n),Q(e,t)},p(s,[n]){1&n&&w(e,"class",s[0])},i:D,o:D,d(s){s&&ee(e)}}}function ve(a,e,t){let{class:s=""}=e;return a.$$set=n=>{"class"in n&&t(0,s=n.class)},[s]}class ht extends X{constructor(e){super(),Y(this,e,ve,Se,Z,{class:0})}}export{gt as $,ae as A,de as B,ne as C,Je as D,y as E,Qe as F,ue as G,$e as H,He as I,rt as J,Ue as K,Be as L,Xe as M,tt as N,ht as O,et as P,Ve as Q,me as R,v as S,C as T,Ye as U,Ze as V,Ke as W,je as X,fe as Y,H as Z,nt as _,Te as a,ct as a0,ot as a1,Oe as b,De as c,Pe as d,Fe as e,qe as f,ie as g,Le as h,We as i,ke as j,Ne as k,lt as l,Ge as m,it as n,at as o,st as p,xe as q,he as r,dt as s,pe as t,Ee as u,oe as v,ce as w,ze as x,ge as y,le as z};
