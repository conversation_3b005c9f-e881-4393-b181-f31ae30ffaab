import{S as I,i as L,s as H,J as b,V as j,c as T,e as k,W as A,u as B,t as G,h as E,Z as J,E as g,a2 as K,a as $,j as M,K as w,L as u,a3 as _,P as p,a4 as P,g as Q,A as S,G as h,Q as V}from"./SpinnerAugment-BUJasFTo.js";import"./BaseButton-ci_067e0.js";import{B as W}from"./BaseTextInput-BYcZ2XaJ.js";function Y(n){let e,a,l,i,r=[{spellcheck:"false"},{class:a=`c-text-area__input c-base-text-input__input ${n[8]}`},n[7]],o={};for(let s=0;s<r.length;s+=1)o=$(o,r[s]);return{c(){e=b("textarea"),w(e,o),u(e,"c-textarea--resize-none",n[5]==="none"),u(e,"c-textarea--resize-both",n[5]==="both"),u(e,"c-textarea--resize-horizontal",n[5]==="horizontal"),u(e,"c-textarea--resize-vertical",n[5]==="vertical"),u(e,"svelte-1fo39y1",!0)},m(s,f){k(s,e,f),e.autofocus&&e.focus(),n[18](e),_(e,n[1]),l||(i=[p(e,"input",n[19]),P(n[9].call(null,e)),p(e,"click",n[10]),p(e,"focus",n[11]),p(e,"keydown",n[12]),p(e,"input",n[13]),p(e,"keyup",n[14]),p(e,"blur",n[15]),p(e,"select",n[16]),p(e,"mouseup",n[17])],l=!0)},p(s,f){w(e,o=Q(r,[{spellcheck:"false"},256&f&&a!==(a=`c-text-area__input c-base-text-input__input ${s[8]}`)&&{class:a},128&f&&s[7]])),2&f&&_(e,s[1]),u(e,"c-textarea--resize-none",s[5]==="none"),u(e,"c-textarea--resize-both",s[5]==="both"),u(e,"c-textarea--resize-horizontal",s[5]==="horizontal"),u(e,"c-textarea--resize-vertical",s[5]==="vertical"),u(e,"svelte-1fo39y1",!0)},d(s){s&&E(e),n[18](null),l=!1,S(i)}}}function Z(n){let e,a,l;return a=new W({props:{type:n[6],variant:n[2],size:n[3],color:n[4],$$slots:{default:[Y]},$$scope:{ctx:n}}}),{c(){e=b("div"),j(a.$$.fragment),T(e,"class","c-text-area svelte-1fo39y1")},m(i,r){k(i,e,r),A(a,e,null),l=!0},p(i,[r]){const o={};64&r&&(o.type=i[6]),4&r&&(o.variant=i[2]),8&r&&(o.size=i[3]),16&r&&(o.color=i[4]),4194723&r&&(o.$$scope={dirty:r,ctx:i}),a.$set(o)},i(i){l||(B(a.$$.fragment,i),l=!0)},o(i){G(a.$$.fragment,i),l=!1},d(i){i&&E(e),J(a)}}}function q(n,e,a){let l,i;const r=["variant","size","color","resize","textInput","type","value"];let o=g(e,r),{variant:s="surface"}=e,{size:f=2}=e,{color:d}=e,{resize:y="none"}=e,{textInput:c}=e,{type:m="default"}=e,{value:z=""}=e;function v(){if(!c)return;a(0,c.style.height="auto",c);const t=.8*window.innerHeight,x=Math.min(c.scrollHeight,t);a(0,c.style.height=`${x}px`,c),a(0,c.style.overflowY=c.scrollHeight>t?"auto":"hidden",c)}return K(()=>{if(c){v();const t=()=>v();return window.addEventListener("resize",t),()=>{window.removeEventListener("resize",t)}}}),n.$$set=t=>{e=$($({},e),M(t)),a(21,o=g(e,r)),"variant"in t&&a(2,s=t.variant),"size"in t&&a(3,f=t.size),"color"in t&&a(4,d=t.color),"resize"in t&&a(5,y=t.resize),"textInput"in t&&a(0,c=t.textInput),"type"in t&&a(6,m=t.type),"value"in t&&a(1,z=t.value)},n.$$.update=()=>{a(8,{class:l,...i}=o,l,(a(7,i),a(21,o)))},[c,z,s,f,d,y,m,i,l,function(t){v();const x=()=>v();return t.addEventListener("input",x),setTimeout(v,0),{destroy(){t.removeEventListener("input",x)}}},function(t){h.call(this,n,t)},function(t){h.call(this,n,t)},function(t){h.call(this,n,t)},function(t){h.call(this,n,t)},function(t){h.call(this,n,t)},function(t){h.call(this,n,t)},function(t){h.call(this,n,t)},function(t){h.call(this,n,t)},function(t){V[t?"unshift":"push"](()=>{c=t,a(0,c)})},function(){z=this.value,a(1,z)}]}class N extends I{constructor(e){super(),L(this,e,q,Z,H,{variant:2,size:3,color:4,resize:5,textInput:0,type:6,value:1})}}export{N as T};
