var Un=Object.defineProperty;var Fn=(e,t,n)=>t in e?Un(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var j=(e,t,n)=>Fn(e,typeof t!="symbol"?t+"":t,n);import{a5 as te,am as Yn,an as fn,ah as dn,aj as Kn,S as _e,i as Ce,s as De,I as Ae,M as Le,N as He,O as je,u as Me,t as ke,J as vn,c as nt,a7 as We,e as hn,P as bt,a4 as mn,a9 as Rt,h as gn,A as yn,ac as bn,G as me,L as Ie,at as Ne,ad as qe,ai as zn,_ as Xn}from"./SpinnerAugment-BUJasFTo.js";import{g as Jn}from"./globals-D0QH3NT1.js";var F="top",tt="bottom",et="right",Y="left",ge="auto",Kt=[F,tt,et,Y],Ct="start",Ut="end",Gn="clippingParents",On="viewport",Pt="popper",Qn="reference",Ue=Kt.reduce(function(e,t){return e.concat([t+"-"+Ct,t+"-"+Ut])},[]),wn=[].concat(Kt,[ge]).reduce(function(e,t){return e.concat([t,t+"-"+Ct,t+"-"+Ut])},[]),Zn=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"];function ut(e){return e?(e.nodeName||"").toLowerCase():null}function X(e){if(e==null)return window;if(e.toString()!=="[object Window]"){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function Et(e){return e instanceof X(e).Element||e instanceof Element}function Z(e){return e instanceof X(e).HTMLElement||e instanceof HTMLElement}function Se(e){return typeof ShadowRoot<"u"&&(e instanceof X(e).ShadowRoot||e instanceof ShadowRoot)}const En={name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach(function(n){var o=t.styles[n]||{},a=t.attributes[n]||{},i=t.elements[n];Z(i)&&ut(i)&&(Object.assign(i.style,o),Object.keys(a).forEach(function(u){var s=a[u];s===!1?i.removeAttribute(u):i.setAttribute(u,s===!0?"":s)}))})},effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach(function(o){var a=t.elements[o],i=t.attributes[o]||{},u=Object.keys(t.styles.hasOwnProperty(o)?t.styles[o]:n[o]).reduce(function(s,c){return s[c]="",s},{});Z(a)&&ut(a)&&(Object.assign(a.style,u),Object.keys(i).forEach(function(s){a.removeAttribute(s)}))})}},requires:["computeStyles"]};function ct(e){return e.split("-")[0]}var wt=Math.max,re=Math.min,Dt=Math.round;function ye(){var e=navigator.userAgentData;return e!=null&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(t){return t.brand+"/"+t.version}).join(" "):navigator.userAgent}function xn(){return!/^((?!chrome|android).)*safari/i.test(ye())}function At(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!1);var o=e.getBoundingClientRect(),a=1,i=1;t&&Z(e)&&(a=e.offsetWidth>0&&Dt(o.width)/e.offsetWidth||1,i=e.offsetHeight>0&&Dt(o.height)/e.offsetHeight||1);var u=(Et(e)?X(e):window).visualViewport,s=!xn()&&n,c=(o.left+(s&&u?u.offsetLeft:0))/a,l=(o.top+(s&&u?u.offsetTop:0))/i,d=o.width/a,v=o.height/i;return{width:d,height:v,top:l,right:c+d,bottom:l+v,left:c,x:c,y:l}}function $e(e){var t=At(e),n=e.offsetWidth,o=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-o)<=1&&(o=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:o}}function Tn(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&Se(n)){var o=t;do{if(o&&e.isSameNode(o))return!0;o=o.parentNode||o.host}while(o)}return!1}function ft(e){return X(e).getComputedStyle(e)}function to(e){return["table","td","th"].indexOf(ut(e))>=0}function vt(e){return((Et(e)?e.ownerDocument:e.document)||window.document).documentElement}function ce(e){return ut(e)==="html"?e:e.assignedSlot||e.parentNode||(Se(e)?e.host:null)||vt(e)}function Fe(e){return Z(e)&&ft(e).position!=="fixed"?e.offsetParent:null}function zt(e){for(var t=X(e),n=Fe(e);n&&to(n)&&ft(n).position==="static";)n=Fe(n);return n&&(ut(n)==="html"||ut(n)==="body"&&ft(n).position==="static")?t:n||function(o){var a=/firefox/i.test(ye());if(/Trident/i.test(ye())&&Z(o)&&ft(o).position==="fixed")return null;var i=ce(o);for(Se(i)&&(i=i.host);Z(i)&&["html","body"].indexOf(ut(i))<0;){var u=ft(i);if(u.transform!=="none"||u.perspective!=="none"||u.contain==="paint"||["transform","perspective"].indexOf(u.willChange)!==-1||a&&u.willChange==="filter"||a&&u.filter&&u.filter!=="none")return i;i=i.parentNode}return null}(e)||t}function Pe(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function Vt(e,t,n){return wt(e,re(t,n))}function _n(e){return Object.assign({},{top:0,right:0,bottom:0,left:0},e)}function Cn(e,t){return t.reduce(function(n,o){return n[o]=e,n},{})}const eo={name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,n=e.state,o=e.name,a=e.options,i=n.elements.arrow,u=n.modifiersData.popperOffsets,s=ct(n.placement),c=Pe(s),l=[Y,et].indexOf(s)>=0?"height":"width";if(i&&u){var d=function(H,A){return _n(typeof(H=typeof H=="function"?H(Object.assign({},A.rects,{placement:A.placement})):H)!="number"?H:Cn(H,Kt))}(a.padding,n),v=$e(i),g=c==="y"?F:Y,b=c==="y"?tt:et,O=n.rects.reference[l]+n.rects.reference[c]-u[c]-n.rects.popper[l],m=u[c]-n.rects.reference[c],f=zt(i),D=f?c==="y"?f.clientHeight||0:f.clientWidth||0:0,x=O/2-m/2,r=d[g],T=D-v[l]-d[b],h=D/2-v[l]/2+x,_=Vt(r,h,T),L=c;n.modifiersData[o]=((t={})[L]=_,t.centerOffset=_-h,t)}},effect:function(e){var t=e.state,n=e.options.element,o=n===void 0?"[data-popper-arrow]":n;o!=null&&(typeof o!="string"||(o=t.elements.popper.querySelector(o)))&&Tn(t.elements.popper,o)&&(t.elements.arrow=o)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Lt(e){return e.split("-")[1]}var no={top:"auto",right:"auto",bottom:"auto",left:"auto"};function Ye(e){var t,n=e.popper,o=e.popperRect,a=e.placement,i=e.variation,u=e.offsets,s=e.position,c=e.gpuAcceleration,l=e.adaptive,d=e.roundOffsets,v=e.isFixed,g=u.x,b=g===void 0?0:g,O=u.y,m=O===void 0?0:O,f=typeof d=="function"?d({x:b,y:m}):{x:b,y:m};b=f.x,m=f.y;var D=u.hasOwnProperty("x"),x=u.hasOwnProperty("y"),r=Y,T=F,h=window;if(l){var _=zt(n),L="clientHeight",H="clientWidth";_===X(n)&&ft(_=vt(n)).position!=="static"&&s==="absolute"&&(L="scrollHeight",H="scrollWidth"),(a===F||(a===Y||a===et)&&i===Ut)&&(T=tt,m-=(v&&_===h&&h.visualViewport?h.visualViewport.height:_[L])-o.height,m*=c?1:-1),(a===Y||(a===F||a===tt)&&i===Ut)&&(r=et,b-=(v&&_===h&&h.visualViewport?h.visualViewport.width:_[H])-o.width,b*=c?1:-1)}var A,k=Object.assign({position:s},l&&no),M=d===!0?function(P,B){var q=P.x,K=P.y,S=B.devicePixelRatio||1;return{x:Dt(q*S)/S||0,y:Dt(K*S)/S||0}}({x:b,y:m},X(n)):{x:b,y:m};return b=M.x,m=M.y,c?Object.assign({},k,((A={})[T]=x?"0":"",A[r]=D?"0":"",A.transform=(h.devicePixelRatio||1)<=1?"translate("+b+"px, "+m+"px)":"translate3d("+b+"px, "+m+"px, 0)",A)):Object.assign({},k,((t={})[T]=x?m+"px":"",t[r]=D?b+"px":"",t.transform="",t))}var ee={passive:!0};const oo={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,n=e.instance,o=e.options,a=o.scroll,i=a===void 0||a,u=o.resize,s=u===void 0||u,c=X(t.elements.popper),l=[].concat(t.scrollParents.reference,t.scrollParents.popper);return i&&l.forEach(function(d){d.addEventListener("scroll",n.update,ee)}),s&&c.addEventListener("resize",n.update,ee),function(){i&&l.forEach(function(d){d.removeEventListener("scroll",n.update,ee)}),s&&c.removeEventListener("resize",n.update,ee)}},data:{}};var ro={left:"right",right:"left",bottom:"top",top:"bottom"};function ne(e){return e.replace(/left|right|bottom|top/g,function(t){return ro[t]})}var io={start:"end",end:"start"};function Ke(e){return e.replace(/start|end/g,function(t){return io[t]})}function Re(e){var t=X(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function be(e){return At(vt(e)).left+Re(e).scrollLeft}function Ve(e){var t=ft(e),n=t.overflow,o=t.overflowX,a=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+a+o)}function Dn(e){return["html","body","#document"].indexOf(ut(e))>=0?e.ownerDocument.body:Z(e)&&Ve(e)?e:Dn(ce(e))}function Wt(e,t){var n;t===void 0&&(t=[]);var o=Dn(e),a=o===((n=e.ownerDocument)==null?void 0:n.body),i=X(o),u=a?[i].concat(i.visualViewport||[],Ve(o)?o:[]):o,s=t.concat(u);return a?s:s.concat(Wt(ce(u)))}function Oe(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function ze(e,t,n){return t===On?Oe(function(o,a){var i=X(o),u=vt(o),s=i.visualViewport,c=u.clientWidth,l=u.clientHeight,d=0,v=0;if(s){c=s.width,l=s.height;var g=xn();(g||!g&&a==="fixed")&&(d=s.offsetLeft,v=s.offsetTop)}return{width:c,height:l,x:d+be(o),y:v}}(e,n)):Et(t)?function(o,a){var i=At(o,!1,a==="fixed");return i.top=i.top+o.clientTop,i.left=i.left+o.clientLeft,i.bottom=i.top+o.clientHeight,i.right=i.left+o.clientWidth,i.width=o.clientWidth,i.height=o.clientHeight,i.x=i.left,i.y=i.top,i}(t,n):Oe(function(o){var a,i=vt(o),u=Re(o),s=(a=o.ownerDocument)==null?void 0:a.body,c=wt(i.scrollWidth,i.clientWidth,s?s.scrollWidth:0,s?s.clientWidth:0),l=wt(i.scrollHeight,i.clientHeight,s?s.scrollHeight:0,s?s.clientHeight:0),d=-u.scrollLeft+be(o),v=-u.scrollTop;return ft(s||i).direction==="rtl"&&(d+=wt(i.clientWidth,s?s.clientWidth:0)-c),{width:c,height:l,x:d,y:v}}(vt(e)))}function so(e,t,n,o){var a=t==="clippingParents"?function(c){var l=Wt(ce(c)),d=["absolute","fixed"].indexOf(ft(c).position)>=0&&Z(c)?zt(c):c;return Et(d)?l.filter(function(v){return Et(v)&&Tn(v,d)&&ut(v)!=="body"}):[]}(e):[].concat(t),i=[].concat(a,[n]),u=i[0],s=i.reduce(function(c,l){var d=ze(e,l,o);return c.top=wt(d.top,c.top),c.right=re(d.right,c.right),c.bottom=re(d.bottom,c.bottom),c.left=wt(d.left,c.left),c},ze(e,u,o));return s.width=s.right-s.left,s.height=s.bottom-s.top,s.x=s.left,s.y=s.top,s}function An(e){var t,n=e.reference,o=e.element,a=e.placement,i=a?ct(a):null,u=a?Lt(a):null,s=n.x+n.width/2-o.width/2,c=n.y+n.height/2-o.height/2;switch(i){case F:t={x:s,y:n.y-o.height};break;case tt:t={x:s,y:n.y+n.height};break;case et:t={x:n.x+n.width,y:c};break;case Y:t={x:n.x-o.width,y:c};break;default:t={x:n.x,y:n.y}}var l=i?Pe(i):null;if(l!=null){var d=l==="y"?"height":"width";switch(u){case Ct:t[l]=t[l]-(n[d]/2-o[d]/2);break;case Ut:t[l]=t[l]+(n[d]/2-o[d]/2)}}return t}function Ft(e,t){t===void 0&&(t={});var n=t,o=n.placement,a=o===void 0?e.placement:o,i=n.strategy,u=i===void 0?e.strategy:i,s=n.boundary,c=s===void 0?Gn:s,l=n.rootBoundary,d=l===void 0?On:l,v=n.elementContext,g=v===void 0?Pt:v,b=n.altBoundary,O=b!==void 0&&b,m=n.padding,f=m===void 0?0:m,D=_n(typeof f!="number"?f:Cn(f,Kt)),x=g===Pt?Qn:Pt,r=e.rects.popper,T=e.elements[O?x:g],h=so(Et(T)?T:T.contextElement||vt(e.elements.popper),c,d,u),_=At(e.elements.reference),L=An({reference:_,element:r,strategy:"absolute",placement:a}),H=Oe(Object.assign({},r,L)),A=g===Pt?H:_,k={top:h.top-A.top+D.top,bottom:A.bottom-h.bottom+D.bottom,left:h.left-A.left+D.left,right:A.right-h.right+D.right},M=e.modifiersData.offset;if(g===Pt&&M){var P=M[a];Object.keys(k).forEach(function(B){var q=[et,tt].indexOf(B)>=0?1:-1,K=[F,tt].indexOf(B)>=0?"y":"x";k[B]+=P[K]*q})}return k}function ao(e,t){t===void 0&&(t={});var n=t,o=n.placement,a=n.boundary,i=n.rootBoundary,u=n.padding,s=n.flipVariations,c=n.allowedAutoPlacements,l=c===void 0?wn:c,d=Lt(o),v=d?s?Ue:Ue.filter(function(O){return Lt(O)===d}):Kt,g=v.filter(function(O){return l.indexOf(O)>=0});g.length===0&&(g=v);var b=g.reduce(function(O,m){return O[m]=Ft(e,{placement:m,boundary:a,rootBoundary:i,padding:u})[ct(m)],O},{});return Object.keys(b).sort(function(O,m){return b[O]-b[m]})}const co={name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,o=e.name;if(!t.modifiersData[o]._skip){for(var a=n.mainAxis,i=a===void 0||a,u=n.altAxis,s=u===void 0||u,c=n.fallbackPlacements,l=n.padding,d=n.boundary,v=n.rootBoundary,g=n.altBoundary,b=n.flipVariations,O=b===void 0||b,m=n.allowedAutoPlacements,f=t.options.placement,D=ct(f),x=c||(D===f||!O?[ne(f)]:function(I){if(ct(I)===ge)return[];var U=ne(I);return[Ke(I),U,Ke(U)]}(f)),r=[f].concat(x).reduce(function(I,U){return I.concat(ct(U)===ge?ao(t,{placement:U,boundary:d,rootBoundary:v,padding:l,flipVariations:O,allowedAutoPlacements:m}):U)},[]),T=t.rects.reference,h=t.rects.popper,_=new Map,L=!0,H=r[0],A=0;A<r.length;A++){var k=r[A],M=ct(k),P=Lt(k)===Ct,B=[F,tt].indexOf(M)>=0,q=B?"width":"height",K=Ft(t,{placement:k,boundary:d,rootBoundary:v,altBoundary:g,padding:l}),S=B?P?et:Y:P?tt:F;T[q]>h[q]&&(S=ne(S));var $=ne(S),rt=[];if(i&&rt.push(K[M]<=0),s&&rt.push(K[S]<=0,K[$]<=0),rt.every(function(I){return I})){H=k,L=!1;break}_.set(k,rt)}if(L)for(var it=function(I){var U=r.find(function(ht){var mt=_.get(ht);if(mt)return mt.slice(0,I).every(function(dt){return dt})});if(U)return H=U,"break"},st=O?3:1;st>0&&it(st)!=="break";st--);t.placement!==H&&(t.modifiersData[o]._skip=!0,t.placement=H,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function Xe(e,t,n){return n===void 0&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function Je(e){return[F,et,tt,Y].some(function(t){return e[t]>=0})}const uo={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,n=e.options,o=e.name,a=n.offset,i=a===void 0?[0,0]:a,u=wn.reduce(function(d,v){return d[v]=function(g,b,O){var m=ct(g),f=[Y,F].indexOf(m)>=0?-1:1,D=typeof O=="function"?O(Object.assign({},b,{placement:g})):O,x=D[0],r=D[1];return x=x||0,r=(r||0)*f,[Y,et].indexOf(m)>=0?{x:r,y:x}:{x,y:r}}(v,t.rects,i),d},{}),s=u[t.placement],c=s.x,l=s.y;t.modifiersData.popperOffsets!=null&&(t.modifiersData.popperOffsets.x+=c,t.modifiersData.popperOffsets.y+=l),t.modifiersData[o]=u}},po={name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,o=e.name,a=n.mainAxis,i=a===void 0||a,u=n.altAxis,s=u!==void 0&&u,c=n.boundary,l=n.rootBoundary,d=n.altBoundary,v=n.padding,g=n.tether,b=g===void 0||g,O=n.tetherOffset,m=O===void 0?0:O,f=Ft(t,{boundary:c,rootBoundary:l,padding:v,altBoundary:d}),D=ct(t.placement),x=Lt(t.placement),r=!x,T=Pe(D),h=T==="x"?"y":"x",_=t.modifiersData.popperOffsets,L=t.rects.reference,H=t.rects.popper,A=typeof m=="function"?m(Object.assign({},t.rects,{placement:t.placement})):m,k=typeof A=="number"?{mainAxis:A,altAxis:A}:Object.assign({mainAxis:0,altAxis:0},A),M=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,P={x:0,y:0};if(_){if(i){var B,q=T==="y"?F:Y,K=T==="y"?tt:et,S=T==="y"?"height":"width",$=_[T],rt=$+f[q],it=$-f[K],st=b?-H[S]/2:0,I=x===Ct?L[S]:H[S],U=x===Ct?-H[S]:-L[S],ht=t.elements.arrow,mt=b&&ht?$e(ht):{width:0,height:0},dt=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},jt=dt[q],pt=dt[K],gt=Vt(0,L[S],mt[S]),Xt=r?L[S]/2-st-gt-jt-k.mainAxis:I-gt-jt-k.mainAxis,Jt=r?-L[S]/2+st+gt+pt+k.mainAxis:U+gt+pt+k.mainAxis,xt=t.elements.arrow&&zt(t.elements.arrow),Gt=xt?T==="y"?xt.clientTop||0:xt.clientLeft||0:0,Mt=(B=M==null?void 0:M[T])!=null?B:0,Qt=$+Jt-Mt,kt=Vt(b?re(rt,$+Xt-Mt-Gt):rt,$,b?wt(it,Qt):it);_[T]=kt,P[T]=kt-$}if(s){var St,$t=T==="x"?F:Y,Zt=T==="x"?tt:et,J=_[h],p=h==="y"?"height":"width",y=J+f[$t],w=J-f[Zt],E=[F,Y].indexOf(D)!==-1,C=(St=M==null?void 0:M[h])!=null?St:0,R=E?y:J-L[p]-H[p]-C+k.altAxis,V=E?J+L[p]+H[p]-C-k.altAxis:w,W=b&&E?function(N,G,Q){var z=Vt(N,G,Q);return z>Q?Q:z}(R,J,V):Vt(b?R:y,J,b?V:w);_[h]=W,P[h]=W-J}t.modifiersData[o]=P}},requiresIfExists:["offset"]};function lo(e,t,n){n===void 0&&(n=!1);var o,a=Z(t),i=Z(t)&&function(d){var v=d.getBoundingClientRect(),g=Dt(v.width)/d.offsetWidth||1,b=Dt(v.height)/d.offsetHeight||1;return g!==1||b!==1}(t),u=vt(t),s=At(e,i,n),c={scrollLeft:0,scrollTop:0},l={x:0,y:0};return(a||!a&&!n)&&((ut(t)!=="body"||Ve(u))&&(c=(o=t)!==X(o)&&Z(o)?function(d){return{scrollLeft:d.scrollLeft,scrollTop:d.scrollTop}}(o):Re(o)),Z(t)?((l=At(t,!0)).x+=t.clientLeft,l.y+=t.clientTop):u&&(l.x=be(u))),{x:s.left+c.scrollLeft-l.x,y:s.top+c.scrollTop-l.y,width:s.width,height:s.height}}function fo(e){var t=new Map,n=new Set,o=[];function a(i){n.add(i.name),[].concat(i.requires||[],i.requiresIfExists||[]).forEach(function(u){if(!n.has(u)){var s=t.get(u);s&&a(s)}}),o.push(i)}return e.forEach(function(i){t.set(i.name,i)}),e.forEach(function(i){n.has(i.name)||a(i)}),o}var Ge={placement:"bottom",modifiers:[],strategy:"absolute"};function Qe(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some(function(o){return!(o&&typeof o.getBoundingClientRect=="function")})}function vo(e){e===void 0&&(e={});var t=e,n=t.defaultModifiers,o=n===void 0?[]:n,a=t.defaultOptions,i=a===void 0?Ge:a;return function(u,s,c){c===void 0&&(c=i);var l,d,v={placement:"bottom",orderedModifiers:[],options:Object.assign({},Ge,i),modifiersData:{},elements:{reference:u,popper:s},attributes:{},styles:{}},g=[],b=!1,O={state:v,setOptions:function(f){var D=typeof f=="function"?f(v.options):f;m(),v.options=Object.assign({},i,v.options,D),v.scrollParents={reference:Et(u)?Wt(u):u.contextElement?Wt(u.contextElement):[],popper:Wt(s)};var x,r,T=function(h){var _=fo(h);return Zn.reduce(function(L,H){return L.concat(_.filter(function(A){return A.phase===H}))},[])}((x=[].concat(o,v.options.modifiers),r=x.reduce(function(h,_){var L=h[_.name];return h[_.name]=L?Object.assign({},L,_,{options:Object.assign({},L.options,_.options),data:Object.assign({},L.data,_.data)}):_,h},{}),Object.keys(r).map(function(h){return r[h]})));return v.orderedModifiers=T.filter(function(h){return h.enabled}),v.orderedModifiers.forEach(function(h){var _=h.name,L=h.options,H=L===void 0?{}:L,A=h.effect;if(typeof A=="function"){var k=A({state:v,name:_,instance:O,options:H}),M=function(){};g.push(k||M)}}),O.update()},forceUpdate:function(){if(!b){var f=v.elements,D=f.reference,x=f.popper;if(Qe(D,x)){v.rects={reference:lo(D,zt(x),v.options.strategy==="fixed"),popper:$e(x)},v.reset=!1,v.placement=v.options.placement,v.orderedModifiers.forEach(function(A){return v.modifiersData[A.name]=Object.assign({},A.data)});for(var r=0;r<v.orderedModifiers.length;r++)if(v.reset!==!0){var T=v.orderedModifiers[r],h=T.fn,_=T.options,L=_===void 0?{}:_,H=T.name;typeof h=="function"&&(v=h({state:v,options:L,name:H,instance:O})||v)}else v.reset=!1,r=-1}}},update:(l=function(){return new Promise(function(f){O.forceUpdate(),f(v)})},function(){return d||(d=new Promise(function(f){Promise.resolve().then(function(){d=void 0,f(l())})})),d}),destroy:function(){m(),b=!0}};if(!Qe(u,s))return O;function m(){g.forEach(function(f){return f()}),g=[]}return O.setOptions(c).then(function(f){!b&&c.onFirstUpdate&&c.onFirstUpdate(f)}),O}}var ho=vo({defaultModifiers:[oo,{name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,n=e.name;t.modifiersData[n]=An({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})},data:{}},{name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,n=e.options,o=n.gpuAcceleration,a=o===void 0||o,i=n.adaptive,u=i===void 0||i,s=n.roundOffsets,c=s===void 0||s,l={placement:ct(t.placement),variation:Lt(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:a,isFixed:t.options.strategy==="fixed"};t.modifiersData.popperOffsets!=null&&(t.styles.popper=Object.assign({},t.styles.popper,Ye(Object.assign({},l,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:u,roundOffsets:c})))),t.modifiersData.arrow!=null&&(t.styles.arrow=Object.assign({},t.styles.arrow,Ye(Object.assign({},l,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:c})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}},En,uo,co,po,eo,{name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,n=e.name,o=t.rects.reference,a=t.rects.popper,i=t.modifiersData.preventOverflow,u=Ft(t,{elementContext:"reference"}),s=Ft(t,{altBoundary:!0}),c=Xe(u,o),l=Xe(s,a,i),d=Je(c),v=Je(l);t.modifiersData[n]={referenceClippingOffsets:c,popperEscapeOffsets:l,isReferenceHidden:d,hasPopperEscaped:v},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":d,"data-popper-escaped":v})}}]}),Ln="tippy-content",mo="tippy-backdrop",Hn="tippy-arrow",jn="tippy-svg-arrow",yt={passive:!0,capture:!0},Mn=function(){return document.body};function pe(e,t,n){if(Array.isArray(e)){var o=e[t];return o??(Array.isArray(n)?n[t]:n)}return e}function Be(e,t){var n={}.toString.call(e);return n.indexOf("[object")===0&&n.indexOf(t+"]")>-1}function kn(e,t){return typeof e=="function"?e.apply(void 0,t):e}function Ze(e,t){return t===0?e:function(o){clearTimeout(n),n=setTimeout(function(){e(o)},t)};var n}function _t(e){return[].concat(e)}function tn(e,t){e.indexOf(t)===-1&&e.push(t)}function ie(e){return[].slice.call(e)}function en(e){return Object.keys(e).reduce(function(t,n){return e[n]!==void 0&&(t[n]=e[n]),t},{})}function It(){return document.createElement("div")}function ue(e){return["Element","Fragment"].some(function(t){return Be(e,t)})}function go(e){return ue(e)?[e]:function(t){return Be(t,"NodeList")}(e)?ie(e):Array.isArray(e)?e:ie(document.querySelectorAll(e))}function le(e,t){e.forEach(function(n){n&&(n.style.transitionDuration=t+"ms")})}function nn(e,t){e.forEach(function(n){n&&n.setAttribute("data-state",t)})}function fe(e,t,n){var o=t+"EventListener";["transitionend","webkitTransitionEnd"].forEach(function(a){e[o](a,n)})}function on(e,t){for(var n=t;n;){var o;if(e.contains(n))return!0;n=n.getRootNode==null||(o=n.getRootNode())==null?void 0:o.host}return!1}var at={isTouch:!1},rn=0;function yo(){at.isTouch||(at.isTouch=!0,window.performance&&document.addEventListener("mousemove",Sn))}function Sn(){var e=performance.now();e-rn<20&&(at.isTouch=!1,document.removeEventListener("mousemove",Sn)),rn=e}function bo(){var e,t=document.activeElement;if((e=t)&&e._tippy&&e._tippy.reference===e){var n=t._tippy;t.blur&&!n.state.isVisible&&t.blur()}}var Oo=typeof window<"u"&&typeof document<"u"&&!!window.msCrypto,ot=Object.assign({appendTo:Mn,aria:{content:"auto",expanded:"auto"},delay:0,duration:[300,250],getReferenceClientRect:null,hideOnClick:!0,ignoreAttributes:!1,interactive:!1,interactiveBorder:2,interactiveDebounce:0,moveTransition:"",offset:[0,10],onAfterUpdate:function(){},onBeforeUpdate:function(){},onCreate:function(){},onDestroy:function(){},onHidden:function(){},onHide:function(){},onMount:function(){},onShow:function(){},onShown:function(){},onTrigger:function(){},onUntrigger:function(){},onClickOutside:function(){},placement:"top",plugins:[],popperOptions:{},render:null,showOnCreate:!1,touch:!0,trigger:"mouseenter focus",triggerTarget:null},{animateFill:!1,followCursor:!1,inlinePositioning:!1,sticky:!1},{allowHTML:!1,animation:"fade",arrow:!0,content:"",inertia:!1,maxWidth:350,role:"tooltip",theme:"",zIndex:9999}),wo=Object.keys(ot);function $n(e){var t=(e.plugins||[]).reduce(function(n,o){var a,i=o.name,u=o.defaultValue;return i&&(n[i]=e[i]!==void 0?e[i]:(a=ot[i])!=null?a:u),n},{});return Object.assign({},e,t)}function sn(e,t){var n=Object.assign({},t,{content:kn(t.content,[e])},t.ignoreAttributes?{}:function(o,a){return(a?Object.keys($n(Object.assign({},ot,{plugins:a}))):wo).reduce(function(i,u){var s=(o.getAttribute("data-tippy-"+u)||"").trim();if(!s)return i;if(u==="content")i[u]=s;else try{i[u]=JSON.parse(s)}catch{i[u]=s}return i},{})}(e,t.plugins));return n.aria=Object.assign({},ot.aria,n.aria),n.aria={expanded:n.aria.expanded==="auto"?t.interactive:n.aria.expanded,content:n.aria.content==="auto"?t.interactive?null:"describedby":n.aria.content},n}var Eo=function(){return"innerHTML"};function we(e,t){e[Eo()]=t}function an(e){var t=It();return e===!0?t.className=Hn:(t.className=jn,ue(e)?t.appendChild(e):we(t,e)),t}function cn(e,t){ue(t.content)?(we(e,""),e.appendChild(t.content)):typeof t.content!="function"&&(t.allowHTML?we(e,t.content):e.textContent=t.content)}function Ee(e){var t=e.firstElementChild,n=ie(t.children);return{box:t,content:n.find(function(o){return o.classList.contains(Ln)}),arrow:n.find(function(o){return o.classList.contains(Hn)||o.classList.contains(jn)}),backdrop:n.find(function(o){return o.classList.contains(mo)})}}function Pn(e){var t=It(),n=It();n.className="tippy-box",n.setAttribute("data-state","hidden"),n.setAttribute("tabindex","-1");var o=It();function a(i,u){var s=Ee(t),c=s.box,l=s.content,d=s.arrow;u.theme?c.setAttribute("data-theme",u.theme):c.removeAttribute("data-theme"),typeof u.animation=="string"?c.setAttribute("data-animation",u.animation):c.removeAttribute("data-animation"),u.inertia?c.setAttribute("data-inertia",""):c.removeAttribute("data-inertia"),c.style.maxWidth=typeof u.maxWidth=="number"?u.maxWidth+"px":u.maxWidth,u.role?c.setAttribute("role",u.role):c.removeAttribute("role"),i.content===u.content&&i.allowHTML===u.allowHTML||cn(l,e.props),u.arrow?d?i.arrow!==u.arrow&&(c.removeChild(d),c.appendChild(an(u.arrow))):c.appendChild(an(u.arrow)):d&&c.removeChild(d)}return o.className=Ln,o.setAttribute("data-state","hidden"),cn(o,e.props),t.appendChild(n),n.appendChild(o),a(e.props,e.props),{popper:t,onUpdate:a}}Pn.$$tippy=!0;var xo=1,oe=[],de=[];function To(e,t){var n,o,a,i,u,s,c,l,d=sn(e,Object.assign({},ot,$n(en(t)))),v=!1,g=!1,b=!1,O=!1,m=[],f=Ze(xt,d.interactiveDebounce),D=xo++,x=(l=d.plugins).filter(function(p,y){return l.indexOf(p)===y}),r={id:D,reference:e,popper:It(),popperInstance:null,props:d,state:{isEnabled:!0,isVisible:!1,isDestroyed:!1,isMounted:!1,isShown:!1},plugins:x,clearDelayTimeouts:function(){clearTimeout(n),clearTimeout(o),cancelAnimationFrame(a)},setProps:function(p){if(!r.state.isDestroyed){$("onBeforeUpdate",[r,p]),Xt();var y=r.props,w=sn(e,Object.assign({},y,en(p),{ignoreAttributes:!0}));r.props=w,gt(),y.interactiveDebounce!==w.interactiveDebounce&&(st(),f=Ze(xt,w.interactiveDebounce)),y.triggerTarget&&!w.triggerTarget?_t(y.triggerTarget).forEach(function(E){E.removeAttribute("aria-expanded")}):w.triggerTarget&&e.removeAttribute("aria-expanded"),it(),S(),_&&_(y,w),r.popperInstance&&(kt(),$t().forEach(function(E){requestAnimationFrame(E._tippy.popperInstance.forceUpdate)})),$("onAfterUpdate",[r,p])}},setContent:function(p){r.setProps({content:p})},show:function(){var p=r.state.isVisible,y=r.state.isDestroyed,w=!r.state.isEnabled,E=at.isTouch&&!r.props.touch,C=pe(r.props.duration,0,ot.duration);if(!(p||y||w||E)&&!P().hasAttribute("disabled")&&($("onShow",[r],!1),r.props.onShow(r)!==!1)){if(r.state.isVisible=!0,M()&&(h.style.visibility="visible"),S(),mt(),r.state.isMounted||(h.style.transition="none"),M()){var R=q();le([R.box,R.content],0)}s=function(){var V;if(r.state.isVisible&&!O){if(O=!0,h.offsetHeight,h.style.transition=r.props.moveTransition,M()&&r.props.animation){var W=q(),N=W.box,G=W.content;le([N,G],C),nn([N,G],"visible")}rt(),it(),tn(de,r),(V=r.popperInstance)==null||V.forceUpdate(),$("onMount",[r]),r.props.animation&&M()&&function(Q,z){jt(Q,z)}(C,function(){r.state.isShown=!0,$("onShown",[r])})}},function(){var V,W=r.props.appendTo,N=P();V=r.props.interactive&&W===Mn||W==="parent"?N.parentNode:kn(W,[N]),V.contains(h)||V.appendChild(h),r.state.isMounted=!0,kt()}()}},hide:function(){var p=!r.state.isVisible,y=r.state.isDestroyed,w=!r.state.isEnabled,E=pe(r.props.duration,1,ot.duration);if(!(p||y||w)&&($("onHide",[r],!1),r.props.onHide(r)!==!1)){if(r.state.isVisible=!1,r.state.isShown=!1,O=!1,v=!1,M()&&(h.style.visibility="hidden"),st(),dt(),S(!0),M()){var C=q(),R=C.box,V=C.content;r.props.animation&&(le([R,V],E),nn([R,V],"hidden"))}rt(),it(),r.props.animation?M()&&function(W,N){jt(W,function(){!r.state.isVisible&&h.parentNode&&h.parentNode.contains(h)&&N()})}(E,r.unmount):r.unmount()}},hideWithInteractivity:function(p){B().addEventListener("mousemove",f),tn(oe,f),f(p)},enable:function(){r.state.isEnabled=!0},disable:function(){r.hide(),r.state.isEnabled=!1},unmount:function(){r.state.isVisible&&r.hide(),r.state.isMounted&&(St(),$t().forEach(function(p){p._tippy.unmount()}),h.parentNode&&h.parentNode.removeChild(h),de=de.filter(function(p){return p!==r}),r.state.isMounted=!1,$("onHidden",[r]))},destroy:function(){r.state.isDestroyed||(r.clearDelayTimeouts(),r.unmount(),Xt(),delete e._tippy,r.state.isDestroyed=!0,$("onDestroy",[r]))}};if(!d.render)return r;var T=d.render(r),h=T.popper,_=T.onUpdate;h.setAttribute("data-tippy-root",""),h.id="tippy-"+r.id,r.popper=h,e._tippy=r,h._tippy=r;var L=x.map(function(p){return p.fn(r)}),H=e.hasAttribute("aria-expanded");return gt(),it(),S(),$("onCreate",[r]),d.showOnCreate&&Zt(),h.addEventListener("mouseenter",function(){r.props.interactive&&r.state.isVisible&&r.clearDelayTimeouts()}),h.addEventListener("mouseleave",function(){r.props.interactive&&r.props.trigger.indexOf("mouseenter")>=0&&B().addEventListener("mousemove",f)}),r;function A(){var p=r.props.touch;return Array.isArray(p)?p:[p,0]}function k(){return A()[0]==="hold"}function M(){var p;return!((p=r.props.render)==null||!p.$$tippy)}function P(){return c||e}function B(){var p=P().parentNode;return p?function(y){var w,E=_t(y)[0];return E!=null&&(w=E.ownerDocument)!=null&&w.body?E.ownerDocument:document}(p):document}function q(){return Ee(h)}function K(p){return r.state.isMounted&&!r.state.isVisible||at.isTouch||i&&i.type==="focus"?0:pe(r.props.delay,p?0:1,ot.delay)}function S(p){p===void 0&&(p=!1),h.style.pointerEvents=r.props.interactive&&!p?"":"none",h.style.zIndex=""+r.props.zIndex}function $(p,y,w){var E;w===void 0&&(w=!0),L.forEach(function(C){C[p]&&C[p].apply(C,y)}),w&&(E=r.props)[p].apply(E,y)}function rt(){var p=r.props.aria;if(p.content){var y="aria-"+p.content,w=h.id;_t(r.props.triggerTarget||e).forEach(function(E){var C=E.getAttribute(y);if(r.state.isVisible)E.setAttribute(y,C?C+" "+w:w);else{var R=C&&C.replace(w,"").trim();R?E.setAttribute(y,R):E.removeAttribute(y)}})}}function it(){!H&&r.props.aria.expanded&&_t(r.props.triggerTarget||e).forEach(function(p){r.props.interactive?p.setAttribute("aria-expanded",r.state.isVisible&&p===P()?"true":"false"):p.removeAttribute("aria-expanded")})}function st(){B().removeEventListener("mousemove",f),oe=oe.filter(function(p){return p!==f})}function I(p){if(!at.isTouch||!b&&p.type!=="mousedown"){var y=p.composedPath&&p.composedPath()[0]||p.target;if(!r.props.interactive||!on(h,y)){if(_t(r.props.triggerTarget||e).some(function(w){return on(w,y)})){if(at.isTouch||r.state.isVisible&&r.props.trigger.indexOf("click")>=0)return}else $("onClickOutside",[r,p]);r.props.hideOnClick===!0&&(r.clearDelayTimeouts(),r.hide(),g=!0,setTimeout(function(){g=!1}),r.state.isMounted||dt())}}}function U(){b=!0}function ht(){b=!1}function mt(){var p=B();p.addEventListener("mousedown",I,!0),p.addEventListener("touchend",I,yt),p.addEventListener("touchstart",ht,yt),p.addEventListener("touchmove",U,yt)}function dt(){var p=B();p.removeEventListener("mousedown",I,!0),p.removeEventListener("touchend",I,yt),p.removeEventListener("touchstart",ht,yt),p.removeEventListener("touchmove",U,yt)}function jt(p,y){var w=q().box;function E(C){C.target===w&&(fe(w,"remove",E),y())}if(p===0)return y();fe(w,"remove",u),fe(w,"add",E),u=E}function pt(p,y,w){w===void 0&&(w=!1),_t(r.props.triggerTarget||e).forEach(function(E){E.addEventListener(p,y,w),m.push({node:E,eventType:p,handler:y,options:w})})}function gt(){var p;k()&&(pt("touchstart",Jt,{passive:!0}),pt("touchend",Gt,{passive:!0})),(p=r.props.trigger,p.split(/\s+/).filter(Boolean)).forEach(function(y){if(y!=="manual")switch(pt(y,Jt),y){case"mouseenter":pt("mouseleave",Gt);break;case"focus":pt(Oo?"focusout":"blur",Mt);break;case"focusin":pt("focusout",Mt)}})}function Xt(){m.forEach(function(p){var y=p.node,w=p.eventType,E=p.handler,C=p.options;y.removeEventListener(w,E,C)}),m=[]}function Jt(p){var y,w=!1;if(r.state.isEnabled&&!Qt(p)&&!g){var E=((y=i)==null?void 0:y.type)==="focus";i=p,c=p.currentTarget,it(),!r.state.isVisible&&Be(p,"MouseEvent")&&oe.forEach(function(C){return C(p)}),p.type==="click"&&(r.props.trigger.indexOf("mouseenter")<0||v)&&r.props.hideOnClick!==!1&&r.state.isVisible?w=!0:Zt(p),p.type==="click"&&(v=!w),w&&!E&&J(p)}}function xt(p){var y=p.target,w=P().contains(y)||h.contains(y);p.type==="mousemove"&&w||function(E,C){var R=C.clientX,V=C.clientY;return E.every(function(W){var N=W.popperRect,G=W.popperState,Q=W.props.interactiveBorder,z=G.placement.split("-")[0],lt=G.modifiersData.offset;if(!lt)return!0;var Tt=z==="bottom"?lt.top.y:0,Rn=z==="top"?lt.bottom.y:0,Vn=z==="right"?lt.left.x:0,Bn=z==="left"?lt.right.x:0,Wn=N.top-V+Tt>Q,In=V-N.bottom-Rn>Q,Nn=N.left-R+Vn>Q,qn=R-N.right-Bn>Q;return Wn||In||Nn||qn})}($t().concat(h).map(function(E){var C,R=(C=E._tippy.popperInstance)==null?void 0:C.state;return R?{popperRect:E.getBoundingClientRect(),popperState:R,props:d}:null}).filter(Boolean),p)&&(st(),J(p))}function Gt(p){Qt(p)||r.props.trigger.indexOf("click")>=0&&v||(r.props.interactive?r.hideWithInteractivity(p):J(p))}function Mt(p){r.props.trigger.indexOf("focusin")<0&&p.target!==P()||r.props.interactive&&p.relatedTarget&&h.contains(p.relatedTarget)||J(p)}function Qt(p){return!!at.isTouch&&k()!==p.type.indexOf("touch")>=0}function kt(){St();var p=r.props,y=p.popperOptions,w=p.placement,E=p.offset,C=p.getReferenceClientRect,R=p.moveTransition,V=M()?Ee(h).arrow:null,W=C?{getBoundingClientRect:C,contextElement:C.contextElement||P()}:e,N={name:"$$tippy",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:function(Q){var z=Q.state;if(M()){var lt=q().box;["placement","reference-hidden","escaped"].forEach(function(Tt){Tt==="placement"?lt.setAttribute("data-placement",z.placement):z.attributes.popper["data-popper-"+Tt]?lt.setAttribute("data-"+Tt,""):lt.removeAttribute("data-"+Tt)}),z.attributes.popper={}}}},G=[{name:"offset",options:{offset:E}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5}},{name:"computeStyles",options:{adaptive:!R}},N];M()&&V&&G.push({name:"arrow",options:{element:V,padding:3}}),G.push.apply(G,(y==null?void 0:y.modifiers)||[]),r.popperInstance=ho(W,h,Object.assign({},y,{placement:w,onFirstUpdate:s,modifiers:G}))}function St(){r.popperInstance&&(r.popperInstance.destroy(),r.popperInstance=null)}function $t(){return ie(h.querySelectorAll("[data-tippy-root]"))}function Zt(p){r.clearDelayTimeouts(),p&&$("onTrigger",[r,p]),mt();var y=K(!0),w=A(),E=w[0],C=w[1];at.isTouch&&E==="hold"&&C&&(y=C),y?n=setTimeout(function(){r.show()},y):r.show()}function J(p){if(r.clearDelayTimeouts(),$("onUntrigger",[r,p]),r.state.isVisible){if(!(r.props.trigger.indexOf("mouseenter")>=0&&r.props.trigger.indexOf("click")>=0&&["mouseleave","mousemove"].indexOf(p.type)>=0&&v)){var y=K(!1);y?o=setTimeout(function(){r.state.isVisible&&r.hide()},y):a=requestAnimationFrame(function(){r.hide()})}}else dt()}}function Bt(e,t){t===void 0&&(t={});var n=ot.plugins.concat(t.plugins||[]);document.addEventListener("touchstart",yo,yt),window.addEventListener("blur",bo);var o=Object.assign({},t,{plugins:n}),a=go(e).reduce(function(i,u){var s=u&&To(u,o);return s&&i.push(s),i},[]);return ue(e)?a[0]:a}Bt.defaultProps=ot,Bt.setDefaultProps=function(e){Object.keys(e).forEach(function(t){ot[t]=e[t]})},Bt.currentInput=at,Object.assign({},En,{effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow)}}),Bt.setDefaultProps({render:Pn});var Yt=(e=>(e.Hover="hover",e.Click="click",e))(Yt||{});const Nt=class Nt extends Event{constructor(){super(Nt.eventType,{bubbles:!0})}static isEvent(t){return t.type===Nt.eventType}};j(Nt,"eventType","augment-ds-event__close-tooltip-request");let Ot=Nt;var un=NaN,_o="[object Symbol]",Co=/^\s+|\s+$/g,Do=/^[-+]0x[0-9a-f]+$/i,Ao=/^0b[01]+$/i,Lo=/^0o[0-7]+$/i,Ho=parseInt,jo=typeof te=="object"&&te&&te.Object===Object&&te,Mo=typeof self=="object"&&self&&self.Object===Object&&self,ko=jo||Mo||Function("return this")(),So=Object.prototype.toString,$o=Math.max,Po=Math.min,ve=function(){return ko.Date.now()};function xe(e){var t=typeof e;return!!e&&(t=="object"||t=="function")}function pn(e){if(typeof e=="number")return e;if(function(o){return typeof o=="symbol"||function(a){return!!a&&typeof a=="object"}(o)&&So.call(o)==_o}(e))return un;if(xe(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=xe(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=e.replace(Co,"");var n=Ao.test(e);return n||Lo.test(e)?Ho(e.slice(2),n?2:8):Do.test(e)?un:+e}const ln=Yn(function(e,t,n){var o,a,i,u,s,c,l=0,d=!1,v=!1,g=!0;if(typeof e!="function")throw new TypeError("Expected a function");function b(x){var r=o,T=a;return o=a=void 0,l=x,u=e.apply(T,r)}function O(x){var r=x-c;return c===void 0||r>=t||r<0||v&&x-l>=i}function m(){var x=ve();if(O(x))return f(x);s=setTimeout(m,function(r){var T=t-(r-c);return v?Po(T,i-(r-l)):T}(x))}function f(x){return s=void 0,g&&o?b(x):(o=a=void 0,u)}function D(){var x=ve(),r=O(x);if(o=arguments,a=this,c=x,r){if(s===void 0)return function(T){return l=T,s=setTimeout(m,t),d?b(T):u}(c);if(v)return s=setTimeout(m,t),b(c)}return s===void 0&&(s=setTimeout(m,t)),u}return t=pn(t)||0,xe(n)&&(d=!!n.leading,i=(v="maxWait"in n)?$o(pn(n.maxWait)||0,t):i,g="trailing"in n?!!n.trailing:g),D.cancel=function(){s!==void 0&&clearTimeout(s),l=0,o=c=a=s=void 0},D.flush=function(){return s===void 0?u:f(ve())},D}),ae=class ae{constructor(t){j(this,"debouncedHoverStart");j(this,"debouncedHoverEnd");j(this,"handleMouseEnter",()=>{var t,n;(t=this.debouncedHoverEnd)==null||t.cancel(),(n=this.debouncedHoverStart)==null||n.call(this)});j(this,"handleMouseLeave",()=>{var t,n;(t=this.debouncedHoverStart)==null||t.cancel(),(n=this.debouncedHoverEnd)==null||n.call(this)});j(this,"handleMouseMove",()=>{var t,n;(t=this.debouncedHoverEnd)==null||t.cancel(),(n=this.debouncedHoverStart)==null||n.call(this)});j(this,"cancelHovers",()=>{var t,n;(t=this.debouncedHoverStart)==null||t.cancel(),(n=this.debouncedHoverEnd)==null||n.cancel()});this.debouncedHoverStart=ln(t.onHoverStart,t.hoverTriggerDuration),this.debouncedHoverEnd=ln(t.onHoverEnd,ae.DEFAULT_HOVER_END_DEBOUNCE_MS)}destroy(){var t,n;(t=this.debouncedHoverStart)==null||t.cancel(),(n=this.debouncedHoverEnd)==null||n.cancel()}};j(ae,"DEFAULT_HOVER_END_DEBOUNCE_MS",67);let se=ae;function Te(e,t){return e.addEventListener("mouseenter",t.handleMouseEnter),e.addEventListener("mouseleave",t.handleMouseLeave),e.addEventListener("mousemove",t.handleMouseMove),{destroy(){e.removeEventListener("mouseenter",t.handleMouseEnter),e.removeEventListener("mouseleave",t.handleMouseLeave),e.removeEventListener("mousemove",t.handleMouseMove)}}}const Ro=Symbol("hover-action-context");function zo(e=100){const t=dn(!1);fn(Ro,t);const n=new se({onHoverStart(){t.set(!0)},onHoverEnd(){t.set(!1)},hoverTriggerDuration:e});return function(o){return Te(o,n)}}const qt=class qt{constructor(t){j(this,"_state");j(this,"_tippy");j(this,"_triggerElement");j(this,"_contentElement");j(this,"_contentProps");j(this,"_hoverContext");j(this,"_referenceClientRect");j(this,"_setOpen",t=>{var n,o;this._isOpen!==t&&(this._state.update(a=>({...a,open:t})),(o=(n=this._opts).onOpenChange)==null||o.call(n,t))});j(this,"openTooltip",()=>{this.internalControlSetOpen(!0)});j(this,"closeTooltip",()=>{this.internalControlSetOpen(!1)});j(this,"toggleTooltip",()=>{this.internalControlSetOpen(!this._isOpen)});j(this,"externalControlSetOpen",t=>{this._opts.open=t,t!==void 0&&this._setOpen(t)});j(this,"internalControlSetOpen",t=>{this._isExternallyControlled||this._setOpen(t)});j(this,"_updateTippy",()=>{var n;if(!this._triggerElement||!this._contentElement||!this._contentProps)return(n=this._tippy)==null||n.destroy(),void(this._tippy=void 0);const t={trigger:"manual",showOnCreate:this._isOpen,offset:[0,2],interactive:!0,content:this._contentElement,popperOptions:{strategy:"fixed"},duration:0,delay:0,placement:Vo(this._contentProps),hideOnClick:!1,appendTo:this._opts.nested?this._triggerElement:document.body,theme:this._opts.tippyTheme};if(this._referenceClientRect!==void 0){const o=this._referenceClientRect;t.getReferenceClientRect=()=>o}if(this._tippy!==void 0)this._tippy.setProps(t);else{const o=this._state.subscribe(a=>{var i,u;a.open?(i=this._tippy)==null||i.show():(u=this._tippy)==null||u.hide()});this._tippy=Bt(this._triggerElement,{...t,onDestroy:o})}});j(this,"update",()=>{var t,n;(n=(t=this._tippy)==null?void 0:t.popperInstance)==null||n.update()});j(this,"registerTrigger",(t,n)=>{this._triggerElement=t,this._referenceClientRect=n;const o=this._hoverContext&&Te(this._triggerElement,this._hoverContext);return this._updateTippy(),{update:a=>{this._referenceClientRect=a,this._updateTippy()},destroy:()=>{o==null||o.destroy(),this._triggerElement=void 0,this._updateTippy()}}});j(this,"registerContents",(t,n)=>{t.remove(),this._contentElement=t,this._contentProps=n;const o=this._hoverContext&&Te(this._contentElement,this._hoverContext);this._updateTippy();const a=function(i,u){const s=new ResizeObserver(()=>u());return s.observe(i),()=>s.disconnect()}(t,this.update);return{destroy:()=>{o==null||o.destroy(),this._contentElement=void 0,this._updateTippy(),a()},update:i=>{n={...n,...i},this._contentProps=n,this._updateTippy()}}});j(this,"requestClose",()=>{var t;(t=this._contentElement)==null||t.dispatchEvent(new Ot)});this._opts=t,this._state=dn({open:this._opts.open??this._opts.defaultOpen??!1}),this.supportsHover&&(this._hoverContext=new se({hoverTriggerDuration:this.delayDurationMs,onHoverStart:()=>{this.openTooltip(),this._opts.onHoverStart()},onHoverEnd:()=>{this.closeTooltip(),this._opts.onHoverEnd()}}))}get supportsHover(){return this._opts.triggerOn.includes(Yt.Hover)}get supportsClick(){return this._opts.triggerOn.includes(Yt.Click)}get triggerElement(){return this._triggerElement}get contentElement(){return this._contentElement}get state(){return this._state}get delayDurationMs(){return this._opts.delayDurationMs??qt.DEFAULT_DELAY_DURATION_MS}get _isExternallyControlled(){const{defaultOpen:t,open:n}=this._opts;return n!==void 0&&(t!==void 0&&console.warn("`defaultOpen` has no effect when `open` is provided"),!0)}get _isOpen(){return Kn(this._state).open}};j(qt,"CONTEXT_KEY","augment-tooltip-context"),j(qt,"DEFAULT_DELAY_DURATION_MS",160);let Ht=qt;function Vo(e){return e.align==="center"?e.side:`${e.side}-${e.align}`}function Bo(e){let t;const n=e[12].default,o=Ae(n,e,e[11],null);return{c(){o&&o.c()},m(a,i){o&&o.m(a,i),t=!0},p(a,[i]){o&&o.p&&(!t||2048&i)&&Le(o,n,a,a[11],t?je(n,a[11],i,null):He(a[11]),null)},i(a){t||(Me(o,a),t=!0)},o(a){ke(o,a),t=!1},d(a){o&&o.d(a)}}}function Wo(e,t,n){let{$$slots:o={},$$scope:a}=t,{defaultOpen:i}=t,{open:u}=t,{onOpenChange:s}=t,{delayDurationMs:c}=t,{nested:l=!0}=t,{onHoverStart:d=()=>{}}=t,{onHoverEnd:v=()=>{}}=t,{triggerOn:g=[Yt.Hover,Yt.Click]}=t,{tippyTheme:b}=t;const O=new Ht({defaultOpen:i,open:u,onOpenChange:s,delayDurationMs:c,nested:l,onHoverStart:d,onHoverEnd:v,triggerOn:g,tippyTheme:b});return fn(Ht.CONTEXT_KEY,O),e.$$set=m=>{"defaultOpen"in m&&n(0,i=m.defaultOpen),"open"in m&&n(1,u=m.open),"onOpenChange"in m&&n(2,s=m.onOpenChange),"delayDurationMs"in m&&n(3,c=m.delayDurationMs),"nested"in m&&n(4,l=m.nested),"onHoverStart"in m&&n(5,d=m.onHoverStart),"onHoverEnd"in m&&n(6,v=m.onHoverEnd),"triggerOn"in m&&n(7,g=m.triggerOn),"tippyTheme"in m&&n(10,b=m.tippyTheme),"$$scope"in m&&n(11,a=m.$$scope)},e.$$.update=()=>{2&e.$$.dirty&&O.externalControlSetOpen(u)},[i,u,s,c,l,d,v,g,()=>O.openTooltip(),()=>O.closeTooltip(),b,a,o]}class Xo extends _e{constructor(t){super(),Ce(this,t,Wo,Bo,De,{defaultOpen:0,open:1,onOpenChange:2,delayDurationMs:3,nested:4,onHoverStart:5,onHoverEnd:6,triggerOn:7,requestOpen:8,requestClose:9,tippyTheme:10})}get requestOpen(){return this.$$.ctx[8]}get requestClose(){return this.$$.ctx[9]}}function Io(e){let t,n,o,a,i,u;const s=e[5].default,c=Ae(s,e,e[4],null);return{c(){t=vn("div"),c&&c.c(),nt(t,"class",n=We(`l-tooltip-trigger ${e[1]}`)+" svelte-18wun1x"),nt(t,"role","button"),nt(t,"tabindex","-1")},m(l,d){hn(l,t,d),c&&c.m(t,null),a=!0,i||(u=[bt(t,"click",e[3]),bt(t,"keydown",e[6]),mn(o=e[2].registerTrigger(t,e[0]))],i=!0)},p(l,[d]){c&&c.p&&(!a||16&d)&&Le(c,s,l,l[4],a?je(s,l[4],d,null):He(l[4]),null),(!a||2&d&&n!==(n=We(`l-tooltip-trigger ${l[1]}`)+" svelte-18wun1x"))&&nt(t,"class",n),o&&Rt(o.update)&&1&d&&o.update.call(null,l[0])},i(l){a||(Me(c,l),a=!0)},o(l){ke(c,l),a=!1},d(l){l&&gn(t),c&&c.d(l),i=!1,yn(u)}}}function No(e,t,n){let{$$slots:o={},$$scope:a}=t,{referenceClientRect:i}=t,{class:u=""}=t;const s=bn(Ht.CONTEXT_KEY);return e.$$set=c=>{"referenceClientRect"in c&&n(0,i=c.referenceClientRect),"class"in c&&n(1,u=c.class),"$$scope"in c&&n(4,a=c.$$scope)},[i,u,s,c=>{s.supportsClick&&(s.toggleTooltip(),c.stopPropagation())},a,o,function(c){me.call(this,e,c)}]}class Jo extends _e{constructor(t){super(),Ce(this,t,No,Io,De,{referenceClientRect:0,class:1})}}const{window:he}=Jn;function qo(e){let t,n,o,a,i;const u=e[14].default,s=Ae(u,e,e[13],null);return{c(){t=vn("div"),s&&s.c(),nt(t,"class","l-tooltip-contents svelte-1mcoenu"),nt(t,"role","button"),nt(t,"tabindex","-1"),nt(t,"data-position-side",e[0]),nt(t,"data-position-align",e[1]),Ie(t,"l-tooltip-contents--open",e[2].open)},m(c,l){hn(c,t,l),s&&s.m(t,null),o=!0,a||(i=[bt(he,"click",function(){Rt(e[2].open?e[5]:void 0)&&(e[2].open?e[5]:void 0).apply(this,arguments)},!0),bt(he,"keydown",function(){Rt(e[2].open?e[6]:void 0)&&(e[2].open?e[6]:void 0).apply(this,arguments)},!0),bt(he,"blur",function(){Rt(e[2].open?e[7]:void 0)&&(e[2].open?e[7]:void 0).apply(this,arguments)},!0),mn(n=e[3].registerContents(t,{side:e[0],align:e[1]})),bt(t,"click",Ne(e[15])),bt(t,"keydown",Ne(e[16]))],a=!0)},p(c,[l]){e=c,s&&s.p&&(!o||8192&l)&&Le(s,u,e,e[13],o?je(u,e[13],l,null):He(e[13]),null),(!o||1&l)&&nt(t,"data-position-side",e[0]),(!o||2&l)&&nt(t,"data-position-align",e[1]),n&&Rt(n.update)&&3&l&&n.update.call(null,{side:e[0],align:e[1]}),(!o||4&l)&&Ie(t,"l-tooltip-contents--open",e[2].open)},i(c){o||(Me(s,c),o=!0)},o(c){ke(s,c),o=!1},d(c){c&&gn(t),s&&s.d(c),a=!1,yn(i)}}}function Uo(e,t,n){let o,a,{$$slots:i={},$$scope:u}=t,{onEscapeKeyDown:s=()=>{}}=t,{onClickOutside:c=()=>{}}=t,{onRequestClose:l=()=>{}}=t,{side:d="top"}=t,{align:v="center"}=t;const g=bn(Ht.CONTEXT_KEY),b=g.state;qe(e,b,f=>n(2,a=f));const O=f=>{var D;if(Ot.isEvent(f)&&f.target&&((D=g.contentElement)!=null&&D.contains(f.target)))return g.closeTooltip(),l(f),void f.stopPropagation()},m=zn(b,f=>f.open);return qe(e,m,f=>n(12,o=f)),Xn(()=>{var f;(f=g.contentElement)==null||f.removeEventListener(Ot.eventType,O)}),e.$$set=f=>{"onEscapeKeyDown"in f&&n(9,s=f.onEscapeKeyDown),"onClickOutside"in f&&n(10,c=f.onClickOutside),"onRequestClose"in f&&n(11,l=f.onRequestClose),"side"in f&&n(0,d=f.side),"align"in f&&n(1,v=f.align),"$$scope"in f&&n(13,u=f.$$scope)},e.$$.update=()=>{4096&e.$$.dirty&&g.contentElement&&(o?g.contentElement.addEventListener(Ot.eventType,O):g.contentElement.removeEventListener(Ot.eventType,O))},[d,v,a,g,b,f=>{f.target!==null&&f.target instanceof Node&&g.contentElement&&g.triggerElement&&a.open&&(f.composedPath().includes(g.contentElement)||f.composedPath().includes(g.triggerElement)||(g.closeTooltip(),c(f)))},f=>{f.target!==null&&f.target instanceof Node&&g.contentElement&&a.open&&f.key==="Escape"&&(g.closeTooltip(),s(f))},f=>{f.target===window&&g.requestClose()},m,s,c,l,o,u,i,function(f){me.call(this,e,f)},function(f){me.call(this,e,f)}]}class Go extends _e{constructor(t){super(),Ce(this,t,Uo,qo,De,{onEscapeKeyDown:9,onClickOutside:10,onRequestClose:11,side:0,align:1})}}export{Go as C,se as H,Xo as R,Yt as T,Jo as a,Ot as b,Ht as c,ln as d,zo as e,Te as o,Bt as t};
