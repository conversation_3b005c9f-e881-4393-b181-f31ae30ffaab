async function A(t){const s=await crypto.subtle.digest("SHA-256",t);return Array.from(new Uint8Array(s)).map(a=>a.toString(16).padStart(2,"0")).join("")}var r=(t=>(t.chat="chat",t))(r||{}),i=(t=>(t.chatMentionFolder="chat-mention-folder",t.chatMentionFile="chat-mention-file",t.chatMentionExternalSource="chat-mention-external-source",t.chatClearContext="chat-clear-context",t.chatRestoreDefaultContext="chat-restore-default-context",t.chatUseActionFind="chat-use-action-find",t.chatUseActionExplain="chat-use-action-explain",t.chatUseActionFix="chat-use-action-autofix",t.chatUseActionWriteTest="chat-use-action-write-test",t.chatNewConversation="chat-new-conversation",t.chatNewAutofixConversation="chat-new-autofix-conversation",t.chatEditConversationName="chat-edit-conversation-name",t.chatFailedSmartPasteResolveFile="chat-failed-smart-paste-resolve-file",t.chatPrecomputeSmartPaste="chat-precompute-smart-paste",t.chatSmartPaste="chat-smart-paste",t.chatCodeblockCopy="chat-codeblock-copy",t.chatCodeblockCreate="chat-codeblock-create",t.chatCodeblockGoToFile="chat-codeblock-go-to-file",t.chatCodespanGoToFile="chat-codespan-go-to-file",t.chatCodespanGoToSymbol="chat-codespan-go-to-symbol",t.chatMermaidblockInitialize="chat-mermaidblock-initialize",t.chatMermaidblockToggle="chat-mermaidblock-toggle",t.chatMermaidblockInteract="chat-mermaidblock-interact",t.chatMermaidBlockError="chat-mermaidblock-error",t.chatUseSuggestedQuestion="chat-use-suggested-question",t.chatDisplaySuggestedQuestions="chat-display-suggested-questions",t.setWorkspaceGuidelines="chat-set-workspace-guidelines",t.clearWorkspaceGuidelines="chat-clear-workspace-guidelines",t.setUserGuidelines="chat-set-user-guidelines",t.clearUserGuidelines="chat-clear-user-guidelines",t))(i||{}),c=(t=>(t[t.TEXT=0]="TEXT",t[t.TOOL_RESULT=1]="TOOL_RESULT",t[t.IMAGE=2]="IMAGE",t[t.IMAGE_ID=3]="IMAGE_ID",t[t.IDE_STATE=4]="IDE_STATE",t[t.EDIT_EVENTS=5]="EDIT_EVENTS",t[t.CHECKPOINT_REF=6]="CHECKPOINT_REF",t[t.CHANGE_PERSONALITY=7]="CHANGE_PERSONALITY",t))(c||{}),E=(t=>(t[t.IMAGE_FORMAT_UNSPECIFIED=0]="IMAGE_FORMAT_UNSPECIFIED",t[t.PNG=1]="PNG",t[t.JPEG=2]="JPEG",t[t.GIF=3]="GIF",t[t.WEBP=4]="WEBP",t))(E||{}),l=(t=>(t[t.UNSPECIFIED=0]="UNSPECIFIED",t[t.USER_EDIT=1]="USER_EDIT",t[t.CHECKPOINT_REVERT=2]="CHECKPOINT_REVERT",t))(l||{}),d=(t=>(t[t.CONTENT_TYPE_UNSPECIFIED=0]="CONTENT_TYPE_UNSPECIFIED",t[t.CONTENT_TEXT=1]="CONTENT_TEXT",t[t.CONTENT_IMAGE=2]="CONTENT_IMAGE",t))(d||{}),T=(t=>(t[t.RAW_RESPONSE=0]="RAW_RESPONSE",t[t.SUGGESTED_QUESTIONS=1]="SUGGESTED_QUESTIONS",t[t.MAIN_TEXT_FINISHED=2]="MAIN_TEXT_FINISHED",t[t.TOOL_USE=5]="TOOL_USE",t[t.AGENT_MEMORY=6]="AGENT_MEMORY",t))(T||{}),g=(t=>(t.chat="CHAT",t.agent="AGENT",t.remoteAgent="REMOTE_AGENT",t.memories="MEMORIES",t.orientation="ORIENTATION",t.memoriesCompression="MEMORIES_COMPRESSION",t))(g||{}),h=(t=>(t[t.DEFAULT=0]="DEFAULT",t[t.PROTOTYPER=1]="PROTOTYPER",t[t.BRAINSTORM=2]="BRAINSTORM",t[t.REVIEWER=3]="REVIEWER",t))(h||{}),u=(t=>(t.getEditListRequest="agent-get-edit-list-request",t.getEditListResponse="agent-get-edit-list-response",t.getEditChangesByRequestIdRequest="agent-get-edit-changes-by-request-id-request",t.getEditChangesByRequestIdResponse="agent-get-edit-changes-by-request-id-response",t.setCurrentConversation="agent-set-current-conversation",t.revertToTimestamp="revert-to-timestamp",t.chatAgentEditAcceptAll="chat-agent-edit-accept-all",t.reportAgentSessionEvent="report-agent-session-event",t.reportAgentRequestEvent="report-agent-request-event",t.chatReviewAgentFile="chat-review-agent-file",t.getAgentEditContentsByRequestId="get-agent-edit-contents-by-request-id",t.getAgentEditContentsByRequestIdResponse="get-agent-edit-contents-by-request-id-response",t.checkHasEverUsedAgent="check-has-ever-used-agent",t.checkHasEverUsedAgentResponse="check-has-ever-used-agent-response",t.setHasEverUsedAgent="set-has-ever-used-agent",t))(u||{}),I=(t=>(t.closeAllToolProcesses="close-all-tool-processes",t.getToolIdentifierRequest="get-tool-identifier-request",t.getToolIdentifierResponse="get-tool-identifier-response",t))(I||{});function R(t){return t.replace(/^data:.*?;base64,/,"")}async function S(t){return new Promise((s,a)=>{const e=new FileReader;e.onload=n=>{var o;return s((o=n.target)==null?void 0:o.result)},e.onerror=a,e.readAsDataURL(t)})}async function p(t){return t.length<1e4?Promise.resolve(function(s){const a=atob(s);return Uint8Array.from(a,e=>e.codePointAt(0)||0)}(t)):new Promise((s,a)=>{const e=new Worker(URL.createObjectURL(new Blob([`
            self.onmessage = function(e) {
              try {
                const base64 = e.data;
                const binString = atob(base64);
                const bytes = new Uint8Array(binString.length);
                for (let i = 0; i < binString.length; i++) {
                  bytes[i] = binString.charCodeAt(i);
                }
                self.postMessage(bytes, [bytes.buffer]);
              } catch (error) {
                self.postMessage({ error: error.message });
              }
            };
            `],{type:"application/javascript"})));e.onmessage=function(n){n.data.error?a(new Error(n.data.error)):s(n.data),e.terminate()},e.onerror=function(n){a(n.error),e.terminate()},e.postMessage(t)})}export{u as A,T as C,l as E,E as I,h as P,I as T,r as W,c as a,p as b,i as c,g as d,d as e,R as g,S as r,A as s};
