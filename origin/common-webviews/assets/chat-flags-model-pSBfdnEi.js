var v=Object.defineProperty;var I=(a,e,t)=>e in a?v(a,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[e]=t;var s=(a,e,t)=>I(a,typeof e!="symbol"?e+"":e,t);import{P as p,W as E,A as h,T as S,g as x,r as C,s as D,b as T,C as q}from"./file-base64-RhZyEMB8.js";import{W as n,S as k}from"./BaseButton-ci_067e0.js";import{n as N}from"./file-paths-BcSg4gks.js";import{p as L}from"./types-e72Yl75f.js";const te="augment-welcome";var u=(a=>(a.draft="draft",a.sent="sent",a.failed="failed",a.success="success",a.cancelled="cancelled",a))(u||{}),g=(a=>(a.seen="seen",a.unseen="unseen",a))(g||{}),H=(a=>(a.signInWelcome="sign-in-welcome",a.generateCommitMessage="generate-commit-message",a.summaryResponse="summary-response",a.summaryTitle="summary-title",a.educateFeatures="educate-features",a.autofixMessage="autofix-message",a.autofixSteeringMessage="autofix-steering-message",a.autofixStage="autofix-stage",a.agentOnboarding="agent-onboarding",a.agenticTurnDelimiter="agentic-turn-delimiter",a.agenticRevertDelimiter="agentic-revert-delimiter",a.agenticCheckpointDelimiter="agentic-checkpoint-delimiter",a.exchange="exchange",a))(H||{});function O(a){return!!a&&(a.chatItemType===void 0||a.chatItemType==="agent-onboarding")}function se(a){return O(a)&&a.status==="success"}function ae(a){return a.chatItemType==="agentic-checkpoint-delimiter"}class W{constructor(e,t,i,r=5,o=4e3,l){s(this,"_isCancelled",!1);this.requestId=e,this.chatMessage=t,this.startStreamFn=i,this.maxRetries=r,this.baseDelay=o,this.flags=l}cancel(){this._isCancelled=!0}async*getStream(){let e=0,t=!1;try{for(;!this._isCancelled;){const i=this.startStreamFn(this.chatMessage,this.flags?{flags:this.flags}:void 0);let r=!1,o="";for await(const d of i){if(d.status===u.failed){if(d.isRetriable!==!0||t)return yield d;r=!0,o=d.display_error_message||"Service is currently unavailable";break}t=!0,yield d}if(!r)return;if(this._isCancelled)return yield this.createCancelledStatus();if(e++,e>this.maxRetries)return void(yield{request_id:this.requestId,seen_state:g.unseen,status:u.failed,display_error_message:o,isRetriable:!1});const l=this.baseDelay*2**(e-1);yield{request_id:this.requestId,status:u.sent,display_error_message:`Service temporarily unavailable. Retrying in ${l/1e3} seconds... (Attempt ${e} of ${this.maxRetries})`,isRetriable:!0},await new Promise(d=>setTimeout(d,l)),yield{request_id:this.requestId,status:u.sent,display_error_message:"Generating response...",isRetriable:!0}}this._isCancelled&&(yield this.createCancelledStatus())}catch(i){yield{request_id:this.requestId,seen_state:g.unseen,status:u.failed,display_error_message:i instanceof Error?i.message:String(i)}}}createCancelledStatus(){return{request_id:this.requestId,seen_state:g.unseen,status:u.cancelled}}}function m(a,e){return e in a&&a[e]!==void 0}function G(a){return m(a,"file")}function U(a){return m(a,"recentFile")}function V(a){return m(a,"folder")}function B(a){return m(a,"sourceFolder")}function ie(a){return m(a,"selection")}function j(a){return m(a,"externalSource")}function ne(a){return m(a,"allDefaultContext")}function re(a){return m(a,"clearContext")}function oe(a){return m(a,"userGuidelines")}function $(a){return m(a,"personality")}const le={allDefaultContext:!0,label:"Default Context",id:"allDefaultContext"},de={clearContext:!0,label:"Clear Context",id:"clearContext"},ce={userGuidelines:{enabled:!1,overLimit:!1,contents:"",lengthLimit:2e3},label:"User Guidelines",id:"userGuidelines"},ue={agentMemories:{},label:"Agent Memories",id:"agentMemories"},R=[{personality:{type:p.DEFAULT,description:"Expert software engineer - trusted coding agent, at your service!"},label:"Agent Auggie",name:"auggie-personality-agent-default",id:"auggie-personality-agent-default"},{personality:{type:p.PROTOTYPER,description:"Fast and loose - let's get it done, boss!"},label:"Prototyper Auggie",name:"auggie-personality-prototyper",id:"auggie-personality-prototyper"},{personality:{type:p.BRAINSTORM,description:"Thoughtful and creative - thinking through all possibilities..."},label:"Brainstorm Auggie",name:"auggie-personality-brainstorm",id:"auggie-personality-brainstorm"},{personality:{type:p.REVIEWER,description:"Code detective - finding issues and analyzing implications"},label:"Reviewer Auggie",name:"auggie-personality-reviewer",id:"auggie-personality-reviewer"}];function he(a){return m(a,"group")}function ge(a){const e=new Map;return a.forEach(t=>{G(t)?e.set("file",[...e.get("file")??[],t]):U(t)?e.set("recentFile",[...e.get("recentFile")??[],t]):V(t)?e.set("folder",[...e.get("folder")??[],t]):j(t)?e.set("externalSource",[...e.get("externalSource")??[],t]):B(t)?e.set("sourceFolder",[...e.get("sourceFolder")??[],t]):$(t)&&e.set("personality",[...e.get("personality")??[],t])}),[{label:"Personalities",id:"personalities",group:{type:"personality",materialIcon:"person",items:e.get("personality")??[]}},{label:"Files",id:"files",group:{type:"file",materialIcon:"insert_drive_file",items:e.get("file")??[]}},{label:"Folders",id:"folders",group:{type:"folder",materialIcon:"folder",items:e.get("folder")??[]}},{label:"Source Folders",id:"sourceFolders",group:{type:"sourceFolder",materialIcon:"folder_managed",items:e.get("sourceFolder")??[]}},{label:"Recently Opened Files",id:"recentlyOpenedFiles",group:{type:"recentFile",materialIcon:"insert_drive_file",items:e.get("recentFile")??[]}},{label:"Documentation",id:"externalSources",group:{type:"externalSource",materialIcon:"link",items:e.get("externalSource")??[]}}].filter(t=>t.group.items.length>0)}function z(a){const e={label:N(a.pathName).split("/").filter(t=>t.trim()!=="").pop()||"",name:a.pathName,id:L({rootPath:a.repoRoot,relPath:a.pathName})};if(a.fullRange){const t=`:L${a.fullRange.startLineNumber}-${a.fullRange.endLineNumber}`;e.label+=t,e.name+=t,e.id+=t}else if(a.range){const t=`:L${a.range.start}-${a.range.stop}`;e.label+=t,e.name+=t,e.id+=t}return e}class me{constructor(e,t,i){s(this,"getChatInitData",async()=>(await this._asyncMsgSender.send({type:n.chatLoaded},3e4)).data);s(this,"reportWebviewClientEvent",e=>{this._asyncMsgSender.send({type:n.reportWebviewClientMetric,data:{webviewName:E.chat,client_metric:e,value:1}})});s(this,"reportAgentSessionEvent",e=>{this._asyncMsgSender.sendToSidecar({type:h.reportAgentSessionEvent,data:e})});s(this,"reportAgentRequestEvent",e=>{this._asyncMsgSender.sendToSidecar({type:h.reportAgentRequestEvent,data:e})});s(this,"getSuggestions",async(e,t=!1)=>{const i={rootPath:"",relPath:e},r=this.findFiles(i,6),o=this.findRecentlyOpenedFiles(i,6),l=this.findFolders(i,3),d=this.findExternalSources(e,t),[y,w,P,A]=await Promise.all([_(r,[]),_(o,[]),_(l,[]),_(d,[])]),b=(c,F)=>({...z(c),[F]:c}),M=[...y.map(c=>b(c,"file")),...P.map(c=>b(c,"folder")),...w.map(c=>b(c,"recentFile")),...A.map(c=>({label:c.name,name:c.name,id:c.id,externalSource:c}))];if(this._flags.enablePersonalities){const c=this.getPersonalities(e);c.length>0&&M.push(...c)}return M});s(this,"getPersonalities",e=>{if(!this._flags.enablePersonalities)return[];if(e==="")return R;const t=e.toLowerCase();return R.filter(i=>{const r=i.personality.description.toLowerCase(),o=i.label.toLowerCase();return r.includes(t)||o.includes(t)})});s(this,"sendAction",e=>{this._host.postMessage({type:n.mainPanelPerformAction,data:e})});s(this,"showAugmentPanel",()=>{this._asyncMsgSender.send({type:n.showAugmentPanel})});s(this,"openConfirmationModal",async e=>(await this._asyncMsgSender.send({type:n.openConfirmationModal,data:e},1e9)).data.ok);s(this,"clearMetadataFor",e=>{this._host.postMessage({type:n.chatClearMetadata,data:e})});s(this,"resolvePath",async(e,t=void 0)=>{const i=await this._asyncMsgSender.send({type:n.resolveFileRequest,data:{...e,exactMatch:!0,maxResults:1,searchScope:t}},5e3);if(i.data)return i.data});s(this,"resolveSymbols",async(e,t)=>(await this._asyncMsgSender.send({type:n.findSymbolRequest,data:{query:e,searchScope:t}},3e4)).data);s(this,"getDiagnostics",async()=>(await this._asyncMsgSender.send({type:n.getDiagnosticsRequest},1e3)).data);s(this,"findFiles",async(e,t=12)=>(await this._asyncMsgSender.send({type:n.findFileRequest,data:{...e,maxResults:t}},5e3)).data);s(this,"findFolders",async(e,t=12)=>(await this._asyncMsgSender.send({type:n.findFolderRequest,data:{...e,maxResults:t}},5e3)).data);s(this,"findRecentlyOpenedFiles",async(e,t=12)=>(await this._asyncMsgSender.send({type:n.findRecentlyOpenedFilesRequest,data:{...e,maxResults:t}},5e3)).data);s(this,"findExternalSources",async(e,t=!1)=>this._flags.enableExternalSourcesInChat?t?[]:(await this._asyncMsgSender.send({type:n.findExternalSourcesRequest,data:{query:e,source_types:[]}},5e3)).data.sources??[]:[]);s(this,"openFile",e=>{this._host.postMessage({type:n.openFile,data:e})});s(this,"saveFile",e=>this._host.postMessage({type:n.saveFile,data:e}));s(this,"loadFile",e=>this._host.postMessage({type:n.loadFile,data:e}));s(this,"openMemoriesFile",()=>{this._host.postMessage({type:n.openMemoriesFile})});s(this,"createFile",(e,t)=>{this._host.postMessage({type:n.chatCreateFile,data:{code:e,relPath:t}})});s(this,"openScratchFile",async(e,t="shellscript")=>{await this._asyncMsgSender.send({type:n.openScratchFileRequest,data:{content:e,language:t}},1e4)});s(this,"resolveWorkspaceFileChunk",async e=>{try{return(await this._asyncMsgSender.send({type:n.resolveWorkspaceFileChunkRequest,data:e},5e3)).data}catch{return}});s(this,"smartPaste",e=>{this._host.postMessage({type:n.chatSmartPaste,data:e})});s(this,"saveChat",async(e,t,i)=>this._asyncMsgSender.send({type:n.saveChat,data:{conversationId:e,chatHistory:t,title:i}}));s(this,"launchAutofixPanel",async(e,t,i)=>this._asyncMsgSender.send({type:n.chatLaunchAutofixPanel,data:{conversationId:e,iterationId:t,stage:i}}));s(this,"updateUserGuidelines",e=>{this._host.postMessage({type:n.updateUserGuidelines,data:e})});s(this,"updateWorkspaceGuidelines",e=>{this._host.postMessage({type:n.updateWorkspaceGuidelines,data:e})});s(this,"openSettingsPage",e=>{this._host.postMessage({type:n.openSettingsPage,data:e})});s(this,"_activeRetryStreams",new Map);s(this,"cancelChatStream",async e=>{var t;(t=this._activeRetryStreams.get(e))==null||t.cancel(),await this._asyncMsgSender.send({type:n.chatUserCancel,data:{requestId:e}},1e4)});s(this,"sendUserRating",async(e,t,i,r="")=>{const o={requestId:e,rating:i,note:r,mode:t},l={type:n.chatRating,data:o};return(await this._asyncMsgSender.send(l,3e4)).data});s(this,"triggerUsedChatMetric",()=>{this._host.postMessage({type:n.usedChat})});s(this,"createProject",e=>{this._host.postMessage({type:n.mainPanelCreateProject,data:{name:e}})});s(this,"openProjectFolder",()=>{this._host.postMessage({type:n.mainPanelPerformAction,data:"open-folder"})});s(this,"closeProjectFolder",()=>{this._host.postMessage({type:n.mainPanelPerformAction,data:"close-folder"})});s(this,"cloneRepository",()=>{this._host.postMessage({type:n.mainPanelPerformAction,data:"clone-repository"})});s(this,"grantSyncPermission",()=>{this._host.postMessage({type:n.mainPanelPerformAction,data:"grant-sync-permission"})});s(this,"callTool",async(e,t,i,r,o,l)=>{const d={type:n.callTool,data:{chatRequestId:e,toolUseId:t,name:i,input:r,chatHistory:o,conversationId:l}};return(await this._asyncMsgSender.send(d,0)).data});s(this,"cancelToolRun",async(e,t)=>{const i={type:n.cancelToolRun,data:{requestId:e,toolUseId:t}};await this._asyncMsgSender.send(i,0)});s(this,"checkSafe",async(e,t)=>{const i={type:n.toolCheckSafe,data:{name:e,input:t}};return(await this._asyncMsgSender.send(i,0)).data.isSafe});s(this,"closeAllToolProcesses",async()=>{await this._asyncMsgSender.sendToSidecar({type:S.closeAllToolProcesses},0)});s(this,"getToolIdentifier",async e=>{const t={type:S.getToolIdentifierRequest,data:{toolName:e}};return(await this._asyncMsgSender.sendToSidecar(t,0)).data});s(this,"executeCommand",async(e,t,i)=>{try{const r=await this._asyncMsgSender.send({type:n.chatAutofixExecuteCommandRequest,data:{iterationId:e,command:t,args:i}},6e5);return{output:r.data.output,returnCode:r.data.returnCode}}catch(r){throw console.error("[ExtensionClient] Execute command failed:",r),r}});s(this,"sendAutofixStateUpdate",async e=>{await this._asyncMsgSender.send({type:n.chatAutofixStateUpdate,data:e})});s(this,"autofixPlan",async(e,t)=>(await this._asyncMsgSender.send({type:n.chatAutofixPlanRequest,data:{command:e,steeringHistory:t}},6e4)).data.plan);s(this,"setChatMode",e=>{this._asyncMsgSender.send({type:n.chatModeChanged,data:{mode:e}})});s(this,"getAgentEditList",async(e,t)=>{const i={type:h.getEditListRequest,data:{fromTimestamp:e,toTimestamp:t}};return(await this._asyncMsgSender.sendToSidecar(i,3e4)).data});s(this,"hasChangesSince",async e=>{const t={type:h.getEditListRequest,data:{fromTimestamp:e,toTimestamp:Number.MAX_SAFE_INTEGER}};return(await this._asyncMsgSender.sendToSidecar(t,3e4)).data.edits.filter(i=>{var r,o;return((r=i.changesSummary)==null?void 0:r.totalAddedLines)||((o=i.changesSummary)==null?void 0:o.totalRemovedLines)}).length>0});s(this,"getToolCallCheckpoint",async e=>{const t={type:n.getToolCallCheckpoint,data:{requestId:e}};return(await this._asyncMsgSender.send(t,3e4)).data.checkpointNumber});s(this,"setCurrentConversation",e=>{this._asyncMsgSender.sendToSidecar({type:h.setCurrentConversation,data:{conversationId:e}})});s(this,"showAgentReview",(e,t,i,r=!0)=>{this._asyncMsgSender.sendToSidecar({type:h.chatReviewAgentFile,data:{qualifiedPathName:e,fromTimestamp:t,toTimestamp:i,retainFocus:r}})});s(this,"acceptAllAgentEdits",async()=>(await this._asyncMsgSender.sendToSidecar({type:h.chatAgentEditAcceptAll}),!0));s(this,"revertToTimestamp",async(e,t)=>(await this._asyncMsgSender.sendToSidecar({type:h.revertToTimestamp,data:{timestamp:e,qualifiedPathNames:t}}),!0));s(this,"getAgentOnboardingPrompt",async()=>(await this._asyncMsgSender.send({type:n.chatGetAgentOnboardingPromptRequest,data:{}},3e4)).data.prompt);s(this,"getAgentEditChangesByRequestId",async e=>{const t={type:h.getEditChangesByRequestIdRequest,data:{requestId:e}};return(await this._asyncMsgSender.sendToSidecar(t,3e4)).data});s(this,"getAgentEditContentsByRequestId",async e=>{const t={type:h.getAgentEditContentsByRequestId,data:{requestId:e}};return(await this._asyncMsgSender.sendToSidecar(t,3e4)).data});s(this,"triggerInitialOrientation",()=>{this._host.postMessage({type:n.triggerInitialOrientation})});s(this,"getWorkspaceInfo",async()=>{try{return(await this._asyncMsgSender.send({type:n.getWorkspaceInfoRequest},5e3)).data}catch(e){return console.error("Error getting workspace info:",e),{}}});s(this,"getRemoteAgentStatus",async()=>{try{return(await this._asyncMsgSender.send({type:n.getRemoteAgentStatus},5e3)).data}catch(e){return console.error("Error getting remote agent status:",e),{isRemoteAgentWindow:!1}}});s(this,"toggleCollapseUnchangedRegions",()=>{this._host.postMessage({type:n.toggleCollapseUnchangedRegions})});s(this,"checkAgentAutoModeApproval",async()=>(await this._asyncMsgSender.send({type:n.checkAgentAutoModeApproval},5e3)).data);s(this,"setAgentAutoModeApproved",async e=>{await this._asyncMsgSender.send({type:n.setAgentAutoModeApproved,data:e},5e3)});s(this,"checkHasEverUsedAgent",async()=>(await this._asyncMsgSender.sendToSidecar({type:h.checkHasEverUsedAgent},5e3)).data);s(this,"setHasEverUsedAgent",async e=>{await this._asyncMsgSender.sendToSidecar({type:h.setHasEverUsedAgent,data:e},5e3)});s(this,"getChatRequestIdeState",async()=>{const e={type:n.getChatRequestIdeStateRequest};return(await this._asyncMsgSender.send(e,3e4)).data});s(this,"reportError",e=>{this._host.postMessage({type:n.reportError,data:e})});this._host=e,this._asyncMsgSender=t,this._flags=i}async*generateCommitMessage(){const e={type:n.generateCommitMessage},t=this._asyncMsgSender.stream(e,3e4,6e4);yield*f(t)}async*sendInstructionMessage(e,t){const i={instruction:e.request_message??"",selectedCodeDetails:t,requestId:e.request_id},r={type:n.chatInstructionMessage,data:i},o=this._asyncMsgSender.stream(r,3e4,6e4);yield*async function*(l){let d;try{for await(const y of l)d=y.data.requestId,yield{request_id:d,response_text:y.data.text,seen_state:g.unseen,status:u.sent};yield{request_id:d,seen_state:g.unseen,status:u.success}}catch{yield{request_id:d,seen_state:g.unseen,status:u.failed}}}(o)}async openGuidelines(e){this._host.postMessage({type:n.openGuidelines,data:e})}async*getExistingChatStream(e,t){if(!e.request_id)return;const i=t==null?void 0:t.flags.enablePreferenceCollection,r=i?1e9:6e4,o=i?1e9:3e5,l={type:n.chatGetStreamRequest,data:{requestId:e.request_id}},d=this._asyncMsgSender.stream(l,r,o);yield*f(d,this.reportError)}async*startChatStream(e,t){const i=t==null?void 0:t.flags.enablePreferenceCollection,r=i?1e9:6e4,o=i?1e9:3e5,l={type:n.chatUserMessage,data:e},d=this._asyncMsgSender.stream(l,r,o);yield*f(d,this.reportError)}async checkToolExists(e){return(await this._asyncMsgSender.send({type:n.checkToolExists,toolName:e},0)).exists}async saveImage(e,t){const i=x(await C(e)),r=t??`${await D(await T(i))}.${e.name.split(".").at(-1)}`;return(await this._asyncMsgSender.send({type:n.chatSaveImageRequest,data:{filename:r,data:i}},1e4)).data}async loadImage(e){const t=await this._asyncMsgSender.send({type:n.chatLoadImageRequest,data:e},1e4),i=t.data?await T(t.data):void 0;if(!i)return;let r="application/octet-stream";const o=e.split(".").at(-1);o==="png"?r="image/png":o!=="jpg"&&o!=="jpeg"||(r="image/jpeg");const l=new File([i],e,{type:r});return await C(l)}async deleteImage(e){await this._asyncMsgSender.send({type:n.chatDeleteImageRequest,data:e},1e4)}async*startChatStreamWithRetry(e,t,i){const r=new W(e,t,(o,l)=>this.startChatStream(o,l),(i==null?void 0:i.maxRetries)??5,4e3,i==null?void 0:i.flags);this._activeRetryStreams.set(e,r);try{yield*r.getStream()}finally{this._activeRetryStreams.delete(e)}}}async function*f(a,e=()=>{}){let t;try{for await(const i of a){if(t=i.data.requestId,i.data.error)return yield{request_id:t,seen_state:g.unseen,status:u.failed,display_error_message:i.data.error.displayErrorMessage,isRetriable:i.data.error.isRetriable};yield{request_id:t,response_text:i.data.text,workspace_file_chunks:i.data.workspaceFileChunks,structured_output_nodes:X(i.data.nodes),seen_state:g.unseen,status:u.sent}}yield{request_id:t,seen_state:g.unseen,status:u.success}}catch(i){e({originalRequestId:t||"",sanitizedMessage:i instanceof Error?i.message:String(i),stackTrace:i instanceof Error&&i.stack||"",diagnostics:[{key:"error_class",value:"Extension-WebView Error"}]}),yield{request_id:t,seen_state:g.unseen,status:u.failed}}}async function _(a,e){try{return await a}catch(t){return console.warn(`Error while resolving promise: ${t}`),e}}function X(a){if(!a)return a;let e=!1;return a.filter(t=>t.type!==q.TOOL_USE||!e&&(e=!0,!0))}const ye=15,pe=1e3,Y=25e4;class _e{constructor(e){s(this,"_enableEditableHistory",!1);s(this,"_enablePreferenceCollection",!1);s(this,"_enableRetrievalDataCollection",!1);s(this,"_enableDebugFeatures",!1);s(this,"_enableRichTextHistory",!1);s(this,"_modelDisplayNameToId",{});s(this,"_fullFeatured",!0);s(this,"_enableExternalSourcesInChat",!1);s(this,"_smallSyncThreshold",15);s(this,"_bigSyncThreshold",1e3);s(this,"_enableSmartPaste",!1);s(this,"_enableDirectApply",!1);s(this,"_summaryTitles",!1);s(this,"_suggestedEditsAvailable",!1);s(this,"_enableShareService",!1);s(this,"_maxTrackableFileCount",Y);s(this,"_enableDesignSystemRichTextEditor",!1);s(this,"_enableSources",!1);s(this,"_enableChatMermaidDiagrams",!1);s(this,"_smartPastePrecomputeMode",k.visibleHover);s(this,"_useNewThreadsMenu",!1);s(this,"_enableChatMermaidDiagramsMinVersion",!1);s(this,"_enablePromptEnhancer",!1);s(this,"_idleNewSessionNotificationTimeoutMs");s(this,"_idleNewSessionMessageTimeoutMs");s(this,"_enableChatMultimodal",!1);s(this,"_enableAgentMode",!1);s(this,"_enableRichCheckpointInfo",!1);s(this,"_agentMemoriesFilePathName");s(this,"_userTier","unknown");s(this,"_eloModelConfiguration",{highPriorityModels:[],regularBattleModels:[],highPriorityThreshold:.5});s(this,"_truncateChatHistory",!1);s(this,"_enableBackgroundAgents",!1);s(this,"_enableVirtualizedMessageList",!1);s(this,"_customPersonalityPrompts",{});s(this,"_enablePersonalities",!1);s(this,"_memoryClassificationOnFirstToken",!1);s(this,"_isRemoteAgentWindow",!1);s(this,"_remoteAgentId");s(this,"_enableGenerateCommitMessage",!1);s(this,"_modelRegistry",{});s(this,"_enableModelRegistry",!1);s(this,"_subscribers",new Set);s(this,"subscribe",e=>(this._subscribers.add(e),e(this),()=>{this._subscribers.delete(e)}));s(this,"update",e=>{this._enableEditableHistory=e.enableEditableHistory??this._enableEditableHistory,this._enablePreferenceCollection=e.enablePreferenceCollection??this._enablePreferenceCollection,this._enableRetrievalDataCollection=e.enableRetrievalDataCollection??this._enableRetrievalDataCollection,this._enableDebugFeatures=e.enableDebugFeatures??this._enableDebugFeatures,this._enableRichTextHistory=e.enableRichTextHistory??this._enableRichTextHistory,this._modelDisplayNameToId={...e.modelDisplayNameToId},this._fullFeatured=e.fullFeatured??this._fullFeatured,this._enableExternalSourcesInChat=e.enableExternalSourcesInChat??this._enableExternalSourcesInChat,this._smallSyncThreshold=e.smallSyncThreshold??this._smallSyncThreshold,this._bigSyncThreshold=e.bigSyncThreshold??this._bigSyncThreshold,this._enableSmartPaste=e.enableSmartPaste??this._enableSmartPaste,this._enableDirectApply=e.enableDirectApply??this._enableDirectApply,this._summaryTitles=e.summaryTitles??this._summaryTitles,this._suggestedEditsAvailable=e.suggestedEditsAvailable??this._suggestedEditsAvailable,this._enableShareService=e.enableShareService??this._enableShareService,this._maxTrackableFileCount=e.maxTrackableFileCount??this._maxTrackableFileCount,this._enableDesignSystemRichTextEditor=e.enableDesignSystemRichTextEditor??this._enableDesignSystemRichTextEditor,this._enableSources=e.enableSources??this._enableSources,this._enableChatMermaidDiagrams=e.enableChatMermaidDiagrams??this._enableChatMermaidDiagrams,this._smartPastePrecomputeMode=e.smartPastePrecomputeMode??this._smartPastePrecomputeMode,this._useNewThreadsMenu=e.useNewThreadsMenu??this._useNewThreadsMenu,this._enableChatMermaidDiagramsMinVersion=e.enableChatMermaidDiagramsMinVersion??this._enableChatMermaidDiagramsMinVersion,this._enablePromptEnhancer=e.enablePromptEnhancer??this._enablePromptEnhancer,this._idleNewSessionMessageTimeoutMs=e.idleNewSessionMessageTimeoutMs??(e.enableDebugFeatures?this._idleNewSessionMessageTimeoutMs??3e5:this._idleNewSessionMessageTimeoutMs),this._idleNewSessionNotificationTimeoutMs=e.idleNewSessionNotificationTimeoutMs??0,this._enableChatMultimodal=e.enableChatMultimodal??this._enableChatMultimodal,this._enableAgentMode=e.enableAgentMode??this._enableAgentMode,this._enableRichCheckpointInfo=e.enableRichCheckpointInfo??this._enableRichCheckpointInfo,this._agentMemoriesFilePathName=e.agentMemoriesFilePathName??this._agentMemoriesFilePathName,this._userTier=e.userTier??this._userTier,this._eloModelConfiguration=e.eloModelConfiguration??this._eloModelConfiguration,this._truncateChatHistory=e.truncateChatHistory??this._truncateChatHistory,this._enableBackgroundAgents=e.enableBackgroundAgents??this._enableBackgroundAgents,this._enableVirtualizedMessageList=e.enableVirtualizedMessageList??this._enableVirtualizedMessageList,this._customPersonalityPrompts=e.customPersonalityPrompts??this._customPersonalityPrompts,this._enablePersonalities=e.enablePersonalities??this._enablePersonalities,this._memoryClassificationOnFirstToken=e.memoryClassificationOnFirstToken??this._memoryClassificationOnFirstToken,this._isRemoteAgentWindow=e.isRemoteAgentWindow??this._isRemoteAgentWindow,this._remoteAgentId=e.remoteAgentId??this._remoteAgentId,this._enableGenerateCommitMessage=e.enableGenerateCommitMessage??this._enableGenerateCommitMessage,this._modelRegistry=e.modelRegistry??this._modelRegistry,this._enableModelRegistry=e.enableModelRegistry??this._enableModelRegistry,this._subscribers.forEach(t=>t(this))});s(this,"isModelIdValid",e=>e!==void 0&&(Object.values(this._modelDisplayNameToId).includes(e)||Object.values(this._modelRegistry).includes(e??"")));s(this,"getModelDisplayName",e=>{if(e!==void 0)return Object.keys(this._modelDisplayNameToId).find(t=>this._modelDisplayNameToId[t]===e)});e&&this.update(e)}get enableEditableHistory(){return this._fullFeatured&&(this._enableEditableHistory||this._enableDebugFeatures)}get enablePreferenceCollection(){return this._enablePreferenceCollection}get enableRetrievalDataCollection(){return this._enableRetrievalDataCollection}get enableDebugFeatures(){return this._enableDebugFeatures}get enableGenerateCommitMessage(){return this._enableGenerateCommitMessage}get enableRichTextHistory(){return this._enableRichTextHistory}get modelDisplayNameToId(){return this._modelDisplayNameToId}get orderedModelDisplayNames(){return Object.keys(this._modelDisplayNameToId).sort((e,t)=>{const i=e.toLowerCase(),r=t.toLowerCase();return i==="default"&&r!=="default"?-1:r==="default"&&i!=="default"?1:e.localeCompare(t)})}get fullFeatured(){return this._fullFeatured}get enableExternalSourcesInChat(){return this._enableExternalSourcesInChat}get smallSyncThreshold(){return this._smallSyncThreshold}get bigSyncThreshold(){return this._bigSyncThreshold}get enableSmartPaste(){return this._enableDebugFeatures||this._enableSmartPaste}get enableDirectApply(){return this._enableDirectApply||this._enableDebugFeatures}get enableShareService(){return this._enableShareService}get summaryTitles(){return this._summaryTitles}get suggestedEditsAvailable(){return this._suggestedEditsAvailable}get maxTrackableFileCount(){return this._maxTrackableFileCount}get enableSources(){return this._enableDebugFeatures||this._enableSources}get enableChatMermaidDiagrams(){return this._enableDebugFeatures||this._enableChatMermaidDiagrams}get smartPastePrecomputeMode(){return this._smartPastePrecomputeMode}get useNewThreadsMenu(){return this._useNewThreadsMenu}get enableChatMermaidDiagramsMinVersion(){return this._enableChatMermaidDiagramsMinVersion}get enablePromptEnhancer(){return this._enablePromptEnhancer}get enableDesignSystemRichTextEditor(){return this._enableDesignSystemRichTextEditor}get idleNewSessionNotificationTimeoutMs(){return this._idleNewSessionNotificationTimeoutMs??0}get idleNewSessionMessageTimeoutMs(){return this._idleNewSessionMessageTimeoutMs??0}get enableChatMultimodal(){return this._enableChatMultimodal}get enableAgentMode(){return this._enableAgentMode}get enableRichCheckpointInfo(){return this._enableRichCheckpointInfo}get agentMemoriesFilePathName(){return this._agentMemoriesFilePathName}get userTier(){return this._userTier}get eloModelConfiguration(){return this._eloModelConfiguration}get truncateChatHistory(){return this._truncateChatHistory}get enableBackgroundAgents(){return this._enableBackgroundAgents}get enableVirtualizedMessageList(){return this._enableVirtualizedMessageList||this._enableDebugFeatures}get customPersonalityPrompts(){return this._customPersonalityPrompts}get enablePersonalities(){return this._enablePersonalities||this._enableDebugFeatures}get memoryClassificationOnFirstToken(){return this._memoryClassificationOnFirstToken}get isRemoteAgentWindow(){return this._isRemoteAgentWindow}get remoteAgentId(){return this._remoteAgentId}get modelRegistry(){return this._modelRegistry}get enableModelRegistry(){return this._enableModelRegistry}}export{ue as A,_e as C,ye as D,me as E,g as S,le as U,se as a,u as b,ae as c,H as d,$ as e,pe as f,Y as g,G as h,O as i,U as j,ie as k,V as l,B as m,j as n,oe as o,z as p,ge as q,he as r,te as s,ne as t,de as u,ce as v,re as w};
