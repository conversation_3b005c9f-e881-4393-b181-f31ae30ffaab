var Ss=Object.defineProperty;var Rt=r=>{throw TypeError(r)};var Ns=(r,e,t)=>e in r?Ss(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t;var se=(r,e,t)=>Ns(r,typeof e!="symbol"?e+"":e,t),Rs=(r,e,t)=>e.has(r)||Rt("Cannot "+t);var Pt=(r,e,t)=>e.has(r)?Rt("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(r):e.set(r,t);var st=(r,e,t)=>(Rs(r,e,"access private method"),t);import{ah as Ie,S as Y,i as X,s as J,J as _,c as A,L as $e,e as x,f as L,n as G,h as v,Q as Ae,a0 as U,a1 as de,V as M,W as T,u as h,t as g,Z as S,T as Z,a8 as me,q as W,r as Q,as,ad as be,_ as cs,a2 as Xe,ae as ve,D as Ce,au as ot,R as Oe,Y as Ve,ag as ds,$ as fe,aa as Ps,ab as It,b as ps,I as De,a as fs,aB as lt,M as Fe,N as ke,O as xe,g as hs,aA as Ot,aC as Is,aD as Vt,P as Ye,A as gs,G as Os,an as xt,ac as vt,a9 as Vs,al as bt}from"./SpinnerAugment-BUJasFTo.js";import"./design-system-init-BKdwvVur.js";import{g as je,p as Zs,a as qs}from"./index-BkU7QRx6.js";import"./design-system-init-e2yaG07g.js";import{W as We,h as $s}from"./BaseButton-ci_067e0.js";import{T as qe,M as Us}from"./TextTooltipAugment-UDQF2J4S.js";import{e as ne,u as js,o as Hs}from"./each-DUdYBCJG.js";import{s as Zt}from"./index-DlpZFSR-.js";import{h as ut,p as Ws,M as Qs,f as ms,e as Ds,b as Gs,i as Js,j as Ys}from"./diff-utils-BYhHYFY1.js";import{a as Et,g as Fs,S as Xs,M as Ks}from"./index-B-fP3g4F.js";import{I as ks,A as ei}from"./IconButtonAugment-DFy7vWkh.js";import{V as Ke}from"./VSCodeCodicon-CzBgPB9u.js";import{B as Le}from"./ButtonAugment-DbAwCSeR.js";import{M as gt}from"./MaterialIcon-d9y4vLnQ.js";import{P as et}from"./play-Dd7ujDDf.js";import{n as xs,a as Ne,g as at}from"./file-paths-BcSg4gks.js";import{T as _t}from"./Content-CSmc2GUv.js";import{g as ti}from"./globals-D0QH3NT1.js";import{E as ni}from"./expand-CE2AcHxk.js";import{E as vs}from"./exclamation-triangle-uzqmF3G7.js";import"./toggleHighContrast-Th-X2FgN.js";import{F as si}from"./types-CF53Ux0u.js";import{L as ii}from"./LanguageIcon-DPvfnfyG.js";import"./toggleHighContrast-CwIv4U26.js";import"./preload-helper-Dv6uf1Os.js";class ct{constructor(e){se(this,"_opts",null);se(this,"_subscribers",new Set);this._asyncMsgSender=e}subscribe(e){return this._subscribers.add(e),e(this),()=>{this._subscribers.delete(e)}}notifySubscribers(){this._subscribers.forEach(e=>e(this))}get opts(){return this._opts}updateOpts(e){this._opts=e,this.notifySubscribers()}async onPanelLoaded(){try{this.updateOpts(null);const e=await this._asyncMsgSender.send({type:We.remoteAgentDiffPanelLoaded});this.updateOpts(e.data)}catch(e){console.error("Failed to load diff panel:",e),this.updateOpts(null)}}handleMessageFromExtension(e){const t=e.data;return!(!t||!t.type)&&t.type===We.remoteAgentDiffPanelSetOpts&&(this.updateOpts(t.data),!0)}}se(ct,"key","remoteAgentDiffModel");class dt{constructor(e){se(this,"_applyingFilePaths",Ie([]));se(this,"_appliedFilePaths",Ie([]));this._asyncMsgSender=e}get applyingFilePaths(){let e=[];return this._applyingFilePaths.subscribe(t=>{e=t})(),e}get appliedFilePaths(){let e=[];return this._appliedFilePaths.subscribe(t=>{e=t})(),e}async getDiffExplanation(e,t,n=3e4){try{return(await this._asyncMsgSender.send({type:We.diffExplanationRequest,data:{changedFiles:e,apikey:t}},n)).data.explanation}catch(s){return console.error("Failed to get diff explanation:",s),[]}}async groupChanges(e,t=!1,n){try{return(await this._asyncMsgSender.send({type:We.diffGroupChangesRequest,data:{changedFiles:e,changesById:t,apikey:n}})).data.groupedChanges}catch(s){return console.error("Failed to group changes:",s),[]}}async getDescriptions(e,t){try{return(await this._asyncMsgSender.send({type:We.diffDescriptionsRequest,data:{groupedChanges:e,apikey:t}})).data.explanation}catch(n){return console.error("Failed to get descriptions:",n),[]}}async applyChanges(e,t,n){this._applyingFilePaths.update(s=>[...s.filter(i=>i!==e),e]);try{(await this._asyncMsgSender.send({type:We.applyChangesRequest,data:{path:e,originalCode:t,newCode:n}})).data.success&&this._appliedFilePaths.update(s=>[...s.filter(i=>i!==e),e])}catch(s){console.error("applyChanges error",s)}finally{this._applyingFilePaths.update(s=>s.filter(i=>i!==e))}}handleMessageFromExtension(e){return!1}}se(dt,"key","remoteAgentsDiffOpsModel");function qt(r){let e=0;for(let t=0;t<r.length;t++)e=(e<<5)-e+r.charCodeAt(t),e|=0;return Math.abs(e).toString(36)}function Ut(r,e,t,n,s={}){const{context:i=3,generateId:o=!0}=s,l=ut(r,e,t,n,"","",{context:i}),u=e||r;let a;return o?a=`${qt(u)}-${qt(t+n)}`:a=Math.random().toString(36).substring(2,15),{id:a,path:u,diff:l,originalCode:t,modifiedCode:n}}function Ct(r){const e=r.split(`
`);return{additions:e.filter(t=>t.startsWith("+")).length,deletions:e.filter(t=>t.startsWith("-")).length}}function Cs(r){return!r.originalCode||r.originalCode.trim()===""}class ri{static generateDiff(e,t,n,s){return Ut(e,t,n,s)}static generateDiffs(e){return function(t,n={}){return t.map(s=>Ut(s.oldPath,s.newPath,s.oldContent,s.newContent,n))}(e)}static getDiffStats(e){return Ct(e)}static getDiffObjectStats(e){return Ct(e.diff)}static isNewFile(e){return Cs(e)}static isDeletedFile(e){return function(t){return!t.modifiedCode||t.modifiedCode.trim()===""}(e)}}function oi(r){let e;return{c(){e=U(r[1])},m(t,n){x(t,e,n)},p(t,n){2&n&&de(e,t[1])},d(t){t&&v(e)}}}function li(r){let e;return{c(){e=U(r[1])},m(t,n){x(t,e,n)},p(t,n){2&n&&de(e,t[1])},d(t){t&&v(e)}}}function ui(r){let e,t,n;function s(l,u){return l[2]?li:oi}let i=s(r),o=i(r);return{c(){e=_("span"),t=_("code"),o.c(),A(t,"class","markdown-codespan svelte-164mxpf"),A(t,"style",n=r[2]?`background-color: ${r[1]}; color: ${r[3]?"white":"black"}`:""),$e(t,"markdown-string",r[4])},m(l,u){x(l,e,u),L(e,t),o.m(t,null),r[6](e)},p(l,[u]){i===(i=s(l))&&o?o.p(l,u):(o.d(1),o=i(l),o&&(o.c(),o.m(t,null))),14&u&&n!==(n=l[2]?`background-color: ${l[1]}; color: ${l[3]?"white":"black"}`:"")&&A(t,"style",n),16&u&&$e(t,"markdown-string",l[4])},i:G,o:G,d(l){l&&v(e),o.d(),r[6](null)}}}function ai(r,e,t){let n,s,i,o,{token:l}=e,{element:u}=e;return r.$$set=a=>{"token"in a&&t(5,l=a.token),"element"in a&&t(0,u=a.element)},r.$$.update=()=>{32&r.$$.dirty&&t(1,n=l.raw.slice(1,l.raw.length-1)),2&r.$$.dirty&&t(4,s=n.startsWith('"')),2&r.$$.dirty&&t(2,i=/^#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3}/.test(n)),6&r.$$.dirty&&t(3,o=i&&function(a){if(!/^#([0-9A-F]{3}|[0-9A-F]{6})$/i.test(a))throw new Error('Invalid hex color format. Expected "#RGB" or "#RRGGBB"');let c,d,p;return a.length===4?(c=parseInt(a.charAt(1),16),d=parseInt(a.charAt(2),16),p=parseInt(a.charAt(3),16),c*=17,d*=17,p*=17):(c=parseInt(a.slice(1,3),16),d=parseInt(a.slice(3,5),16),p=parseInt(a.slice(5,7),16)),.299*c+.587*d+.114*p<130}(n))},[u,n,i,o,s,l,function(a){Ae[a?"unshift":"push"](()=>{u=a,t(0,u)})}]}let ci=class extends Y{constructor(r){super(),X(this,r,ai,ui,J,{token:5,element:0})}};function di(r){let e,t;return e=new Qs({props:{markdown:r[1](r[0]),renderers:r[2]}}),{c(){M(e.$$.fragment)},m(n,s){T(e,n,s),t=!0},p(n,[s]){const i={};1&s&&(i.markdown=n[1](n[0])),e.$set(i)},i(n){t||(h(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){S(e,n)}}}function pi(r,e,t){let{markdown:n}=e;const s={codespan:ci};return r.$$set=i=>{"markdown"in i&&t(0,n=i.markdown)},[n,i=>i.replace(/`?#[0-9a-fA-F]{3,6}`?/g,o=>o.startsWith("`")?o:`\`${o}\``),s]}let ws=class extends Y{constructor(r){super(),X(this,r,pi,di,J,{markdown:0})}};function jt(r,e,t){const n=r.slice();return n[44]=e[t],n[46]=t,n}function Ht(r){let e,t,n,s,i;t=new ks({props:{variant:"ghost",color:"neutral",size:1,$$slots:{default:[gi]},$$scope:{ctx:r}}}),t.$on("click",r[23]);let o=ne(r[1]),l=[];for(let a=0;a<o.length;a+=1)l[a]=Wt(jt(r,o,a));const u=a=>g(l[a],1,1,()=>{l[a]=null});return{c(){e=_("div"),M(t.$$.fragment),n=Z(),s=_("div");for(let a=0;a<l.length;a+=1)l[a].c();A(e,"class","toggle-button svelte-14s1ghg"),A(s,"class","descriptions svelte-14s1ghg"),me(s,"transform","translateY("+-r[4]+"px)")},m(a,c){x(a,e,c),T(t,e,null),x(a,n,c),x(a,s,c);for(let d=0;d<l.length;d+=1)l[d]&&l[d].m(s,null);i=!0},p(a,c){const d={};if(1&c[0]|65536&c[1]&&(d.$$scope={dirty:c,ctx:a}),t.$set(d),546&c[0]){let p;for(o=ne(a[1]),p=0;p<o.length;p+=1){const D=jt(a,o,p);l[p]?(l[p].p(D,c),h(l[p],1)):(l[p]=Wt(D),l[p].c(),h(l[p],1),l[p].m(s,null))}for(W(),p=o.length;p<l.length;p+=1)u(p);Q()}(!i||16&c[0])&&me(s,"transform","translateY("+-a[4]+"px)")},i(a){if(!i){h(t.$$.fragment,a);for(let c=0;c<o.length;c+=1)h(l[c]);i=!0}},o(a){g(t.$$.fragment,a),l=l.filter(Boolean);for(let c=0;c<l.length;c+=1)g(l[c]);i=!1},d(a){a&&(v(e),v(n),v(s)),S(t),ve(l,a)}}}function fi(r){let e,t;return e=new Ke({props:{icon:"book"}}),{c(){M(e.$$.fragment)},m(n,s){T(e,n,s),t=!0},i(n){t||(h(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){S(e,n)}}}function hi(r){let e,t;return e=new Ke({props:{icon:"x"}}),{c(){M(e.$$.fragment)},m(n,s){T(e,n,s),t=!0},i(n){t||(h(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){S(e,n)}}}function gi(r){let e,t,n,s;const i=[hi,fi],o=[];function l(u,a){return u[0]?0:1}return e=l(r),t=o[e]=i[e](r),{c(){t.c(),n=Ce()},m(u,a){o[e].m(u,a),x(u,n,a),s=!0},p(u,a){let c=e;e=l(u),e!==c&&(W(),g(o[c],1,1,()=>{o[c]=null}),Q(),t=o[e],t||(t=o[e]=i[e](u),t.c()),h(t,1),t.m(n.parentNode,n))},i(u){s||(h(t),s=!0)},o(u){g(t),s=!1},d(u){u&&v(n),o[e].d(u)}}}function Wt(r){let e,t,n,s;return t=new ws({props:{markdown:r[44].text}}),{c(){e=_("div"),M(t.$$.fragment),n=Z(),A(e,"class","description svelte-14s1ghg"),me(e,"top",(r[5][r[46]]||r[9](r[44]))+"px"),me(e,"--ds-panel-solid","transparent")},m(i,o){x(i,e,o),T(t,e,null),L(e,n),s=!0},p(i,o){const l={};2&o[0]&&(l.markdown=i[44].text),t.$set(l),(!s||34&o[0])&&me(e,"top",(i[5][i[46]]||i[9](i[44]))+"px")},i(i){s||(h(t.$$.fragment,i),s=!0)},o(i){g(t.$$.fragment,i),s=!1},d(i){i&&v(e),S(t)}}}function $i(r){let e,t,n,s,i=r[1].length>0&&Ht(r);return{c(){e=_("div"),t=_("div"),n=Z(),i&&i.c(),A(t,"class","editor-container svelte-14s1ghg"),me(t,"height",r[3]+"px"),A(e,"class","monaco-diff-container svelte-14s1ghg"),$e(e,"monaco-diff-container-with-descriptions",r[1].length>0&&r[0])},m(o,l){x(o,e,l),L(e,t),r[22](t),L(e,n),i&&i.m(e,null),s=!0},p(o,l){(!s||8&l[0])&&me(t,"height",o[3]+"px"),o[1].length>0?i?(i.p(o,l),2&l[0]&&h(i,1)):(i=Ht(o),i.c(),h(i,1),i.m(e,null)):i&&(W(),g(i,1,1,()=>{i=null}),Q()),(!s||3&l[0])&&$e(e,"monaco-diff-container-with-descriptions",o[1].length>0&&o[0])},i(o){s||(h(i),s=!0)},o(o){g(i),s=!1},d(o){o&&v(e),r[22](null),i&&i.d()}}}function mi(r,e,t){let n,s,i;const o=as();let{originalCode:l=""}=e,{modifiedCode:u=""}=e,{path:a}=e,{descriptions:c=[]}=e,{lineOffset:d=0}=e,{extraPrefixLines:p=[]}=e,{extraSuffixLines:D=[]}=e,{theme:b}=e,{areDescriptionsVisible:m=!0}=e,{isNewFile:k=!1}=e;const y=Et.getContext().monaco;let w,$,B,f;be(r,y,F=>t(21,n=F));let C,E=[],V=Ie(0);be(r,V,F=>t(4,s=F));let j=k?20*u.split(`
`).length+40:100;const K=n?n.languages.getLanguages().map(F=>F.id):[];function te(F,z){var N,P;if(z){const O=(N=z.split(".").pop())==null?void 0:N.toLowerCase();if(O){const H=(P=n==null?void 0:n.languages.getLanguages().find(ce=>{var ie;return(ie=ce.extensions)==null?void 0:ie.includes("."+O)}))==null?void 0:P.id;if(H&&K.includes(H))return H}}return"plaintext"}const pe=Ie({});be(r,pe,F=>t(5,i=F));let q=null;function oe(){if(!w)return;E=E.filter(N=>(N.dispose(),!1));const F=w.getOriginalEditor(),z=w.getModifiedEditor();E.push(F.onDidScrollChange(()=>{ot(V,s=F.getScrollTop(),s)}),z.onDidScrollChange(()=>{ot(V,s=z.getScrollTop(),s)}))}function ge(){if(!w||!C)return;const F=w.getOriginalEditor(),z=w.getModifiedEditor();E.push(z.onDidContentSizeChange(()=>ae()),F.onDidContentSizeChange(()=>ae()),w.onDidUpdateDiff(()=>ae()),z.onDidChangeHiddenAreas(()=>ae()),F.onDidChangeHiddenAreas(()=>ae()),z.onDidLayoutChange(()=>ae()),F.onDidLayoutChange(()=>ae()),z.onDidFocusEditorWidget(()=>{R(!0)}),F.onDidFocusEditorWidget(()=>{R(!0)}),z.onDidBlurEditorWidget(()=>{R(!1)}),F.onDidBlurEditorWidget(()=>{R(!1)}),z.onDidChangeModelContent(()=>{we=!0,_e=Date.now();const N=(f==null?void 0:f.getValue())||"";if(N===u)return;const P=N.replace(p.join(""),"").replace(D.join(""),"");o("codeChange",{modifiedCode:P});const O=setTimeout(()=>{we=!1},500);E.push({dispose:()=>clearTimeout(O)})})),function(){!C||!w||(q&&clearTimeout(q),q=setTimeout(()=>{if(!C.__hasClickListener){const N=P=>{const O=P.target;O&&(O.closest('[title="Show Unchanged Region"]')||O.closest('[title="Hide Unchanged Region"]'))&&Ee()};C.addEventListener("click",N),t(2,C.__hasClickListener=!0,C),E.push({dispose:()=>{C.removeEventListener("click",N)}})}w&&E.push(w.onDidUpdateDiff(()=>{Ee()}))},300))}()}cs(()=>{w==null||w.dispose(),$==null||$.dispose(),B==null||B.dispose(),f==null||f.dispose(),E.forEach(F=>F.dispose()),q&&clearTimeout(q)});let ee=null;function Ee(){ee&&clearTimeout(ee),ee=setTimeout(()=>{ae(),ee=null},100),ee&&E.push({dispose:()=>{ee&&(clearTimeout(ee),ee=null)}})}function Te(F,z,N,P=[],O=[]){if(!n)return void console.error("Monaco not loaded. Diff view cannot be updated.");B==null||B.dispose(),f==null||f.dispose(),z=z||"",N=N||"";const H=P.join(""),ce=O.join("");z=k?N.split(`
`).map(()=>" ").join(`
`):H+z+ce,N=H+N+ce,B=n.editor.createModel(z,void 0,F!==void 0?n.Uri.parse("file://"+F+`#${crypto.randomUUID()}`):void 0),t(20,f=n.editor.createModel(N,void 0,F!==void 0?n.Uri.parse("file://"+F+`#${crypto.randomUUID()}`):void 0)),w&&(w.setModel({original:B,modified:f}),oe(),q&&clearTimeout(q),q=setTimeout(()=>{ge(),q=null},300))}Xe(()=>{if(n)if(k){t(19,$=n.editor.create(C,{automaticLayout:!0,stickyScroll:{enabled:!0},scrollBeyondLastLine:!1,minimap:{enabled:!1},overviewRulerBorder:!1,theme:b,scrollbar:{alwaysConsumeMouseWheel:!1,handleMouseWheel:!1},lineNumbers:N=>`${d-p.length+N}`}));const F=te(0,a);t(20,f=n.editor.createModel(u,F,a!==void 0?n.Uri.parse("file://"+a+`#${crypto.randomUUID()}`):void 0)),$.setModel(f),E.push($.onDidChangeModelContent(()=>{we=!0,_e=Date.now();const N=(f==null?void 0:f.getValue())||"";if(N===u)return;o("codeChange",{modifiedCode:N});const P=setTimeout(()=>{we=!1},500);E.push({dispose:()=>clearTimeout(P)})})),E.push($.onDidFocusEditorWidget(()=>{$==null||$.updateOptions({scrollbar:{handleMouseWheel:!0}})}),$.onDidBlurEditorWidget(()=>{$==null||$.updateOptions({scrollbar:{handleMouseWheel:!1}})})),t(3,j=20*u.split(`
`).length+40);const z=setTimeout(()=>{$==null||$.layout()},0);E.push({dispose:()=>clearTimeout(z)})}else t(18,w=n.editor.createDiffEditor(C,{automaticLayout:!0,useInlineViewWhenSpaceIsLimited:!0,enableSplitViewResizing:!0,stickyScroll:{enabled:!0},scrollBeyondLastLine:!1,minimap:{enabled:!1},renderOverviewRuler:!1,renderGutterMenu:!1,theme:b,scrollbar:{alwaysConsumeMouseWheel:!1,handleMouseWheel:!1},lineNumbers:F=>`${d-p.length+F}`,hideUnchangedRegions:{enabled:!0,revealLineCount:3,minimumLineCount:3,contextLineCount:3}})),Te(a,l,u,p,D),oe(),ge(),q&&clearTimeout(q),q=setTimeout(()=>{ae(),q=null},100);else console.error("Monaco not loaded. Diff view cannot be initialized.")});let we=!1,_e=0;function Se(F,z=!0){return w?(z?w.getModifiedEditor():w.getOriginalEditor()).getTopForLineNumber(F):18*F}function ae(){if(!w)return;const F=w.getModel(),z=F==null?void 0:F.original,N=F==null?void 0:F.modified;if(!z||!N)return;const P=w.getOriginalEditor(),O=w.getModifiedEditor(),H=w.getLineChanges()||[];let ce;if(H.length===0){const ie=P.getContentHeight(),he=O.getContentHeight();ce=Math.max(100,ie,he)}else{let ie=0,he=0;for(const Me of H)Me.originalEndLineNumber>0&&(ie=Math.max(ie,Me.originalEndLineNumber)),Me.modifiedEndLineNumber>0&&(he=Math.max(he,Me.modifiedEndLineNumber));ie=Math.min(ie+3,z.getLineCount()),he=Math.min(he+3,N.getLineCount());const Re=P.getTopForLineNumber(ie),Dt=O.getTopForLineNumber(he);ce=Math.max(Re,Dt)+60}t(3,j=Math.min(ce,2e3)),w.layout(),re()}function R(F){if(!w)return;const z=w.getOriginalEditor(),N=w.getModifiedEditor();z.updateOptions({scrollbar:{handleMouseWheel:F}}),N.updateOptions({scrollbar:{handleMouseWheel:F}})}function I(F){if(!w)return 0;const z=w.getModel(),N=z==null?void 0:z.original,P=z==null?void 0:z.modified;if(!N||!P)return 0;const O=Se(F.range.start+1,!1),H=Se(F.range.start+1,!0);return O&&!H?O:!O&&H?H:Math.min(O,H)}function re(){if(!w||c.length===0)return;const F={};c.forEach((z,N)=>{F[N]=I(z)}),pe.set(F)}return r.$$set=F=>{"originalCode"in F&&t(10,l=F.originalCode),"modifiedCode"in F&&t(11,u=F.modifiedCode),"path"in F&&t(12,a=F.path),"descriptions"in F&&t(1,c=F.descriptions),"lineOffset"in F&&t(13,d=F.lineOffset),"extraPrefixLines"in F&&t(14,p=F.extraPrefixLines),"extraSuffixLines"in F&&t(15,D=F.extraSuffixLines),"theme"in F&&t(16,b=F.theme),"areDescriptionsVisible"in F&&t(0,m=F.areDescriptionsVisible),"isNewFile"in F&&t(17,k=F.isNewFile)},r.$$.update=()=>{if(4119552&r.$$.dirty[0]&&(F=u,!(we||Date.now()-_e<1e3||f&&f.getValue()===p.join("")+F+D.join(""))))if(k&&$){if(f)f.setValue(u);else{const z=te(0,a);n&&t(20,f=n.editor.createModel(u,z,a!==void 0?n.Uri.parse("file://"+a+`#${crypto.randomUUID()}`):void 0)),f&&$.setModel(f)}t(3,j=20*u.split(`
`).length+40),$.layout()}else!k&&w&&(Te(a,l,u,p,D),ae());var F;262146&r.$$.dirty[0]&&w&&c.length>0&&re(),657408&r.$$.dirty[0]&&k&&u&&$&&(t(3,j=20*u.split(`
`).length+40),$.layout())},[m,c,C,j,s,i,y,V,pe,I,l,u,a,d,p,D,b,k,w,$,f,n,function(F){Ae[F?"unshift":"push"](()=>{C=F,t(2,C)})},()=>t(0,m=!m)]}let Di=class extends Y{constructor(r){super(),X(this,r,mi,$i,J,{originalCode:10,modifiedCode:11,path:12,descriptions:1,lineOffset:13,extraPrefixLines:14,extraSuffixLines:15,theme:16,areDescriptionsVisible:0,isNewFile:17},null,[-1,-1])}};function Fi(r){let e,t,n,s;function i(l){r[20](l)}let o={path:r[3],originalCode:r[0].originalCode,modifiedCode:r[7],theme:r[9],descriptions:r[4],isNewFile:r[10]};return r[1]!==void 0&&(o.areDescriptionsVisible=r[1]),t=new Di({props:o}),Ae.push(()=>Oe(t,"areDescriptionsVisible",i)),t.$on("codeChange",r[13]),{c(){e=_("div"),M(t.$$.fragment),A(e,"class","changes svelte-1o3rbzw")},m(l,u){x(l,e,u),T(t,e,null),s=!0},p(l,u){const a={};8&u&&(a.path=l[3]),1&u&&(a.originalCode=l[0].originalCode),128&u&&(a.modifiedCode=l[7]),512&u&&(a.theme=l[9]),16&u&&(a.descriptions=l[4]),1024&u&&(a.isNewFile=l[10]),!n&&2&u&&(n=!0,a.areDescriptionsVisible=l[1],Ve(()=>n=!1)),t.$set(a)},i(l){s||(h(t.$$.fragment,l),s=!0)},o(l){g(t.$$.fragment,l),s=!1},d(l){l&&v(e),S(t)}}}function ki(r){let e,t=at(r[8])+"";return{c(){e=U(t)},m(n,s){x(n,e,s)},p(n,s){256&s&&t!==(t=at(n[8])+"")&&de(e,t)},d(n){n&&v(e)}}}function xi(r){let e,t;return e=new Le({props:{variant:"ghost-block",color:"neutral",size:1,class:"c-codeblock__filename",$$slots:{default:[ki]},$$scope:{ctx:r}}}),{c(){M(e.$$.fragment)},m(n,s){T(e,n,s),t=!0},p(n,s){const i={};4194560&s&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(h(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){S(e,n)}}}function Qt(r){let e,t,n=Ne(r[8])+"";return{c(){e=_("span"),t=U(n),A(e,"class","c-directory svelte-1o3rbzw")},m(s,i){x(s,e,i),L(e,t)},p(s,i){256&i&&n!==(n=Ne(s[8])+"")&&de(t,n)},d(s){s&&v(e)}}}function vi(r){let e,t,n,s=r[12]>0&&Gt(r),i=r[11]>0&&Jt(r);return{c(){e=_("div"),s&&s.c(),t=Z(),i&&i.c(),A(e,"class","changes-indicator svelte-1o3rbzw")},m(o,l){x(o,e,l),s&&s.m(e,null),L(e,t),i&&i.m(e,null),n=!0},p(o,l){o[12]>0?s?(s.p(o,l),4096&l&&h(s,1)):(s=Gt(o),s.c(),h(s,1),s.m(e,t)):s&&(W(),g(s,1,1,()=>{s=null}),Q()),o[11]>0?i?(i.p(o,l),2048&l&&h(i,1)):(i=Jt(o),i.c(),h(i,1),i.m(e,null)):i&&(W(),g(i,1,1,()=>{i=null}),Q())},i(o){n||(h(s),h(i),n=!0)},o(o){g(s),g(i),n=!1},d(o){o&&v(e),s&&s.d(),i&&i.d()}}}function Ci(r){let e;return{c(){e=_("span"),e.textContent="New File",A(e,"class","new-file-badge svelte-1o3rbzw")},m(t,n){x(t,e,n)},p:G,i:G,o:G,d(t){t&&v(e)}}}function Gt(r){let e,t,n;return t=new fe({props:{size:1,$$slots:{default:[wi]},$$scope:{ctx:r}}}),{c(){e=_("span"),M(t.$$.fragment),A(e,"class","additions svelte-1o3rbzw")},m(s,i){x(s,e,i),T(t,e,null),n=!0},p(s,i){const o={};4198400&i&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){n||(h(t.$$.fragment,s),n=!0)},o(s){g(t.$$.fragment,s),n=!1},d(s){s&&v(e),S(t)}}}function wi(r){let e,t;return{c(){e=U("+"),t=U(r[12])},m(n,s){x(n,e,s),x(n,t,s)},p(n,s){4096&s&&de(t,n[12])},d(n){n&&(v(e),v(t))}}}function Jt(r){let e,t,n;return t=new fe({props:{size:1,$$slots:{default:[Ai]},$$scope:{ctx:r}}}),{c(){e=_("span"),M(t.$$.fragment),A(e,"class","deletions svelte-1o3rbzw")},m(s,i){x(s,e,i),T(t,e,null),n=!0},p(s,i){const o={};4196352&i&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){n||(h(t.$$.fragment,s),n=!0)},o(s){g(t.$$.fragment,s),n=!1},d(s){s&&v(e),S(t)}}}function Ai(r){let e,t;return{c(){e=U("-"),t=U(r[11])},m(n,s){x(n,e,s),x(n,t,s)},p(n,s){2048&s&&de(t,n[11])},d(n){n&&(v(e),v(t))}}}function yi(r){let e,t;return e=new qe({props:{content:r[5]?"Applying changes...":"Apply changes to file",$$slots:{default:[_i]},$$scope:{ctx:r}}}),{c(){M(e.$$.fragment)},m(n,s){T(e,n,s),t=!0},p(n,s){const i={};32&s&&(i.content=n[5]?"Applying changes...":"Apply changes to file"),4194304&s&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(h(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){S(e,n)}}}function bi(r){let e,t,n;return t=new fe({props:{size:1,$$slots:{default:[Bi]},$$scope:{ctx:r}}}),{c(){e=_("div"),M(t.$$.fragment),A(e,"class","applied svelte-1o3rbzw")},m(s,i){x(s,e,i),T(t,e,null),n=!0},p(s,i){const o={};4194304&i&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){n||(h(t.$$.fragment,s),n=!0)},o(s){g(t.$$.fragment,s),n=!1},d(s){s&&v(e),S(t)}}}function Ei(r){let e,t,n,s;return n=new et({}),{c(){e=U(`Apply
            `),t=_("div"),M(n.$$.fragment),A(t,"class","applied__icon svelte-1o3rbzw")},m(i,o){x(i,e,o),x(i,t,o),T(n,t,null),s=!0},p:G,i(i){s||(h(n.$$.fragment,i),s=!0)},o(i){g(n.$$.fragment,i),s=!1},d(i){i&&(v(e),v(t)),S(n)}}}function _i(r){let e,t;return e=new Le({props:{variant:"ghost-block",color:"neutral",size:2,$$slots:{default:[Ei]},$$scope:{ctx:r}}}),e.$on("click",r[14]),{c(){M(e.$$.fragment)},m(n,s){T(e,n,s),t=!0},p(n,s){const i={};4194304&s&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(h(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){S(e,n)}}}function Bi(r){let e,t,n;return t=new gt({props:{iconName:"check"}}),{c(){e=U(`Applied
            `),M(t.$$.fragment)},m(s,i){x(s,e,i),T(t,s,i),n=!0},p:G,i(s){n||(h(t.$$.fragment,s),n=!0)},o(s){g(t.$$.fragment,s),n=!1},d(s){s&&v(e),S(t,s)}}}function zi(r){let e,t,n,s,i,o,l,u,a,c,d,p,D,b=Ne(r[8]);t=new Ds({}),i=new qe({props:{content:r[8],triggerOn:[_t.Hover],$$slots:{default:[xi]},$$scope:{ctx:r}}});let m=b&&Qt(r);const k=[Ci,vi],y=[];function w(C,E){return C[10]?0:1}u=w(r),a=y[u]=k[u](r);const $=[bi,yi],B=[];function f(C,E){return C[6]?0:1}return d=f(r),p=B[d]=$[d](r),{c(){e=_("div"),M(t.$$.fragment),n=Z(),s=_("div"),M(i.$$.fragment),o=Z(),m&&m.c(),l=Z(),a.c(),c=Z(),p.c(),A(s,"class","c-path svelte-1o3rbzw"),A(e,"slot","header"),A(e,"class","header svelte-1o3rbzw")},m(C,E){x(C,e,E),T(t,e,null),L(e,n),L(e,s),T(i,s,null),L(s,o),m&&m.m(s,null),L(e,l),y[u].m(e,null),L(e,c),B[d].m(e,null),D=!0},p(C,E){const V={};256&E&&(V.content=C[8]),4194560&E&&(V.$$scope={dirty:E,ctx:C}),i.$set(V),256&E&&(b=Ne(C[8])),b?m?m.p(C,E):(m=Qt(C),m.c(),m.m(s,null)):m&&(m.d(1),m=null);let j=u;u=w(C),u===j?y[u].p(C,E):(W(),g(y[j],1,1,()=>{y[j]=null}),Q(),a=y[u],a?a.p(C,E):(a=y[u]=k[u](C),a.c()),h(a,1),a.m(e,c));let K=d;d=f(C),d===K?B[d].p(C,E):(W(),g(B[K],1,1,()=>{B[K]=null}),Q(),p=B[d],p?p.p(C,E):(p=B[d]=$[d](C),p.c()),h(p,1),p.m(e,null))},i(C){D||(h(t.$$.fragment,C),h(i.$$.fragment,C),h(a),h(p),D=!0)},o(C){g(t.$$.fragment,C),g(i.$$.fragment,C),g(a),g(p),D=!1},d(C){C&&v(e),S(t),S(i),m&&m.d(),y[u].d(),B[d].d()}}}function Li(r){let e,t,n,s;function i(l){r[21](l)}let o={stickyHeader:!0,$$slots:{header:[zi],default:[Fi]},$$scope:{ctx:r}};return r[2]!==void 0&&(o.collapsed=r[2]),t=new ms({props:o}),Ae.push(()=>Oe(t,"collapsed",i)),{c(){e=_("div"),M(t.$$.fragment),A(e,"class","c svelte-1o3rbzw")},m(l,u){x(l,e,u),T(t,e,null),s=!0},p(l,[u]){const a={};4202491&u&&(a.$$scope={dirty:u,ctx:l}),!n&&4&u&&(n=!0,a.collapsed=l[2],Ve(()=>n=!1)),t.$set(a)},i(l){s||(h(t.$$.fragment,l),s=!0)},o(l){g(t.$$.fragment,l),s=!1},d(l){l&&v(e),S(t)}}}function Mi(r,e,t){let n,s,i,o,l,u,a;be(r,ds,f=>t(19,a=f));let{path:c}=e,{change:d}=e,{descriptions:p=[]}=e,{areDescriptionsVisible:D=!0}=e,{isExpandedDefault:b}=e,{isCollapsed:m=!b}=e,{isApplying:k}=e,{hasApplied:y}=e,{onApplyChanges:w}=e,{onCodeChange:$}=e,B=d.modifiedCode;return r.$$set=f=>{"path"in f&&t(3,c=f.path),"change"in f&&t(0,d=f.change),"descriptions"in f&&t(4,p=f.descriptions),"areDescriptionsVisible"in f&&t(1,D=f.areDescriptionsVisible),"isExpandedDefault"in f&&t(15,b=f.isExpandedDefault),"isCollapsed"in f&&t(2,m=f.isCollapsed),"isApplying"in f&&t(5,k=f.isApplying),"hasApplied"in f&&t(6,y=f.hasApplied),"onApplyChanges"in f&&t(16,w=f.onApplyChanges),"onCodeChange"in f&&t(17,$=f.onCodeChange)},r.$$.update=()=>{1&r.$$.dirty&&t(7,B=d.modifiedCode),1&r.$$.dirty&&t(18,n=Ct(d.diff)),262144&r.$$.dirty&&t(12,s=n.additions),262144&r.$$.dirty&&t(11,i=n.deletions),1&r.$$.dirty&&t(10,o=Cs(d)),524288&r.$$.dirty&&t(9,l=Fs(a==null?void 0:a.category,a==null?void 0:a.intensity)),8&r.$$.dirty&&t(8,u=xs(c))},[d,D,m,c,p,k,y,B,u,l,o,i,s,function(f){t(7,B=f.detail.modifiedCode),$==null||$(B)},function(){t(0,d.modifiedCode=B,d),$==null||$(B),w==null||w()},b,w,$,n,a,function(f){D=f,t(1,D)},function(f){m=f,t(2,m)}]}let Ti=class extends Y{constructor(r){super(),X(this,r,Mi,Li,J,{path:3,change:0,descriptions:4,areDescriptionsVisible:1,isExpandedDefault:15,isCollapsed:2,isApplying:5,hasApplied:6,onApplyChanges:16,onCodeChange:17})}};function Yt(r,e,t){const n=r.slice();return n[16]=e[t],n}function Si(r){let e,t,n;return t=new fe({props:{size:1,color:"neutral",$$slots:{default:[Ri]},$$scope:{ctx:r}}}),{c(){e=_("div"),M(t.$$.fragment),A(e,"class","c-edits-list c-edits-list--empty svelte-1r8nb9r")},m(s,i){x(s,e,i),T(t,e,null),n=!0},p(s,i){const o={};524288&i&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){n||(h(t.$$.fragment,s),n=!0)},o(s){g(t.$$.fragment,s),n=!1},d(s){s&&v(e),S(t)}}}function Ni(r){let e,t,n,s,i,o,l=[],u=new Map,a=r[6].length>0&&Xt(r),c=ne(r[6]);const d=p=>p[16].qualifiedPathName.relPath;for(let p=0;p<c.length;p+=1){let D=Yt(r,c,p),b=d(D);u.set(b,l[p]=Kt(b,D))}return{c(){e=_("div"),t=_("div"),a&&a.c(),n=Z(),s=_("div"),i=_("div");for(let p=0;p<l.length;p+=1)l[p].c();A(t,"class","c-edits-list-controls svelte-1r8nb9r"),A(e,"class","c-edits-list-header svelte-1r8nb9r"),A(i,"class","c-edits-section svelte-1r8nb9r"),A(s,"class","c-edits-list svelte-1r8nb9r")},m(p,D){x(p,e,D),L(e,t),a&&a.m(t,null),x(p,n,D),x(p,s,D),L(s,i);for(let b=0;b<l.length;b+=1)l[b]&&l[b].m(i,null);o=!0},p(p,D){p[6].length>0?a?(a.p(p,D),64&D&&h(a,1)):(a=Xt(p),a.c(),h(a,1),a.m(t,null)):a&&(W(),g(a,1,1,()=>{a=null}),Q()),335&D&&(c=ne(p[6]),W(),l=js(l,D,d,1,p,c,u,i,Hs,Kt,null,Yt),Q())},i(p){if(!o){h(a);for(let D=0;D<c.length;D+=1)h(l[D]);o=!0}},o(p){g(a);for(let D=0;D<l.length;D+=1)g(l[D]);o=!1},d(p){p&&(v(e),v(n),v(s)),a&&a.d();for(let D=0;D<l.length;D+=1)l[D].d()}}}function Ri(r){let e;return{c(){e=U("No changes to show")},m(t,n){x(t,e,n)},d(t){t&&v(e)}}}function Xt(r){let e,t;return e=new Le({props:{variant:"ghost-block",color:"neutral",size:2,disabled:r[4]||r[5]||r[1].length>0||!r[7],$$slots:{default:[Vi]},$$scope:{ctx:r}}}),e.$on("click",r[9]),{c(){M(e.$$.fragment)},m(n,s){T(e,n,s),t=!0},p(n,s){const i={};178&s&&(i.disabled=n[4]||n[5]||n[1].length>0||!n[7]),524336&s&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(h(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){S(e,n)}}}function Pi(r){let e;return{c(){e=U("Apply all")},m(t,n){x(t,e,n)},d(t){t&&v(e)}}}function Ii(r){let e;return{c(){e=U("All applied")},m(t,n){x(t,e,n)},d(t){t&&v(e)}}}function Oi(r){let e;return{c(){e=U("Applying...")},m(t,n){x(t,e,n)},d(t){t&&v(e)}}}function Vi(r){let e,t,n,s;function i(u,a){return u[4]?Oi:u[5]?Ii:Pi}let o=i(r),l=o(r);return n=new et({}),{c(){l.c(),e=Z(),t=_("div"),M(n.$$.fragment),A(t,"class","c-edits-list-controls__icon svelte-1r8nb9r")},m(u,a){l.m(u,a),x(u,e,a),x(u,t,a),T(n,t,null),s=!0},p(u,a){o!==(o=i(u))&&(l.d(1),l=o(u),l&&(l.c(),l.m(e.parentNode,e)))},i(u){s||(h(n.$$.fragment,u),s=!0)},o(u){g(n.$$.fragment,u),s=!1},d(u){u&&(v(e),v(t)),l.d(u),S(n)}}}function Kt(r,e){let t,n,s,i,o;function l(...a){return e[14](e[16],...a)}function u(){return e[15](e[16])}return n=new Ti({props:{path:e[16].qualifiedPathName.relPath,change:e[16].diff,isApplying:e[1].includes(e[16].qualifiedPathName.relPath),hasApplied:e[2].includes(e[16].qualifiedPathName.relPath),onCodeChange:l,onApplyChanges:u,isExpandedDefault:!0}}),{key:r,first:null,c(){t=_("div"),M(n.$$.fragment),s=Z(),A(t,"class",""),this.first=t},m(a,c){x(a,t,c),T(n,t,null),L(t,s),o=!0},p(a,c){e=a;const d={};64&c&&(d.path=e[16].qualifiedPathName.relPath),64&c&&(d.change=e[16].diff),66&c&&(d.isApplying=e[1].includes(e[16].qualifiedPathName.relPath)),68&c&&(d.hasApplied=e[2].includes(e[16].qualifiedPathName.relPath)),64&c&&(d.onCodeChange=l),73&c&&(d.onApplyChanges=u),n.$set(d)},i(a){o||(h(n.$$.fragment,a),a&&Ps(()=>{o&&(i||(i=It(t,Zt,{},!0)),i.run(1))}),o=!0)},o(a){g(n.$$.fragment,a),a&&(i||(i=It(t,Zt,{},!1)),i.run(0)),o=!1},d(a){a&&v(t),S(n),a&&i&&i.end()}}}function Zi(r){let e,t,n,s;const i=[Ni,Si],o=[];function l(u,a){return u[6].length>0?0:1}return t=l(r),n=o[t]=i[t](r),{c(){e=_("div"),n.c(),A(e,"class","c-edits-list-container svelte-1r8nb9r")},m(u,a){x(u,e,a),o[t].m(e,null),s=!0},p(u,[a]){let c=t;t=l(u),t===c?o[t].p(u,a):(W(),g(o[c],1,1,()=>{o[c]=null}),Q(),n=o[t],n?n.p(u,a):(n=o[t]=i[t](u),n.c()),h(n,1),n.m(e,null))},i(u){s||(h(n),s=!0)},o(u){g(n),s=!1},d(u){u&&v(e),o[t].d()}}}function qi(r,e,t){let n,s,i,o,l,{changedFiles:u}=e,{onApplyChanges:a}=e,{pendingFiles:c=[]}=e,{appliedFiles:d=[]}=e,p={},D=!1,b=!1;function m(k,y){t(3,p[k]=y,p)}return r.$$set=k=>{"changedFiles"in k&&t(10,u=k.changedFiles),"onApplyChanges"in k&&t(0,a=k.onApplyChanges),"pendingFiles"in k&&t(1,c=k.pendingFiles),"appliedFiles"in k&&t(2,d=k.appliedFiles)},r.$$.update=()=>{if(1024&r.$$.dirty&&t(13,n=JSON.stringify(u)),4&r.$$.dirty&&t(11,s=JSON.stringify(d)),2&r.$$.dirty&&t(12,i=JSON.stringify(c)),8192&r.$$.dirty&&n&&(t(3,p={}),t(4,D=!1),t(5,b=!1)),1032&r.$$.dirty&&t(6,l=u.map(k=>{const y=k.new_path||k.old_path,w=k.old_contents||"",$=k.new_contents||"",B=ri.generateDiff(k.old_path,k.new_path,w,$),f=function(C,E){const V=ut("oldFile","newFile",C,E,"","",{context:3}),j=Ws(V);let K=0,te=0,pe=[];for(const q of j)for(const oe of q.hunks)for(const ge of oe.lines){const ee=ge.startsWith("+"),Ee=ge.startsWith("-");ee&&K++,Ee&&te++,pe.push({value:ge,added:ee,removed:Ee})}return{totalAddedLines:K,totalRemovedLines:te,changes:pe,diff:V}}(w,$);return p[y]||t(3,p[y]=$,p),{qualifiedPathName:{rootPath:"",relPath:y},lineChanges:f,oldContents:w,newContents:$,diff:B}})),14406&r.$$.dirty&&t(7,o=(()=>{if(n&&s&&i){const k=l.map(y=>y.qualifiedPathName.relPath);return k.length!==0&&k.some(y=>!d.includes(y)&&!c.includes(y))}return!1})()),86&r.$$.dirty&&D){const k=l.map(y=>y.qualifiedPathName.relPath);k.filter(y=>!d.includes(y)&&!c.includes(y)).length===0&&k.every(y=>d.includes(y)||c.includes(y))&&c.length===0&&d.length>0&&(t(4,D=!1),t(5,b=!0))}if(2164&r.$$.dirty&&l.length>0&&!D&&s){const k=l.map(y=>y.qualifiedPathName.relPath);if(k.length>0){const y=k.every(w=>d.includes(w));y&&d.length>0?t(5,b=!0):!y&&b&&t(5,b=!1)}}},[a,c,d,p,D,b,l,o,m,function(){if(!a)return;const k=l.map(w=>w.qualifiedPathName.relPath);if(k.every(w=>d.includes(w)))return void t(5,b=!0);const y=k.filter(w=>!d.includes(w)&&!c.includes(w));y.length!==0&&(t(4,D=!0),t(5,b=!1),y.forEach(w=>{const $=l.find(B=>B.qualifiedPathName.relPath===w);if($){const B=p[w]||$.newContents;a(w,$.oldContents,B)}}))},u,s,i,n,(k,y)=>{m(k.qualifiedPathName.relPath,y)},k=>{const y=p[k.qualifiedPathName.relPath]||k.newContents;a(k.qualifiedPathName.relPath,k.oldContents,y)}]}class Ui extends Y{constructor(e){super(),X(this,e,qi,Zi,J,{changedFiles:10,onApplyChanges:0,pendingFiles:1,appliedFiles:2})}}function en(r,e,t){const n=r.slice();return n[3]=e[t],n}function tn(r){let e,t=ne(r[1].paths),n=[];for(let s=0;s<t.length;s+=1)n[s]=nn(en(r,t,s));return{c(){for(let s=0;s<n.length;s+=1)n[s].c();e=Ce()},m(s,i){for(let o=0;o<n.length;o+=1)n[o]&&n[o].m(s,i);x(s,e,i)},p(s,i){if(2&i){let o;for(t=ne(s[1].paths),o=0;o<t.length;o+=1){const l=en(s,t,o);n[o]?n[o].p(l,i):(n[o]=nn(l),n[o].c(),n[o].m(e.parentNode,e))}for(;o<n.length;o+=1)n[o].d(1);n.length=t.length}},d(s){s&&v(e),ve(n,s)}}}function nn(r){let e,t;return{c(){e=ps("path"),A(e,"d",t=r[3]),A(e,"fill-rule","evenodd"),A(e,"clip-rule","evenodd")},m(n,s){x(n,e,s)},p(n,s){2&s&&t!==(t=n[3])&&A(e,"d",t)},d(n){n&&v(e)}}}function ji(r){let e,t=r[1]&&tn(r);return{c(){e=ps("svg"),t&&t.c(),A(e,"width","14"),A(e,"viewBox","0 0 20 20"),A(e,"fill","currentColor"),A(e,"class","svelte-10h4f31")},m(n,s){x(n,e,s),t&&t.m(e,null)},p(n,s){n[1]?t?t.p(n,s):(t=tn(n),t.c(),t.m(e,null)):t&&(t.d(1),t=null)},d(n){n&&v(e),t&&t.d()}}}function Hi(r){let e,t;return e=new qe({props:{content:`This is a ${r[0]} change`,triggerOn:[_t.Hover],$$slots:{default:[ji]},$$scope:{ctx:r}}}),{c(){M(e.$$.fragment)},m(n,s){T(e,n,s),t=!0},p(n,[s]){const i={};1&s&&(i.content=`This is a ${n[0]} change`),66&s&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(h(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){S(e,n)}}}function Wi(r,e,t){let n,{type:s}=e;const i={fix:{paths:["M6.56 1.14a.75.75 0 0 1 .177 1.045 3.989 3.989 0 0 0-.464.86c.185.17.382.329.59.473A3.993 3.993 0 0 1 10 2c1.272 0 2.405.594 3.137 1.518.208-.144.405-.302.59-.473a3.989 3.989 0 0 0-.464-.86.75.75 0 0 1 1.222-.869c.369.519.65 1.105.822 1.736a.75.75 0 0 1-.174.707 7.03 7.03 0 0 1-1.299 1.098A4 4 0 0 1 14 6c0 .52-.301.963-.723 1.187a6.961 6.961 0 0 1-1.158.486c.13.208.231.436.296.679 1.413-.174 2.779-.5 4.081-.96a19.655 19.655 0 0 0-.09-2.319.75.75 0 1 1 1.493-.146 21.239 21.239 0 0 1 .08 3.028.75.75 0 0 1-.482.667 20.873 20.873 0 0 1-5.153 1.249 2.521 2.521 0 0 1-.107.247 20.945 20.945 0 0 1 5.252 1.257.75.75 0 0 1 .482.74 20.945 20.945 0 0 1-.908 5.107.75.75 0 0 1-1.433-.444c.415-1.34.69-2.743.806-4.191-.495-.173-1-.327-1.512-.46.05.284.076.575.076.873 0 1.814-.517 3.312-1.426 4.37A4.639 4.639 0 0 1 10 19a4.639 4.639 0 0 1-3.574-1.63C5.516 16.311 5 14.813 5 13c0-.298.026-.59.076-.873-.513.133-1.017.287-1.512.46.116 1.448.39 2.85.806 4.191a.75.75 0 1 1-1.433.444 20.94 20.94 0 0 1-.908-5.107.75.75 0 0 1 .482-.74 20.838 20.838 0 0 1 5.252-1.257 2.493 2.493 0 0 1-.107-.247 20.874 20.874 0 0 1-5.153-1.249.75.75 0 0 1-.482-.667 21.342 21.342 0 0 1 .08-3.028.75.75 0 1 1 1.493.146 19.745 19.745 0 0 0-.09 2.319c1.302.46 2.668.786 4.08.96.066-.243.166-.471.297-.679a6.962 6.962 0 0 1-1.158-.486A1.348 1.348 0 0 1 6 6a4 4 0 0 1 .166-1.143 7.032 7.032 0 0 1-1.3-1.098.75.75 0 0 1-.173-.707 5.48 5.48 0 0 1 .822-1.736.75.75 0 0 1 1.046-.177Z"],color:"var(--ds-color-warning-9)"},feature:{paths:["M14 6a2.5 2.5 0 0 0-4-3 2.5 2.5 0 0 0-4 3H3.25C2.56 6 2 6.56 2 7.25v.5C2 8.44 2.56 9 3.25 9h6V6h1.5v3h6C17.44 9 18 8.44 18 7.75v-.5C18 6.56 17.44 6 16.75 6H14Zm-1-1.5a1 1 0 0 1-1 1h-1v-1a1 1 0 1 1 2 0Zm-6 0a1 1 0 0 0 1 1h1v-1a1 1 0 0 0-2 0Z","M9.25 10.5H3v4.75A2.75 2.75 0 0 0 5.75 18h3.5v-7.5ZM10.75 18v-7.5H17v4.75A2.75 2.75 0 0 1 14.25 18h-3.5Z"],color:"var(--ds-color-warning-9)"},refactor:{paths:["M8.157 2.176a1.5 1.5 0 0 0-1.147 0l-4.084 1.69A1.5 1.5 0 0 0 2 5.25v10.877a1.5 1.5 0 0 0 2.074 1.386l3.51-1.452 4.26 1.762a1.5 1.5 0 0 0 1.146 0l4.083-1.69A1.5 1.5 0 0 0 18 14.75V3.872a1.5 1.5 0 0 0-2.073-1.386l-3.51 1.452-4.26-1.762ZM7.58 5a.75.75 0 0 1 .75.75v6.5a.75.75 0 0 1-1.5 0v-6.5A.75.75 0 0 1 7.58 5Zm5.59 2.75a.75.75 0 0 0-1.5 0v6.5a.75.75 0 0 0 1.5 0v-6.5Z"],color:"var(--ds-color-warning-9)"},documentation:{paths:["M4.5 2A1.5 1.5 0 0 0 3 3.5v13A1.5 1.5 0 0 0 4.5 18h11a1.5 1.5 0 0 0 1.5-1.5V7.621a1.5 1.5 0 0 0-.44-1.06l-4.12-4.122A1.5 1.5 0 0 0 11.378 2H4.5Zm2.25 8.5a.75.75 0 0 0 0 1.5h6.5a.75.75 0 0 0 0-1.5h-6.5Zm0 3a.75.75 0 0 0 0 1.5h6.5a.75.75 0 0 0 0-1.5h-6.5Z"],color:"var(--ds-color-warning-9)"},style:{paths:["M15.993 1.385a1.87 1.87 0 0 1 2.623 2.622l-4.03 5.27a12.749 12.749 0 0 1-4.237 3.562 4.508 4.508 0 0 0-3.188-3.188 12.75 12.75 0 0 1 3.562-4.236l5.27-4.03ZM6 11a3 3 0 0 0-3 3 .5.5 0 0 1-.72.45.75.75 0 0 0-1.035.931A4.001 4.001 0 0 0 9 14.004V14a3.01 3.01 0 0 0-1.66-2.685A2.99 2.99 0 0 0 6 11Z"],color:"var(--ds-color-warning-9)"},test:{paths:["M8.5 3.528v4.644c0 .729-.29 1.428-.805 1.944l-1.217 1.216a8.75 8.75 0 0 1 3.55.621l.502.201a7.25 7.25 0 0 0 4.178.365l-2.403-2.403a2.75 2.75 0 0 1-.805-1.944V3.528a40.205 40.205 0 0 0-3 0Zm4.5.084.19.015a.75.75 0 1 0 .12-1.495 41.364 41.364 0 0 0-6.62 0 .75.75 0 0 0 .12 1.495L7 3.612v4.56c0 .331-.132.649-.366.883L2.6 13.09c-1.496 1.496-.817 4.15 1.403 4.475C5.961 17.852 7.963 18 10 18s4.039-.148 5.997-.436c2.22-.325 2.9-2.979 1.403-4.475l-4.034-4.034A1.25 1.25 0 0 1 13 8.172v-4.56Z"],color:"var(--ds-color-warning-9)"},chore:{paths:["m6.75.98-.884.883a1.25 1.25 0 1 0 1.768 0L6.75.98ZM13.25.98l-.884.883a1.25 1.25 0 1 0 1.768 0L13.25.98ZM10 .98l.884.883a1.25 1.25 0 1 1-1.768 0L10 .98ZM7.5 5.75a.75.75 0 0 0-1.5 0v.464c-1.179.304-2 1.39-2 2.622v.094c.1-.02.202-.038.306-.052A42.867 42.867 0 0 1 10 8.5c1.93 0 3.83.129 5.694.378.104.014.206.032.306.052v-.094c0-1.232-.821-2.317-2-2.622V5.75a.75.75 0 0 0-1.5 0v.318a45.645 45.645 0 0 0-1.75-.062V5.75a.75.75 0 0 0-1.5 0v.256c-.586.01-1.17.03-1.75.062V5.75ZM4.505 10.365A41.36 41.36 0 0 1 10 10c1.863 0 3.697.124 5.495.365C16.967 10.562 18 11.838 18 13.28v.693a3.72 3.72 0 0 1-1.665-.393 5.222 5.222 0 0 0-4.67 0 3.722 3.722 0 0 1-3.33 0 5.222 5.222 0 0 0-4.67 0A3.72 3.72 0 0 1 2 13.972v-.693c0-1.441 1.033-2.717 2.505-2.914ZM15.665 14.92a5.22 5.22 0 0 0 2.335.552V16.5a1.5 1.5 0 0 1-1.5 1.5h-13A1.5 1.5 0 0 1 2 16.5v-1.028c.8 0 1.6-.184 2.335-.551a3.722 3.722 0 0 1 3.33 0c1.47.735 3.2.735 4.67 0a3.722 3.722 0 0 1 3.33 0Z"],color:"var(--ds-color-warning-9)"},performance:{paths:["M4.606 12.97a.75.75 0 0 1-.134 1.051 2.494 2.494 0 0 0-.93 2.437 2.494 2.494 0 0 0 2.437-.93.75.75 0 1 1 1.186.918 3.995 3.995 0 0 1-4.482 1.332.75.75 0 0 1-.461-.461 3.994 3.994 0 0 1 1.332-4.482.75.75 0 0 1 1.052.134Z","M5.752 12A13.07 13.07 0 0 0 8 14.248v4.002c0 .414.336.75.75.75a5 5 0 0 0 4.797-6.414 12.984 12.984 0 0 0 5.45-10.848.75.75 0 0 0-.735-.735 12.984 12.984 0 0 0-10.849 5.45A5 5 0 0 0 1 11.25c.001.414.337.75.751.75h4.002ZM13 9a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z"],color:"var(--ds-color-warning-9)"},revert:{paths:["M7.50043 1.37598C7.2023 1.37598 6.91637 1.49431 6.70543 1.70499L6.70521 1.70521L1.70521 6.70521L1.70499 6.70543C1.49431 6.91637 1.37598 7.2023 1.37598 7.50043C1.37598 7.79855 1.49431 8.08449 1.70499 8.29543L1.70521 8.29565L6.69987 13.2903C6.80149 13.3974 6.92322 13.4835 7.05815 13.5436C7.19615 13.6051 7.34512 13.6382 7.49617 13.6408C7.64723 13.6435 7.79727 13.6157 7.93735 13.5591C8.07744 13.5026 8.20469 13.4183 8.31151 13.3115C8.41834 13.2047 8.50256 13.0774 8.55914 12.9374C8.61572 12.7973 8.64351 12.6472 8.64084 12.4962C8.63818 12.3451 8.60511 12.1961 8.54363 12.0581C8.48351 11.9232 8.39743 11.8015 8.29032 11.6999L5.21587 8.62543H12.5004C13.0093 8.62543 13.5132 8.72566 13.9833 8.92039C14.4535 9.11513 14.8806 9.40056 15.2405 9.76039C15.6003 10.1202 15.8857 10.5474 16.0805 11.0175C16.2752 11.4877 16.3754 11.9916 16.3754 12.5004C16.3754 13.0093 16.2752 13.5132 16.0805 13.9833C15.8857 14.4535 15.6003 14.8806 15.2405 15.2405C14.8806 15.6003 14.4535 15.8857 13.9833 16.0805C13.5132 16.2752 13.0093 16.3754 12.5004 16.3754H10.0004C9.70206 16.3754 9.41591 16.494 9.20493 16.7049C8.99395 16.9159 8.87543 17.2021 8.87543 17.5004C8.87543 17.7988 8.99395 18.0849 9.20493 18.2959C9.41591 18.5069 9.70206 18.6254 10.0004 18.6254H12.5004C14.1249 18.6254 15.6828 17.9801 16.8315 16.8315C17.9801 15.6828 18.6254 14.1249 18.6254 12.5004C18.6254 10.876 17.9801 9.31806 16.8315 8.1694C15.6828 7.02074 14.1249 6.37543 12.5004 6.37543H5.21587L8.29565 3.29565L8.29587 3.29543C8.50654 3.08449 8.62488 2.79855 8.62488 2.50043C8.62488 2.2023 8.50654 1.91636 8.29587 1.70543L8.29543 1.70499C8.08449 1.49431 7.79855 1.37598 7.50043 1.37598Z","M7.712 4.818A1.5 1.5 0 0 1 10 6.095v2.972c.104-.13.234-.248.389-.343l6.323-3.906A1.5 1.5 0 0 1 19 6.095v7.81a1.5 1.5 0 0 1-2.288 1.276l-6.323-3.905a1.505 1.505 0 0 1-.389-.344v2.973a1.5 1.5 0 0 1-2.288 1.276l-6.323-3.905a1.5 1.5 0 0 1 0-2.552l6.323-3.906Z"],color:"var(--ds-color-warning-9)"},other:{paths:["M2 4.25C2 3.65326 2.23705 3.08097 2.65901 2.65901C3.08097 2.23705 3.65326 2 4.25 2H6.75C7.34674 2 7.91903 2.23705 8.34099 2.65901C8.76295 3.08097 9 3.65326 9 4.25V6.75C9 7.34674 8.76295 7.91903 8.34099 8.34099C7.91903 8.76295 7.34674 9 6.75 9H4.25C3.65326 9 3.08097 8.76295 2.65901 8.34099C2.23705 7.91903 2 7.34674 2 6.75V4.25ZM15.25 11.75C15.25 11.5511 15.171 11.3603 15.0303 11.2197C14.8897 11.079 14.6989 11 14.5 11C14.3011 11 14.1103 11.079 13.9697 11.2197C13.829 11.3603 13.75 11.5511 13.75 11.75V13.75H11.75C11.5511 13.75 11.3603 13.829 11.2197 13.9697C11.079 14.1103 11 14.3011 11 14.5C11 14.6989 11.079 14.8897 11.2197 15.0303C11.3603 15.171 11.5511 15.25 11.75 15.25H13.75V17.25C13.75 17.4489 13.829 17.6397 13.9697 17.7803C14.1103 17.921 14.3011 18 14.5 18C14.6989 18 14.8897 17.921 15.0303 17.7803C15.171 17.6397 15.25 17.4489 15.25 17.25V15.25H17.25C17.4489 15.25 17.6397 15.171 17.7803 15.0303C17.921 14.8897 18 14.6989 18 14.5C18 14.3011 17.921 14.1103 17.7803 13.9697C17.6397 13.829 17.4489 13.75 17.25 13.75H15.25V11.75Z","M13.8399 2.86538C14.1332 2.37829 14.867 2.37829 15.1603 2.86538L17.8969 7.40443C18.1901 7.89152 17.8228 8.50006 17.2363 8.50006H11.7635C11.1766 8.50006 10.8097 7.89152 11.1034 7.40443L13.8399 2.86538Z","M9 14.5C9 16.433 7.433 18 5.5 18C3.567 18 2 16.433 2 14.5C2 12.567 3.567 11 5.5 11C7.433 11 9 12.567 9 14.5Z","M13.8399 2.86538C14.1332 2.37829 14.867 2.37829 15.1603 2.86538L17.8969 7.40443C18.1901 7.89152 17.8228 8.50006 17.2363 8.50006H11.7635C11.1766 8.50006 10.8097 7.89152 11.1034 7.40443L13.8399 2.86538Z","M 9 14.5 A 3.5 3.5 0 1 1 2 14.5 A 3.5 3.5 0 1 1 9 14.5 Z"],color:"var(--ds-color-warning-9)"}};return r.$$set=o=>{"type"in o&&t(0,s=o.type)},r.$$.update=()=>{1&r.$$.dirty&&t(1,n=i[s]??i.other)},[s,n]}class Qi extends Y{constructor(e){super(),X(this,e,Wi,Hi,J,{type:0})}}function sn(r,e,t){const n=r.slice();return n[44]=e[t],n[46]=t,n}function rn(r){let e,t,n,s,i;t=new ks({props:{variant:"ghost",color:"neutral",size:1,$$slots:{default:[Yi]},$$scope:{ctx:r}}}),t.$on("click",r[23]);let o=ne(r[1]),l=[];for(let a=0;a<o.length;a+=1)l[a]=on(sn(r,o,a));const u=a=>g(l[a],1,1,()=>{l[a]=null});return{c(){e=_("div"),M(t.$$.fragment),n=Z(),s=_("div");for(let a=0;a<l.length;a+=1)l[a].c();A(e,"class","toggle-button svelte-14s1ghg"),A(s,"class","descriptions svelte-14s1ghg"),me(s,"transform","translateY("+-r[4]+"px)")},m(a,c){x(a,e,c),T(t,e,null),x(a,n,c),x(a,s,c);for(let d=0;d<l.length;d+=1)l[d]&&l[d].m(s,null);i=!0},p(a,c){const d={};if(1&c[0]|65536&c[1]&&(d.$$scope={dirty:c,ctx:a}),t.$set(d),546&c[0]){let p;for(o=ne(a[1]),p=0;p<o.length;p+=1){const D=sn(a,o,p);l[p]?(l[p].p(D,c),h(l[p],1)):(l[p]=on(D),l[p].c(),h(l[p],1),l[p].m(s,null))}for(W(),p=o.length;p<l.length;p+=1)u(p);Q()}(!i||16&c[0])&&me(s,"transform","translateY("+-a[4]+"px)")},i(a){if(!i){h(t.$$.fragment,a);for(let c=0;c<o.length;c+=1)h(l[c]);i=!0}},o(a){g(t.$$.fragment,a),l=l.filter(Boolean);for(let c=0;c<l.length;c+=1)g(l[c]);i=!1},d(a){a&&(v(e),v(n),v(s)),S(t),ve(l,a)}}}function Gi(r){let e,t;return e=new Ke({props:{icon:"book"}}),{c(){M(e.$$.fragment)},m(n,s){T(e,n,s),t=!0},i(n){t||(h(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){S(e,n)}}}function Ji(r){let e,t;return e=new Ke({props:{icon:"x"}}),{c(){M(e.$$.fragment)},m(n,s){T(e,n,s),t=!0},i(n){t||(h(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){S(e,n)}}}function Yi(r){let e,t,n,s;const i=[Ji,Gi],o=[];function l(u,a){return u[0]?0:1}return e=l(r),t=o[e]=i[e](r),{c(){t.c(),n=Ce()},m(u,a){o[e].m(u,a),x(u,n,a),s=!0},p(u,a){let c=e;e=l(u),e!==c&&(W(),g(o[c],1,1,()=>{o[c]=null}),Q(),t=o[e],t||(t=o[e]=i[e](u),t.c()),h(t,1),t.m(n.parentNode,n))},i(u){s||(h(t),s=!0)},o(u){g(t),s=!1},d(u){u&&v(n),o[e].d(u)}}}function on(r){let e,t,n,s;return t=new ws({props:{markdown:r[44].text}}),{c(){e=_("div"),M(t.$$.fragment),n=Z(),A(e,"class","description svelte-14s1ghg"),me(e,"top",(r[5][r[46]]||r[9](r[44]))+"px"),me(e,"--ds-panel-solid","transparent")},m(i,o){x(i,e,o),T(t,e,null),L(e,n),s=!0},p(i,o){const l={};2&o[0]&&(l.markdown=i[44].text),t.$set(l),(!s||34&o[0])&&me(e,"top",(i[5][i[46]]||i[9](i[44]))+"px")},i(i){s||(h(t.$$.fragment,i),s=!0)},o(i){g(t.$$.fragment,i),s=!1},d(i){i&&v(e),S(t)}}}function Xi(r){let e,t,n,s,i=r[1].length>0&&rn(r);return{c(){e=_("div"),t=_("div"),n=Z(),i&&i.c(),A(t,"class","editor-container svelte-14s1ghg"),me(t,"height",r[3]+"px"),A(e,"class","monaco-diff-container svelte-14s1ghg"),$e(e,"monaco-diff-container-with-descriptions",r[1].length>0&&r[0])},m(o,l){x(o,e,l),L(e,t),r[22](t),L(e,n),i&&i.m(e,null),s=!0},p(o,l){(!s||8&l[0])&&me(t,"height",o[3]+"px"),o[1].length>0?i?(i.p(o,l),2&l[0]&&h(i,1)):(i=rn(o),i.c(),h(i,1),i.m(e,null)):i&&(W(),g(i,1,1,()=>{i=null}),Q()),(!s||3&l[0])&&$e(e,"monaco-diff-container-with-descriptions",o[1].length>0&&o[0])},i(o){s||(h(i),s=!0)},o(o){g(i),s=!1},d(o){o&&v(e),r[22](null),i&&i.d()}}}function Ki(r,e,t){let n,s,i;const o=as();let{originalCode:l=""}=e,{modifiedCode:u=""}=e,{path:a}=e,{descriptions:c=[]}=e,{lineOffset:d=0}=e,{extraPrefixLines:p=[]}=e,{extraSuffixLines:D=[]}=e,{theme:b}=e,{areDescriptionsVisible:m=!0}=e,{isNewFile:k=!1}=e;const y=Et.getContext().monaco;let w,$,B,f;be(r,y,F=>t(21,n=F));let C,E=[],V=Ie(0);be(r,V,F=>t(4,s=F));let j=k?20*u.split(`
`).length+40:100;const K=n?n.languages.getLanguages().map(F=>F.id):[];function te(F,z){var N,P;if(z){const O=(N=z.split(".").pop())==null?void 0:N.toLowerCase();if(O){const H=(P=n==null?void 0:n.languages.getLanguages().find(ce=>{var ie;return(ie=ce.extensions)==null?void 0:ie.includes("."+O)}))==null?void 0:P.id;if(H&&K.includes(H))return H}}return"plaintext"}const pe=Ie({});be(r,pe,F=>t(5,i=F));let q=null;function oe(){if(!w)return;E=E.filter(N=>(N.dispose(),!1));const F=w.getOriginalEditor(),z=w.getModifiedEditor();E.push(F.onDidScrollChange(()=>{ot(V,s=F.getScrollTop(),s)}),z.onDidScrollChange(()=>{ot(V,s=z.getScrollTop(),s)}))}function ge(){if(!w||!C)return;const F=w.getOriginalEditor(),z=w.getModifiedEditor();E.push(z.onDidContentSizeChange(()=>ae()),F.onDidContentSizeChange(()=>ae()),w.onDidUpdateDiff(()=>ae()),z.onDidChangeHiddenAreas(()=>ae()),F.onDidChangeHiddenAreas(()=>ae()),z.onDidLayoutChange(()=>ae()),F.onDidLayoutChange(()=>ae()),z.onDidFocusEditorWidget(()=>{R(!0)}),F.onDidFocusEditorWidget(()=>{R(!0)}),z.onDidBlurEditorWidget(()=>{R(!1)}),F.onDidBlurEditorWidget(()=>{R(!1)}),z.onDidChangeModelContent(()=>{we=!0,_e=Date.now();const N=(f==null?void 0:f.getValue())||"";if(N===u)return;const P=N.replace(p.join(""),"").replace(D.join(""),"");o("codeChange",{modifiedCode:P});const O=setTimeout(()=>{we=!1},500);E.push({dispose:()=>clearTimeout(O)})})),function(){!C||!w||(q&&clearTimeout(q),q=setTimeout(()=>{if(!C.__hasClickListener){const N=P=>{const O=P.target;O&&(O.closest('[title="Show Unchanged Region"]')||O.closest('[title="Hide Unchanged Region"]'))&&Ee()};C.addEventListener("click",N),t(2,C.__hasClickListener=!0,C),E.push({dispose:()=>{C.removeEventListener("click",N)}})}w&&E.push(w.onDidUpdateDiff(()=>{Ee()}))},300))}()}cs(()=>{w==null||w.dispose(),$==null||$.dispose(),B==null||B.dispose(),f==null||f.dispose(),E.forEach(F=>F.dispose()),q&&clearTimeout(q)});let ee=null;function Ee(){ee&&clearTimeout(ee),ee=setTimeout(()=>{ae(),ee=null},100),ee&&E.push({dispose:()=>{ee&&(clearTimeout(ee),ee=null)}})}function Te(F,z,N,P=[],O=[]){if(!n)return void console.error("Monaco not loaded. Diff view cannot be updated.");B==null||B.dispose(),f==null||f.dispose(),z=z||"",N=N||"";const H=P.join(""),ce=O.join("");z=k?N.split(`
`).map(()=>" ").join(`
`):H+z+ce,N=H+N+ce,B=n.editor.createModel(z,void 0,F!==void 0?n.Uri.parse("file://"+F+`#${crypto.randomUUID()}`):void 0),t(20,f=n.editor.createModel(N,void 0,F!==void 0?n.Uri.parse("file://"+F+`#${crypto.randomUUID()}`):void 0)),w&&(w.setModel({original:B,modified:f}),oe(),q&&clearTimeout(q),q=setTimeout(()=>{ge(),q=null},300))}Xe(()=>{if(n)if(k){t(19,$=n.editor.create(C,{automaticLayout:!0,stickyScroll:{enabled:!0},scrollBeyondLastLine:!1,minimap:{enabled:!1},overviewRulerBorder:!1,theme:b,scrollbar:{alwaysConsumeMouseWheel:!1,handleMouseWheel:!1},lineNumbers:N=>`${d-p.length+N}`}));const F=te(0,a);t(20,f=n.editor.createModel(u,F,a!==void 0?n.Uri.parse("file://"+a+`#${crypto.randomUUID()}`):void 0)),$.setModel(f),E.push($.onDidChangeModelContent(()=>{we=!0,_e=Date.now();const N=(f==null?void 0:f.getValue())||"";if(N===u)return;o("codeChange",{modifiedCode:N});const P=setTimeout(()=>{we=!1},500);E.push({dispose:()=>clearTimeout(P)})})),E.push($.onDidFocusEditorWidget(()=>{$==null||$.updateOptions({scrollbar:{handleMouseWheel:!0}})}),$.onDidBlurEditorWidget(()=>{$==null||$.updateOptions({scrollbar:{handleMouseWheel:!1}})})),t(3,j=20*u.split(`
`).length+40);const z=setTimeout(()=>{$==null||$.layout()},0);E.push({dispose:()=>clearTimeout(z)})}else t(18,w=n.editor.createDiffEditor(C,{automaticLayout:!0,useInlineViewWhenSpaceIsLimited:!0,enableSplitViewResizing:!0,stickyScroll:{enabled:!0},scrollBeyondLastLine:!1,minimap:{enabled:!1},renderOverviewRuler:!1,renderGutterMenu:!1,theme:b,scrollbar:{alwaysConsumeMouseWheel:!1,handleMouseWheel:!1},lineNumbers:F=>`${d-p.length+F}`,hideUnchangedRegions:{enabled:!0,revealLineCount:3,minimumLineCount:3,contextLineCount:3}})),Te(a,l,u,p,D),oe(),ge(),q&&clearTimeout(q),q=setTimeout(()=>{ae(),q=null},100);else console.error("Monaco not loaded. Diff view cannot be initialized.")});let we=!1,_e=0;function Se(F,z=!0){return w?(z?w.getModifiedEditor():w.getOriginalEditor()).getTopForLineNumber(F):18*F}function ae(){if(!w)return;const F=w.getModel(),z=F==null?void 0:F.original,N=F==null?void 0:F.modified;if(!z||!N)return;const P=w.getOriginalEditor(),O=w.getModifiedEditor(),H=w.getLineChanges()||[];let ce;if(H.length===0){const ie=P.getContentHeight(),he=O.getContentHeight();ce=Math.max(100,ie,he)}else{let ie=0,he=0;for(const Me of H)Me.originalEndLineNumber>0&&(ie=Math.max(ie,Me.originalEndLineNumber)),Me.modifiedEndLineNumber>0&&(he=Math.max(he,Me.modifiedEndLineNumber));ie=Math.min(ie+3,z.getLineCount()),he=Math.min(he+3,N.getLineCount());const Re=P.getTopForLineNumber(ie),Dt=O.getTopForLineNumber(he);ce=Math.max(Re,Dt)+60}t(3,j=Math.min(ce,2e3)),w.layout(),re()}function R(F){if(!w)return;const z=w.getOriginalEditor(),N=w.getModifiedEditor();z.updateOptions({scrollbar:{handleMouseWheel:F}}),N.updateOptions({scrollbar:{handleMouseWheel:F}})}function I(F){if(!w)return 0;const z=w.getModel(),N=z==null?void 0:z.original,P=z==null?void 0:z.modified;if(!N||!P)return 0;const O=Se(F.range.start+1,!1),H=Se(F.range.start+1,!0);return O&&!H?O:!O&&H?H:Math.min(O,H)}function re(){if(!w||c.length===0)return;const F={};c.forEach((z,N)=>{F[N]=I(z)}),pe.set(F)}return r.$$set=F=>{"originalCode"in F&&t(10,l=F.originalCode),"modifiedCode"in F&&t(11,u=F.modifiedCode),"path"in F&&t(12,a=F.path),"descriptions"in F&&t(1,c=F.descriptions),"lineOffset"in F&&t(13,d=F.lineOffset),"extraPrefixLines"in F&&t(14,p=F.extraPrefixLines),"extraSuffixLines"in F&&t(15,D=F.extraSuffixLines),"theme"in F&&t(16,b=F.theme),"areDescriptionsVisible"in F&&t(0,m=F.areDescriptionsVisible),"isNewFile"in F&&t(17,k=F.isNewFile)},r.$$.update=()=>{if(4119552&r.$$.dirty[0]&&(F=u,!(we||Date.now()-_e<1e3||f&&f.getValue()===p.join("")+F+D.join(""))))if(k&&$){if(f)f.setValue(u);else{const z=te(0,a);n&&t(20,f=n.editor.createModel(u,z,a!==void 0?n.Uri.parse("file://"+a+`#${crypto.randomUUID()}`):void 0)),f&&$.setModel(f)}t(3,j=20*u.split(`
`).length+40),$.layout()}else!k&&w&&(Te(a,l,u,p,D),ae());var F;262146&r.$$.dirty[0]&&w&&c.length>0&&re(),657408&r.$$.dirty[0]&&k&&u&&$&&(t(3,j=20*u.split(`
`).length+40),$.layout())},[m,c,C,j,s,i,y,V,pe,I,l,u,a,d,p,D,b,k,w,$,f,n,function(F){Ae[F?"unshift":"push"](()=>{C=F,t(2,C)})},()=>t(0,m=!m)]}class er extends Y{constructor(e){super(),X(this,e,Ki,Xi,J,{originalCode:10,modifiedCode:11,path:12,descriptions:1,lineOffset:13,extraPrefixLines:14,extraSuffixLines:15,theme:16,areDescriptionsVisible:0,isNewFile:17},null,[-1,-1])}}function tr(r){let e,t,n,s;function i(l){r[20](l)}let o={path:r[3],originalCode:r[0].originalCode,modifiedCode:r[7],theme:r[9],descriptions:r[4],isNewFile:r[10]};return r[1]!==void 0&&(o.areDescriptionsVisible=r[1]),t=new er({props:o}),Ae.push(()=>Oe(t,"areDescriptionsVisible",i)),t.$on("codeChange",r[13]),{c(){e=_("div"),M(t.$$.fragment),A(e,"class","changes svelte-1o3rbzw")},m(l,u){x(l,e,u),T(t,e,null),s=!0},p(l,u){const a={};8&u&&(a.path=l[3]),1&u&&(a.originalCode=l[0].originalCode),128&u&&(a.modifiedCode=l[7]),512&u&&(a.theme=l[9]),16&u&&(a.descriptions=l[4]),1024&u&&(a.isNewFile=l[10]),!n&&2&u&&(n=!0,a.areDescriptionsVisible=l[1],Ve(()=>n=!1)),t.$set(a)},i(l){s||(h(t.$$.fragment,l),s=!0)},o(l){g(t.$$.fragment,l),s=!1},d(l){l&&v(e),S(t)}}}function nr(r){let e,t=at(r[8])+"";return{c(){e=U(t)},m(n,s){x(n,e,s)},p(n,s){256&s&&t!==(t=at(n[8])+"")&&de(e,t)},d(n){n&&v(e)}}}function sr(r){let e,t;return e=new Le({props:{variant:"ghost-block",color:"neutral",size:1,class:"c-codeblock__filename",$$slots:{default:[nr]},$$scope:{ctx:r}}}),{c(){M(e.$$.fragment)},m(n,s){T(e,n,s),t=!0},p(n,s){const i={};4194560&s&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(h(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){S(e,n)}}}function ln(r){let e,t,n=Ne(r[8])+"";return{c(){e=_("span"),t=U(n),A(e,"class","c-directory svelte-1o3rbzw")},m(s,i){x(s,e,i),L(e,t)},p(s,i){256&i&&n!==(n=Ne(s[8])+"")&&de(t,n)},d(s){s&&v(e)}}}function ir(r){let e,t,n,s=r[12]>0&&un(r),i=r[11]>0&&an(r);return{c(){e=_("div"),s&&s.c(),t=Z(),i&&i.c(),A(e,"class","changes-indicator svelte-1o3rbzw")},m(o,l){x(o,e,l),s&&s.m(e,null),L(e,t),i&&i.m(e,null),n=!0},p(o,l){o[12]>0?s?(s.p(o,l),4096&l&&h(s,1)):(s=un(o),s.c(),h(s,1),s.m(e,t)):s&&(W(),g(s,1,1,()=>{s=null}),Q()),o[11]>0?i?(i.p(o,l),2048&l&&h(i,1)):(i=an(o),i.c(),h(i,1),i.m(e,null)):i&&(W(),g(i,1,1,()=>{i=null}),Q())},i(o){n||(h(s),h(i),n=!0)},o(o){g(s),g(i),n=!1},d(o){o&&v(e),s&&s.d(),i&&i.d()}}}function rr(r){let e;return{c(){e=_("span"),e.textContent="New File",A(e,"class","new-file-badge svelte-1o3rbzw")},m(t,n){x(t,e,n)},p:G,i:G,o:G,d(t){t&&v(e)}}}function un(r){let e,t,n;return t=new fe({props:{size:1,$$slots:{default:[or]},$$scope:{ctx:r}}}),{c(){e=_("span"),M(t.$$.fragment),A(e,"class","additions svelte-1o3rbzw")},m(s,i){x(s,e,i),T(t,e,null),n=!0},p(s,i){const o={};4198400&i&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){n||(h(t.$$.fragment,s),n=!0)},o(s){g(t.$$.fragment,s),n=!1},d(s){s&&v(e),S(t)}}}function or(r){let e,t;return{c(){e=U("+"),t=U(r[12])},m(n,s){x(n,e,s),x(n,t,s)},p(n,s){4096&s&&de(t,n[12])},d(n){n&&(v(e),v(t))}}}function an(r){let e,t,n;return t=new fe({props:{size:1,$$slots:{default:[lr]},$$scope:{ctx:r}}}),{c(){e=_("span"),M(t.$$.fragment),A(e,"class","deletions svelte-1o3rbzw")},m(s,i){x(s,e,i),T(t,e,null),n=!0},p(s,i){const o={};4196352&i&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){n||(h(t.$$.fragment,s),n=!0)},o(s){g(t.$$.fragment,s),n=!1},d(s){s&&v(e),S(t)}}}function lr(r){let e,t;return{c(){e=U("-"),t=U(r[11])},m(n,s){x(n,e,s),x(n,t,s)},p(n,s){2048&s&&de(t,n[11])},d(n){n&&(v(e),v(t))}}}function ur(r){let e,t;return e=new qe({props:{content:r[5]?"Applying changes...":"Apply changes to file",$$slots:{default:[dr]},$$scope:{ctx:r}}}),{c(){M(e.$$.fragment)},m(n,s){T(e,n,s),t=!0},p(n,s){const i={};32&s&&(i.content=n[5]?"Applying changes...":"Apply changes to file"),4194304&s&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(h(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){S(e,n)}}}function ar(r){let e,t,n;return t=new fe({props:{size:1,$$slots:{default:[pr]},$$scope:{ctx:r}}}),{c(){e=_("div"),M(t.$$.fragment),A(e,"class","applied svelte-1o3rbzw")},m(s,i){x(s,e,i),T(t,e,null),n=!0},p(s,i){const o={};4194304&i&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){n||(h(t.$$.fragment,s),n=!0)},o(s){g(t.$$.fragment,s),n=!1},d(s){s&&v(e),S(t)}}}function cr(r){let e,t,n,s;return n=new et({}),{c(){e=U(`Apply
            `),t=_("div"),M(n.$$.fragment),A(t,"class","applied__icon svelte-1o3rbzw")},m(i,o){x(i,e,o),x(i,t,o),T(n,t,null),s=!0},p:G,i(i){s||(h(n.$$.fragment,i),s=!0)},o(i){g(n.$$.fragment,i),s=!1},d(i){i&&(v(e),v(t)),S(n)}}}function dr(r){let e,t;return e=new Le({props:{variant:"ghost-block",color:"neutral",size:2,$$slots:{default:[cr]},$$scope:{ctx:r}}}),e.$on("click",r[14]),{c(){M(e.$$.fragment)},m(n,s){T(e,n,s),t=!0},p(n,s){const i={};4194304&s&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(h(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){S(e,n)}}}function pr(r){let e,t,n;return t=new gt({props:{iconName:"check"}}),{c(){e=U(`Applied
            `),M(t.$$.fragment)},m(s,i){x(s,e,i),T(t,s,i),n=!0},p:G,i(s){n||(h(t.$$.fragment,s),n=!0)},o(s){g(t.$$.fragment,s),n=!1},d(s){s&&v(e),S(t,s)}}}function fr(r){let e,t,n,s,i,o,l,u,a,c,d,p,D,b=Ne(r[8]);t=new Ds({}),i=new qe({props:{content:r[8],triggerOn:[_t.Hover],$$slots:{default:[sr]},$$scope:{ctx:r}}});let m=b&&ln(r);const k=[rr,ir],y=[];function w(C,E){return C[10]?0:1}u=w(r),a=y[u]=k[u](r);const $=[ar,ur],B=[];function f(C,E){return C[6]?0:1}return d=f(r),p=B[d]=$[d](r),{c(){e=_("div"),M(t.$$.fragment),n=Z(),s=_("div"),M(i.$$.fragment),o=Z(),m&&m.c(),l=Z(),a.c(),c=Z(),p.c(),A(s,"class","c-path svelte-1o3rbzw"),A(e,"slot","header"),A(e,"class","header svelte-1o3rbzw")},m(C,E){x(C,e,E),T(t,e,null),L(e,n),L(e,s),T(i,s,null),L(s,o),m&&m.m(s,null),L(e,l),y[u].m(e,null),L(e,c),B[d].m(e,null),D=!0},p(C,E){const V={};256&E&&(V.content=C[8]),4194560&E&&(V.$$scope={dirty:E,ctx:C}),i.$set(V),256&E&&(b=Ne(C[8])),b?m?m.p(C,E):(m=ln(C),m.c(),m.m(s,null)):m&&(m.d(1),m=null);let j=u;u=w(C),u===j?y[u].p(C,E):(W(),g(y[j],1,1,()=>{y[j]=null}),Q(),a=y[u],a?a.p(C,E):(a=y[u]=k[u](C),a.c()),h(a,1),a.m(e,c));let K=d;d=f(C),d===K?B[d].p(C,E):(W(),g(B[K],1,1,()=>{B[K]=null}),Q(),p=B[d],p?p.p(C,E):(p=B[d]=$[d](C),p.c()),h(p,1),p.m(e,null))},i(C){D||(h(t.$$.fragment,C),h(i.$$.fragment,C),h(a),h(p),D=!0)},o(C){g(t.$$.fragment,C),g(i.$$.fragment,C),g(a),g(p),D=!1},d(C){C&&v(e),S(t),S(i),m&&m.d(),y[u].d(),B[d].d()}}}function hr(r){let e,t,n,s;function i(l){r[21](l)}let o={stickyHeader:!0,$$slots:{header:[fr],default:[tr]},$$scope:{ctx:r}};return r[2]!==void 0&&(o.collapsed=r[2]),t=new ms({props:o}),Ae.push(()=>Oe(t,"collapsed",i)),{c(){e=_("div"),M(t.$$.fragment),A(e,"class","c svelte-1o3rbzw")},m(l,u){x(l,e,u),T(t,e,null),s=!0},p(l,[u]){const a={};4202491&u&&(a.$$scope={dirty:u,ctx:l}),!n&&4&u&&(n=!0,a.collapsed=l[2],Ve(()=>n=!1)),t.$set(a)},i(l){s||(h(t.$$.fragment,l),s=!0)},o(l){g(t.$$.fragment,l),s=!1},d(l){l&&v(e),S(t)}}}function gr(r,e,t){let n,s,i,o,l,u,a;be(r,ds,f=>t(19,a=f));let{path:c}=e,{change:d}=e,{descriptions:p=[]}=e,{areDescriptionsVisible:D=!0}=e,{isExpandedDefault:b}=e,{isCollapsed:m=!b}=e,{isApplying:k}=e,{hasApplied:y}=e,{onApplyChanges:w}=e,{onCodeChange:$}=e,B=d.modifiedCode;return r.$$set=f=>{"path"in f&&t(3,c=f.path),"change"in f&&t(0,d=f.change),"descriptions"in f&&t(4,p=f.descriptions),"areDescriptionsVisible"in f&&t(1,D=f.areDescriptionsVisible),"isExpandedDefault"in f&&t(15,b=f.isExpandedDefault),"isCollapsed"in f&&t(2,m=f.isCollapsed),"isApplying"in f&&t(5,k=f.isApplying),"hasApplied"in f&&t(6,y=f.hasApplied),"onApplyChanges"in f&&t(16,w=f.onApplyChanges),"onCodeChange"in f&&t(17,$=f.onCodeChange)},r.$$.update=()=>{1&r.$$.dirty&&t(7,B=d.modifiedCode),1&r.$$.dirty&&t(18,n=Gs(d.diff)),262144&r.$$.dirty&&t(12,s=n.additions),262144&r.$$.dirty&&t(11,i=n.deletions),1&r.$$.dirty&&t(10,o=Js(d)),524288&r.$$.dirty&&t(9,l=Fs(a==null?void 0:a.category,a==null?void 0:a.intensity)),8&r.$$.dirty&&t(8,u=xs(c))},[d,D,m,c,p,k,y,B,u,l,o,i,s,function(f){t(7,B=f.detail.modifiedCode),$==null||$(B)},function(){t(0,d.modifiedCode=B,d),$==null||$(B),w==null||w()},b,w,$,n,a,function(f){D=f,t(1,D)},function(f){m=f,t(2,m)}]}class $r extends Y{constructor(e){super(),X(this,e,gr,hr,J,{path:3,change:0,descriptions:4,areDescriptionsVisible:1,isExpandedDefault:15,isCollapsed:2,isApplying:5,hasApplied:6,onApplyChanges:16,onCodeChange:17})}}function cn(r,e,t){const n=r.slice();return n[1]=e[t],n[3]=t,n}function mr(r,e,t){const n=r.slice();return n[1]=e[t],n}function Dr(r,e,t){const n=r.slice();return n[1]=e[t],n}function Fr(r){let e;return{c(){e=_("div"),e.innerHTML='<div class="c-skeleton-diff__file-header svelte-1eiztmz"><div class="c-skeleton-diff__file-info svelte-1eiztmz"><div class="c-skeleton-diff__file-icon svelte-1eiztmz"></div> <div class="c-skeleton-diff__file-path svelte-1eiztmz"></div></div> <div class="c-skeleton-diff__file-actions svelte-1eiztmz"></div></div> <div class="c-skeleton-diff__code-block svelte-1eiztmz"><div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz"></span></div> <div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz" style="width: 70%;"></span></div> <div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz" style="width: 85%;"></span></div> <div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz" style="width: 60%;"></span></div></div>',A(e,"class","c-skeleton-diff__changes-item svelte-1eiztmz")},m(t,n){x(t,e,n)},p:G,d(t){t&&v(e)}}}function kr(r){let e,t,n,s,i=ne(Array(2)),o=[];for(let l=0;l<i.length;l+=1)o[l]=Fr(Dr(r,i,l));return{c(){e=_("div"),t=_("div"),t.innerHTML='<div class="c-skeleton-diff__content svelte-1eiztmz"><div class="c-skeleton-diff__subtitle svelte-1eiztmz"></div></div> <div class="c-skeleton-diff__icon svelte-1eiztmz"></div>',n=Z(),s=_("div");for(let l=0;l<o.length;l+=1)o[l].c();A(t,"class","c-skeleton-diff__header svelte-1eiztmz"),A(s,"class","c-skeleton-diff__changes svelte-1eiztmz"),A(e,"class","c-skeleton-diff__subsection svelte-1eiztmz")},m(l,u){x(l,e,u),L(e,t),L(e,n),L(e,s);for(let a=0;a<o.length;a+=1)o[a]&&o[a].m(s,null)},p:G,d(l){l&&v(e),ve(o,l)}}}function dn(r){let e,t,n,s,i,o,l=r[3]===0&&function(c){let d;return{c(){d=_("div"),d.innerHTML='<div class="c-skeleton-diff__button svelte-1eiztmz"></div>',A(d,"class","c-skeleton-diff__controls svelte-1eiztmz")},m(p,D){x(p,d,D)},d(p){p&&v(d)}}}(),u=ne(Array(2)),a=[];for(let c=0;c<u.length;c+=1)a[c]=kr(mr(r,u,c));return{c(){e=_("div"),t=_("div"),n=_("div"),n.innerHTML='<div class="c-skeleton-diff__title svelte-1eiztmz"></div> <div class="c-skeleton-diff__description svelte-1eiztmz"><div class="c-skeleton-diff__line svelte-1eiztmz"></div> <div class="c-skeleton-diff__line svelte-1eiztmz" style="width: 85%;"></div></div>',s=Z(),l&&l.c(),i=Z();for(let c=0;c<a.length;c+=1)a[c].c();o=Z(),A(n,"class","c-skeleton-diff__content svelte-1eiztmz"),A(t,"class","c-skeleton-diff__header svelte-1eiztmz"),A(e,"class","c-skeleton-diff__section svelte-1eiztmz")},m(c,d){x(c,e,d),L(e,t),L(t,n),L(t,s),l&&l.m(t,null),L(e,i);for(let p=0;p<a.length;p+=1)a[p]&&a[p].m(e,null);L(e,o)},p(c,d){},d(c){c&&v(e),l&&l.d(),ve(a,c)}}}function xr(r){let e,t=ne(Array(r[0])),n=[];for(let s=0;s<t.length;s+=1)n[s]=dn(cn(r,t,s));return{c(){e=_("div");for(let s=0;s<n.length;s+=1)n[s].c();A(e,"class","c-skeleton-diff svelte-1eiztmz")},m(s,i){x(s,e,i);for(let o=0;o<n.length;o+=1)n[o]&&n[o].m(e,null)},p(s,[i]){if(1&i){let o;for(t=ne(Array(s[0])),o=0;o<t.length;o+=1){const l=cn(s,t,o);n[o]?n[o].p(l,i):(n[o]=dn(l),n[o].c(),n[o].m(e,null))}for(;o<n.length;o+=1)n[o].d(1);n.length=t.length}},i:G,o:G,d(s){s&&v(e),ve(n,s)}}}function vr(r,e,t){let{count:n=2}=e;return r.$$set=s=>{"count"in s&&t(0,n=s.count)},[n]}class Cr extends Y{constructor(e){super(),X(this,e,vr,xr,J,{count:0})}}function pn(...r){return"/"+r.flatMap(e=>e.split("/")).filter(e=>!!e).join("/")}function fn(r){return r.startsWith("/")||r.startsWith("#")}function Ft(r){let e,t;const n=r[5].default,s=De(n,r,r[4],null);let i=[{id:r[1]}],o={};for(let l=0;l<i.length;l+=1)o=fs(o,i[l]);return{c(){e=_(`h${r[0].depth}`),s&&s.c(),lt(`h${r[0].depth}`)(e,o)},m(l,u){x(l,e,u),s&&s.m(e,null),t=!0},p(l,u){s&&s.p&&(!t||16&u)&&Fe(s,n,l,l[4],t?xe(n,l[4],u,null):ke(l[4]),null),lt(`h${l[0].depth}`)(e,o=hs(i,[(!t||2&u)&&{id:l[1]}]))},i(l){t||(h(s,l),t=!0)},o(l){g(s,l),t=!1},d(l){l&&v(e),s&&s.d(l)}}}function wr(r){let e,t,n=`h${r[0].depth}`,s=`h${r[0].depth}`&&Ft(r);return{c(){s&&s.c(),e=Ce()},m(i,o){s&&s.m(i,o),x(i,e,o),t=!0},p(i,[o]){`h${i[0].depth}`?n?J(n,`h${i[0].depth}`)?(s.d(1),s=Ft(i),n=`h${i[0].depth}`,s.c(),s.m(e.parentNode,e)):s.p(i,o):(s=Ft(i),n=`h${i[0].depth}`,s.c(),s.m(e.parentNode,e)):n&&(s.d(1),s=null,n=`h${i[0].depth}`)},i(i){t||(h(s,i),t=!0)},o(i){g(s,i),t=!1},d(i){i&&v(e),s&&s.d(i)}}}function Ar(r,e,t){let{$$slots:n={},$$scope:s}=e,{token:i}=e,{options:o}=e,l;return r.$$set=u=>{"token"in u&&t(0,i=u.token),"options"in u&&t(2,o=u.options),"$$scope"in u&&t(4,s=u.$$scope)},r.$$.update=()=>{var u,a;5&r.$$.dirty&&t(1,(u=i.text,a=o.slugger,l=a.slug(u).replace(/--+/g,"-")))},[i,l,o,void 0,s,n]}class yr extends Y{constructor(e){super(),X(this,e,Ar,wr,J,{token:0,options:2,renderers:3})}get renderers(){return this.$$.ctx[3]}}function br(r){let e,t;const n=r[4].default,s=De(n,r,r[3],null);return{c(){e=_("blockquote"),s&&s.c()},m(i,o){x(i,e,o),s&&s.m(e,null),t=!0},p(i,[o]){s&&s.p&&(!t||8&o)&&Fe(s,n,i,i[3],t?xe(n,i[3],o,null):ke(i[3]),null)},i(i){t||(h(s,i),t=!0)},o(i){g(s,i),t=!1},d(i){i&&v(e),s&&s.d(i)}}}function Er(r,e,t){let{$$slots:n={},$$scope:s}=e;return r.$$set=i=>{"$$scope"in i&&t(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}class _r extends Y{constructor(e){super(),X(this,e,Er,br,J,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function hn(r,e,t){const n=r.slice();return n[3]=e[t],n}function gn(r){let e,t,n=ne(r[0]),s=[];for(let o=0;o<n.length;o+=1)s[o]=$n(hn(r,n,o));const i=o=>g(s[o],1,1,()=>{s[o]=null});return{c(){for(let o=0;o<s.length;o+=1)s[o].c();e=Ce()},m(o,l){for(let u=0;u<s.length;u+=1)s[u]&&s[u].m(o,l);x(o,e,l),t=!0},p(o,l){if(7&l){let u;for(n=ne(o[0]),u=0;u<n.length;u+=1){const a=hn(o,n,u);s[u]?(s[u].p(a,l),h(s[u],1)):(s[u]=$n(a),s[u].c(),h(s[u],1),s[u].m(e.parentNode,e))}for(W(),u=n.length;u<s.length;u+=1)i(u);Q()}},i(o){if(!t){for(let l=0;l<n.length;l+=1)h(s[l]);t=!0}},o(o){s=s.filter(Boolean);for(let l=0;l<s.length;l+=1)g(s[l]);t=!1},d(o){o&&v(e),ve(s,o)}}}function $n(r){let e,t;return e=new As({props:{token:r[3],renderers:r[1],options:r[2]}}),{c(){M(e.$$.fragment)},m(n,s){T(e,n,s),t=!0},p(n,s){const i={};1&s&&(i.token=n[3]),2&s&&(i.renderers=n[1]),4&s&&(i.options=n[2]),e.$set(i)},i(n){t||(h(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){S(e,n)}}}function Br(r){let e,t,n=r[0]&&gn(r);return{c(){n&&n.c(),e=Ce()},m(s,i){n&&n.m(s,i),x(s,e,i),t=!0},p(s,[i]){s[0]?n?(n.p(s,i),1&i&&h(n,1)):(n=gn(s),n.c(),h(n,1),n.m(e.parentNode,e)):n&&(W(),g(n,1,1,()=>{n=null}),Q())},i(s){t||(h(n),t=!0)},o(s){g(n),t=!1},d(s){s&&v(e),n&&n.d(s)}}}function zr(r,e,t){let{tokens:n}=e,{renderers:s}=e,{options:i}=e;return r.$$set=o=>{"tokens"in o&&t(0,n=o.tokens),"renderers"in o&&t(1,s=o.renderers),"options"in o&&t(2,i=o.options)},[n,s,i]}class $t extends Y{constructor(e){super(),X(this,e,zr,Br,J,{tokens:0,renderers:1,options:2})}}function mn(r){let e,t,n;var s=r[1][r[0].type];function i(o,l){return{props:{token:o[0],options:o[2],renderers:o[1],$$slots:{default:[Tr]},$$scope:{ctx:o}}}}return s&&(e=Ot(s,i(r))),{c(){e&&M(e.$$.fragment),t=Ce()},m(o,l){e&&T(e,o,l),x(o,t,l),n=!0},p(o,l){if(3&l&&s!==(s=o[1][o[0].type])){if(e){W();const u=e;g(u.$$.fragment,1,0,()=>{S(u,1)}),Q()}s?(e=Ot(s,i(o)),M(e.$$.fragment),h(e.$$.fragment,1),T(e,t.parentNode,t)):e=null}else if(s){const u={};1&l&&(u.token=o[0]),4&l&&(u.options=o[2]),2&l&&(u.renderers=o[1]),15&l&&(u.$$scope={dirty:l,ctx:o}),e.$set(u)}},i(o){n||(e&&h(e.$$.fragment,o),n=!0)},o(o){e&&g(e.$$.fragment,o),n=!1},d(o){o&&v(t),e&&S(e,o)}}}function Lr(r){let e,t=r[0].raw+"";return{c(){e=U(t)},m(n,s){x(n,e,s)},p(n,s){1&s&&t!==(t=n[0].raw+"")&&de(e,t)},i:G,o:G,d(n){n&&v(e)}}}function Mr(r){let e,t;return e=new $t({props:{tokens:r[0].tokens,renderers:r[1],options:r[2]}}),{c(){M(e.$$.fragment)},m(n,s){T(e,n,s),t=!0},p(n,s){const i={};1&s&&(i.tokens=n[0].tokens),2&s&&(i.renderers=n[1]),4&s&&(i.options=n[2]),e.$set(i)},i(n){t||(h(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){S(e,n)}}}function Tr(r){let e,t,n,s;const i=[Mr,Lr],o=[];function l(u,a){return"tokens"in u[0]&&u[0].tokens?0:1}return e=l(r),t=o[e]=i[e](r),{c(){t.c(),n=Ce()},m(u,a){o[e].m(u,a),x(u,n,a),s=!0},p(u,a){let c=e;e=l(u),e===c?o[e].p(u,a):(W(),g(o[c],1,1,()=>{o[c]=null}),Q(),t=o[e],t?t.p(u,a):(t=o[e]=i[e](u),t.c()),h(t,1),t.m(n.parentNode,n))},i(u){s||(h(t),s=!0)},o(u){g(t),s=!1},d(u){u&&v(n),o[e].d(u)}}}function Sr(r){let e,t,n=r[1][r[0].type]&&mn(r);return{c(){n&&n.c(),e=Ce()},m(s,i){n&&n.m(s,i),x(s,e,i),t=!0},p(s,[i]){s[1][s[0].type]?n?(n.p(s,i),3&i&&h(n,1)):(n=mn(s),n.c(),h(n,1),n.m(e.parentNode,e)):n&&(W(),g(n,1,1,()=>{n=null}),Q())},i(s){t||(h(n),t=!0)},o(s){g(n),t=!1},d(s){s&&v(e),n&&n.d(s)}}}function Nr(r,e,t){let{token:n}=e,{renderers:s}=e,{options:i}=e;return r.$$set=o=>{"token"in o&&t(0,n=o.token),"renderers"in o&&t(1,s=o.renderers),"options"in o&&t(2,i=o.options)},[n,s,i]}class As extends Y{constructor(e){super(),X(this,e,Nr,Sr,J,{token:0,renderers:1,options:2})}}function Dn(r,e,t){const n=r.slice();return n[4]=e[t],n}function Fn(r){let e,t;return e=new As({props:{token:{...r[4]},options:r[1],renderers:r[2]}}),{c(){M(e.$$.fragment)},m(n,s){T(e,n,s),t=!0},p(n,s){const i={};1&s&&(i.token={...n[4]}),2&s&&(i.options=n[1]),4&s&&(i.renderers=n[2]),e.$set(i)},i(n){t||(h(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){S(e,n)}}}function kt(r){let e,t,n,s=ne(r[0].items),i=[];for(let a=0;a<s.length;a+=1)i[a]=Fn(Dn(r,s,a));const o=a=>g(i[a],1,1,()=>{i[a]=null});let l=[{start:t=r[0].start||1}],u={};for(let a=0;a<l.length;a+=1)u=fs(u,l[a]);return{c(){e=_(r[3]);for(let a=0;a<i.length;a+=1)i[a].c();lt(r[3])(e,u)},m(a,c){x(a,e,c);for(let d=0;d<i.length;d+=1)i[d]&&i[d].m(e,null);n=!0},p(a,c){if(7&c){let d;for(s=ne(a[0].items),d=0;d<s.length;d+=1){const p=Dn(a,s,d);i[d]?(i[d].p(p,c),h(i[d],1)):(i[d]=Fn(p),i[d].c(),h(i[d],1),i[d].m(e,null))}for(W(),d=s.length;d<i.length;d+=1)o(d);Q()}lt(a[3])(e,u=hs(l,[(!n||1&c&&t!==(t=a[0].start||1))&&{start:t}]))},i(a){if(!n){for(let c=0;c<s.length;c+=1)h(i[c]);n=!0}},o(a){i=i.filter(Boolean);for(let c=0;c<i.length;c+=1)g(i[c]);n=!1},d(a){a&&v(e),ve(i,a)}}}function Rr(r){let e,t=r[3],n=r[3]&&kt(r);return{c(){n&&n.c(),e=Ce()},m(s,i){n&&n.m(s,i),x(s,e,i)},p(s,[i]){s[3]?t?J(t,s[3])?(n.d(1),n=kt(s),t=s[3],n.c(),n.m(e.parentNode,e)):n.p(s,i):(n=kt(s),t=s[3],n.c(),n.m(e.parentNode,e)):t&&(n.d(1),n=null,t=s[3])},i:G,o(s){g(n,s)},d(s){s&&v(e),n&&n.d(s)}}}function Pr(r,e,t){let n,{token:s}=e,{options:i}=e,{renderers:o}=e;return r.$$set=l=>{"token"in l&&t(0,s=l.token),"options"in l&&t(1,i=l.options),"renderers"in l&&t(2,o=l.renderers)},r.$$.update=()=>{1&r.$$.dirty&&t(3,n=s.ordered?"ol":"ul")},[s,i,o,n]}class Ir extends Y{constructor(e){super(),X(this,e,Pr,Rr,J,{token:0,options:1,renderers:2})}}function Or(r){let e,t;const n=r[4].default,s=De(n,r,r[3],null);return{c(){e=_("li"),s&&s.c()},m(i,o){x(i,e,o),s&&s.m(e,null),t=!0},p(i,[o]){s&&s.p&&(!t||8&o)&&Fe(s,n,i,i[3],t?xe(n,i[3],o,null):ke(i[3]),null)},i(i){t||(h(s,i),t=!0)},o(i){g(s,i),t=!1},d(i){i&&v(e),s&&s.d(i)}}}function Vr(r,e,t){let{$$slots:n={},$$scope:s}=e;return r.$$set=i=>{"$$scope"in i&&t(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}class Zr extends Y{constructor(e){super(),X(this,e,Vr,Or,J,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function qr(r){let e;return{c(){e=_("br")},m(t,n){x(t,e,n)},p:G,i:G,o:G,d(t){t&&v(e)}}}function Ur(r,e,t){return[void 0,void 0,void 0]}class jr extends Y{constructor(e){super(),X(this,e,Ur,qr,J,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Hr(r){let e,t,n,s,i=r[0].text+"";return{c(){e=_("pre"),t=_("code"),n=U(i),A(t,"class",s=`lang-${r[0].lang}`)},m(o,l){x(o,e,l),L(e,t),L(t,n)},p(o,[l]){1&l&&i!==(i=o[0].text+"")&&de(n,i),1&l&&s!==(s=`lang-${o[0].lang}`)&&A(t,"class",s)},i:G,o:G,d(o){o&&v(e)}}}function Wr(r,e,t){let{token:n}=e;return r.$$set=s=>{"token"in s&&t(0,n=s.token)},[n,void 0,void 0]}class Qr extends Y{constructor(e){super(),X(this,e,Wr,Hr,J,{token:0,options:1,renderers:2})}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Gr(r){let e,t,n=r[0].raw.slice(1,r[0].raw.length-1)+"";return{c(){e=_("code"),t=U(n)},m(s,i){x(s,e,i),L(e,t)},p(s,[i]){1&i&&n!==(n=s[0].raw.slice(1,s[0].raw.length-1)+"")&&de(t,n)},i:G,o:G,d(s){s&&v(e)}}}function Jr(r,e,t){let{token:n}=e;return r.$$set=s=>{"token"in s&&t(0,n=s.token)},[n,void 0,void 0]}class Yr extends Y{constructor(e){super(),X(this,e,Jr,Gr,J,{token:0,options:1,renderers:2})}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function kn(r,e,t){const n=r.slice();return n[3]=e[t],n}function xn(r,e,t){const n=r.slice();return n[6]=e[t],n}function vn(r,e,t){const n=r.slice();return n[9]=e[t],n}function Cn(r){let e,t,n,s;return t=new $t({props:{tokens:r[9].tokens,options:r[1],renderers:r[2]}}),{c(){e=_("th"),M(t.$$.fragment),n=Z(),A(e,"scope","col")},m(i,o){x(i,e,o),T(t,e,null),L(e,n),s=!0},p(i,o){const l={};1&o&&(l.tokens=i[9].tokens),2&o&&(l.options=i[1]),4&o&&(l.renderers=i[2]),t.$set(l)},i(i){s||(h(t.$$.fragment,i),s=!0)},o(i){g(t.$$.fragment,i),s=!1},d(i){i&&v(e),S(t)}}}function wn(r){let e,t,n;return t=new $t({props:{tokens:r[6].tokens,options:r[1],renderers:r[2]}}),{c(){e=_("td"),M(t.$$.fragment)},m(s,i){x(s,e,i),T(t,e,null),n=!0},p(s,i){const o={};1&i&&(o.tokens=s[6].tokens),2&i&&(o.options=s[1]),4&i&&(o.renderers=s[2]),t.$set(o)},i(s){n||(h(t.$$.fragment,s),n=!0)},o(s){g(t.$$.fragment,s),n=!1},d(s){s&&v(e),S(t)}}}function An(r){let e,t,n,s=ne(r[3]),i=[];for(let l=0;l<s.length;l+=1)i[l]=wn(xn(r,s,l));const o=l=>g(i[l],1,1,()=>{i[l]=null});return{c(){e=_("tr");for(let l=0;l<i.length;l+=1)i[l].c();t=Z()},m(l,u){x(l,e,u);for(let a=0;a<i.length;a+=1)i[a]&&i[a].m(e,null);L(e,t),n=!0},p(l,u){if(7&u){let a;for(s=ne(l[3]),a=0;a<s.length;a+=1){const c=xn(l,s,a);i[a]?(i[a].p(c,u),h(i[a],1)):(i[a]=wn(c),i[a].c(),h(i[a],1),i[a].m(e,t))}for(W(),a=s.length;a<i.length;a+=1)o(a);Q()}},i(l){if(!n){for(let u=0;u<s.length;u+=1)h(i[u]);n=!0}},o(l){i=i.filter(Boolean);for(let u=0;u<i.length;u+=1)g(i[u]);n=!1},d(l){l&&v(e),ve(i,l)}}}function Xr(r){let e,t,n,s,i,o,l=ne(r[0].header),u=[];for(let D=0;D<l.length;D+=1)u[D]=Cn(vn(r,l,D));const a=D=>g(u[D],1,1,()=>{u[D]=null});let c=ne(r[0].rows),d=[];for(let D=0;D<c.length;D+=1)d[D]=An(kn(r,c,D));const p=D=>g(d[D],1,1,()=>{d[D]=null});return{c(){e=_("table"),t=_("thead"),n=_("tr");for(let D=0;D<u.length;D+=1)u[D].c();s=Z(),i=_("tbody");for(let D=0;D<d.length;D+=1)d[D].c()},m(D,b){x(D,e,b),L(e,t),L(t,n);for(let m=0;m<u.length;m+=1)u[m]&&u[m].m(n,null);L(e,s),L(e,i);for(let m=0;m<d.length;m+=1)d[m]&&d[m].m(i,null);o=!0},p(D,[b]){if(7&b){let m;for(l=ne(D[0].header),m=0;m<l.length;m+=1){const k=vn(D,l,m);u[m]?(u[m].p(k,b),h(u[m],1)):(u[m]=Cn(k),u[m].c(),h(u[m],1),u[m].m(n,null))}for(W(),m=l.length;m<u.length;m+=1)a(m);Q()}if(7&b){let m;for(c=ne(D[0].rows),m=0;m<c.length;m+=1){const k=kn(D,c,m);d[m]?(d[m].p(k,b),h(d[m],1)):(d[m]=An(k),d[m].c(),h(d[m],1),d[m].m(i,null))}for(W(),m=c.length;m<d.length;m+=1)p(m);Q()}},i(D){if(!o){for(let b=0;b<l.length;b+=1)h(u[b]);for(let b=0;b<c.length;b+=1)h(d[b]);o=!0}},o(D){u=u.filter(Boolean);for(let b=0;b<u.length;b+=1)g(u[b]);d=d.filter(Boolean);for(let b=0;b<d.length;b+=1)g(d[b]);o=!1},d(D){D&&v(e),ve(u,D),ve(d,D)}}}function Kr(r,e,t){let{token:n}=e,{options:s}=e,{renderers:i}=e;return r.$$set=o=>{"token"in o&&t(0,n=o.token),"options"in o&&t(1,s=o.options),"renderers"in o&&t(2,i=o.renderers)},[n,s,i]}class eo extends Y{constructor(e){super(),X(this,e,Kr,Xr,J,{token:0,options:1,renderers:2})}}function to(r){let e,t,n=r[0].text+"";return{c(){e=new Is(!1),t=Ce(),e.a=t},m(s,i){e.m(n,s,i),x(s,t,i)},p(s,[i]){1&i&&n!==(n=s[0].text+"")&&e.p(n)},i:G,o:G,d(s){s&&(v(t),e.d())}}}function no(r,e,t){let{token:n}=e;return r.$$set=s=>{"token"in s&&t(0,n=s.token)},[n,void 0,void 0]}class so extends Y{constructor(e){super(),X(this,e,no,to,J,{token:0,options:1,renderers:2})}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function io(r){let e,t;const n=r[4].default,s=De(n,r,r[3],null);return{c(){e=_("p"),s&&s.c()},m(i,o){x(i,e,o),s&&s.m(e,null),t=!0},p(i,[o]){s&&s.p&&(!t||8&o)&&Fe(s,n,i,i[3],t?xe(n,i[3],o,null):ke(i[3]),null)},i(i){t||(h(s,i),t=!0)},o(i){g(s,i),t=!1},d(i){i&&v(e),s&&s.d(i)}}}function ro(r,e,t){let{$$slots:n={},$$scope:s}=e;return r.$$set=i=>{"$$scope"in i&&t(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}let oo=class extends Y{constructor(r){super(),X(this,r,ro,io,J,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}};function lo(r){let e,t,n,s;const i=r[4].default,o=De(i,r,r[3],null);return{c(){e=_("a"),o&&o.c(),A(e,"href",t=fn(r[0].href)?pn(r[1].baseUrl,r[0].href):r[0].href),A(e,"title",n=r[0].title)},m(l,u){x(l,e,u),o&&o.m(e,null),s=!0},p(l,[u]){o&&o.p&&(!s||8&u)&&Fe(o,i,l,l[3],s?xe(i,l[3],u,null):ke(l[3]),null),(!s||3&u&&t!==(t=fn(l[0].href)?pn(l[1].baseUrl,l[0].href):l[0].href))&&A(e,"href",t),(!s||1&u&&n!==(n=l[0].title))&&A(e,"title",n)},i(l){s||(h(o,l),s=!0)},o(l){g(o,l),s=!1},d(l){l&&v(e),o&&o.d(l)}}}function uo(r,e,t){let{$$slots:n={},$$scope:s}=e,{token:i}=e,{options:o}=e;return r.$$set=l=>{"token"in l&&t(0,i=l.token),"options"in l&&t(1,o=l.options),"$$scope"in l&&t(3,s=l.$$scope)},[i,o,void 0,s,n]}class ao extends Y{constructor(e){super(),X(this,e,uo,lo,J,{token:0,options:1,renderers:2})}get renderers(){return this.$$.ctx[2]}}function co(r){let e;const t=r[4].default,n=De(t,r,r[3],null);return{c(){n&&n.c()},m(s,i){n&&n.m(s,i),e=!0},p(s,[i]){n&&n.p&&(!e||8&i)&&Fe(n,t,s,s[3],e?xe(t,s[3],i,null):ke(s[3]),null)},i(s){e||(h(n,s),e=!0)},o(s){g(n,s),e=!1},d(s){n&&n.d(s)}}}function po(r,e,t){let{$$slots:n={},$$scope:s}=e;return r.$$set=i=>{"$$scope"in i&&t(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}class fo extends Y{constructor(e){super(),X(this,e,po,co,J,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function ho(r){let e,t;const n=r[4].default,s=De(n,r,r[3],null);return{c(){e=_("dfn"),s&&s.c()},m(i,o){x(i,e,o),s&&s.m(e,null),t=!0},p(i,[o]){s&&s.p&&(!t||8&o)&&Fe(s,n,i,i[3],t?xe(n,i[3],o,null):ke(i[3]),null)},i(i){t||(h(s,i),t=!0)},o(i){g(s,i),t=!1},d(i){i&&v(e),s&&s.d(i)}}}function go(r,e,t){let{$$slots:n={},$$scope:s}=e;return r.$$set=i=>{"$$scope"in i&&t(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}class $o extends Y{constructor(e){super(),X(this,e,go,ho,J,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function mo(r){let e,t;const n=r[4].default,s=De(n,r,r[3],null);return{c(){e=_("del"),s&&s.c()},m(i,o){x(i,e,o),s&&s.m(e,null),t=!0},p(i,[o]){s&&s.p&&(!t||8&o)&&Fe(s,n,i,i[3],t?xe(n,i[3],o,null):ke(i[3]),null)},i(i){t||(h(s,i),t=!0)},o(i){g(s,i),t=!1},d(i){i&&v(e),s&&s.d(i)}}}function Do(r,e,t){let{$$slots:n={},$$scope:s}=e;return r.$$set=i=>{"$$scope"in i&&t(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}class Fo extends Y{constructor(e){super(),X(this,e,Do,mo,J,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function ko(r){let e,t;const n=r[4].default,s=De(n,r,r[3],null);return{c(){e=_("em"),s&&s.c()},m(i,o){x(i,e,o),s&&s.m(e,null),t=!0},p(i,[o]){s&&s.p&&(!t||8&o)&&Fe(s,n,i,i[3],t?xe(n,i[3],o,null):ke(i[3]),null)},i(i){t||(h(s,i),t=!0)},o(i){g(s,i),t=!1},d(i){i&&v(e),s&&s.d(i)}}}function xo(r,e,t){let{$$slots:n={},$$scope:s}=e;return r.$$set=i=>{"$$scope"in i&&t(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}class vo extends Y{constructor(e){super(),X(this,e,xo,ko,J,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Co(r){let e;return{c(){e=_("hr")},m(t,n){x(t,e,n)},p:G,i:G,o:G,d(t){t&&v(e)}}}function wo(r,e,t){return[void 0,void 0,void 0]}class Ao extends Y{constructor(e){super(),X(this,e,wo,Co,J,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function yo(r){let e,t;const n=r[4].default,s=De(n,r,r[3],null);return{c(){e=_("strong"),s&&s.c()},m(i,o){x(i,e,o),s&&s.m(e,null),t=!0},p(i,[o]){s&&s.p&&(!t||8&o)&&Fe(s,n,i,i[3],t?xe(n,i[3],o,null):ke(i[3]),null)},i(i){t||(h(s,i),t=!0)},o(i){g(s,i),t=!1},d(i){i&&v(e),s&&s.d(i)}}}function bo(r,e,t){let{$$slots:n={},$$scope:s}=e;return r.$$set=i=>{"$$scope"in i&&t(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}class Eo extends Y{constructor(e){super(),X(this,e,bo,yo,J,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function _o(r){let e,t,n,s;return{c(){e=_("img"),Vt(e.src,t=r[0].href)||A(e,"src",t),A(e,"title",n=r[0].title),A(e,"alt",s=r[0].text),A(e,"class","markdown-image svelte-z38cge")},m(i,o){x(i,e,o)},p(i,[o]){1&o&&!Vt(e.src,t=i[0].href)&&A(e,"src",t),1&o&&n!==(n=i[0].title)&&A(e,"title",n),1&o&&s!==(s=i[0].text)&&A(e,"alt",s)},i:G,o:G,d(i){i&&v(e)}}}function Bo(r,e,t){let{token:n}=e;return r.$$set=s=>{"token"in s&&t(0,n=s.token)},[n,void 0,void 0]}class zo extends Y{constructor(e){super(),X(this,e,Bo,_o,J,{token:0,options:1,renderers:2})}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function Lo(r){let e;const t=r[4].default,n=De(t,r,r[3],null);return{c(){n&&n.c()},m(s,i){n&&n.m(s,i),e=!0},p(s,[i]){n&&n.p&&(!e||8&i)&&Fe(n,t,s,s[3],e?xe(t,s[3],i,null):ke(s[3]),null)},i(s){e||(h(n,s),e=!0)},o(s){g(n,s),e=!1},d(s){n&&n.d(s)}}}function Mo(r,e,t){let{$$slots:n={},$$scope:s}=e;return r.$$set=i=>{"$$scope"in i&&t(3,s=i.$$scope)},[void 0,void 0,void 0,s,n]}class yn extends Y{constructor(e){super(),X(this,e,Mo,Lo,J,{token:0,options:1,renderers:2})}get token(){return this.$$.ctx[0]}get options(){return this.$$.ctx[1]}get renderers(){return this.$$.ctx[2]}}function To(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}let Ue={async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null};function bn(r){Ue=r}const ys=/[&<>"']/,So=new RegExp(ys.source,"g"),bs=/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,No=new RegExp(bs.source,"g"),Ro={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},En=r=>Ro[r];function ye(r,e){if(e){if(ys.test(r))return r.replace(So,En)}else if(bs.test(r))return r.replace(No,En);return r}const Po=/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi;function Io(r){return r.replace(Po,(e,t)=>(t=t.toLowerCase())==="colon"?":":t.charAt(0)==="#"?t.charAt(1)==="x"?String.fromCharCode(parseInt(t.substring(2),16)):String.fromCharCode(+t.substring(1)):"")}const Oo=/(^|[^\[])\^/g;function ue(r,e){let t=typeof r=="string"?r:r.source;e=e||"";const n={replace:(s,i)=>{let o=typeof i=="string"?i:i.source;return o=o.replace(Oo,"$1"),t=t.replace(s,o),n},getRegex:()=>new RegExp(t,e)};return n}function _n(r){try{r=encodeURI(r).replace(/%25/g,"%")}catch{return null}return r}const Ge={exec:()=>null};function Bn(r,e){const t=r.replace(/\|/g,(s,i,o)=>{let l=!1,u=i;for(;--u>=0&&o[u]==="\\";)l=!l;return l?"|":" |"}).split(/ \|/);let n=0;if(t[0].trim()||t.shift(),t.length>0&&!t[t.length-1].trim()&&t.pop(),e)if(t.length>e)t.splice(e);else for(;t.length<e;)t.push("");for(;n<t.length;n++)t[n]=t[n].trim().replace(/\\\|/g,"|");return t}function it(r,e,t){const n=r.length;if(n===0)return"";let s=0;for(;s<n;){const i=r.charAt(n-s-1);if(i!==e||t){if(i===e||!t)break;s++}else s++}return r.slice(0,n-s)}function zn(r,e,t,n){const s=e.href,i=e.title?ye(e.title):null,o=r[1].replace(/\\([\[\]])/g,"$1");if(r[0].charAt(0)!=="!"){n.state.inLink=!0;const l={type:"link",raw:t,href:s,title:i,text:o,tokens:n.inlineTokens(o)};return n.state.inLink=!1,l}return{type:"image",raw:t,href:s,title:i,text:ye(o)}}class pt{constructor(e){se(this,"options");se(this,"rules");se(this,"lexer");this.options=e||Ue}space(e){const t=this.rules.block.newline.exec(e);if(t&&t[0].length>0)return{type:"space",raw:t[0]}}code(e){const t=this.rules.block.code.exec(e);if(t){const n=t[0].replace(/^ {1,4}/gm,"");return{type:"code",raw:t[0],codeBlockStyle:"indented",text:this.options.pedantic?n:it(n,`
`)}}}fences(e){const t=this.rules.block.fences.exec(e);if(t){const n=t[0],s=function(i,o){const l=i.match(/^(\s+)(?:```)/);if(l===null)return o;const u=l[1];return o.split(`
`).map(a=>{const c=a.match(/^\s+/);if(c===null)return a;const[d]=c;return d.length>=u.length?a.slice(u.length):a}).join(`
`)}(n,t[3]||"");return{type:"code",raw:n,lang:t[2]?t[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):t[2],text:s}}}heading(e){const t=this.rules.block.heading.exec(e);if(t){let n=t[2].trim();if(/#$/.test(n)){const s=it(n,"#");this.options.pedantic?n=s.trim():s&&!/ $/.test(s)||(n=s.trim())}return{type:"heading",raw:t[0],depth:t[1].length,text:n,tokens:this.lexer.inline(n)}}}hr(e){const t=this.rules.block.hr.exec(e);if(t)return{type:"hr",raw:t[0]}}blockquote(e){const t=this.rules.block.blockquote.exec(e);if(t){const n=it(t[0].replace(/^ *>[ \t]?/gm,""),`
`),s=this.lexer.state.top;this.lexer.state.top=!0;const i=this.lexer.blockTokens(n);return this.lexer.state.top=s,{type:"blockquote",raw:t[0],tokens:i,text:n}}}list(e){let t=this.rules.block.list.exec(e);if(t){let n=t[1].trim();const s=n.length>1,i={type:"list",raw:"",ordered:s,start:s?+n.slice(0,-1):"",loose:!1,items:[]};n=s?`\\d{1,9}\\${n.slice(-1)}`:`\\${n}`,this.options.pedantic&&(n=s?n:"[*+-]");const o=new RegExp(`^( {0,3}${n})((?:[	 ][^\\n]*)?(?:\\n|$))`);let l="",u="",a=!1;for(;e;){let c=!1;if(!(t=o.exec(e))||this.rules.block.hr.test(e))break;l=t[0],e=e.substring(l.length);let d=t[2].split(`
`,1)[0].replace(/^\t+/,y=>" ".repeat(3*y.length)),p=e.split(`
`,1)[0],D=0;this.options.pedantic?(D=2,u=d.trimStart()):(D=t[2].search(/[^ ]/),D=D>4?1:D,u=d.slice(D),D+=t[1].length);let b=!1;if(!d&&/^ *$/.test(p)&&(l+=p+`
`,e=e.substring(p.length+1),c=!0),!c){const y=new RegExp(`^ {0,${Math.min(3,D-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),w=new RegExp(`^ {0,${Math.min(3,D-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),$=new RegExp(`^ {0,${Math.min(3,D-1)}}(?:\`\`\`|~~~)`),B=new RegExp(`^ {0,${Math.min(3,D-1)}}#`);for(;e;){const f=e.split(`
`,1)[0];if(p=f,this.options.pedantic&&(p=p.replace(/^ {1,4}(?=( {4})*[^ ])/g,"  ")),$.test(p)||B.test(p)||y.test(p)||w.test(e))break;if(p.search(/[^ ]/)>=D||!p.trim())u+=`
`+p.slice(D);else{if(b||d.search(/[^ ]/)>=4||$.test(d)||B.test(d)||w.test(d))break;u+=`
`+p}b||p.trim()||(b=!0),l+=f+`
`,e=e.substring(f.length+1),d=p.slice(D)}}i.loose||(a?i.loose=!0:/\n *\n *$/.test(l)&&(a=!0));let m,k=null;this.options.gfm&&(k=/^\[[ xX]\] /.exec(u),k&&(m=k[0]!=="[ ] ",u=u.replace(/^\[[ xX]\] +/,""))),i.items.push({type:"list_item",raw:l,task:!!k,checked:m,loose:!1,text:u,tokens:[]}),i.raw+=l}i.items[i.items.length-1].raw=l.trimEnd(),i.items[i.items.length-1].text=u.trimEnd(),i.raw=i.raw.trimEnd();for(let c=0;c<i.items.length;c++)if(this.lexer.state.top=!1,i.items[c].tokens=this.lexer.blockTokens(i.items[c].text,[]),!i.loose){const d=i.items[c].tokens.filter(D=>D.type==="space"),p=d.length>0&&d.some(D=>/\n.*\n/.test(D.raw));i.loose=p}if(i.loose)for(let c=0;c<i.items.length;c++)i.items[c].loose=!0;return i}}html(e){const t=this.rules.block.html.exec(e);if(t)return{type:"html",block:!0,raw:t[0],pre:t[1]==="pre"||t[1]==="script"||t[1]==="style",text:t[0]}}def(e){const t=this.rules.block.def.exec(e);if(t){const n=t[1].toLowerCase().replace(/\s+/g," "),s=t[2]?t[2].replace(/^<(.*)>$/,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",i=t[3]?t[3].substring(1,t[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):t[3];return{type:"def",tag:n,raw:t[0],href:s,title:i}}}table(e){const t=this.rules.block.table.exec(e);if(!t||!/[:|]/.test(t[2]))return;const n=Bn(t[1]),s=t[2].replace(/^\||\| *$/g,"").split("|"),i=t[3]&&t[3].trim()?t[3].replace(/\n[ \t]*$/,"").split(`
`):[],o={type:"table",raw:t[0],header:[],align:[],rows:[]};if(n.length===s.length){for(const l of s)/^ *-+: *$/.test(l)?o.align.push("right"):/^ *:-+: *$/.test(l)?o.align.push("center"):/^ *:-+ *$/.test(l)?o.align.push("left"):o.align.push(null);for(const l of n)o.header.push({text:l,tokens:this.lexer.inline(l)});for(const l of i)o.rows.push(Bn(l,o.header.length).map(u=>({text:u,tokens:this.lexer.inline(u)})));return o}}lheading(e){const t=this.rules.block.lheading.exec(e);if(t)return{type:"heading",raw:t[0],depth:t[2].charAt(0)==="="?1:2,text:t[1],tokens:this.lexer.inline(t[1])}}paragraph(e){const t=this.rules.block.paragraph.exec(e);if(t){const n=t[1].charAt(t[1].length-1)===`
`?t[1].slice(0,-1):t[1];return{type:"paragraph",raw:t[0],text:n,tokens:this.lexer.inline(n)}}}text(e){const t=this.rules.block.text.exec(e);if(t)return{type:"text",raw:t[0],text:t[0],tokens:this.lexer.inline(t[0])}}escape(e){const t=this.rules.inline.escape.exec(e);if(t)return{type:"escape",raw:t[0],text:ye(t[1])}}tag(e){const t=this.rules.inline.tag.exec(e);if(t)return!this.lexer.state.inLink&&/^<a /i.test(t[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&/^<\/a>/i.test(t[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&/^<(pre|code|kbd|script)(\s|>)/i.test(t[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&/^<\/(pre|code|kbd|script)(\s|>)/i.test(t[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:t[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:t[0]}}link(e){const t=this.rules.inline.link.exec(e);if(t){const n=t[2].trim();if(!this.options.pedantic&&/^</.test(n)){if(!/>$/.test(n))return;const o=it(n.slice(0,-1),"\\");if((n.length-o.length)%2==0)return}else{const o=function(l,u){if(l.indexOf(u[1])===-1)return-1;let a=0;for(let c=0;c<l.length;c++)if(l[c]==="\\")c++;else if(l[c]===u[0])a++;else if(l[c]===u[1]&&(a--,a<0))return c;return-1}(t[2],"()");if(o>-1){const l=(t[0].indexOf("!")===0?5:4)+t[1].length+o;t[2]=t[2].substring(0,o),t[0]=t[0].substring(0,l).trim(),t[3]=""}}let s=t[2],i="";if(this.options.pedantic){const o=/^([^'"]*[^\s])\s+(['"])(.*)\2/.exec(s);o&&(s=o[1],i=o[3])}else i=t[3]?t[3].slice(1,-1):"";return s=s.trim(),/^</.test(s)&&(s=this.options.pedantic&&!/>$/.test(n)?s.slice(1):s.slice(1,-1)),zn(t,{href:s&&s.replace(this.rules.inline.anyPunctuation,"$1"),title:i&&i.replace(this.rules.inline.anyPunctuation,"$1")},t[0],this.lexer)}}reflink(e,t){let n;if((n=this.rules.inline.reflink.exec(e))||(n=this.rules.inline.nolink.exec(e))){const s=t[(n[2]||n[1]).replace(/\s+/g," ").toLowerCase()];if(!s){const i=n[0].charAt(0);return{type:"text",raw:i,text:i}}return zn(n,s,n[0],this.lexer)}}emStrong(e,t,n=""){let s=this.rules.inline.emStrongLDelim.exec(e);if(s&&!(s[3]&&n.match(/[\p{L}\p{N}]/u))&&(!(s[1]||s[2])||!n||this.rules.inline.punctuation.exec(n))){const i=[...s[0]].length-1;let o,l,u=i,a=0;const c=s[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(c.lastIndex=0,t=t.slice(-1*e.length+i);(s=c.exec(t))!=null;){if(o=s[1]||s[2]||s[3]||s[4]||s[5]||s[6],!o)continue;if(l=[...o].length,s[3]||s[4]){u+=l;continue}if((s[5]||s[6])&&i%3&&!((i+l)%3)){a+=l;continue}if(u-=l,u>0)continue;l=Math.min(l,l+u+a);const d=[...s[0]][0].length,p=e.slice(0,i+s.index+d+l);if(Math.min(i,l)%2){const b=p.slice(1,-1);return{type:"em",raw:p,text:b,tokens:this.lexer.inlineTokens(b)}}const D=p.slice(2,-2);return{type:"strong",raw:p,text:D,tokens:this.lexer.inlineTokens(D)}}}}codespan(e){const t=this.rules.inline.code.exec(e);if(t){let n=t[2].replace(/\n/g," ");const s=/[^ ]/.test(n),i=/^ /.test(n)&&/ $/.test(n);return s&&i&&(n=n.substring(1,n.length-1)),n=ye(n,!0),{type:"codespan",raw:t[0],text:n}}}br(e){const t=this.rules.inline.br.exec(e);if(t)return{type:"br",raw:t[0]}}del(e){const t=this.rules.inline.del.exec(e);if(t)return{type:"del",raw:t[0],text:t[2],tokens:this.lexer.inlineTokens(t[2])}}autolink(e){const t=this.rules.inline.autolink.exec(e);if(t){let n,s;return t[2]==="@"?(n=ye(t[1]),s="mailto:"+n):(n=ye(t[1]),s=n),{type:"link",raw:t[0],text:n,href:s,tokens:[{type:"text",raw:n,text:n}]}}}url(e){var n;let t;if(t=this.rules.inline.url.exec(e)){let s,i;if(t[2]==="@")s=ye(t[0]),i="mailto:"+s;else{let o;do o=t[0],t[0]=((n=this.rules.inline._backpedal.exec(t[0]))==null?void 0:n[0])??"";while(o!==t[0]);s=ye(t[0]),i=t[1]==="www."?"http://"+t[0]:t[0]}return{type:"link",raw:t[0],text:s,href:i,tokens:[{type:"text",raw:s,text:s}]}}}inlineText(e){const t=this.rules.inline.text.exec(e);if(t){let n;return n=this.lexer.state.inRawBlock?t[0]:ye(t[0]),{type:"text",raw:t[0],text:n}}}}const tt=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,Es=/(?:[*+-]|\d{1,9}[.)])/,_s=ue(/^(?!bull )((?:.|\n(?!\s*?\n|bull ))+?)\n {0,3}(=+|-+) *(?:\n+|$)/).replace(/bull/g,Es).getRegex(),Bt=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,zt=/(?!\s*\])(?:\\.|[^\[\]\\])+/,Vo=ue(/^ {0,3}\[(label)\]: *(?:\n *)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n *)?| *\n *)(title))? *(?:\n+|$)/).replace("label",zt).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),Zo=ue(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,Es).getRegex(),mt="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|section|source|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",Lt=/<!--(?!-?>)[\s\S]*?(?:-->|$)/,qo=ue("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n *)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n *)+\\n|$))","i").replace("comment",Lt).replace("tag",mt).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),Ln=ue(Bt).replace("hr",tt).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",mt).getRegex(),Mt={blockquote:ue(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",Ln).getRegex(),code:/^( {4}[^\n]+(?:\n(?: *(?:\n|$))*)?)+/,def:Vo,fences:/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,hr:tt,html:qo,lheading:_s,list:Zo,newline:/^(?: *(?:\n|$))+/,paragraph:Ln,table:Ge,text:/^[^\n]+/},Mn=ue("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",tt).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code"," {4}[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",mt).getRegex(),Uo={...Mt,table:Mn,paragraph:ue(Bt).replace("hr",tt).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",Mn).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",mt).getRegex()},jo={...Mt,html:ue(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",Lt).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:Ge,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:ue(Bt).replace("hr",tt).replace("heading",` *#{1,6} *[^
]`).replace("lheading",_s).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},Bs=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,zs=/^( {2,}|\\)\n(?!\s*$)/,nt="\\p{P}$+<=>`^|~",Ho=ue(/^((?![*_])[\spunctuation])/,"u").replace(/punctuation/g,nt).getRegex(),Wo=ue(/^(?:\*+(?:((?!\*)[punct])|[^\s*]))|^_+(?:((?!_)[punct])|([^\s_]))/,"u").replace(/punct/g,nt).getRegex(),Qo=ue("^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)[punct](\\*+)(?=[\\s]|$)|[^punct\\s](\\*+)(?!\\*)(?=[punct\\s]|$)|(?!\\*)[punct\\s](\\*+)(?=[^punct\\s])|[\\s](\\*+)(?!\\*)(?=[punct])|(?!\\*)[punct](\\*+)(?!\\*)(?=[punct])|[^punct\\s](\\*+)(?=[^punct\\s])","gu").replace(/punct/g,nt).getRegex(),Go=ue("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)[punct](_+)(?=[\\s]|$)|[^punct\\s](_+)(?!_)(?=[punct\\s]|$)|(?!_)[punct\\s](_+)(?=[^punct\\s])|[\\s](_+)(?!_)(?=[punct])|(?!_)[punct](_+)(?!_)(?=[punct])","gu").replace(/punct/g,nt).getRegex(),Jo=ue(/\\([punct])/,"gu").replace(/punct/g,nt).getRegex(),Yo=ue(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),Xo=ue(Lt).replace("(?:-->|$)","-->").getRegex(),Ko=ue("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",Xo).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),ft=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,el=ue(/^!?\[(label)\]\(\s*(href)(?:\s+(title))?\s*\)/).replace("label",ft).replace("href",/<(?:\\.|[^\n<>\\])+>|[^\s\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),Tn=ue(/^!?\[(label)\]\[(ref)\]/).replace("label",ft).replace("ref",zt).getRegex(),Sn=ue(/^!?\[(ref)\](?:\[\])?/).replace("ref",zt).getRegex(),Tt={_backpedal:Ge,anyPunctuation:Jo,autolink:Yo,blockSkip:/\[[^[\]]*?\]\([^\(\)]*?\)|`[^`]*?`|<[^<>]*?>/g,br:zs,code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,del:Ge,emStrongLDelim:Wo,emStrongRDelimAst:Qo,emStrongRDelimUnd:Go,escape:Bs,link:el,nolink:Sn,punctuation:Ho,reflink:Tn,reflinkSearch:ue("reflink|nolink(?!\\()","g").replace("reflink",Tn).replace("nolink",Sn).getRegex(),tag:Ko,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,url:Ge},tl={...Tt,link:ue(/^!?\[(label)\]\((.*?)\)/).replace("label",ft).getRegex(),reflink:ue(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",ft).getRegex()},wt={...Tt,escape:ue(Bs).replace("])","~|])").getRegex(),url:ue(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])([\s\S]*?[^\s~])\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},nl={...wt,br:ue(zs).replace("{2,}","*").getRegex(),text:ue(wt.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},rt={normal:Mt,gfm:Uo,pedantic:jo},Qe={normal:Tt,gfm:wt,breaks:nl,pedantic:tl};class Be{constructor(e){se(this,"tokens");se(this,"options");se(this,"state");se(this,"tokenizer");se(this,"inlineQueue");this.tokens=[],this.tokens.links=Object.create(null),this.options=e||Ue,this.options.tokenizer=this.options.tokenizer||new pt,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const t={block:rt.normal,inline:Qe.normal};this.options.pedantic?(t.block=rt.pedantic,t.inline=Qe.pedantic):this.options.gfm&&(t.block=rt.gfm,this.options.breaks?t.inline=Qe.breaks:t.inline=Qe.gfm),this.tokenizer.rules=t}static get rules(){return{block:rt,inline:Qe}}static lex(e,t){return new Be(t).lex(e)}static lexInline(e,t){return new Be(t).inlineTokens(e)}lex(e){e=e.replace(/\r\n|\r/g,`
`),this.blockTokens(e,this.tokens);for(let t=0;t<this.inlineQueue.length;t++){const n=this.inlineQueue[t];this.inlineTokens(n.src,n.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(e,t=[]){let n,s,i,o;for(e=this.options.pedantic?e.replace(/\t/g,"    ").replace(/^ +$/gm,""):e.replace(/^( *)(\t+)/gm,(l,u,a)=>u+"    ".repeat(a.length));e;)if(!(this.options.extensions&&this.options.extensions.block&&this.options.extensions.block.some(l=>!!(n=l.call({lexer:this},e,t))&&(e=e.substring(n.raw.length),t.push(n),!0))))if(n=this.tokenizer.space(e))e=e.substring(n.raw.length),n.raw.length===1&&t.length>0?t[t.length-1].raw+=`
`:t.push(n);else if(n=this.tokenizer.code(e))e=e.substring(n.raw.length),s=t[t.length-1],!s||s.type!=="paragraph"&&s.type!=="text"?t.push(n):(s.raw+=`
`+n.raw,s.text+=`
`+n.text,this.inlineQueue[this.inlineQueue.length-1].src=s.text);else if(n=this.tokenizer.fences(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.heading(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.hr(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.blockquote(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.list(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.html(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.def(e))e=e.substring(n.raw.length),s=t[t.length-1],!s||s.type!=="paragraph"&&s.type!=="text"?this.tokens.links[n.tag]||(this.tokens.links[n.tag]={href:n.href,title:n.title}):(s.raw+=`
`+n.raw,s.text+=`
`+n.raw,this.inlineQueue[this.inlineQueue.length-1].src=s.text);else if(n=this.tokenizer.table(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.lheading(e))e=e.substring(n.raw.length),t.push(n);else{if(i=e,this.options.extensions&&this.options.extensions.startBlock){let l=1/0;const u=e.slice(1);let a;this.options.extensions.startBlock.forEach(c=>{a=c.call({lexer:this},u),typeof a=="number"&&a>=0&&(l=Math.min(l,a))}),l<1/0&&l>=0&&(i=e.substring(0,l+1))}if(this.state.top&&(n=this.tokenizer.paragraph(i)))s=t[t.length-1],o&&s.type==="paragraph"?(s.raw+=`
`+n.raw,s.text+=`
`+n.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=s.text):t.push(n),o=i.length!==e.length,e=e.substring(n.raw.length);else if(n=this.tokenizer.text(e))e=e.substring(n.raw.length),s=t[t.length-1],s&&s.type==="text"?(s.raw+=`
`+n.raw,s.text+=`
`+n.text,this.inlineQueue.pop(),this.inlineQueue[this.inlineQueue.length-1].src=s.text):t.push(n);else if(e){const l="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(l);break}throw new Error(l)}}return this.state.top=!0,t}inline(e,t=[]){return this.inlineQueue.push({src:e,tokens:t}),t}inlineTokens(e,t=[]){let n,s,i,o,l,u,a=e;if(this.tokens.links){const c=Object.keys(this.tokens.links);if(c.length>0)for(;(o=this.tokenizer.rules.inline.reflinkSearch.exec(a))!=null;)c.includes(o[0].slice(o[0].lastIndexOf("[")+1,-1))&&(a=a.slice(0,o.index)+"["+"a".repeat(o[0].length-2)+"]"+a.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(o=this.tokenizer.rules.inline.blockSkip.exec(a))!=null;)a=a.slice(0,o.index)+"["+"a".repeat(o[0].length-2)+"]"+a.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);for(;(o=this.tokenizer.rules.inline.anyPunctuation.exec(a))!=null;)a=a.slice(0,o.index)+"++"+a.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;e;)if(l||(u=""),l=!1,!(this.options.extensions&&this.options.extensions.inline&&this.options.extensions.inline.some(c=>!!(n=c.call({lexer:this},e,t))&&(e=e.substring(n.raw.length),t.push(n),!0))))if(n=this.tokenizer.escape(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.tag(e))e=e.substring(n.raw.length),s=t[t.length-1],s&&n.type==="text"&&s.type==="text"?(s.raw+=n.raw,s.text+=n.text):t.push(n);else if(n=this.tokenizer.link(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.reflink(e,this.tokens.links))e=e.substring(n.raw.length),s=t[t.length-1],s&&n.type==="text"&&s.type==="text"?(s.raw+=n.raw,s.text+=n.text):t.push(n);else if(n=this.tokenizer.emStrong(e,a,u))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.codespan(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.br(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.del(e))e=e.substring(n.raw.length),t.push(n);else if(n=this.tokenizer.autolink(e))e=e.substring(n.raw.length),t.push(n);else if(this.state.inLink||!(n=this.tokenizer.url(e))){if(i=e,this.options.extensions&&this.options.extensions.startInline){let c=1/0;const d=e.slice(1);let p;this.options.extensions.startInline.forEach(D=>{p=D.call({lexer:this},d),typeof p=="number"&&p>=0&&(c=Math.min(c,p))}),c<1/0&&c>=0&&(i=e.substring(0,c+1))}if(n=this.tokenizer.inlineText(i))e=e.substring(n.raw.length),n.raw.slice(-1)!=="_"&&(u=n.raw.slice(-1)),l=!0,s=t[t.length-1],s&&s.type==="text"?(s.raw+=n.raw,s.text+=n.text):t.push(n);else if(e){const c="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(c);break}throw new Error(c)}}else e=e.substring(n.raw.length),t.push(n);return t}}class ht{constructor(e){se(this,"options");this.options=e||Ue}code(e,t,n){var i;const s=(i=(t||"").match(/^\S*/))==null?void 0:i[0];return e=e.replace(/\n$/,"")+`
`,s?'<pre><code class="language-'+ye(s)+'">'+(n?e:ye(e,!0))+`</code></pre>
`:"<pre><code>"+(n?e:ye(e,!0))+`</code></pre>
`}blockquote(e){return`<blockquote>
${e}</blockquote>
`}html(e,t){return e}heading(e,t,n){return`<h${t}>${e}</h${t}>
`}hr(){return`<hr>
`}list(e,t,n){const s=t?"ol":"ul";return"<"+s+(t&&n!==1?' start="'+n+'"':"")+`>
`+e+"</"+s+`>
`}listitem(e,t,n){return`<li>${e}</li>
`}checkbox(e){return"<input "+(e?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph(e){return`<p>${e}</p>
`}table(e,t){return t&&(t=`<tbody>${t}</tbody>`),`<table>
<thead>
`+e+`</thead>
`+t+`</table>
`}tablerow(e){return`<tr>
${e}</tr>
`}tablecell(e,t){const n=t.header?"th":"td";return(t.align?`<${n} align="${t.align}">`:`<${n}>`)+e+`</${n}>
`}strong(e){return`<strong>${e}</strong>`}em(e){return`<em>${e}</em>`}codespan(e){return`<code>${e}</code>`}br(){return"<br>"}del(e){return`<del>${e}</del>`}link(e,t,n){const s=_n(e);if(s===null)return n;let i='<a href="'+(e=s)+'"';return t&&(i+=' title="'+t+'"'),i+=">"+n+"</a>",i}image(e,t,n){const s=_n(e);if(s===null)return n;let i=`<img src="${e=s}" alt="${n}"`;return t&&(i+=` title="${t}"`),i+=">",i}text(e){return e}}class St{strong(e){return e}em(e){return e}codespan(e){return e}del(e){return e}html(e){return e}text(e){return e}link(e,t,n){return""+n}image(e,t,n){return""+n}br(){return""}}class ze{constructor(e){se(this,"options");se(this,"renderer");se(this,"textRenderer");this.options=e||Ue,this.options.renderer=this.options.renderer||new ht,this.renderer=this.options.renderer,this.renderer.options=this.options,this.textRenderer=new St}static parse(e,t){return new ze(t).parse(e)}static parseInline(e,t){return new ze(t).parseInline(e)}parse(e,t=!0){let n="";for(let s=0;s<e.length;s++){const i=e[s];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[i.type]){const o=i,l=this.options.extensions.renderers[o.type].call({parser:this},o);if(l!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(o.type)){n+=l||"";continue}}switch(i.type){case"space":continue;case"hr":n+=this.renderer.hr();continue;case"heading":{const o=i;n+=this.renderer.heading(this.parseInline(o.tokens),o.depth,Io(this.parseInline(o.tokens,this.textRenderer)));continue}case"code":{const o=i;n+=this.renderer.code(o.text,o.lang,!!o.escaped);continue}case"table":{const o=i;let l="",u="";for(let c=0;c<o.header.length;c++)u+=this.renderer.tablecell(this.parseInline(o.header[c].tokens),{header:!0,align:o.align[c]});l+=this.renderer.tablerow(u);let a="";for(let c=0;c<o.rows.length;c++){const d=o.rows[c];u="";for(let p=0;p<d.length;p++)u+=this.renderer.tablecell(this.parseInline(d[p].tokens),{header:!1,align:o.align[p]});a+=this.renderer.tablerow(u)}n+=this.renderer.table(l,a);continue}case"blockquote":{const o=i,l=this.parse(o.tokens);n+=this.renderer.blockquote(l);continue}case"list":{const o=i,l=o.ordered,u=o.start,a=o.loose;let c="";for(let d=0;d<o.items.length;d++){const p=o.items[d],D=p.checked,b=p.task;let m="";if(p.task){const k=this.renderer.checkbox(!!D);a?p.tokens.length>0&&p.tokens[0].type==="paragraph"?(p.tokens[0].text=k+" "+p.tokens[0].text,p.tokens[0].tokens&&p.tokens[0].tokens.length>0&&p.tokens[0].tokens[0].type==="text"&&(p.tokens[0].tokens[0].text=k+" "+p.tokens[0].tokens[0].text)):p.tokens.unshift({type:"text",text:k+" "}):m+=k+" "}m+=this.parse(p.tokens,a),c+=this.renderer.listitem(m,b,!!D)}n+=this.renderer.list(c,l,u);continue}case"html":{const o=i;n+=this.renderer.html(o.text,o.block);continue}case"paragraph":{const o=i;n+=this.renderer.paragraph(this.parseInline(o.tokens));continue}case"text":{let o=i,l=o.tokens?this.parseInline(o.tokens):o.text;for(;s+1<e.length&&e[s+1].type==="text";)o=e[++s],l+=`
`+(o.tokens?this.parseInline(o.tokens):o.text);n+=t?this.renderer.paragraph(l):l;continue}default:{const o='Token with "'+i.type+'" type was not found.';if(this.options.silent)return console.error(o),"";throw new Error(o)}}}return n}parseInline(e,t){t=t||this.renderer;let n="";for(let s=0;s<e.length;s++){const i=e[s];if(this.options.extensions&&this.options.extensions.renderers&&this.options.extensions.renderers[i.type]){const o=this.options.extensions.renderers[i.type].call({parser:this},i);if(o!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(i.type)){n+=o||"";continue}}switch(i.type){case"escape":{const o=i;n+=t.text(o.text);break}case"html":{const o=i;n+=t.html(o.text);break}case"link":{const o=i;n+=t.link(o.href,o.title,this.parseInline(o.tokens,t));break}case"image":{const o=i;n+=t.image(o.href,o.title,o.text);break}case"strong":{const o=i;n+=t.strong(this.parseInline(o.tokens,t));break}case"em":{const o=i;n+=t.em(this.parseInline(o.tokens,t));break}case"codespan":{const o=i;n+=t.codespan(o.text);break}case"br":n+=t.br();break;case"del":{const o=i;n+=t.del(this.parseInline(o.tokens,t));break}case"text":{const o=i;n+=t.text(o.text);break}default:{const o='Token with "'+i.type+'" type was not found.';if(this.options.silent)return console.error(o),"";throw new Error(o)}}}return n}}class Je{constructor(e){se(this,"options");this.options=e||Ue}preprocess(e){return e}postprocess(e){return e}processAllTokens(e){return e}}se(Je,"passThroughHooks",new Set(["preprocess","postprocess","processAllTokens"]));var Ze,At,Ls,us;const Pe=new(us=class{constructor(...r){Pt(this,Ze);se(this,"defaults",{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null});se(this,"options",this.setOptions);se(this,"parse",st(this,Ze,At).call(this,Be.lex,ze.parse));se(this,"parseInline",st(this,Ze,At).call(this,Be.lexInline,ze.parseInline));se(this,"Parser",ze);se(this,"Renderer",ht);se(this,"TextRenderer",St);se(this,"Lexer",Be);se(this,"Tokenizer",pt);se(this,"Hooks",Je);this.use(...r)}walkTokens(r,e){var n,s;let t=[];for(const i of r)switch(t=t.concat(e.call(this,i)),i.type){case"table":{const o=i;for(const l of o.header)t=t.concat(this.walkTokens(l.tokens,e));for(const l of o.rows)for(const u of l)t=t.concat(this.walkTokens(u.tokens,e));break}case"list":{const o=i;t=t.concat(this.walkTokens(o.items,e));break}default:{const o=i;(s=(n=this.defaults.extensions)==null?void 0:n.childTokens)!=null&&s[o.type]?this.defaults.extensions.childTokens[o.type].forEach(l=>{const u=o[l].flat(1/0);t=t.concat(this.walkTokens(u,e))}):o.tokens&&(t=t.concat(this.walkTokens(o.tokens,e)))}}return t}use(...r){const e=this.defaults.extensions||{renderers:{},childTokens:{}};return r.forEach(t=>{const n={...t};if(n.async=this.defaults.async||n.async||!1,t.extensions&&(t.extensions.forEach(s=>{if(!s.name)throw new Error("extension name required");if("renderer"in s){const i=e.renderers[s.name];e.renderers[s.name]=i?function(...o){let l=s.renderer.apply(this,o);return l===!1&&(l=i.apply(this,o)),l}:s.renderer}if("tokenizer"in s){if(!s.level||s.level!=="block"&&s.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");const i=e[s.level];i?i.unshift(s.tokenizer):e[s.level]=[s.tokenizer],s.start&&(s.level==="block"?e.startBlock?e.startBlock.push(s.start):e.startBlock=[s.start]:s.level==="inline"&&(e.startInline?e.startInline.push(s.start):e.startInline=[s.start]))}"childTokens"in s&&s.childTokens&&(e.childTokens[s.name]=s.childTokens)}),n.extensions=e),t.renderer){const s=this.defaults.renderer||new ht(this.defaults);for(const i in t.renderer){if(!(i in s))throw new Error(`renderer '${i}' does not exist`);if(i==="options")continue;const o=i,l=t.renderer[o],u=s[o];s[o]=(...a)=>{let c=l.apply(s,a);return c===!1&&(c=u.apply(s,a)),c||""}}n.renderer=s}if(t.tokenizer){const s=this.defaults.tokenizer||new pt(this.defaults);for(const i in t.tokenizer){if(!(i in s))throw new Error(`tokenizer '${i}' does not exist`);if(["options","rules","lexer"].includes(i))continue;const o=i,l=t.tokenizer[o],u=s[o];s[o]=(...a)=>{let c=l.apply(s,a);return c===!1&&(c=u.apply(s,a)),c}}n.tokenizer=s}if(t.hooks){const s=this.defaults.hooks||new Je;for(const i in t.hooks){if(!(i in s))throw new Error(`hook '${i}' does not exist`);if(i==="options")continue;const o=i,l=t.hooks[o],u=s[o];Je.passThroughHooks.has(i)?s[o]=a=>{if(this.defaults.async)return Promise.resolve(l.call(s,a)).then(d=>u.call(s,d));const c=l.call(s,a);return u.call(s,c)}:s[o]=(...a)=>{let c=l.apply(s,a);return c===!1&&(c=u.apply(s,a)),c}}n.hooks=s}if(t.walkTokens){const s=this.defaults.walkTokens,i=t.walkTokens;n.walkTokens=function(o){let l=[];return l.push(i.call(this,o)),s&&(l=l.concat(s.call(this,o))),l}}this.defaults={...this.defaults,...n}}),this}setOptions(r){return this.defaults={...this.defaults,...r},this}lexer(r,e){return Be.lex(r,e??this.defaults)}parser(r,e){return ze.parse(r,e??this.defaults)}},Ze=new WeakSet,At=function(r,e){return(t,n)=>{const s={...n},i={...this.defaults,...s};this.defaults.async===!0&&s.async===!1&&(i.silent||console.warn("marked(): The async option was set to true by an extension. The async: false option sent to parse will be ignored."),i.async=!0);const o=st(this,Ze,Ls).call(this,!!i.silent,!!i.async);if(t==null)return o(new Error("marked(): input parameter is undefined or null"));if(typeof t!="string")return o(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(t)+", string expected"));if(i.hooks&&(i.hooks.options=i),i.async)return Promise.resolve(i.hooks?i.hooks.preprocess(t):t).then(l=>r(l,i)).then(l=>i.hooks?i.hooks.processAllTokens(l):l).then(l=>i.walkTokens?Promise.all(this.walkTokens(l,i.walkTokens)).then(()=>l):l).then(l=>e(l,i)).then(l=>i.hooks?i.hooks.postprocess(l):l).catch(o);try{i.hooks&&(t=i.hooks.preprocess(t));let l=r(t,i);i.hooks&&(l=i.hooks.processAllTokens(l)),i.walkTokens&&this.walkTokens(l,i.walkTokens);let u=e(l,i);return i.hooks&&(u=i.hooks.postprocess(u)),u}catch(l){return o(l)}}},Ls=function(r,e){return t=>{if(t.message+=`
Please report this to https://github.com/markedjs/marked.`,r){const n="<p>An error occurred:</p><pre>"+ye(t.message+"",!0)+"</pre>";return e?Promise.resolve(n):n}if(e)return Promise.reject(t);throw t}},us);function le(r,e){return Pe.parse(r,e)}le.options=le.setOptions=function(r){return Pe.setOptions(r),le.defaults=Pe.defaults,bn(le.defaults),le},le.getDefaults=To,le.defaults=Ue,le.use=function(...r){return Pe.use(...r),le.defaults=Pe.defaults,bn(le.defaults),le},le.walkTokens=function(r,e){return Pe.walkTokens(r,e)},le.parseInline=Pe.parseInline,le.Parser=ze,le.parser=ze.parse,le.Renderer=ht,le.TextRenderer=St,le.Lexer=Be,le.lexer=Be.lex,le.Tokenizer=pt,le.Hooks=Je,le.parse=le,le.options,le.setOptions,le.use,le.walkTokens,le.parseInline,ze.parse,Be.lex;const sl=/[\0-\x1F!-,\.\/:-@\[-\^`\{-\xA9\xAB-\xB4\xB6-\xB9\xBB-\xBF\xD7\xF7\u02C2-\u02C5\u02D2-\u02DF\u02E5-\u02EB\u02ED\u02EF-\u02FF\u0375\u0378\u0379\u037E\u0380-\u0385\u0387\u038B\u038D\u03A2\u03F6\u0482\u0530\u0557\u0558\u055A-\u055F\u0589-\u0590\u05BE\u05C0\u05C3\u05C6\u05C8-\u05CF\u05EB-\u05EE\u05F3-\u060F\u061B-\u061F\u066A-\u066D\u06D4\u06DD\u06DE\u06E9\u06FD\u06FE\u0700-\u070F\u074B\u074C\u07B2-\u07BF\u07F6-\u07F9\u07FB\u07FC\u07FE\u07FF\u082E-\u083F\u085C-\u085F\u086B-\u089F\u08B5\u08C8-\u08D2\u08E2\u0964\u0965\u0970\u0984\u098D\u098E\u0991\u0992\u09A9\u09B1\u09B3-\u09B5\u09BA\u09BB\u09C5\u09C6\u09C9\u09CA\u09CF-\u09D6\u09D8-\u09DB\u09DE\u09E4\u09E5\u09F2-\u09FB\u09FD\u09FF\u0A00\u0A04\u0A0B-\u0A0E\u0A11\u0A12\u0A29\u0A31\u0A34\u0A37\u0A3A\u0A3B\u0A3D\u0A43-\u0A46\u0A49\u0A4A\u0A4E-\u0A50\u0A52-\u0A58\u0A5D\u0A5F-\u0A65\u0A76-\u0A80\u0A84\u0A8E\u0A92\u0AA9\u0AB1\u0AB4\u0ABA\u0ABB\u0AC6\u0ACA\u0ACE\u0ACF\u0AD1-\u0ADF\u0AE4\u0AE5\u0AF0-\u0AF8\u0B00\u0B04\u0B0D\u0B0E\u0B11\u0B12\u0B29\u0B31\u0B34\u0B3A\u0B3B\u0B45\u0B46\u0B49\u0B4A\u0B4E-\u0B54\u0B58-\u0B5B\u0B5E\u0B64\u0B65\u0B70\u0B72-\u0B81\u0B84\u0B8B-\u0B8D\u0B91\u0B96-\u0B98\u0B9B\u0B9D\u0BA0-\u0BA2\u0BA5-\u0BA7\u0BAB-\u0BAD\u0BBA-\u0BBD\u0BC3-\u0BC5\u0BC9\u0BCE\u0BCF\u0BD1-\u0BD6\u0BD8-\u0BE5\u0BF0-\u0BFF\u0C0D\u0C11\u0C29\u0C3A-\u0C3C\u0C45\u0C49\u0C4E-\u0C54\u0C57\u0C5B-\u0C5F\u0C64\u0C65\u0C70-\u0C7F\u0C84\u0C8D\u0C91\u0CA9\u0CB4\u0CBA\u0CBB\u0CC5\u0CC9\u0CCE-\u0CD4\u0CD7-\u0CDD\u0CDF\u0CE4\u0CE5\u0CF0\u0CF3-\u0CFF\u0D0D\u0D11\u0D45\u0D49\u0D4F-\u0D53\u0D58-\u0D5E\u0D64\u0D65\u0D70-\u0D79\u0D80\u0D84\u0D97-\u0D99\u0DB2\u0DBC\u0DBE\u0DBF\u0DC7-\u0DC9\u0DCB-\u0DCE\u0DD5\u0DD7\u0DE0-\u0DE5\u0DF0\u0DF1\u0DF4-\u0E00\u0E3B-\u0E3F\u0E4F\u0E5A-\u0E80\u0E83\u0E85\u0E8B\u0EA4\u0EA6\u0EBE\u0EBF\u0EC5\u0EC7\u0ECE\u0ECF\u0EDA\u0EDB\u0EE0-\u0EFF\u0F01-\u0F17\u0F1A-\u0F1F\u0F2A-\u0F34\u0F36\u0F38\u0F3A-\u0F3D\u0F48\u0F6D-\u0F70\u0F85\u0F98\u0FBD-\u0FC5\u0FC7-\u0FFF\u104A-\u104F\u109E\u109F\u10C6\u10C8-\u10CC\u10CE\u10CF\u10FB\u1249\u124E\u124F\u1257\u1259\u125E\u125F\u1289\u128E\u128F\u12B1\u12B6\u12B7\u12BF\u12C1\u12C6\u12C7\u12D7\u1311\u1316\u1317\u135B\u135C\u1360-\u137F\u1390-\u139F\u13F6\u13F7\u13FE-\u1400\u166D\u166E\u1680\u169B-\u169F\u16EB-\u16ED\u16F9-\u16FF\u170D\u1715-\u171F\u1735-\u173F\u1754-\u175F\u176D\u1771\u1774-\u177F\u17D4-\u17D6\u17D8-\u17DB\u17DE\u17DF\u17EA-\u180A\u180E\u180F\u181A-\u181F\u1879-\u187F\u18AB-\u18AF\u18F6-\u18FF\u191F\u192C-\u192F\u193C-\u1945\u196E\u196F\u1975-\u197F\u19AC-\u19AF\u19CA-\u19CF\u19DA-\u19FF\u1A1C-\u1A1F\u1A5F\u1A7D\u1A7E\u1A8A-\u1A8F\u1A9A-\u1AA6\u1AA8-\u1AAF\u1AC1-\u1AFF\u1B4C-\u1B4F\u1B5A-\u1B6A\u1B74-\u1B7F\u1BF4-\u1BFF\u1C38-\u1C3F\u1C4A-\u1C4C\u1C7E\u1C7F\u1C89-\u1C8F\u1CBB\u1CBC\u1CC0-\u1CCF\u1CD3\u1CFB-\u1CFF\u1DFA\u1F16\u1F17\u1F1E\u1F1F\u1F46\u1F47\u1F4E\u1F4F\u1F58\u1F5A\u1F5C\u1F5E\u1F7E\u1F7F\u1FB5\u1FBD\u1FBF-\u1FC1\u1FC5\u1FCD-\u1FCF\u1FD4\u1FD5\u1FDC-\u1FDF\u1FED-\u1FF1\u1FF5\u1FFD-\u203E\u2041-\u2053\u2055-\u2070\u2072-\u207E\u2080-\u208F\u209D-\u20CF\u20F1-\u2101\u2103-\u2106\u2108\u2109\u2114\u2116-\u2118\u211E-\u2123\u2125\u2127\u2129\u212E\u213A\u213B\u2140-\u2144\u214A-\u214D\u214F-\u215F\u2189-\u24B5\u24EA-\u2BFF\u2C2F\u2C5F\u2CE5-\u2CEA\u2CF4-\u2CFF\u2D26\u2D28-\u2D2C\u2D2E\u2D2F\u2D68-\u2D6E\u2D70-\u2D7E\u2D97-\u2D9F\u2DA7\u2DAF\u2DB7\u2DBF\u2DC7\u2DCF\u2DD7\u2DDF\u2E00-\u2E2E\u2E30-\u3004\u3008-\u3020\u3030\u3036\u3037\u303D-\u3040\u3097\u3098\u309B\u309C\u30A0\u30FB\u3100-\u3104\u3130\u318F-\u319F\u31C0-\u31EF\u3200-\u33FF\u4DC0-\u4DFF\u9FFD-\u9FFF\uA48D-\uA4CF\uA4FE\uA4FF\uA60D-\uA60F\uA62C-\uA63F\uA673\uA67E\uA6F2-\uA716\uA720\uA721\uA789\uA78A\uA7C0\uA7C1\uA7CB-\uA7F4\uA828-\uA82B\uA82D-\uA83F\uA874-\uA87F\uA8C6-\uA8CF\uA8DA-\uA8DF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA954-\uA95F\uA97D-\uA97F\uA9C1-\uA9CE\uA9DA-\uA9DF\uA9FF\uAA37-\uAA3F\uAA4E\uAA4F\uAA5A-\uAA5F\uAA77-\uAA79\uAAC3-\uAADA\uAADE\uAADF\uAAF0\uAAF1\uAAF7-\uAB00\uAB07\uAB08\uAB0F\uAB10\uAB17-\uAB1F\uAB27\uAB2F\uAB5B\uAB6A-\uAB6F\uABEB\uABEE\uABEF\uABFA-\uABFF\uD7A4-\uD7AF\uD7C7-\uD7CA\uD7FC-\uD7FF\uE000-\uF8FF\uFA6E\uFA6F\uFADA-\uFAFF\uFB07-\uFB12\uFB18-\uFB1C\uFB29\uFB37\uFB3D\uFB3F\uFB42\uFB45\uFBB2-\uFBD2\uFD3E-\uFD4F\uFD90\uFD91\uFDC8-\uFDEF\uFDFC-\uFDFF\uFE10-\uFE1F\uFE30-\uFE32\uFE35-\uFE4C\uFE50-\uFE6F\uFE75\uFEFD-\uFF0F\uFF1A-\uFF20\uFF3B-\uFF3E\uFF40\uFF5B-\uFF65\uFFBF-\uFFC1\uFFC8\uFFC9\uFFD0\uFFD1\uFFD8\uFFD9\uFFDD-\uFFFF]|\uD800[\uDC0C\uDC27\uDC3B\uDC3E\uDC4E\uDC4F\uDC5E-\uDC7F\uDCFB-\uDD3F\uDD75-\uDDFC\uDDFE-\uDE7F\uDE9D-\uDE9F\uDED1-\uDEDF\uDEE1-\uDEFF\uDF20-\uDF2C\uDF4B-\uDF4F\uDF7B-\uDF7F\uDF9E\uDF9F\uDFC4-\uDFC7\uDFD0\uDFD6-\uDFFF]|\uD801[\uDC9E\uDC9F\uDCAA-\uDCAF\uDCD4-\uDCD7\uDCFC-\uDCFF\uDD28-\uDD2F\uDD64-\uDDFF\uDF37-\uDF3F\uDF56-\uDF5F\uDF68-\uDFFF]|\uD802[\uDC06\uDC07\uDC09\uDC36\uDC39-\uDC3B\uDC3D\uDC3E\uDC56-\uDC5F\uDC77-\uDC7F\uDC9F-\uDCDF\uDCF3\uDCF6-\uDCFF\uDD16-\uDD1F\uDD3A-\uDD7F\uDDB8-\uDDBD\uDDC0-\uDDFF\uDE04\uDE07-\uDE0B\uDE14\uDE18\uDE36\uDE37\uDE3B-\uDE3E\uDE40-\uDE5F\uDE7D-\uDE7F\uDE9D-\uDEBF\uDEC8\uDEE7-\uDEFF\uDF36-\uDF3F\uDF56-\uDF5F\uDF73-\uDF7F\uDF92-\uDFFF]|\uD803[\uDC49-\uDC7F\uDCB3-\uDCBF\uDCF3-\uDCFF\uDD28-\uDD2F\uDD3A-\uDE7F\uDEAA\uDEAD-\uDEAF\uDEB2-\uDEFF\uDF1D-\uDF26\uDF28-\uDF2F\uDF51-\uDFAF\uDFC5-\uDFDF\uDFF7-\uDFFF]|\uD804[\uDC47-\uDC65\uDC70-\uDC7E\uDCBB-\uDCCF\uDCE9-\uDCEF\uDCFA-\uDCFF\uDD35\uDD40-\uDD43\uDD48-\uDD4F\uDD74\uDD75\uDD77-\uDD7F\uDDC5-\uDDC8\uDDCD\uDDDB\uDDDD-\uDDFF\uDE12\uDE38-\uDE3D\uDE3F-\uDE7F\uDE87\uDE89\uDE8E\uDE9E\uDEA9-\uDEAF\uDEEB-\uDEEF\uDEFA-\uDEFF\uDF04\uDF0D\uDF0E\uDF11\uDF12\uDF29\uDF31\uDF34\uDF3A\uDF45\uDF46\uDF49\uDF4A\uDF4E\uDF4F\uDF51-\uDF56\uDF58-\uDF5C\uDF64\uDF65\uDF6D-\uDF6F\uDF75-\uDFFF]|\uD805[\uDC4B-\uDC4F\uDC5A-\uDC5D\uDC62-\uDC7F\uDCC6\uDCC8-\uDCCF\uDCDA-\uDD7F\uDDB6\uDDB7\uDDC1-\uDDD7\uDDDE-\uDDFF\uDE41-\uDE43\uDE45-\uDE4F\uDE5A-\uDE7F\uDEB9-\uDEBF\uDECA-\uDEFF\uDF1B\uDF1C\uDF2C-\uDF2F\uDF3A-\uDFFF]|\uD806[\uDC3B-\uDC9F\uDCEA-\uDCFE\uDD07\uDD08\uDD0A\uDD0B\uDD14\uDD17\uDD36\uDD39\uDD3A\uDD44-\uDD4F\uDD5A-\uDD9F\uDDA8\uDDA9\uDDD8\uDDD9\uDDE2\uDDE5-\uDDFF\uDE3F-\uDE46\uDE48-\uDE4F\uDE9A-\uDE9C\uDE9E-\uDEBF\uDEF9-\uDFFF]|\uD807[\uDC09\uDC37\uDC41-\uDC4F\uDC5A-\uDC71\uDC90\uDC91\uDCA8\uDCB7-\uDCFF\uDD07\uDD0A\uDD37-\uDD39\uDD3B\uDD3E\uDD48-\uDD4F\uDD5A-\uDD5F\uDD66\uDD69\uDD8F\uDD92\uDD99-\uDD9F\uDDAA-\uDEDF\uDEF7-\uDFAF\uDFB1-\uDFFF]|\uD808[\uDF9A-\uDFFF]|\uD809[\uDC6F-\uDC7F\uDD44-\uDFFF]|[\uD80A\uD80B\uD80E-\uD810\uD812-\uD819\uD824-\uD82B\uD82D\uD82E\uD830-\uD833\uD837\uD839\uD83D\uD83F\uD87B-\uD87D\uD87F\uD885-\uDB3F\uDB41-\uDBFF][\uDC00-\uDFFF]|\uD80D[\uDC2F-\uDFFF]|\uD811[\uDE47-\uDFFF]|\uD81A[\uDE39-\uDE3F\uDE5F\uDE6A-\uDECF\uDEEE\uDEEF\uDEF5-\uDEFF\uDF37-\uDF3F\uDF44-\uDF4F\uDF5A-\uDF62\uDF78-\uDF7C\uDF90-\uDFFF]|\uD81B[\uDC00-\uDE3F\uDE80-\uDEFF\uDF4B-\uDF4E\uDF88-\uDF8E\uDFA0-\uDFDF\uDFE2\uDFE5-\uDFEF\uDFF2-\uDFFF]|\uD821[\uDFF8-\uDFFF]|\uD823[\uDCD6-\uDCFF\uDD09-\uDFFF]|\uD82C[\uDD1F-\uDD4F\uDD53-\uDD63\uDD68-\uDD6F\uDEFC-\uDFFF]|\uD82F[\uDC6B-\uDC6F\uDC7D-\uDC7F\uDC89-\uDC8F\uDC9A-\uDC9C\uDC9F-\uDFFF]|\uD834[\uDC00-\uDD64\uDD6A-\uDD6C\uDD73-\uDD7A\uDD83\uDD84\uDD8C-\uDDA9\uDDAE-\uDE41\uDE45-\uDFFF]|\uD835[\uDC55\uDC9D\uDCA0\uDCA1\uDCA3\uDCA4\uDCA7\uDCA8\uDCAD\uDCBA\uDCBC\uDCC4\uDD06\uDD0B\uDD0C\uDD15\uDD1D\uDD3A\uDD3F\uDD45\uDD47-\uDD49\uDD51\uDEA6\uDEA7\uDEC1\uDEDB\uDEFB\uDF15\uDF35\uDF4F\uDF6F\uDF89\uDFA9\uDFC3\uDFCC\uDFCD]|\uD836[\uDC00-\uDDFF\uDE37-\uDE3A\uDE6D-\uDE74\uDE76-\uDE83\uDE85-\uDE9A\uDEA0\uDEB0-\uDFFF]|\uD838[\uDC07\uDC19\uDC1A\uDC22\uDC25\uDC2B-\uDCFF\uDD2D-\uDD2F\uDD3E\uDD3F\uDD4A-\uDD4D\uDD4F-\uDEBF\uDEFA-\uDFFF]|\uD83A[\uDCC5-\uDCCF\uDCD7-\uDCFF\uDD4C-\uDD4F\uDD5A-\uDFFF]|\uD83B[\uDC00-\uDDFF\uDE04\uDE20\uDE23\uDE25\uDE26\uDE28\uDE33\uDE38\uDE3A\uDE3C-\uDE41\uDE43-\uDE46\uDE48\uDE4A\uDE4C\uDE50\uDE53\uDE55\uDE56\uDE58\uDE5A\uDE5C\uDE5E\uDE60\uDE63\uDE65\uDE66\uDE6B\uDE73\uDE78\uDE7D\uDE7F\uDE8A\uDE9C-\uDEA0\uDEA4\uDEAA\uDEBC-\uDFFF]|\uD83C[\uDC00-\uDD2F\uDD4A-\uDD4F\uDD6A-\uDD6F\uDD8A-\uDFFF]|\uD83E[\uDC00-\uDFEF\uDFFA-\uDFFF]|\uD869[\uDEDE-\uDEFF]|\uD86D[\uDF35-\uDF3F]|\uD86E[\uDC1E\uDC1F]|\uD873[\uDEA2-\uDEAF]|\uD87A[\uDFE1-\uDFFF]|\uD87E[\uDE1E-\uDFFF]|\uD884[\uDF4B-\uDFFF]|\uDB40[\uDC00-\uDCFF\uDDF0-\uDFFF]/g,il=Object.hasOwnProperty;class rl{constructor(){this.occurrences,this.reset()}slug(e,t){const n=this;let s=function(o,l){return typeof o!="string"?"":(l||(o=o.toLowerCase()),o.replace(sl,"").replace(/ /g,"-"))}(e,t===!0);const i=s;for(;il.call(n.occurrences,s);)n.occurrences[i]++,s=i+"-"+n.occurrences[i];return n.occurrences[s]=0,s}reset(){this.occurrences=Object.create(null)}}function ol(r){let e,t;return e=new $t({props:{tokens:r[0],renderers:r[1],options:r[2]}}),{c(){M(e.$$.fragment)},m(n,s){T(e,n,s),t=!0},p(n,[s]){const i={};1&s&&(i.tokens=n[0]),2&s&&(i.renderers=n[1]),4&s&&(i.options=n[2]),e.$set(i)},i(n){t||(h(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){S(e,n)}}}function ll(r,e,t){(function(){const a=console.warn;console.warn=c=>{c.includes("unknown prop")||c.includes("unexpected slot")||a(c)},Xe(()=>{console.warn=a})})();let n,s,i,{source:o}=e,{options:l={}}=e,{renderers:u={}}=e;return r.$$set=a=>{"source"in a&&t(3,o=a.source),"options"in a&&t(4,l=a.options),"renderers"in a&&t(5,u=a.renderers)},r.$$.update=()=>{var a;56&r.$$.dirty&&(t(0,(a=o,n=new Be().lex(a))),t(1,s={heading:yr,blockquote:_r,list:Ir,list_item:Zr,br:jr,code:Qr,codespan:Yr,table:eo,html:so,paragraph:oo,link:ao,text:fo,def:$o,del:Fo,em:vo,hr:Ao,strong:Eo,image:zo,space:yn,escape:yn,...u}),t(2,i={baseUrl:"/",slugger:new rl,...l}))},[n,s,i,o,l,u]}class ul extends Y{constructor(e){super(),X(this,e,ll,ol,J,{source:3,options:4,renderers:5})}}const al=r=>({}),Nn=r=>({}),cl=r=>({}),Rn=r=>({}),dl=r=>({}),Pn=r=>({});function pl(r){let e,t,n,s,i,o,l,u,a,c,d,p;const D=r[12].topBarLeft,b=De(D,r,r[11],Pn),m=r[12].topBarRight,k=De(m,r,r[11],Rn);function y(f){r[15](f)}let w={options:{lineNumbers:"off",wrappingIndent:"same",padding:r[5],wordWrap:r[2]?"off":"on",contextmenu:!1,wordBasedSuggestions:"off",renderLineHighlight:"none",occurrencesHighlight:"off",selectionHighlight:!1,codeLens:!1,links:!1,hover:{enabled:!1},hideCursorInOverviewRuler:!0,renderWhitespace:"none",renderFinalNewline:"on"},text:r[3].text,lang:r[4]||r[3].lang,height:r[6]};r[0]!==void 0&&(w.editorInstance=r[0]),o=new Xs({props:w}),Ae.push(()=>Oe(o,"editorInstance",y));const $=r[12].actionsBar,B=De($,r,r[11],Nn);return{c(){e=_("div"),t=_("div"),n=_("div"),b&&b.c(),s=Z(),k&&k.c(),i=Z(),M(o.$$.fragment),u=Z(),a=_("div"),B&&B.c(),A(n,"class","c-codeblock__top-bar-left svelte-1jljgam"),A(t,"class","c-codeblock__top-bar-anchor monaco-component svelte-1jljgam"),A(a,"class","c-codeblock__actions-bar-anchor svelte-1jljgam"),A(e,"class","c-codeblock svelte-1jljgam"),A(e,"role","button"),A(e,"tabindex","0")},m(f,C){x(f,e,C),L(e,t),L(t,n),b&&b.m(n,null),L(t,s),k&&k.m(t,null),L(e,i),T(o,e,null),L(e,u),L(e,a),B&&B.m(a,null),r[16](e),c=!0,d||(p=[Ye(window,"focus",r[14]),Ye(e,"mouseenter",r[13])],d=!0)},p(f,[C]){b&&b.p&&(!c||2048&C)&&Fe(b,D,f,f[11],c?xe(D,f[11],C,dl):ke(f[11]),Pn),k&&k.p&&(!c||2048&C)&&Fe(k,m,f,f[11],c?xe(m,f[11],C,cl):ke(f[11]),Rn);const E={};36&C&&(E.options={lineNumbers:"off",wrappingIndent:"same",padding:f[5],wordWrap:f[2]?"off":"on",contextmenu:!1,wordBasedSuggestions:"off",renderLineHighlight:"none",occurrencesHighlight:"off",selectionHighlight:!1,codeLens:!1,links:!1,hover:{enabled:!1},hideCursorInOverviewRuler:!0,renderWhitespace:"none",renderFinalNewline:"on"}),8&C&&(E.text=f[3].text),24&C&&(E.lang=f[4]||f[3].lang),64&C&&(E.height=f[6]),!l&&1&C&&(l=!0,E.editorInstance=f[0],Ve(()=>l=!1)),o.$set(E),B&&B.p&&(!c||2048&C)&&Fe(B,$,f,f[11],c?xe($,f[11],C,al):ke(f[11]),Nn)},i(f){c||(h(b,f),h(k,f),h(o.$$.fragment,f),h(B,f),c=!0)},o(f){g(b,f),g(k,f),g(o.$$.fragment,f),g(B,f),c=!1},d(f){f&&v(e),b&&b.d(f),k&&k.d(f),S(o),B&&B.d(f),r[16](null),d=!1,gs(p)}}}function fl(r,e,t){let n,{$$slots:s={},$$scope:i}=e,{scroll:o=!1}=e,{token:l}=e,{language:u}=e,{padding:a={top:0,bottom:0}}=e,{editorInstance:c}=e,{element:d}=e,{height:p}=e;const D=Et.getContext().monaco;be(r,D,k=>t(17,n=k));const b=()=>{if(!c)return;const k=c.getSelections();if(!(k!=null&&k.length))return;const y=c.getModel();if(k.map(w=>(y==null?void 0:y.getValueLengthInRange(w))||0).reduce((w,$)=>w+$,0)!==0)return k.sort(n==null?void 0:n.Range.compareRangesUsingStarts).map(w=>(y==null?void 0:y.getValueInRange(w))||"").join(`
`)},m=()=>{if(c)return c.getValue()||""};return r.$$set=k=>{"scroll"in k&&t(2,o=k.scroll),"token"in k&&t(3,l=k.token),"language"in k&&t(4,u=k.language),"padding"in k&&t(5,a=k.padding),"editorInstance"in k&&t(0,c=k.editorInstance),"element"in k&&t(1,d=k.element),"height"in k&&t(6,p=k.height),"$$scope"in k&&t(11,i=k.$$scope)},r.$$.update=()=>{var k;32&r.$$.dirty&&(k=a,c==null||c.updateOptions({padding:k})),65&r.$$.dirty&&(c==null||c.updateOptions({scrollbar:{vertical:p!==void 0?"auto":"hidden"}}))},[c,d,o,l,u,a,p,D,()=>c&&(b()||m())||"",b,m,i,s,function(k){Os.call(this,r,k)},()=>c==null?void 0:c.layout(),function(k){c=k,t(0,c)},function(k){Ae[k?"unshift":"push"](()=>{d=k,t(1,d)})}]}class In extends Y{constructor(e){super(),X(this,e,fl,pl,J,{scroll:2,token:3,language:4,padding:5,editorInstance:0,element:1,height:6,getSelectionOrContents:8,getSelections:9,getContents:10})}get getSelectionOrContents(){return this.$$.ctx[8]}get getSelections(){return this.$$.ctx[9]}get getContents(){return this.$$.ctx[10]}}const hl=r=>({codespanContents:2&r}),On=r=>({codespanContents:r[1]});function gl(r){let e,t,n;const s=r[4].default,i=De(s,r,r[3],On),o=i||function(l){let u;return{c(){u=U(l[1])},m(a,c){x(a,u,c)},p(a,c){2&c&&de(u,a[1])},d(a){a&&v(u)}}}(r);return{c(){e=_("span"),t=_("code"),o&&o.c(),A(t,"class","markdown-codespan svelte-1ufogiu")},m(l,u){x(l,e,u),L(e,t),o&&o.m(t,null),r[5](e),n=!0},p(l,[u]){i?i.p&&(!n||10&u)&&Fe(i,s,l,l[3],n?xe(s,l[3],u,hl):ke(l[3]),On):o&&o.p&&(!n||2&u)&&o.p(l,n?u:-1)},i(l){n||(h(o,l),n=!0)},o(l){g(o,l),n=!1},d(l){l&&v(e),o&&o.d(l),r[5](null)}}}function $l(r,e,t){let n,{$$slots:s={},$$scope:i}=e,{token:o}=e,{element:l}=e;return r.$$set=u=>{"token"in u&&t(2,o=u.token),"element"in u&&t(0,l=u.element),"$$scope"in u&&t(3,i=u.$$scope)},r.$$.update=()=>{4&r.$$.dirty&&t(1,n=o.raw.slice(1,o.raw.length-1))},[l,n,o,i,s,function(u){Ae[u?"unshift":"push"](()=>{l=u,t(0,l)})}]}class Vn extends Y{constructor(e){super(),X(this,e,$l,gl,J,{token:2,element:0})}}function ml(r){let e,t;const n=r[1].default,s=De(n,r,r[0],null);return{c(){e=_("p"),s&&s.c(),A(e,"class","augment-markdown-paragraph svelte-1edcdk9")},m(i,o){x(i,e,o),s&&s.m(e,null),t=!0},p(i,[o]){s&&s.p&&(!t||1&o)&&Fe(s,n,i,i[0],t?xe(n,i[0],o,null):ke(i[0]),null)},i(i){t||(h(s,i),t=!0)},o(i){g(s,i),t=!1},d(i){i&&v(e),s&&s.d(i)}}}function Dl(r,e,t){let{$$slots:n={},$$scope:s}=e;return r.$$set=i=>{"$$scope"in i&&t(0,s=i.$$scope)},[s,n]}class Zn extends Y{constructor(e){super(),X(this,e,Dl,ml,J,{})}}function Fl(r){let e,t,n;return t=new ul({props:{source:r[0],renderers:{codespan:Vn,code:In,paragraph:Zn,...r[1]}}}),{c(){e=_("div"),M(t.$$.fragment),A(e,"class","c-markdown svelte-n6ddeo")},m(s,i){x(s,e,i),T(t,e,null),n=!0},p(s,[i]){const o={};1&i&&(o.source=s[0]),2&i&&(o.renderers={codespan:Vn,code:In,paragraph:Zn,...s[1]}),t.$set(o)},i(s){n||(h(t.$$.fragment,s),n=!0)},o(s){g(t.$$.fragment,s),n=!1},d(s){s&&v(e),S(t)}}}function kl(r,e,t){let{markdown:n}=e,{renderers:s={}}=e;return r.$$set=i=>{"markdown"in i&&t(0,n=i.markdown),"renderers"in i&&t(1,s=i.renderers)},[n,s]}class xl extends Y{constructor(e){super(),X(this,e,kl,Fl,J,{markdown:0,renderers:1})}}function vl(r){let e;return{c(){e=U(r[1])},m(t,n){x(t,e,n)},p(t,n){2&n&&de(e,t[1])},d(t){t&&v(e)}}}function Cl(r){let e;return{c(){e=U(r[1])},m(t,n){x(t,e,n)},p(t,n){2&n&&de(e,t[1])},d(t){t&&v(e)}}}function wl(r){let e,t,n;function s(l,u){return l[2]?Cl:vl}let i=s(r),o=i(r);return{c(){e=_("span"),t=_("code"),o.c(),A(t,"class","markdown-codespan svelte-164mxpf"),A(t,"style",n=r[2]?`background-color: ${r[1]}; color: ${r[3]?"white":"black"}`:""),$e(t,"markdown-string",r[4])},m(l,u){x(l,e,u),L(e,t),o.m(t,null),r[6](e)},p(l,[u]){i===(i=s(l))&&o?o.p(l,u):(o.d(1),o=i(l),o&&(o.c(),o.m(t,null))),14&u&&n!==(n=l[2]?`background-color: ${l[1]}; color: ${l[3]?"white":"black"}`:"")&&A(t,"style",n),16&u&&$e(t,"markdown-string",l[4])},i:G,o:G,d(l){l&&v(e),o.d(),r[6](null)}}}function Al(r,e,t){let n,s,i,o,{token:l}=e,{element:u}=e;return r.$$set=a=>{"token"in a&&t(5,l=a.token),"element"in a&&t(0,u=a.element)},r.$$.update=()=>{32&r.$$.dirty&&t(1,n=l.raw.slice(1,l.raw.length-1)),2&r.$$.dirty&&t(4,s=n.startsWith('"')),2&r.$$.dirty&&t(2,i=/^#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3}/.test(n)),6&r.$$.dirty&&t(3,o=i&&function(a){if(!/^#([0-9A-F]{3}|[0-9A-F]{6})$/i.test(a))throw new Error('Invalid hex color format. Expected "#RGB" or "#RRGGBB"');let c,d,p;return a.length===4?(c=parseInt(a.charAt(1),16),d=parseInt(a.charAt(2),16),p=parseInt(a.charAt(3),16),c*=17,d*=17,p*=17):(c=parseInt(a.slice(1,3),16),d=parseInt(a.slice(3,5),16),p=parseInt(a.slice(5,7),16)),.299*c+.587*d+.114*p<130}(n))},[u,n,i,o,s,l,function(a){Ae[a?"unshift":"push"](()=>{u=a,t(0,u)})}]}class yl extends Y{constructor(e){super(),X(this,e,Al,wl,J,{token:5,element:0})}}function bl(r){let e,t;return e=new xl({props:{markdown:r[1](r[0]),renderers:r[2]}}),{c(){M(e.$$.fragment)},m(n,s){T(e,n,s),t=!0},p(n,[s]){const i={};1&s&&(i.markdown=n[1](n[0])),e.$set(i)},i(n){t||(h(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){S(e,n)}}}function El(r,e,t){let{markdown:n}=e;const s={codespan:yl};return r.$$set=i=>{"markdown"in i&&t(0,n=i.markdown)},[n,i=>i.replace(/`?#[0-9a-fA-F]{3,6}`?/g,o=>o.startsWith("`")?o:`\`${o}\``),s]}class _l extends Y{constructor(e){super(),X(this,e,El,bl,J,{markdown:0})}}const Ms=Symbol("focusedPath");function qn(r,e,t){const n=r.slice();return n[6]=e[t],n}function Bl(r){let e,t;return e=new ii({props:{filename:r[0].name}}),{c(){M(e.$$.fragment)},m(n,s){T(e,n,s),t=!0},p(n,s){const i={};1&s&&(i.filename=n[0].name),e.$set(i)},i(n){t||(h(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){S(e,n)}}}function zl(r){let e,t;return e=new Ke({props:{icon:r[0].isExpanded?"chevron-down":"chevron-right"}}),{c(){M(e.$$.fragment)},m(n,s){T(e,n,s),t=!0},p(n,s){const i={};1&s&&(i.icon=n[0].isExpanded?"chevron-down":"chevron-right"),e.$set(i)},i(n){t||(h(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){S(e,n)}}}function Ll(r){let e,t,n=(r[0].displayName||r[0].name)+"";return{c(){e=_("span"),t=U(n),A(e,"class","full-path-text svelte-qnxoj")},m(s,i){x(s,e,i),L(e,t)},p(s,i){1&i&&n!==(n=(s[0].displayName||s[0].name)+"")&&de(t,n)},d(s){s&&v(e)}}}function Un(r){let e,t,n=ne(Array.from(r[0].children.values()).sort(Hn)),s=[];for(let o=0;o<n.length;o+=1)s[o]=jn(qn(r,n,o));const i=o=>g(s[o],1,1,()=>{s[o]=null});return{c(){e=_("div");for(let o=0;o<s.length;o+=1)s[o].c();A(e,"class","tree-node__children svelte-qnxoj"),A(e,"role","group")},m(o,l){x(o,e,l);for(let u=0;u<s.length;u+=1)s[u]&&s[u].m(e,null);t=!0},p(o,l){if(3&l){let u;for(n=ne(Array.from(o[0].children.values()).sort(Hn)),u=0;u<n.length;u+=1){const a=qn(o,n,u);s[u]?(s[u].p(a,l),h(s[u],1)):(s[u]=jn(a),s[u].c(),h(s[u],1),s[u].m(e,null))}for(W(),u=n.length;u<s.length;u+=1)i(u);Q()}},i(o){if(!t){for(let l=0;l<n.length;l+=1)h(s[l]);t=!0}},o(o){s=s.filter(Boolean);for(let l=0;l<s.length;l+=1)g(s[l]);t=!1},d(o){o&&v(e),ve(s,o)}}}function jn(r){let e,t;return e=new Ts({props:{node:r[6],indentLevel:r[1]+1}}),{c(){M(e.$$.fragment)},m(n,s){T(e,n,s),t=!0},p(n,s){const i={};1&s&&(i.node=n[6]),2&s&&(i.indentLevel=n[1]+1),e.$set(i)},i(n){t||(h(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){S(e,n)}}}function Ml(r){let e,t,n,s,i,o,l,u,a,c,d,p,D,b,m,k,y;const w=[zl,Bl],$=[];function B(C,E){return C[0].isFile?1:0}o=B(r),l=$[o]=w[o](r),c=new fe({props:{size:1,$$slots:{default:[Ll]},$$scope:{ctx:r}}});let f=!r[0].isFile&&r[0].isExpanded&&r[0].children.size>0&&Un(r);return{c(){e=_("div"),t=_("div"),n=_("div"),s=Z(),i=_("div"),l.c(),u=Z(),a=_("span"),M(c.$$.fragment),b=Z(),f&&f.c(),A(n,"class","tree-node__indent svelte-qnxoj"),me(n,"width",6*r[1]+"px"),A(i,"class","tree-node__icon-container svelte-qnxoj"),A(a,"class","tree-node__label svelte-qnxoj"),A(a,"title",d=r[0].displayName||r[0].name),$e(a,"full-path",r[0].displayName),A(t,"class","tree-node__content svelte-qnxoj"),A(t,"role","treeitem"),A(t,"tabindex","0"),A(t,"aria-selected",p=r[0].path===r[2]),A(t,"aria-expanded",D=r[0].isFile?void 0:r[0].isExpanded),$e(t,"selected",r[0].path===r[2]),$e(t,"collapsed-folder",r[0].displayName&&!r[0].isFile),A(e,"class","tree-node svelte-qnxoj")},m(C,E){x(C,e,E),L(e,t),L(t,n),L(t,s),L(t,i),$[o].m(i,null),L(t,u),L(t,a),T(c,a,null),L(e,b),f&&f.m(e,null),m=!0,k||(y=[Ye(t,"click",r[4]),Ye(t,"keydown",r[5])],k=!0)},p(C,[E]){(!m||2&E)&&me(n,"width",6*C[1]+"px");let V=o;o=B(C),o===V?$[o].p(C,E):(W(),g($[V],1,1,()=>{$[V]=null}),Q(),l=$[o],l?l.p(C,E):(l=$[o]=w[o](C),l.c()),h(l,1),l.m(i,null));const j={};513&E&&(j.$$scope={dirty:E,ctx:C}),c.$set(j),(!m||1&E&&d!==(d=C[0].displayName||C[0].name))&&A(a,"title",d),(!m||1&E)&&$e(a,"full-path",C[0].displayName),(!m||5&E&&p!==(p=C[0].path===C[2]))&&A(t,"aria-selected",p),(!m||1&E&&D!==(D=C[0].isFile?void 0:C[0].isExpanded))&&A(t,"aria-expanded",D),(!m||5&E)&&$e(t,"selected",C[0].path===C[2]),(!m||1&E)&&$e(t,"collapsed-folder",C[0].displayName&&!C[0].isFile),!C[0].isFile&&C[0].isExpanded&&C[0].children.size>0?f?(f.p(C,E),1&E&&h(f,1)):(f=Un(C),f.c(),h(f,1),f.m(e,null)):f&&(W(),g(f,1,1,()=>{f=null}),Q())},i(C){m||(h(l),h(c.$$.fragment,C),h(f),m=!0)},o(C){g(l),g(c.$$.fragment,C),g(f),m=!1},d(C){C&&v(e),$[o].d(),S(c),f&&f.d(),k=!1,gs(y)}}}const Hn=(r,e)=>r.isFile===e.isFile?r.name.localeCompare(e.name):r.isFile?1:-1;function Tl(r,e,t){let n,{node:s}=e,{indentLevel:i=0}=e;const o=vt(Ms);function l(){s.isFile?o.set(s.path):t(0,s.isExpanded=!s.isExpanded,s)}return be(r,o,u=>t(2,n=u)),r.$$set=u=>{"node"in u&&t(0,s=u.node),"indentLevel"in u&&t(1,i=u.indentLevel)},[s,i,n,o,l,u=>u.key==="Enter"&&l()]}class Ts extends Y{constructor(e){super(),X(this,e,Tl,Ml,J,{node:0,indentLevel:1})}}function Wn(r,e,t){const n=r.slice();return n[4]=e[t],n}function Sl(r){let e,t,n=ne(Array.from(r[1].children.values()).sort(Gn)),s=[];for(let o=0;o<n.length;o+=1)s[o]=Qn(Wn(r,n,o));const i=o=>g(s[o],1,1,()=>{s[o]=null});return{c(){for(let o=0;o<s.length;o+=1)s[o].c();e=Ce()},m(o,l){for(let u=0;u<s.length;u+=1)s[u]&&s[u].m(o,l);x(o,e,l),t=!0},p(o,l){if(2&l){let u;for(n=ne(Array.from(o[1].children.values()).sort(Gn)),u=0;u<n.length;u+=1){const a=Wn(o,n,u);s[u]?(s[u].p(a,l),h(s[u],1)):(s[u]=Qn(a),s[u].c(),h(s[u],1),s[u].m(e.parentNode,e))}for(W(),u=n.length;u<s.length;u+=1)i(u);Q()}},i(o){if(!t){for(let l=0;l<n.length;l+=1)h(s[l]);t=!0}},o(o){s=s.filter(Boolean);for(let l=0;l<s.length;l+=1)g(s[l]);t=!1},d(o){o&&v(e),ve(s,o)}}}function Nl(r){let e,t,n;return t=new fe({props:{size:1,color:"neutral",$$slots:{default:[Pl]},$$scope:{ctx:r}}}),{c(){e=_("div"),M(t.$$.fragment),A(e,"class","tree-view__empty svelte-1tnd9l7")},m(s,i){x(s,e,i),T(t,e,null),n=!0},p(s,i){const o={};128&i&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){n||(h(t.$$.fragment,s),n=!0)},o(s){g(t.$$.fragment,s),n=!1},d(s){s&&v(e),S(t)}}}function Rl(r){let e;return{c(){e=_("div"),e.innerHTML='<div class="tree-view__skeleton svelte-1tnd9l7"><div class="tree-view__skeleton-item svelte-1tnd9l7"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="margin-left: 12px;"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="margin-left: 12px;"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="margin-left: 12px;"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="width: 70%;"></div></div>',A(e,"class","tree-view__loading svelte-1tnd9l7")},m(t,n){x(t,e,n)},p:G,i:G,o:G,d(t){t&&v(e)}}}function Qn(r){let e,t;return e=new Ts({props:{node:r[4],indentLevel:0}}),{c(){M(e.$$.fragment)},m(n,s){T(e,n,s),t=!0},p(n,s){const i={};2&s&&(i.node=n[4]),e.$set(i)},i(n){t||(h(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){S(e,n)}}}function Pl(r){let e;return{c(){e=U("No changed files")},m(t,n){x(t,e,n)},d(t){t&&v(e)}}}function Il(r){let e,t,n,s,i;const o=[Rl,Nl,Sl],l=[];function u(a,c){return a[0]?0:a[1].children.size===0?1:2}return n=u(r),s=l[n]=o[n](r),{c(){e=_("div"),t=_("div"),s.c(),A(t,"class","tree-view__content svelte-1tnd9l7"),A(t,"role","tree"),A(t,"aria-label","Changed Files"),A(e,"class","tree-view svelte-1tnd9l7")},m(a,c){x(a,e,c),L(e,t),l[n].m(t,null),i=!0},p(a,[c]){let d=n;n=u(a),n===d?l[n].p(a,c):(W(),g(l[d],1,1,()=>{l[d]=null}),Q(),s=l[n],s?s.p(a,c):(s=l[n]=o[n](a),s.c()),h(s,1),s.m(t,null))},i(a){i||(h(s),i=!0)},o(a){g(s),i=!1},d(a){a&&v(e),l[n].d()}}}function yt(r,e=!1){if(r.isFile)return;let t="";e&&(t=function(o){let l=o.path.split("/"),u=o;for(;;){const a=Array.from(u.children.values()).filter(d=>!d.isFile),c=Array.from(u.children.values()).filter(d=>d.isFile);if(a.length!==1||c.length!==0)break;u=a[0],l.push(u.name)}return l.join("/")}(r));const n=Array.from(r.children.values()).filter(o=>!o.isFile);for(const o of n)yt(o);const s=Array.from(r.children.values()).filter(o=>!o.isFile),i=Array.from(r.children.values()).filter(o=>o.isFile);if(s.length===1&&i.length===0){const o=s[0],l=o.name;if(e){r.displayName=t||`${r.name}/${l}`;for(const[u,a]of o.children.entries()){const c=`${u}`;r.children.set(c,a)}r.children.delete(l)}else{r.displayName?o.displayName=`${r.displayName}/${l}`:o.displayName=`${r.name}/${l}`;for(const[u,a]of o.children.entries()){const c=`${l}/${u}`;r.children.set(c,a)}r.children.delete(l)}}}const Gn=(r,e)=>r.isFile===e.isFile?r.name.localeCompare(e.name):r.isFile?1:-1;function Ol(r,e,t){let n,{changedFiles:s=[]}=e,{isLoading:i=!1}=e;function o(l){const u={name:"",path:"",isFile:!1,children:new Map,isExpanded:!0};return l.forEach(a=>{const c=a.change_type===si.deleted?a.old_path:a.new_path;c&&function(d,p){const D=p.split("/");let b=d;for(let m=0;m<D.length;m++){const k=D[m],y=m===D.length-1,w=D.slice(0,m+1).join("/");b.children.has(k)||b.children.set(k,{name:k,path:w,isFile:y,children:new Map,isExpanded:!0}),b=b.children.get(k)}}(u,c)}),function(a){if(!a.isFile)if(a.path!=="")yt(a);else{const c=Array.from(a.children.values()).filter(d=>!d.isFile);for(const d of c)yt(d,!0)}}(u),u}return r.$$set=l=>{"changedFiles"in l&&t(2,s=l.changedFiles),"isLoading"in l&&t(0,i=l.isLoading)},r.$$.update=()=>{4&r.$$.dirty&&t(1,n=o(s))},[i,n,s]}class Vl extends Y{constructor(e){super(),X(this,e,Ol,Il,J,{changedFiles:2,isLoading:0})}}const{Boolean:Nt}=ti;function Jn(r,e,t){const n=r.slice();return n[43]=e[t],n[44]=e,n[45]=t,n}function Yn(r,e,t){const n=r.slice();return n[46]=e[t],n[47]=e,n[48]=t,n}function Xn(r,e,t){const n=r.slice();return n[49]=e[t],n[50]=e,n[51]=t,n}function Kn(r){let e,t,n,s,i,o,l,u;t=new vs({}),o=new Le({props:{variant:"ghost",size:1,$$slots:{default:[Zl]},$$scope:{ctx:r}}}),o.$on("click",r[27]);let a=r[4]&&es(r);return{c(){e=_("div"),M(t.$$.fragment),n=Z(),s=U(r[17]),i=Z(),M(o.$$.fragment),l=Z(),a&&a.c(),A(e,"class","c-diff-view__error svelte-1vfv2fd")},m(c,d){x(c,e,d),T(t,e,null),L(e,n),L(e,s),L(e,i),T(o,e,null),L(e,l),a&&a.m(e,null),u=!0},p(c,d){(!u||131072&d[0])&&de(s,c[17]);const p={};2097152&d[1]&&(p.$$scope={dirty:d,ctx:c}),o.$set(p),c[4]?a?(a.p(c,d),16&d[0]&&h(a,1)):(a=es(c),a.c(),h(a,1),a.m(e,null)):a&&(W(),g(a,1,1,()=>{a=null}),Q())},i(c){u||(h(t.$$.fragment,c),h(o.$$.fragment,c),h(a),u=!0)},o(c){g(t.$$.fragment,c),g(o.$$.fragment,c),g(a),u=!1},d(c){c&&v(e),S(t),S(o),a&&a.d()}}}function Zl(r){let e;return{c(){e=U("Retry")},m(t,n){x(t,e,n)},d(t){t&&v(e)}}}function es(r){let e,t;return e=new Le({props:{variant:"ghost",size:1,$$slots:{default:[ql]},$$scope:{ctx:r}}}),e.$on("click",function(){Vs(r[4])&&r[4].apply(this,arguments)}),{c(){M(e.$$.fragment)},m(n,s){T(e,n,s),t=!0},p(n,s){r=n;const i={};2097152&s[1]&&(i.$$scope={dirty:s,ctx:r}),e.$set(i)},i(n){t||(h(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){S(e,n)}}}function ql(r){let e;return{c(){e=U("Render as list")},m(t,n){x(t,e,n)},d(t){t&&v(e)}}}function Ul(r){let e,t,n,s,i,o,l,u,a,c,d,p,D,b=r[1]&&r[2]!==r[1]&&ts(r),m=r[2]&&ns(r);o=new fe({props:{size:1,class:"c-diff-view__tree__header__label",$$slots:{default:[Jl]},$$scope:{ctx:r}}}),u=new Vl({props:{changedFiles:r[0]}});const k=[Xl,Yl],y=[];function w($,B){return $[15]&&$[14].length===0?0:$[7]&&$[7].length>0?1:-1}return~(d=w(r))&&(p=y[d]=k[d](r)),{c(){e=_("div"),t=_("div"),n=_("div"),b&&b.c(),s=Z(),m&&m.c(),i=Z(),M(o.$$.fragment),l=Z(),M(u.$$.fragment),a=Z(),c=_("div"),p&&p.c(),A(n,"class","c-diff-view__tree__header svelte-1vfv2fd"),A(t,"class","c-diff-view__tree svelte-1vfv2fd"),A(c,"class","c-diff-view__explanation svelte-1vfv2fd"),A(e,"class","c-diff-view__layout svelte-1vfv2fd")},m($,B){x($,e,B),L(e,t),L(t,n),b&&b.m(n,null),L(n,s),m&&m.m(n,null),L(n,i),T(o,n,null),L(n,l),T(u,n,null),L(e,a),L(e,c),~d&&y[d].m(c,null),D=!0},p($,B){$[1]&&$[2]!==$[1]?b?(b.p($,B),6&B[0]&&h(b,1)):(b=ts($),b.c(),h(b,1),b.m(n,s)):b&&(W(),g(b,1,1,()=>{b=null}),Q()),$[2]?m?(m.p($,B),4&B[0]&&h(m,1)):(m=ns($),m.c(),h(m,1),m.m(n,i)):m&&(W(),g(m,1,1,()=>{m=null}),Q());const f={};2097152&B[1]&&(f.$$scope={dirty:B,ctx:$}),o.$set(f);const C={};1&B[0]&&(C.changedFiles=$[0]),u.$set(C);let E=d;d=w($),d===E?~d&&y[d].p($,B):(p&&(W(),g(y[E],1,1,()=>{y[E]=null}),Q()),~d?(p=y[d],p?p.p($,B):(p=y[d]=k[d]($),p.c()),h(p,1),p.m(c,null)):p=null)},i($){D||(h(b),h(m),h(o.$$.fragment,$),h(u.$$.fragment,$),h(p),D=!0)},o($){g(b),g(m),g(o.$$.fragment,$),g(u.$$.fragment,$),g(p),D=!1},d($){$&&v(e),b&&b.d(),m&&m.d(),S(o),S(u),~d&&y[d].d()}}}function jl(r){let e,t,n;return t=new fe({props:{size:2,color:"secondary",$$slots:{default:[vu]},$$scope:{ctx:r}}}),{c(){e=_("div"),M(t.$$.fragment),A(e,"class","c-diff-view__empty svelte-1vfv2fd")},m(s,i){x(s,e,i),T(t,e,null),n=!0},p(s,i){const o={};2097152&i[1]&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(s){n||(h(t.$$.fragment,s),n=!0)},o(s){g(t.$$.fragment,s),n=!1},d(s){s&&v(e),S(t)}}}function ts(r){let e,t,n,s;return e=new fe({props:{size:1,class:"c-diff-view__tree__header__label",$$slots:{default:[Hl]},$$scope:{ctx:r}}}),n=new fe({props:{size:1,weight:"medium",class:"c-diff-view__tree__header__title",$$slots:{default:[Wl]},$$scope:{ctx:r}}}),{c(){M(e.$$.fragment),t=Z(),M(n.$$.fragment)},m(i,o){T(e,i,o),x(i,t,o),T(n,i,o),s=!0},p(i,o){const l={};2097152&o[1]&&(l.$$scope={dirty:o,ctx:i}),e.$set(l);const u={};2&o[0]|2097152&o[1]&&(u.$$scope={dirty:o,ctx:i}),n.$set(u)},i(i){s||(h(e.$$.fragment,i),h(n.$$.fragment,i),s=!0)},o(i){g(e.$$.fragment,i),g(n.$$.fragment,i),s=!1},d(i){i&&v(t),S(e,i),S(n,i)}}}function Hl(r){let e;return{c(){e=U("Changes from agent")},m(t,n){x(t,e,n)},d(t){t&&v(e)}}}function Wl(r){let e;return{c(){e=U(r[1])},m(t,n){x(t,e,n)},p(t,n){2&n[0]&&de(e,t[1])},d(t){t&&v(e)}}}function ns(r){let e,t,n,s;return e=new fe({props:{size:1,class:"c-diff-view__tree__header__label",$$slots:{default:[Ql]},$$scope:{ctx:r}}}),n=new fe({props:{size:1,weight:"medium",class:"c-diff-view__tree__header__title",$$slots:{default:[Gl]},$$scope:{ctx:r}}}),{c(){M(e.$$.fragment),t=Z(),M(n.$$.fragment)},m(i,o){T(e,i,o),x(i,t,o),T(n,i,o),s=!0},p(i,o){const l={};2097152&o[1]&&(l.$$scope={dirty:o,ctx:i}),e.$set(l);const u={};4&o[0]|2097152&o[1]&&(u.$$scope={dirty:o,ctx:i}),n.$set(u)},i(i){s||(h(e.$$.fragment,i),h(n.$$.fragment,i),s=!0)},o(i){g(e.$$.fragment,i),g(n.$$.fragment,i),s=!1},d(i){i&&v(t),S(e,i),S(n,i)}}}function Ql(r){let e;return{c(){e=U("Last user prompt")},m(t,n){x(t,e,n)},d(t){t&&v(e)}}}function Gl(r){let e;return{c(){e=U(r[2])},m(t,n){x(t,e,n)},p(t,n){4&n[0]&&de(e,t[2])},d(t){t&&v(e)}}}function Jl(r){let e;return{c(){e=U("Changed files")},m(t,n){x(t,e,n)},d(t){t&&v(e)}}}function Yl(r){let e,t,n=ne(r[7]),s=[];for(let o=0;o<n.length;o+=1)s[o]=os(Jn(r,n,o));const i=o=>g(s[o],1,1,()=>{s[o]=null});return{c(){for(let o=0;o<s.length;o+=1)s[o].c();e=Ce()},m(o,l){for(let u=0;u<s.length;u+=1)s[u]&&s[u].m(o,l);x(o,e,l),t=!0},p(o,l){if(124600296&l[0]){let u;for(n=ne(o[7]),u=0;u<n.length;u+=1){const a=Jn(o,n,u);s[u]?(s[u].p(a,l),h(s[u],1)):(s[u]=os(a),s[u].c(),h(s[u],1),s[u].m(e.parentNode,e))}for(W(),u=n.length;u<s.length;u+=1)i(u);Q()}},i(o){if(!t){for(let l=0;l<n.length;l+=1)h(s[l]);t=!0}},o(o){s=s.filter(Nt);for(let l=0;l<s.length;l+=1)g(s[l]);t=!1},d(o){o&&v(e),ve(s,o)}}}function Xl(r){let e,t,n,s,i;return t=new qe({props:{content:r[10]?"Applying changes...":r[11]?"All changes applied":r[21]?"Apply all changes":"No changes to apply",$$slots:{default:[xu]},$$scope:{ctx:r}}}),s=new Cr({props:{count:2}}),{c(){e=_("div"),M(t.$$.fragment),n=Z(),M(s.$$.fragment),A(e,"class","c-diff-view__controls svelte-1vfv2fd")},m(o,l){x(o,e,l),T(t,e,null),x(o,n,l),T(s,o,l),i=!0},p(o,l){const u={};2100224&l[0]&&(u.content=o[10]?"Applying changes...":o[11]?"All changes applied":o[21]?"Apply all changes":"No changes to apply"),2100256&l[0]|2097152&l[1]&&(u.$$scope={dirty:l,ctx:o}),t.$set(u)},i(o){i||(h(t.$$.fragment,o),h(s.$$.fragment,o),i=!0)},o(o){g(t.$$.fragment,o),g(s.$$.fragment,o),i=!1},d(o){o&&(v(e),v(n)),S(t),S(s,o)}}}function Kl(r){let e,t=r[43].title+"";return{c(){e=U(t)},m(n,s){x(n,e,s)},p(n,s){128&s[0]&&t!==(t=n[43].title+"")&&de(e,t)},d(n){n&&v(e)}}}function eu(r){let e;return{c(){e=_("div"),A(e,"class","c-diff-view__skeleton-title svelte-1vfv2fd")},m(t,n){x(t,e,n)},p:G,d(t){t&&v(e)}}}function tu(r){let e,t;return e=new _l({props:{markdown:r[43].description}}),{c(){M(e.$$.fragment)},m(n,s){T(e,n,s),t=!0},p(n,s){const i={};128&s[0]&&(i.markdown=n[43].description),e.$set(i)},i(n){t||(h(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){S(e,n)}}}function nu(r){let e,t,n;return{c(){e=_("div"),t=Z(),n=_("div"),A(e,"class","c-diff-view__skeleton-text svelte-1vfv2fd"),A(n,"class","c-diff-view__skeleton-text svelte-1vfv2fd")},m(s,i){x(s,e,i),x(s,t,i),x(s,n,i)},p:G,i:G,o:G,d(s){s&&(v(e),v(t),v(n))}}}function su(r){let e,t,n;return e=new ni({}),{c(){M(e.$$.fragment),t=U(`
                        Expand All`)},m(s,i){T(e,s,i),x(s,t,i),n=!0},i(s){n||(h(e.$$.fragment,s),n=!0)},o(s){g(e.$$.fragment,s),n=!1},d(s){s&&v(t),S(e,s)}}}function iu(r){let e,t,n;return e=new Ys({}),{c(){M(e.$$.fragment),t=U(`
                        Collapse All`)},m(s,i){T(e,s,i),x(s,t,i),n=!0},i(s){n||(h(e.$$.fragment,s),n=!0)},o(s){g(e.$$.fragment,s),n=!1},d(s){s&&v(t),S(e,s)}}}function ru(r){let e,t,n,s;const i=[iu,su],o=[];function l(u,a){return u[22]?1:0}return e=l(r),t=o[e]=i[e](r),{c(){t.c(),n=Ce()},m(u,a){o[e].m(u,a),x(u,n,a),s=!0},p(u,a){let c=e;e=l(u),e!==c&&(W(),g(o[c],1,1,()=>{o[c]=null}),Q(),t=o[e],t||(t=o[e]=i[e](u),t.c()),h(t,1),t.m(n.parentNode,n))},i(u){s||(h(t),s=!0)},o(u){g(t),s=!1},d(u){u&&v(n),o[e].d(u)}}}function ou(r){let e,t,n,s;return n=new et({}),{c(){e=U(`Apply all
                          `),t=_("div"),M(n.$$.fragment),A(t,"class","c-diff-view__controls__icon svelte-1vfv2fd")},m(i,o){x(i,e,o),x(i,t,o),T(n,t,null),s=!0},i(i){s||(h(n.$$.fragment,i),s=!0)},o(i){g(n.$$.fragment,i),s=!1},d(i){i&&(v(e),v(t)),S(n)}}}function lu(r){let e,t,n;return t=new fe({props:{size:2,$$slots:{default:[au]},$$scope:{ctx:r}}}),{c(){e=_("div"),M(t.$$.fragment),A(e,"class","c-diff-view__applied svelte-1vfv2fd")},m(s,i){x(s,e,i),T(t,e,null),n=!0},i(s){n||(h(t.$$.fragment,s),n=!0)},o(s){g(t.$$.fragment,s),n=!1},d(s){s&&v(e),S(t)}}}function uu(r){let e,t,n,s,i;return t=new bt({props:{size:1,useCurrentColor:!0}}),s=new fe({props:{size:2,$$slots:{default:[cu]},$$scope:{ctx:r}}}),{c(){e=_("div"),M(t.$$.fragment),n=Z(),M(s.$$.fragment),A(e,"class","c-diff-view__applying svelte-1vfv2fd")},m(o,l){x(o,e,l),T(t,e,null),L(e,n),T(s,e,null),i=!0},i(o){i||(h(t.$$.fragment,o),h(s.$$.fragment,o),i=!0)},o(o){g(t.$$.fragment,o),g(s.$$.fragment,o),i=!1},d(o){o&&v(e),S(t),S(s)}}}function au(r){let e,t,n;return t=new gt({props:{iconName:"check"}}),{c(){e=U(`Applied
                              `),M(t.$$.fragment)},m(s,i){x(s,e,i),T(t,s,i),n=!0},p:G,i(s){n||(h(t.$$.fragment,s),n=!0)},o(s){g(t.$$.fragment,s),n=!1},d(s){s&&v(e),S(t,s)}}}function cu(r){let e;return{c(){e=U("Applying...")},m(t,n){x(t,e,n)},d(t){t&&v(e)}}}function du(r){let e,t,n,s;const i=[uu,lu,ou],o=[];function l(u,a){return u[10]?0:u[11]?1:2}return e=l(r),t=o[e]=i[e](r),{c(){t.c(),n=Ce()},m(u,a){o[e].m(u,a),x(u,n,a),s=!0},p(u,a){let c=e;e=l(u),e!==c&&(W(),g(o[c],1,1,()=>{o[c]=null}),Q(),t=o[e],t||(t=o[e]=i[e](u),t.c()),h(t,1),t.m(n.parentNode,n))},i(u){s||(h(t),s=!0)},o(u){g(t),s=!1},d(u){u&&v(n),o[e].d(u)}}}function pu(r){let e,t;return e=new Le({props:{variant:"ghost-block",color:"neutral",size:2,disabled:r[10]||r[11]||r[5].length>0||!r[21],$$slots:{default:[du]},$$scope:{ctx:r}}}),e.$on("click",r[26]),{c(){M(e.$$.fragment)},m(n,s){T(e,n,s),t=!0},p(n,s){const i={};2100256&s[0]&&(i.disabled=n[10]||n[11]||n[5].length>0||!n[21]),3072&s[0]|2097152&s[1]&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(h(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){S(e,n)}}}function fu(r){let e,t=r[46].title+"";return{c(){e=U(t)},m(n,s){x(n,e,s)},p(n,s){128&s[0]&&t!==(t=n[46].title+"")&&de(e,t)},d(n){n&&v(e)}}}function hu(r){let e;return{c(){e=_("div"),A(e,"class","c-diff-view__skeleton-text svelte-1vfv2fd")},m(t,n){x(t,e,n)},p:G,d(t){t&&v(e)}}}function ss(r){let e,t,n,s,i,o=r[46].warning+"";return t=new vs({}),{c(){e=_("div"),M(t.$$.fragment),n=Z(),s=U(o),A(e,"class","c-diff-view__warning svelte-1vfv2fd")},m(l,u){x(l,e,u),T(t,e,null),L(e,n),L(e,s),i=!0},p(l,u){(!i||128&u[0])&&o!==(o=l[46].warning+"")&&de(s,o)},i(l){i||(h(t.$$.fragment,l),i=!0)},o(l){g(t.$$.fragment,l),i=!1},d(l){l&&v(e),S(t)}}}function is(r){let e,t,n,s,i,o,l=r[45],u=r[48],a=r[49];function c(...y){return r[35](r[49],...y)}function d(){return r[36](r[49])}function p(y){r[37](y,r[49])}const D=()=>r[38](t,l,u,a),b=()=>r[38](null,l,u,a);function m(y){r[39](y)}let k={path:r[49].path,change:r[49],descriptions:r[46].descriptions,isExpandedDefault:r[9][r[49].path]!==void 0?!r[9][r[49].path]:r[8],isApplying:r[5].includes(r[49].path),hasApplied:r[6].includes(r[49].path),onCodeChange:c,onApplyChanges:d};return r[9][r[49].path]!==void 0&&(k.isCollapsed=r[9][r[49].path]),r[19]!==void 0&&(k.areDescriptionsVisible=r[19]),t=new $r({props:k}),Ae.push(()=>Oe(t,"isCollapsed",p)),D(),Ae.push(()=>Oe(t,"areDescriptionsVisible",m)),{c(){e=_("div"),M(t.$$.fragment),A(e,"class","c-diff-view__changes-item svelte-1vfv2fd"),A(e,"id",i=`file-diff-${r[49].path.replace(/[/.]/g,"-")}`),$e(e,"focused",r[13]===r[49].path)},m(y,w){x(y,e,w),T(t,e,null),o=!0},p(y,w){l===(r=y)[45]&&u===r[48]&&a===r[49]||(b(),l=r[45],u=r[48],a=r[49],D());const $={};128&w[0]&&($.path=r[49].path),128&w[0]&&($.change=r[49]),128&w[0]&&($.descriptions=r[46].descriptions),896&w[0]&&($.isExpandedDefault=r[9][r[49].path]!==void 0?!r[9][r[49].path]:r[8]),160&w[0]&&($.isApplying=r[5].includes(r[49].path)),192&w[0]&&($.hasApplied=r[6].includes(r[49].path)),128&w[0]&&($.onCodeChange=c),4232&w[0]&&($.onApplyChanges=d),!n&&640&w[0]&&(n=!0,$.isCollapsed=r[9][r[49].path],Ve(()=>n=!1)),!s&&524288&w[0]&&(s=!0,$.areDescriptionsVisible=r[19],Ve(()=>s=!1)),t.$set($),(!o||128&w[0]&&i!==(i=`file-diff-${r[49].path.replace(/[/.]/g,"-")}`))&&A(e,"id",i),(!o||8320&w[0])&&$e(e,"focused",r[13]===r[49].path)},i(y){o||(h(t.$$.fragment,y),o=!0)},o(y){g(t.$$.fragment,y),o=!1},d(y){y&&v(e),b(),S(t)}}}function rs(r){let e,t,n,s,i,o,l,u,a,c,d;function p($,B){return $[16]&&$[46].descriptions.length===0?hu:fu}i=new Qi({props:{type:r[46].type}});let D=p(r),b=D(r),m=!r[16]&&r[46].warning&&ss(r),k=ne(r[46].changes),y=[];for(let $=0;$<k.length;$+=1)y[$]=is(Xn(r,k,$));const w=$=>g(y[$],1,1,()=>{y[$]=null});return{c(){e=_("div"),t=_("div"),n=_("div"),s=_("div"),M(i.$$.fragment),o=Z(),l=_("h5"),b.c(),u=Z(),m&&m.c(),a=Z(),c=_("div");for(let $=0;$<y.length;$+=1)y[$].c();A(s,"class","c-diff-view__icon svelte-1vfv2fd"),A(l,"class","c-diff-view__title svelte-1vfv2fd"),A(n,"class","c-diff-view__content svelte-1vfv2fd"),A(t,"class","c-diff-view__header svelte-1vfv2fd"),A(c,"class","c-diff-view__changes svelte-1vfv2fd"),A(e,"class","c-diff-view__subsection svelte-1vfv2fd"),A(e,"id",`subsection-${r[45]}-${r[48]}`)},m($,B){x($,e,B),L(e,t),L(t,n),L(n,s),T(i,s,null),L(n,o),L(n,l),b.m(l,null),L(t,u),m&&m.m(t,null),L(e,a),L(e,c);for(let f=0;f<y.length;f+=1)y[f]&&y[f].m(c,null);d=!0},p($,B){const f={};if(128&B[0]&&(f.type=$[46].type),i.$set(f),D===(D=p($))&&b?b.p($,B):(b.d(1),b=D($),b&&(b.c(),b.m(l,null))),!$[16]&&$[46].warning?m?(m.p($,B),65664&B[0]&&h(m,1)):(m=ss($),m.c(),h(m,1),m.m(t,null)):m&&(W(),g(m,1,1,()=>{m=null}),Q()),34354152&B[0]){let C;for(k=ne($[46].changes),C=0;C<k.length;C+=1){const E=Xn($,k,C);y[C]?(y[C].p(E,B),h(y[C],1)):(y[C]=is(E),y[C].c(),h(y[C],1),y[C].m(c,null))}for(W(),C=k.length;C<y.length;C+=1)w(C);Q()}},i($){if(!d){h(i.$$.fragment,$),h(m);for(let B=0;B<k.length;B+=1)h(y[B]);d=!0}},o($){g(i.$$.fragment,$),g(m),y=y.filter(Nt);for(let B=0;B<y.length;B+=1)g(y[B]);d=!1},d($){$&&v(e),S(i),b.d(),m&&m.d(),ve(y,$)}}}function os(r){let e,t,n,s,i,o,l,u,a,c,d,p;function D(E,V){return E[16]&&E[43].title==="Loading..."?eu:Kl}let b=D(r),m=b(r);const k=[nu,tu],y=[];function w(E,V){return E[16]&&E[43].description===""?0:1}l=w(r),u=y[l]=k[l](r);let $=r[45]===0&&function(E){let V,j,K,te,pe;return j=new Le({props:{variant:"ghost-block",color:"neutral",size:2,$$slots:{default:[ru]},$$scope:{ctx:E}}}),j.$on("click",E[24]),te=new qe({props:{content:E[10]?"Applying changes...":E[11]?"All changes applied":E[21]?"Apply all changes":"No changes to apply",$$slots:{default:[pu]},$$scope:{ctx:E}}}),{c(){V=_("div"),M(j.$$.fragment),K=Z(),M(te.$$.fragment),A(V,"class","c-diff-view__controls svelte-1vfv2fd")},m(q,oe){x(q,V,oe),T(j,V,null),L(V,K),T(te,V,null),pe=!0},p(q,oe){const ge={};4194304&oe[0]|2097152&oe[1]&&(ge.$$scope={dirty:oe,ctx:q}),j.$set(ge);const ee={};2100224&oe[0]&&(ee.content=q[10]?"Applying changes...":q[11]?"All changes applied":q[21]?"Apply all changes":"No changes to apply"),2100256&oe[0]|2097152&oe[1]&&(ee.$$scope={dirty:oe,ctx:q}),te.$set(ee)},i(q){pe||(h(j.$$.fragment,q),h(te.$$.fragment,q),pe=!0)},o(q){g(j.$$.fragment,q),g(te.$$.fragment,q),pe=!1},d(q){q&&v(V),S(j),S(te)}}}(r),B=ne(r[43].sections||[]),f=[];for(let E=0;E<B.length;E+=1)f[E]=rs(Yn(r,B,E));const C=E=>g(f[E],1,1,()=>{f[E]=null});return{c(){e=_("div"),t=_("div"),n=_("div"),s=_("h5"),m.c(),i=Z(),o=_("div"),u.c(),a=Z(),$&&$.c(),c=Z();for(let E=0;E<f.length;E+=1)f[E].c();d=Z(),A(s,"class","c-diff-view__title svelte-1vfv2fd"),A(o,"class","c-diff-view__description svelte-1vfv2fd"),A(n,"class","c-diff-view__content svelte-1vfv2fd"),A(t,"class","c-diff-view__header svelte-1vfv2fd"),A(e,"class","c-diff-view__section svelte-1vfv2fd"),A(e,"id",`section-${r[45]}`)},m(E,V){x(E,e,V),L(e,t),L(t,n),L(n,s),m.m(s,null),L(n,i),L(n,o),y[l].m(o,null),L(t,a),$&&$.m(t,null),L(e,c);for(let j=0;j<f.length;j+=1)f[j]&&f[j].m(e,null);L(e,d),p=!0},p(E,V){b===(b=D(E))&&m?m.p(E,V):(m.d(1),m=b(E),m&&(m.c(),m.m(s,null)));let j=l;if(l=w(E),l===j?y[l].p(E,V):(W(),g(y[j],1,1,()=>{y[j]=null}),Q(),u=y[l],u?u.p(E,V):(u=y[l]=k[l](E),u.c()),h(u,1),u.m(o,null)),E[45]===0&&$.p(E,V),34419688&V[0]){let K;for(B=ne(E[43].sections||[]),K=0;K<B.length;K+=1){const te=Yn(E,B,K);f[K]?(f[K].p(te,V),h(f[K],1)):(f[K]=rs(te),f[K].c(),h(f[K],1),f[K].m(e,d))}for(W(),K=B.length;K<f.length;K+=1)C(K);Q()}},i(E){if(!p){h(u),h($);for(let V=0;V<B.length;V+=1)h(f[V]);p=!0}},o(E){g(u),g($),f=f.filter(Nt);for(let V=0;V<f.length;V+=1)g(f[V]);p=!1},d(E){E&&v(e),m.d(),y[l].d(),$&&$.d(),ve(f,E)}}}function gu(r){let e,t,n,s;return n=new et({}),{c(){e=U(`Apply all
                  `),t=_("div"),M(n.$$.fragment),A(t,"class","c-diff-view__controls__icon svelte-1vfv2fd")},m(i,o){x(i,e,o),x(i,t,o),T(n,t,null),s=!0},i(i){s||(h(n.$$.fragment,i),s=!0)},o(i){g(n.$$.fragment,i),s=!1},d(i){i&&(v(e),v(t)),S(n)}}}function $u(r){let e,t,n;return t=new fe({props:{size:2,$$slots:{default:[Du]},$$scope:{ctx:r}}}),{c(){e=_("div"),M(t.$$.fragment),A(e,"class","c-diff-view__applied svelte-1vfv2fd")},m(s,i){x(s,e,i),T(t,e,null),n=!0},i(s){n||(h(t.$$.fragment,s),n=!0)},o(s){g(t.$$.fragment,s),n=!1},d(s){s&&v(e),S(t)}}}function mu(r){let e,t,n,s,i;return t=new bt({props:{size:1,useCurrentColor:!0}}),s=new fe({props:{size:2,$$slots:{default:[Fu]},$$scope:{ctx:r}}}),{c(){e=_("div"),M(t.$$.fragment),n=Z(),M(s.$$.fragment),A(e,"class","c-diff-view__applying svelte-1vfv2fd")},m(o,l){x(o,e,l),T(t,e,null),L(e,n),T(s,e,null),i=!0},i(o){i||(h(t.$$.fragment,o),h(s.$$.fragment,o),i=!0)},o(o){g(t.$$.fragment,o),g(s.$$.fragment,o),i=!1},d(o){o&&v(e),S(t),S(s)}}}function Du(r){let e,t,n;return t=new gt({props:{iconName:"check"}}),{c(){e=U(`Applied
                      `),M(t.$$.fragment)},m(s,i){x(s,e,i),T(t,s,i),n=!0},p:G,i(s){n||(h(t.$$.fragment,s),n=!0)},o(s){g(t.$$.fragment,s),n=!1},d(s){s&&v(e),S(t,s)}}}function Fu(r){let e;return{c(){e=U("Applying...")},m(t,n){x(t,e,n)},d(t){t&&v(e)}}}function ku(r){let e,t,n,s;const i=[mu,$u,gu],o=[];function l(u,a){return u[10]?0:u[11]?1:2}return e=l(r),t=o[e]=i[e](r),{c(){t.c(),n=Ce()},m(u,a){o[e].m(u,a),x(u,n,a),s=!0},p(u,a){let c=e;e=l(u),e!==c&&(W(),g(o[c],1,1,()=>{o[c]=null}),Q(),t=o[e],t||(t=o[e]=i[e](u),t.c()),h(t,1),t.m(n.parentNode,n))},i(u){s||(h(t),s=!0)},o(u){g(t),s=!1},d(u){u&&v(n),o[e].d(u)}}}function xu(r){let e,t;return e=new Le({props:{variant:"ghost-block",color:"neutral",size:2,disabled:r[10]||r[11]||r[5].length>0||!r[21],$$slots:{default:[ku]},$$scope:{ctx:r}}}),e.$on("click",r[26]),{c(){M(e.$$.fragment)},m(n,s){T(e,n,s),t=!0},p(n,s){const i={};2100256&s[0]&&(i.disabled=n[10]||n[11]||n[5].length>0||!n[21]),3072&s[0]|2097152&s[1]&&(i.$$scope={dirty:s,ctx:n}),e.$set(i)},i(n){t||(h(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){S(e,n)}}}function vu(r){let e;return{c(){e=U("No files changed")},m(t,n){x(t,e,n)},d(t){t&&v(e)}}}function Cu(r){let e,t,n,s,i,o=r[17]&&Kn(r);const l=[jl,Ul],u=[];function a(c,d){return c[20]?0:1}return n=a(r),s=u[n]=l[n](r),{c(){e=_("div"),o&&o.c(),t=Z(),s.c(),A(e,"class","c-diff-view svelte-1vfv2fd")},m(c,d){x(c,e,d),o&&o.m(e,null),L(e,t),u[n].m(e,null),i=!0},p(c,d){c[17]?o?(o.p(c,d),131072&d[0]&&h(o,1)):(o=Kn(c),o.c(),h(o,1),o.m(e,t)):o&&(W(),g(o,1,1,()=>{o=null}),Q());let p=n;n=a(c),n===p?u[n].p(c,d):(W(),g(u[p],1,1,()=>{u[p]=null}),Q(),s=u[n],s?s.p(c,d):(s=u[n]=l[n](c),s.c()),h(s,1),s.m(e,null))},i(c){i||(h(o),h(s),i=!0)},o(c){g(o),g(s),i=!1},d(c){c&&v(e),o&&o.d(),u[n].d()}}}function He(r){let e=0;const t=1e4,n=r.length>t?r.substring(0,5e3)+r.substring(r.length-5e3):r;for(let s=0;s<n.length;s++)e=(e<<5)-e+n.charCodeAt(s),e|=0;return Math.abs(e).toString(36)}function wu(r,e,t){let n,s,i,o,l,u,a,c,{changedFiles:d}=e,{agentLabel:p}=e,{latestUserPrompt:D}=e,{onApplyChanges:b}=e,{onRenderBackup:m}=e,{pendingFiles:k=[]}=e,{appliedFiles:y=[]}=e,{preloadedExplanation:w}=e,$="",B=!1,f=[],C=[],E=!1,V=!1,j=null,K=!0,te={},pe=[],q=!1,oe=!1,ge=!0,ee={};const Ee=function(R=null){const I=Ie(R);return xt(Ms,I),I}(null);function Te(R,I){t(12,ee[R]=I,ee)}function we(R){const I={title:"Changed Files",description:`${R.length} files were changed`,sections:[]},re=[],F=[],z=[];return R.forEach(N=>{N.old_path?N.new_path?F.push(N):z.push(N):re.push(N)}),re.length>0&&I.sections.push(_e("Added files","feature",re)),F.length>0&&I.sections.push(_e("Modified files","fix",F)),z.length>0&&I.sections.push(_e("Deleted files","chore",z)),[I]}function _e(R,I,re){const F=[];return re.forEach(z=>{const N=z.new_path||z.old_path,P=z.old_contents||"",O=z.new_contents||"",H=z.old_path?z.old_path:"",ce=ut(H,z.new_path||"/dev/null",P,O,"","",{context:3}),ie=`${He(N)}-${He(P+O)}`;F.push({id:ie,path:N,diff:ce,originalCode:P,modifiedCode:O})}),{title:R,descriptions:[],type:I,changes:F}}async function Se(){if(!B)return;if(t(15,E=!0),t(16,V=!1),t(17,j=null),t(14,C=[]),t(7,f=[]),u)return void t(15,E=!1);let R=0;if(d.forEach(I=>{var re,F;R+=(((re=I.old_contents)==null?void 0:re.length)||0)+(((F=I.new_contents)==null?void 0:F.length)||0)}),d.length>12||R>512e3){try{t(7,f=we(d))}catch(I){console.error("Failed to create simple explanation:",I),t(17,j="Failed to create explanation for large changes.")}t(15,E=!1)}else try{const I=new ei(z=>$s.postMessage(z)),re=new Map,F=d.map(z=>{const N=z.new_path||z.old_path,P=z.old_contents||"",O=z.new_contents||"",H=`${He(N)}-${He(P+O)}`;return re.set(H,{old_path:z.old_path,new_path:z.new_path,old_contents:P,new_contents:O,change_type:z.change_type}),{id:H,old_path:z.old_path,new_path:z.new_path,change_type:z.change_type}});try{const z=F.length===1;let N=[];z?N=F.map(P=>({path:P.new_path||P.old_path,changes:[{id:P.id,path:P.new_path||P.old_path,diff:`File: ${P.new_path||P.old_path}
Change type: ${P.change_type||"modified"}`,originalCode:"",modifiedCode:""}]})):N=(await I.send({type:"get-diff-group-changes-request",data:{changedFiles:F,changesById:!0,apikey:$}},3e4)).data.groupedChanges,t(14,C=N.map(P=>({path:P.path,changes:P.changes.map(O=>{if(O.id&&re.has(O.id)){const H=re.get(O.id);let ce=O.diff;return ce&&!ce.startsWith("File:")||(ce=ut(H.old_path||"",H.new_path||"",H.old_contents||"",H.new_contents||"")),{...O,diff:ce,old_path:H.old_path,new_path:H.new_path,old_contents:H.old_contents,new_contents:H.new_contents,change_type:H.change_type,originalCode:H.old_contents||"",modifiedCode:H.new_contents||""}}return O})})))}catch(z){console.error("Failed to group changes with LLM, falling back to simple grouping:",z);try{const N=F.map(P=>{if(P.id&&re.has(P.id)){const O=re.get(P.id);return{...P,old_path:O.old_path,new_path:O.new_path,old_contents:O.old_contents||"",new_contents:O.new_contents||"",change_type:O.change_type}}return P});t(7,f=we(N)),t(14,C=f[0].sections.map(P=>({path:P.title,changes:P.changes}))),t(16,V=!1)}catch(N){console.error("Failed to create simple explanation:",N),t(17,j="Failed to group changes. Please try again.")}}if(t(15,E=!1),!C||C.length===0)throw new Error("Failed to group changes");if(!f||f.length===0){t(7,f=function(N){const P={title:"Loading...",description:"",sections:[]};return N.forEach(O=>{const H=O.changes.map(ie=>{if(ie.id)return ie;const he=He(ie.path),Re=He(ie.originalCode+ie.modifiedCode);return{...ie,id:`${he}-${Re}`}}),ce={title:O.path,descriptions:[],type:"other",changes:H};P.sections.push(ce)}),[P]}(C));const z=f[0].sections.map(N=>({path:N.title,changes:N.changes.map(P=>({id:P.id,path:P.path,diff:P.diff,originalCode:P.originalCode,modifiedCode:P.modifiedCode}))}));t(16,V=!0);try{const N=await I.send({type:"get-diff-descriptions-request",data:{groupedChanges:z,apikey:$}},1e5);t(7,f=N.data.explanation)}catch(N){console.error("Failed to get descriptions, using skeleton explanation:",N)}}f.length===0&&t(17,j="Failed to generate explanation.")}catch(I){console.error("Failed to get explanation:",I),t(17,j=I instanceof Error?I.message:"An error occurred while generating the explanation.")}finally{t(15,E=!1),t(16,V=!1)}}be(r,Ee,R=>t(13,c=R)),Xe(()=>{const R=localStorage.getItem("anthropic_apikey");R&&($=R),t(29,B=!0)});let ae="";return r.$$set=R=>{"changedFiles"in R&&t(0,d=R.changedFiles),"agentLabel"in R&&t(1,p=R.agentLabel),"latestUserPrompt"in R&&t(2,D=R.latestUserPrompt),"onApplyChanges"in R&&t(3,b=R.onApplyChanges),"onRenderBackup"in R&&t(4,m=R.onRenderBackup),"pendingFiles"in R&&t(5,k=R.pendingFiles),"appliedFiles"in R&&t(6,y=R.appliedFiles),"preloadedExplanation"in R&&t(28,w=R.preloadedExplanation)},r.$$.update=()=>{if(1&r.$$.dirty[0]&&t(33,a=JSON.stringify(d)),1879048192&r.$$.dirty[0]|4&r.$$.dirty[1]&&B&&a&&a!==ae&&(t(30,ae=a),w&&w.length>0?(t(7,f=w),t(15,E=!1),t(16,V=!1)):Se(),t(10,q=!1),t(11,oe=!1),t(12,ee={})),896&r.$$.dirty[0]&&f&&f.length>0){const R=je(f);Array.from(R).forEach(F=>{te[F]===void 0&&t(9,te[F]=!K,te)});const I=Object.keys(te).filter(F=>te[F]),re=Array.from(R);re.length>0&&t(8,K=!re.some(F=>I.includes(F)))}if(512&r.$$.dirty[0]&&t(22,n=Object.values(te).some(Boolean)),4224&r.$$.dirty[0]&&f&&f.length>0&&f.flatMap(R=>R.sections||[]).flatMap(R=>R.changes).forEach(R=>{ee[R.path]||t(12,ee[R.path]=R.modifiedCode,ee)}),128&r.$$.dirty[0]&&t(32,s=JSON.stringify(f)),64&r.$$.dirty[0]&&t(31,i=JSON.stringify(y)),32&r.$$.dirty[0]&&t(34,o=JSON.stringify(k)),224&r.$$.dirty[0]|11&r.$$.dirty[1]&&t(21,l=(()=>{if(s&&i&&o){const R=je(f);return R.size!==0&&Array.from(R).some(I=>!y.includes(I)&&!k.includes(I))}return!1})()),1&r.$$.dirty[0]&&t(20,u=d.length===0),8192&r.$$.dirty[0]&&c){const R=document.getElementById(`file-diff-${c.replace(/[/.]/g,"-")}`);R&&R.scrollIntoView({behavior:"smooth",block:"center"})}if(2240&r.$$.dirty[0]|2&r.$$.dirty[1]&&s&&oe){const R=je(f);Array.from(R).every(I=>y.includes(I))||t(11,oe=!1)}if(1248&r.$$.dirty[0]&&q){const R=je(f);f.flatMap(I=>I.sections||[]).flatMap(I=>I.changes).filter(I=>!y.includes(I.path)&&!k.includes(I.path)).length===0&&Array.from(R).every(I=>y.includes(I)||k.includes(I))&&k.length===0&&y.length>0&&(t(10,q=!1),t(11,oe=!0))}if(3264&r.$$.dirty[0]|1&r.$$.dirty[1]&&f&&f.length>0&&!q&&i){const R=je(f);if(R.size>0){const I=Array.from(R).every(re=>y.includes(re));I&&y.length>0?t(11,oe=!0):!I&&oe&&t(11,oe=!1)}}},[d,p,D,b,m,k,y,f,K,te,q,oe,ee,c,C,E,V,j,pe,ge,u,l,n,Ee,function(){const R=je(f),I=Object.values(te).some(Boolean);t(8,K=I),Array.from(R).forEach(re=>{t(9,te[re]=!K,te)})},Te,function(){if(!b)return;const{filesToApply:R,areAllPathsApplied:I}=Zs(f,d,ee,y,k);I||R.length===0?t(11,oe=I):(t(10,q=!0),t(11,oe=!1),qs(R,b))},Se,w,B,ae,i,s,a,o,(R,I)=>{Te(R.path,I)},R=>{const I=ee[R.path]||R.modifiedCode;b==null||b(R.path,R.originalCode,I)},function(R,I){r.$$.not_equal(te[I.path],R)&&(te[I.path]=R,t(9,te),t(7,f),t(8,K),t(29,B),t(33,a),t(30,ae),t(28,w),t(0,d))},function(R,I,re,F){Ae[R?"unshift":"push"](()=>{pe[100*I+10*re+F.path.length%10]=R,t(18,pe)})},function(R){ge=R,t(19,ge)}]}class Au extends Y{constructor(e){super(),X(this,e,wu,Cu,J,{changedFiles:0,agentLabel:1,latestUserPrompt:2,onApplyChanges:3,onRenderBackup:4,pendingFiles:5,appliedFiles:6,preloadedExplanation:28},null,[-1,-1])}}function ls(r){let e,t,n,s;const i=[bu,yu],o=[];function l(u,a){return u[5]==="changedFiles"?0:1}return t=l(r),n=o[t]=i[t](r),{c(){e=_("div"),n.c(),A(e,"class","file-explorer-contents svelte-5tfpo4")},m(u,a){x(u,e,a),o[t].m(e,null),s=!0},p(u,a){let c=t;t=l(u),t===c?o[t].p(u,a):(W(),g(o[c],1,1,()=>{o[c]=null}),Q(),n=o[t],n?n.p(u,a):(n=o[t]=i[t](u),n.c()),h(n,1),n.m(e,null))},i(u){s||(h(n),s=!0)},o(u){g(n),s=!1},d(u){u&&v(e),o[t].d()}}}function yu(r){var n,s;let e,t;return e=new Au({props:{changedFiles:r[0],onApplyChanges:r[8],pendingFiles:r[1],appliedFiles:r[2],agentLabel:r[3],latestUserPrompt:r[4],onRenderBackup:r[9],preloadedExplanation:(s=(n=r[6])==null?void 0:n.opts)==null?void 0:s.preloadedExplanation}}),{c(){M(e.$$.fragment)},m(i,o){T(e,i,o),t=!0},p(i,o){var u,a;const l={};1&o&&(l.changedFiles=i[0]),2&o&&(l.pendingFiles=i[1]),4&o&&(l.appliedFiles=i[2]),8&o&&(l.agentLabel=i[3]),16&o&&(l.latestUserPrompt=i[4]),32&o&&(l.onRenderBackup=i[9]),64&o&&(l.preloadedExplanation=(a=(u=i[6])==null?void 0:u.opts)==null?void 0:a.preloadedExplanation),e.$set(l)},i(i){t||(h(e.$$.fragment,i),t=!0)},o(i){g(e.$$.fragment,i),t=!1},d(i){S(e,i)}}}function bu(r){let e,t;return e=new Ui({props:{changedFiles:r[0],onApplyChanges:r[8],pendingFiles:r[1],appliedFiles:r[2]}}),{c(){M(e.$$.fragment)},m(n,s){T(e,n,s),t=!0},p(n,s){const i={};1&s&&(i.changedFiles=n[0]),2&s&&(i.pendingFiles=n[1]),4&s&&(i.appliedFiles=n[2]),e.$set(i)},i(n){t||(h(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){S(e,n)}}}function Eu(r){let e,t,n,s=r[0]&&ls(r);return{c(){e=_("div"),t=_("div"),s&&s.c(),A(t,"class","file-explorer-main svelte-5tfpo4"),A(e,"class","diff-page svelte-5tfpo4")},m(i,o){x(i,e,o),L(e,t),s&&s.m(t,null),n=!0},p(i,[o]){i[0]?s?(s.p(i,o),1&o&&h(s,1)):(s=ls(i),s.c(),h(s,1),s.m(t,null)):s&&(W(),g(s,1,1,()=>{s=null}),Q())},i(i){n||(h(s),n=!0)},o(i){g(s),n=!1},d(i){i&&v(e),s&&s.d()}}}function _u(r,e,t){let n,{changedFiles:s=[]}=e,{pendingFiles:i=[]}=e,{appliedFiles:o=[]}=e,{agentLabel:l}=e,{latestUserPrompt:u}=e;const a=vt(dt.key),c=vt(ct.key);be(r,c,p=>t(6,n=p));let d="summary";return r.$$set=p=>{"changedFiles"in p&&t(0,s=p.changedFiles),"pendingFiles"in p&&t(1,i=p.pendingFiles),"appliedFiles"in p&&t(2,o=p.appliedFiles),"agentLabel"in p&&t(3,l=p.agentLabel),"latestUserPrompt"in p&&t(4,u=p.latestUserPrompt)},[s,i,o,l,u,d,n,c,(p,D,b)=>{a.applyChanges(p,D,b)},()=>{t(5,d="changedFiles")}]}class Bu extends Y{constructor(e){super(),X(this,e,_u,Eu,J,{changedFiles:0,pendingFiles:1,appliedFiles:2,agentLabel:3,latestUserPrompt:4})}}function zu(r){let e,t,n,s,i;return t=new bt({props:{size:1}}),{c(){e=_("div"),M(t.$$.fragment),n=Z(),s=_("p"),s.textContent="Loading diff view...",A(e,"class","l-center svelte-ccste2")},m(o,l){x(o,e,l),T(t,e,null),L(e,n),L(e,s),i=!0},p:G,i(o){i||(h(t.$$.fragment,o),i=!0)},o(o){g(t.$$.fragment,o),i=!1},d(o){o&&v(e),S(t)}}}function Lu(r){let e,t;return e=new Bu({props:{changedFiles:r[0].changedFiles,agentLabel:r[0].sessionSummary,latestUserPrompt:r[0].userPrompt,pendingFiles:r[3].applyingFilePaths||[],appliedFiles:r[3].appliedFilePaths||[]}}),{c(){M(e.$$.fragment)},m(n,s){T(e,n,s),t=!0},p(n,s){const i={};1&s&&(i.changedFiles=n[0].changedFiles),1&s&&(i.agentLabel=n[0].sessionSummary),1&s&&(i.latestUserPrompt=n[0].userPrompt),e.$set(i)},i(n){t||(h(e.$$.fragment,n),t=!0)},o(n){g(e.$$.fragment,n),t=!1},d(n){S(e,n)}}}function Mu(r){let e,t,n,s;const i=[Lu,zu],o=[];function l(u,a){return u[0]?0:1}return t=l(r),n=o[t]=i[t](r),{c(){e=_("div"),n.c(),A(e,"class","l-main svelte-ccste2")},m(u,a){x(u,e,a),o[t].m(e,null),s=!0},p(u,a){let c=t;t=l(u),t===c?o[t].p(u,a):(W(),g(o[c],1,1,()=>{o[c]=null}),Q(),n=o[t],n?n.p(u,a):(n=o[t]=i[t](u),n.c()),h(n,1),n.m(e,null))},i(u){s||(h(n),s=!0)},o(u){g(n),s=!1},d(u){u&&v(e),o[t].d()}}}function Tu(r){let e,t,n,s;return e=new Ks.Root({props:{$$slots:{default:[Mu]},$$scope:{ctx:r}}}),{c(){M(e.$$.fragment)},m(i,o){T(e,i,o),t=!0,n||(s=Ye(window,"message",r[1].onMessageFromExtension),n=!0)},p(i,[o]){const l={};33&o&&(l.$$scope={dirty:o,ctx:i}),e.$set(l)},i(i){t||(h(e.$$.fragment,i),t=!0)},o(i){g(e.$$.fragment,i),t=!1},d(i){S(e,i),n=!1,s()}}}function Su(r,e,t){let n,s,i=new Us($s),o=new ct(i);be(r,o,u=>t(4,s=u)),i.registerConsumer(o);let l=new dt(i);return i.registerConsumer(l),xt(dt.key,l),xt(ct.key,o),Xe(()=>(o.onPanelLoaded(),()=>{i.dispose()})),r.$$.update=()=>{16&r.$$.dirty&&t(0,n=s.opts)},[n,i,o,l,s]}new class extends Y{constructor(r){super(),X(this,r,Su,Tu,J,{})}}({target:document.getElementById("app")});
