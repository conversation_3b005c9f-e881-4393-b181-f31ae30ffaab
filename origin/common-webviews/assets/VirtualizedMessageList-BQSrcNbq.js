var Ut=Object.defineProperty;var Jt=(o,t,n)=>t in o?Ut(o,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):o[t]=n;var w=(o,t,n)=>Jt(o,typeof t!="symbol"?t+"":t,n);import{ah as Z,ai as k,aj as T,ak as Vt,S as Qt,i as Zt,s as Kt,J as W,V as M,T as N,c as G,L as O,e as C,W as L,f as K,u as p,q as P,t as $,r as R,h as v,Z as b,ac as xt,ad as H,_ as Ot,a2 as Xt,a4 as lt,a9 as ct,A as Yt,n as D,af as at,Q as te,D as j}from"./SpinnerAugment-BUJasFTo.js";import{e as X,u as kt,o as Dt}from"./each-DUdYBCJG.js";import{k as ee,G as ne,g as se,t as ie,a as oe,M as re,l as ae,m as le,A as ce,b as ue,c as me,S as de,d as he,e as ge,R as pe,C as fe,f as $e,U as _e,h as Pt,E as Se,i as Ie,j as xe}from"./MessageListBottomButton-DxojfPD0.js";import"./Content-CSmc2GUv.js";import{g as ut,a as Rt,h as At,b as Ht,c as Gt,d as Et,e as Ft,f as Nt,j as jt,k as mt,m as we,R as Me,S as dt,i as wt,E as Le}from"./open-in-new-window-eiueNVFd.js";import"./diff-operations-DcwKj7d6.js";import{aq as be,ar as ye}from"./AugmentMessage-DqNyWKeW.js";import{S as Be}from"./main-panel-C8Qm-2QZ.js";import"./isObjectLike-DZvo29T1.js";import"./BaseButton-ci_067e0.js";import"./MaterialIcon-d9y4vLnQ.js";import"./keypress-DD1aQVr0.js";import"./autofix-state-d-ymFdyn.js";import"./Keybindings-CJ37aOb-.js";import"./CardAugment-DvO45c5p.js";import"./TextTooltipAugment-UDQF2J4S.js";import"./IconButtonAugment-DFy7vWkh.js";import"./index-yg8vr2DA.js";import"./BaseTextInput-BYcZ2XaJ.js";import"./exclamation-triangle-uzqmF3G7.js";import"./pen-to-square-DxHNIIBu.js";import"./augment-logo-CSOE_v2f.js";import"./ButtonAugment-DbAwCSeR.js";import"./expand-CE2AcHxk.js";import"./diff-utils-BYhHYFY1.js";import"./toggleHighContrast-CwIv4U26.js";import"./preload-helper-Dv6uf1Os.js";import"./index-B-fP3g4F.js";import"./layer-group-DiHphAz9.js";import"./circle-check-D3m08yO6.js";import"./types-CF53Ux0u.js";import"./globals-D0QH3NT1.js";import"./file-base64-RhZyEMB8.js";import"./file-paths-BcSg4gks.js";import"./types-e72Yl75f.js";import"./folder-opened-C1X7jSw2.js";import"./await_block-MKx3qG42.js";import"./magnifying-glass--PD1Uw4z.js";import"./ellipsis-CRdQranZ.js";import"./IconFilePath-XiFTI0mW.js";import"./LanguageIcon-DPvfnfyG.js";import"./next-edit-types-904A5ehg.js";import"./chevron-down-BPcCn3Z6.js";import"./play-Dd7ujDDf.js";import"./lodash-BHrlUNHT.js";import"./terminal-CwJUqtXN.js";import"./design-system-init-BKdwvVur.js";import"./trash-mbophkQL.js";import"./index-DlpZFSR-.js";import"./VSCodeCodicon-CzBgPB9u.js";import"./chat-flags-model-pSBfdnEi.js";class Ce{constructor(t=10){w(this,"samples",[]);w(this,"maxSamples");this.maxSamples=t}addSample(t,n=performance.now()){this.samples.push({position:t,timestamp:n}),this.samples.length>this.maxSamples&&this.samples.shift()}getVelocity(){if(this.samples.length<2)return 0;const t=this.samples.at(-1),n=this.samples.at(0),e=t.position-n.position,s=t.timestamp-n.timestamp;return s>0?e/s:0}getVelocityPPS(){return 1e3*this.getVelocity()}isFastScroll(t=500){return Math.abs(this.getVelocityPPS())>t}getDirection(){const t=this.getVelocity();return t>.05?"down":t<-.05?"up":"static"}predictPositionAfter(t){if(this.samples.length===0)return 0;const n=this.getVelocity();return this.samples[this.samples.length-1].position+n*t}reset(){this.samples=[]}getCurrentPosition(){return this.samples.length===0?0:this.samples[this.samples.length-1].position}}class ve{captureScrollPosition(t){const{scrollHeight:n,scrollTop:e,clientHeight:s}=t;return{scrollHeight:n,scrollTop:e,clientHeight:s,distanceFromBottom:ee(t),timestamp:performance.now()}}restoreScrollPosition(t,n){const e=t.scrollHeight-n.distanceFromBottom-t.clientHeight;t.scrollTop=Math.max(0,e)}}class qe{constructor(t,n={initialVisibleCount:20,batchSize:10}){w(this,"_config");w(this,"_disposables",[]);w(this,"_currentBatchSize");w(this,"_startIndex");w(this,"_endIndex");w(this,"_isLoading");w(this,"_loadingDirection");w(this,"_allItems");w(this,"_groupedAllItems");w(this,"_groupedVisibleItems");w(this,"hasMoreBefore");w(this,"hasMoreAfter");w(this,"isLoading");w(this,"loadingDirection");w(this,"totalItemCount");this._conversationModel=t,this._config={...n,minBatchSize:n.minBatchSize??n.batchSize,maxBatchSize:n.maxBatchSize??3*n.batchSize},this._currentBatchSize=this._config.batchSize,this._startIndex=Z(0),this._endIndex=Z(void 0),this._isLoading=Z(!1),this._loadingDirection=Z(null),this._allItems=k(this._conversationModel,s=>s.chatHistory.filter(i=>ut(i)||Rt(i)||At(i)||Ht(i)||Gt(i)||Et(i)||Ft(i)||Nt(i)||jt(i)||mt(i))),this._groupedAllItems=k(this._allItems,s=>{const i=s.map((r,u)=>({turn:r,idx:u}));return this._groupItems(i)}),this._groupedVisibleItems=k([this._groupedAllItems,this._startIndex,this._endIndex],([s,i,r])=>{if(s.length===0)return[];let u=0;for(let l=0;l<s.length;l++){const c=s[l];if(c.turns[c.turns.length-1].idx>=i){u=l;break}if(l===s.length-1&&c.turns[c.turns.length-1].idx>=i){u=l;break}}let a=s.length-1;if(r!==void 0)for(let l=s.length-1;l>=0;l--){const c=s[l];if(c.turns[0].idx<=r){a=l;break}if(l===0&&c.turns[0].idx<=r){a=l;break}}return s.slice(u,a+1).map((l,c,f)=>({...l,isLastGroup:c===f.length-1}))}),this.hasMoreBefore=k([this._startIndex],([s])=>s>0),this.hasMoreAfter=k([this._endIndex,this._allItems],([s,i])=>s!==void 0&&s<i.length-1),this.isLoading=k(this._isLoading,s=>s),this.loadingDirection=k(this._loadingDirection,s=>s),this.totalItemCount=k(this._allItems,s=>s.length);const e=T(this._conversationModel);typeof e.onNewConversation=="function"&&this._disposables.push(e.onNewConversation(()=>{this.resetToBottom()})),this.resetToBottom()}subscribe(t){return this._groupedVisibleItems.subscribe(t)}loadMoreBefore(t){const n=T(this._startIndex),e=T(this._isLoading);if(n<=0||e)return!1;this._isLoading.set(!0),this._loadingDirection.set("before");const s=this._getValidBatchSize(t),i=Math.max(0,n-s);return this._startIndex.set(i),this._isLoading.set(!1),this._loadingDirection.set(null),!0}async loadToStart(t={}){const n=T(this._startIndex),e=T(this._isLoading);if(n<=0||e)return!1;this._isLoading.set(!0),this._loadingDirection.set("before");const{smooth:s,smoothInterval:i=500}=t;if(s)for(;this.loadMoreBefore();)await new Promise(r=>setTimeout(r,i)),await Vt();else this._startIndex.set(0);return this._isLoading.set(!1),this._loadingDirection.set(null),!0}loadMoreAfter(t){const n=T(this._endIndex),e=T(this._isLoading),s=T(this._allItems);if(n===void 0||n>=s.length-1||e)return!1;this._isLoading.set(!0),this._loadingDirection.set("after");const i=this._getValidBatchSize(t),r=Math.min(s.length-1,n+i);return r>=s.length-1?this._endIndex.set(void 0):this._endIndex.set(r),this._isLoading.set(!1),this._loadingDirection.set(null),!0}resetToBottom(){const t=T(this._allItems);if(t.length===0)return;const n=Math.max(0,t.length-this._config.initialVisibleCount);this._startIndex.set(n),this._endIndex.set(void 0)}resetToTop(){const t=T(this._allItems);if(t.length===0)return;const n=Math.min(t.length-1,this._config.initialVisibleCount-1);this._startIndex.set(0),this._endIndex.set(n)}async jumpToMessage(t){const n=T(this._allItems),e=n.findIndex(u=>u.request_id===t);if(e===-1)return!1;const s=Math.floor(this._config.initialVisibleCount/2),i=Math.max(0,e-s),r=e+s>=n.length-5?void 0:Math.min(n.length-1,e+s);return this._startIndex.set(i),this._endIndex.set(r),!0}_groupItems(t){return t.reduce((n,{turn:e,idx:s})=>((e.isToolResult===!0||ut(e)&&we(e))&&n.length>0||mt(e)&&n.length>0?n[n.length-1].turns.push({turn:e,idx:s}):n.push({turns:[{turn:e,idx:s}],firstRequestId:e.request_id,lastRequestId:e.request_id,isLastGroup:!1}),n),[]).map((n,e,s)=>{const i=n.turns.findLast(({turn:u})=>!!u.request_id),r=i==null?void 0:i.turn.request_id;return{...n,lastRequestId:r,isLastGroup:e===s.length-1}})}setDynamicBatchSize(t){this._currentBatchSize=this._getValidBatchSize(t)}getCurrentBatchSize(){return this._currentBatchSize}_getValidBatchSize(t){if(t===void 0)return this._currentBatchSize;const n=this._config.minBatchSize??this._config.batchSize,e=this._config.maxBatchSize??3*this._config.batchSize;return Math.max(n,Math.min(e,t))}dispose(){this._disposables.forEach(t=>t())}}function Mt(o,t,n){const e=o.slice();return e[45]=t[n],e[47]=n,e}function Lt(o,t,n){const e=o.slice();e[48]=t[n].turn,e[49]=t[n].idx;const s=e[49]+1===e[10].length;return e[50]=s,e}function bt(o){let t,n;return t=new ce({}),{c(){M(t.$$.fragment)},m(e,s){L(t,e,s),n=!0},i(e){n||(p(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){b(t,e)}}}function yt(o){let t;return{c(){t=W("div"),t.textContent="Loading earlier messages...",G(t,"class","c-msg-list__loading svelte-80qwt2")},m(n,e){C(n,t,e)},d(n){n&&v(t)}}}function ze(o){let t,n,e,s;const i=[Ge,He],r=[];function u(a,l){return a[16].enableRichCheckpointInfo?0:1}return t=u(o),n=r[t]=i[t](o),{c(){n.c(),e=j()},m(a,l){r[t].m(a,l),C(a,e,l),s=!0},p(a,l){let c=t;t=u(a),t===c?r[t].p(a,l):(P(),$(r[c],1,1,()=>{r[c]=null}),R(),n=r[t],n?n.p(a,l):(n=r[t]=i[t](a),n.c()),p(n,1),n.m(e.parentNode,e))},i(a){s||(p(n),s=!0)},o(a){$(n),s=!1},d(a){a&&v(e),r[t].d(a)}}}function Te(o){let t,n;return t=new fe({props:{chatModel:o[1],turn:o[48],turnIndex:o[49],isLastTurn:o[50],messageListContainer:o[0]}}),{c(){M(t.$$.fragment)},m(e,s){L(t,e,s),n=!0},p(e,s){const i={};2&s[0]&&(i.chatModel=e[1]),262144&s[0]&&(i.turn=e[48]),262144&s[0]&&(i.turnIndex=e[49]),263168&s[0]&&(i.isLastTurn=e[50]),1&s[0]&&(i.messageListContainer=e[0]),t.$set(i)},i(e){n||(p(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){b(t,e)}}}function Ve(o){let t,n;return t=new $e({props:{stage:o[48].stage,iterationId:o[48].iterationId,stageCount:o[48].stageCount}}),{c(){M(t.$$.fragment)},m(e,s){L(t,e,s),n=!0},p(e,s){const i={};262144&s[0]&&(i.stage=e[48].stage),262144&s[0]&&(i.iterationId=e[48].iterationId),262144&s[0]&&(i.stageCount=e[48].stageCount),t.$set(i)},i(e){n||(p(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){b(t,e)}}}function ke(o){let t,n;return t=new _e({props:{chatModel:o[1],msg:o[48].response_text??""}}),{c(){M(t.$$.fragment)},m(e,s){L(t,e,s),n=!0},p(e,s){const i={};2&s[0]&&(i.chatModel=e[1]),262144&s[0]&&(i.msg=e[48].response_text??""),t.$set(i)},i(e){n||(p(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){b(t,e)}}}function De(o){let t,n;return t=new be({props:{markdown:o[48].response_text??"",messageListContainer:o[0]}}),{c(){M(t.$$.fragment)},m(e,s){L(t,e,s),n=!0},p(e,s){const i={};262144&s[0]&&(i.markdown=e[48].response_text??""),1&s[0]&&(i.messageListContainer=e[0]),t.$set(i)},i(e){n||(p(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){b(t,e)}}}function Pe(o){let t,n;function e(){return o[35](o[48])}return t=new Pt({props:{turn:o[48],preamble:Be,resendTurn:e,$$slots:{default:[Ee]},$$scope:{ctx:o}}}),{c(){M(t.$$.fragment)},m(s,i){L(t,s,i),n=!0},p(s,i){o=s;const r={};262144&i[0]&&(r.turn=o[48]),262148&i[0]&&(r.resendTurn=e),270336&i[0]|4194304&i[1]&&(r.$$scope={dirty:i,ctx:o}),t.$set(r)},i(s){n||(p(t.$$.fragment,s),n=!0)},o(s){$(t.$$.fragment,s),n=!1},d(s){b(t,s)}}}function Re(o){let t,n;return t=new Se({props:{flagsModel:o[11],turn:o[48]}}),{c(){M(t.$$.fragment)},m(e,s){L(t,e,s),n=!0},p(e,s){const i={};2048&s[0]&&(i.flagsModel=e[11]),262144&s[0]&&(i.turn=e[48]),t.$set(i)},i(e){n||(p(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){b(t,e)}}}function Ae(o){let t,n;return t=new Pt({props:{turn:o[48]}}),{c(){M(t.$$.fragment)},m(e,s){L(t,e,s),n=!0},p(e,s){const i={};262144&s[0]&&(i.turn=e[48]),t.$set(i)},i(e){n||(p(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){b(t,e)}}}function He(o){let t,n;return t=new Ie({props:{turn:o[48]}}),{c(){M(t.$$.fragment)},m(e,s){L(t,e,s),n=!0},p(e,s){const i={};262144&s[0]&&(i.turn=e[48]),t.$set(i)},i(e){n||(p(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){b(t,e)}}}function Ge(o){let t,n;return t=new xe({props:{turn:o[48]}}),{c(){M(t.$$.fragment)},m(e,s){L(t,e,s),n=!0},p(e,s){const i={};262144&s[0]&&(i.turn=e[48]),t.$set(i)},i(e){n||(p(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){b(t,e)}}}function Ee(o){let t,n;return t=new ye({props:{conversationModel:o[13],turn:o[48]}}),{c(){M(t.$$.fragment)},m(e,s){L(t,e,s),n=!0},p(e,s){const i={};8192&s[0]&&(i.conversationModel=e[13]),262144&s[0]&&(i.turn=e[48]),t.$set(i)},i(e){n||(p(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){b(t,e)}}}function Bt(o){let t,n,e,s;function i(){return o[36](o[48])}return{c(){t=W("div"),G(t,"class","c-msg-list__turn-seen")},m(r,u){C(r,t,u),e||(s=lt(n=me.call(null,t,{onSeen:i,track:o[48].seen_state!==dt.seen})),e=!0)},p(r,u){o=r,n&&ct(n.update)&&262144&u[0]&&n.update.call(null,{onSeen:i,track:o[48].seen_state!==dt.seen})},d(r){r&&v(t),e=!1,s()}}}function Ct(o,t){let n,e,s,i,r,u,a,l,c,f,h,I,B,z,x=wt(t[48]);const d=[Ae,Re,Pe,De,ke,Ve,Te,ze],g=[];function q(_,S){return 262144&S[0]&&(e=null),262144&S[0]&&(s=null),262144&S[0]&&(i=null),262144&S[0]&&(r=null),262144&S[0]&&(u=null),262144&S[0]&&(a=null),262144&S[0]&&(l=null),262144&S[0]&&(c=null),e==null&&(e=!!Rt(_[48])),e?0:(s==null&&(s=!!Ht(_[48])),s?1:(i==null&&(i=!!Gt(_[48])),i?2:(r==null&&(r=!!Et(_[48])),r?3:(u==null&&(u=!!Ft(_[48])),u?4:(a==null&&(a=!!Nt(_[48])),a?5:(l==null&&(l=!!(ut(_[48])||At(_[48])||jt(_[48]))),l?6:(c==null&&(c=!(!mt(_[48])||_[48].status!==Le.success)),c?7:-1)))))))}~(f=q(t,[-1,-1]))&&(h=g[f]=d[f](t));let y=x&&Bt(t);return{key:o,first:null,c(){n=j(),h&&h.c(),I=N(),y&&y.c(),B=j(),this.first=n},m(_,S){C(_,n,S),~f&&g[f].m(_,S),C(_,I,S),y&&y.m(_,S),C(_,B,S),z=!0},p(_,S){let E=f;f=q(t=_,S),f===E?~f&&g[f].p(t,S):(h&&(P(),$(g[E],1,1,()=>{g[E]=null}),R()),~f?(h=g[f],h?h.p(t,S):(h=g[f]=d[f](t),h.c()),p(h,1),h.m(I.parentNode,I)):h=null),262144&S[0]&&(x=wt(t[48])),x?y?y.p(t,S):(y=Bt(t),y.c(),y.m(B.parentNode,B)):y&&(y.d(1),y=null)},i(_){z||(p(h),z=!0)},o(_){$(h),z=!1},d(_){_&&(v(n),v(I),v(B)),~f&&g[f].d(_),y&&y.d(_)}}}function vt(o){let t,n,e,s;const i=[Ue,We,je,Ne,Fe],r=[];function u(a,l){return a[8].retryMessage?0:a[8].showGeneratingResponse?1:a[8].showAwaitingUserInput?2:a[8].showRunningSpacer?3:a[8].showStopped?4:-1}return~(t=u(o))&&(n=r[t]=i[t](o)),{c(){n&&n.c(),e=j()},m(a,l){~t&&r[t].m(a,l),C(a,e,l),s=!0},p(a,l){let c=t;t=u(a),t===c?~t&&r[t].p(a,l):(n&&(P(),$(r[c],1,1,()=>{r[c]=null}),R()),~t?(n=r[t],n?n.p(a,l):(n=r[t]=i[t](a),n.c()),p(n,1),n.m(e.parentNode,e)):n=null)},i(a){s||(p(n),s=!0)},o(a){$(n),s=!1},d(a){a&&v(e),~t&&r[t].d(a)}}}function Fe(o){let t,n;return t=new de({}),{c(){M(t.$$.fragment)},m(e,s){L(t,e,s),n=!0},p:D,i(e){n||(p(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){b(t,e)}}}function Ne(o){let t;return{c(){t=W("div"),G(t,"class","c-agent-running-spacer svelte-80qwt2")},m(n,e){C(n,t,e)},p:D,i:D,o:D,d(n){n&&v(t)}}}function je(o){let t,n;return t=new he({}),{c(){M(t.$$.fragment)},m(e,s){L(t,e,s),n=!0},p:D,i(e){n||(p(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){b(t,e)}}}function We(o){let t,n;return t=new ge({}),{c(){M(t.$$.fragment)},m(e,s){L(t,e,s),n=!0},p:D,i(e){n||(p(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){b(t,e)}}}function Ue(o){let t,n;return t=new pe({props:{message:o[8].retryMessage}}),{c(){M(t.$$.fragment)},m(e,s){L(t,e,s),n=!0},p(e,s){const i={};256&s[0]&&(i.message=e[8].retryMessage),t.$set(i)},i(e){n||(p(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){b(t,e)}}}function Je(o){let t,n,e,s=[],i=new Map,r=X(o[45].turns);const u=l=>l[48].request_id??`no-request-id-${l[49]}`;for(let l=0;l<r.length;l+=1){let c=Lt(o,r,l),f=u(c);i.set(f,s[l]=Ct(f,c))}let a=o[45].isLastGroup&&vt(o);return{c(){for(let l=0;l<s.length;l+=1)s[l].c();t=N(),a&&a.c(),n=j()},m(l,c){for(let f=0;f<s.length;f+=1)s[f]&&s[f].m(l,c);C(l,t,c),a&&a.m(l,c),C(l,n,c),e=!0},p(l,c){537209863&c[0]&&(r=X(l[45].turns),P(),s=kt(s,c,u,1,l,r,i,t.parentNode,Dt,Ct,t,Lt),R()),l[45].isLastGroup?a?(a.p(l,c),262144&c[0]&&p(a,1)):(a=vt(l),a.c(),p(a,1),a.m(n.parentNode,n)):a&&(P(),$(a,1,1,()=>{a=null}),R())},i(l){if(!e){for(let c=0;c<r.length;c+=1)p(s[c]);p(a),e=!0}},o(l){for(let c=0;c<s.length;c+=1)$(s[c]);$(a),e=!1},d(l){l&&(v(t),v(n));for(let c=0;c<s.length;c+=1)s[c].d(l);a&&a.d(l)}}}function qt(o,t){let n,e,s;return e=new ue({props:{class:"c-msg-list__item--grouped",chatModel:t[1],isLastItem:t[45].isLastGroup,userControlsScroll:t[4],requestId:t[45].firstRequestId,releaseScroll:t[37],messageListContainer:t[0],minHeight:t[45].isLastGroup?t[9]:0,dataRequestId:t[45].firstRequestId,$$slots:{default:[Je]},$$scope:{ctx:t}}}),{key:o,first:null,c(){n=j(),M(e.$$.fragment),this.first=n},m(i,r){C(i,n,r),L(e,i,r),s=!0},p(i,r){t=i;const u={};2&r[0]&&(u.chatModel=t[1]),262144&r[0]&&(u.isLastItem=t[45].isLastGroup),16&r[0]&&(u.userControlsScroll=t[4]),262144&r[0]&&(u.requestId=t[45].firstRequestId),16&r[0]&&(u.releaseScroll=t[37]),1&r[0]&&(u.messageListContainer=t[0]),262656&r[0]&&(u.minHeight=t[45].isLastGroup?t[9]:0),262144&r[0]&&(u.dataRequestId=t[45].firstRequestId),339207&r[0]|4194304&r[1]&&(u.$$scope={dirty:r,ctx:t}),e.$set(u)},i(i){s||(p(e.$$.fragment,i),s=!0)},o(i){$(e.$$.fragment,i),s=!1},d(i){i&&v(n),b(e,i)}}}function zt(o){let t;return{c(){t=W("div"),t.textContent="Loading more messages...",G(t,"class","c-msg-list__loading svelte-80qwt2")},m(n,e){C(n,t,e)},d(n){n&&v(t)}}}function Qe(o){let t,n,e,s,i,r,u,a,l,c=[],f=new Map,h=o[6]&&bt(),I=o[15]&&o[17]==="before"&&yt(),B=X(o[18]);const z=d=>d[45].firstRequestId??`no-request-id-${d[47]}`;for(let d=0;d<B.length;d+=1){let g=Mt(o,B,d),q=z(g);f.set(q,c[d]=qt(q,g))}let x=o[15]&&o[17]==="after"&&zt();return{c(){t=W("div"),h&&h.c(),n=N(),I&&I.c(),e=N();for(let d=0;d<c.length;d+=1)c[d].c();s=N(),x&&x.c(),G(t,"class","c-msg-list svelte-80qwt2"),O(t,"c-msg-list--minimal",!o[16].fullFeatured)},m(d,g){C(d,t,g),h&&h.m(t,null),K(t,n),I&&I.m(t,null),K(t,e);for(let q=0;q<c.length;q+=1)c[q]&&c[q].m(t,null);K(t,s),x&&x.m(t,null),o[38](t),u=!0,a||(l=[lt(i=ie.call(null,t,{onScrollIntoBottom:o[39],onScrollAwayFromBottom:o[40],onScroll:o[41]})),lt(r=oe.call(null,t,{onHeightChange:o[42]}))],a=!0)},p(d,g){d[6]?h?64&g[0]&&p(h,1):(h=bt(),h.c(),p(h,1),h.m(t,n)):h&&(P(),$(h,1,1,()=>{h=null}),R()),d[15]&&d[17]==="before"?I||(I=yt(),I.c(),I.m(t,e)):I&&(I.d(1),I=null),537210647&g[0]&&(B=X(d[18]),P(),c=kt(c,g,z,1,d,B,f,t,Dt,qt,s,Mt),R()),d[15]&&d[17]==="after"?x||(x=zt(),x.c(),x.m(t,null)):x&&(x.d(1),x=null),i&&ct(i.update)&&16433&g[0]&&i.update.call(null,{onScrollIntoBottom:d[39],onScrollAwayFromBottom:d[40],onScroll:d[41]}),r&&ct(r.update)&&8&g[0]&&r.update.call(null,{onHeightChange:d[42]}),(!u||65536&g[0])&&O(t,"c-msg-list--minimal",!d[16].fullFeatured)},i(d){if(!u){p(h);for(let g=0;g<B.length;g+=1)p(c[g]);u=!0}},o(d){$(h);for(let g=0;g<c.length;g+=1)$(c[g]);u=!1},d(d){d&&v(t),h&&h.d(),I&&I.d();for(let g=0;g<c.length;g+=1)c[g].d();x&&x.d(),o[38](null),a=!1,Yt(l)}}}function Tt(o){let t,n;return t=new re({props:{messageListElement:o[0],showScrollDown:!o[5]}}),{c(){M(t.$$.fragment)},m(e,s){L(t,e,s),n=!0},p(e,s){const i={};1&s[0]&&(i.messageListElement=e[0]),32&s[0]&&(i.showScrollDown=!e[5]),t.$set(i)},i(e){n||(p(t.$$.fragment,e),n=!0)},o(e){$(t.$$.fragment,e),n=!1},d(e){b(t,e)}}}function Ze(o){let t,n,e,s;n=new ne({props:{$$slots:{default:[Qe]},$$scope:{ctx:o}}});let i=o[7]&&Tt(o);return{c(){t=W("div"),M(n.$$.fragment),e=N(),i&&i.c(),G(t,"class","c-msg-list-container svelte-80qwt2"),G(t,"data-testid","chat-message-list"),O(t,"c-msg-list--minimal",!o[16].fullFeatured)},m(r,u){C(r,t,u),L(n,t,null),K(t,e),i&&i.m(t,null),s=!0},p(r,u){const a={};520063&u[0]|4194304&u[1]&&(a.$$scope={dirty:u,ctx:r}),n.$set(a),r[7]?i?(i.p(r,u),128&u[0]&&p(i,1)):(i=Tt(r),i.c(),p(i,1),i.m(t,null)):i&&(P(),$(i,1,1,()=>{i=null}),R()),(!s||65536&u[0])&&O(t,"c-msg-list--minimal",!r[16].fullFeatured)},i(r){s||(p(n.$$.fragment,r),p(i),s=!0)},o(r){$(n.$$.fragment,r),$(i),s=!1},d(r){r&&v(t),b(n),i&&i.d()}}}function Ke(o,t,n){let e,s,i,r,u,a,l,c,f,h,I,B,z,x,d,g,q,y,_=D,S=D,E=()=>(S(),S=at(U,m=>n(34,d=m)),U),Y=D;o.$$.on_destroy.push(()=>_()),o.$$.on_destroy.push(()=>S()),o.$$.on_destroy.push(()=>Y());let{chatModel:U}=t;E();let{onboardingWorkspaceModel:tt}=t,{msgListElement:V}=t;const Wt=xt("agentConversationModel"),{agentExchangeStatus:ht,isCurrConversationAgentic:gt}=Wt;H(o,ht,m=>n(33,x=m)),H(o,gt,m=>n(32,z=m));const pt=xt(Me.key);H(o,pt,m=>n(31,B=m));const A=new qe(k(U,m=>m.currentConversationModel),{initialVisibleCount:20,batchSize:10,minBatchSize:5,maxBatchSize:20});H(o,A,m=>n(18,y=m));const{hasMoreBefore:ft,isLoading:$t,loadingDirection:_t}=A;H(o,ft,m=>n(14,h=m)),H(o,$t,m=>n(15,I=m)),H(o,_t,m=>n(17,q=m));const J=new Ce(3),St=new ve;Ot(()=>{A.dispose()});let Q=!1;function F(){n(4,Q=!0)}async function et(){if(!V||!h||I)return;F();const m=St.captureScrollPosition(V),it=J.getVelocityPPS(),ot=ae(it,A.getCurrentBatchSize()),rt=A.loadMoreBefore(ot);m&&(F(),St.restoreScrollPosition(V,m),await Vt()),rt&&V&&V.scrollTop<=1&&h&&et()}Xt(()=>{var m;((m=f.lastExchange)==null?void 0:m.seen_state)===dt.unseen&&F()});let nt=0,st=!0;const It=m=>f.markSeen(m);return o.$$set=m=>{"chatModel"in m&&E(n(1,U=m.chatModel)),"onboardingWorkspaceModel"in m&&n(2,tt=m.onboardingWorkspaceModel),"msgListElement"in m&&n(0,V=m.msgListElement)},o.$$.update=()=>{8&o.$$.dirty[1]&&(n(12,e=d.currentConversationModel),_(),_=at(e,m=>n(13,f=m))),8&o.$$.dirty[1]&&(n(11,s=d.flags),Y(),Y=at(s,m=>n(16,g=m))),15&o.$$.dirty[1]&&n(30,i=se(d,x,z,B)),1073741824&o.$$.dirty[0]&&n(10,r=i.chatHistory),8&o.$$.dirty[0]&&n(9,u=nt),1073741824&o.$$.dirty[0]&&n(8,a=i.lastGroupConfig),1073741824&o.$$.dirty[0]&&n(7,l=i.doShowFloatingButtons),1073741824&o.$$.dirty[0]&&n(6,c=i.doShowAgentSetupLogs)},[V,U,tt,nt,Q,st,c,l,a,u,r,s,e,f,h,I,g,q,y,ht,gt,pt,A,ft,$t,_t,J,F,et,It,i,B,z,x,d,m=>tt.retryProjectSummary(m),m=>It(m),()=>n(4,Q=!0),function(m){te[m?"unshift":"push"](()=>{V=m,n(0,V)})},()=>{n(4,Q=!1),n(5,st=!0),A.resetToBottom()},()=>{F(),n(5,st=!1)},m=>{if(m<=1&&F(),V){J.addSample(m);const it=J.getVelocityPPS(),ot=J.getDirection(),rt=le(it,{baseThreshold:200,predictTime:300});ot==="up"&&m<rt&&h&&et()}},m=>n(3,nt=m)]}class Kn extends Qt{constructor(t){super(),Zt(this,t,Ke,Ze,Kt,{chatModel:1,onboardingWorkspaceModel:2,msgListElement:0},null,[-1,-1])}}export{Kn as default};
