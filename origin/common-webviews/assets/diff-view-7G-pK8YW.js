var pp=Object.defineProperty;var Xl=o=>{throw TypeError(o)};var gp=(o,t,r)=>t in o?pp(o,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):o[t]=r;var _=(o,t,r)=>gp(o,typeof t!="symbol"?t+"":t,r),_u=(o,t,r)=>t.has(o)||Xl("Cannot "+r);var y=(o,t,r)=>(_u(o,t,"read from private field"),r?r.call(o):t.get(o)),ke=(o,t,r)=>t.has(o)?Xl("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(o):t.set(o,r),ue=(o,t,r,i)=>(_u(o,t,"write to private field"),i?i.call(o,r):t.set(o,r),r),Q=(o,t,r)=>(_u(o,t,"access private method"),r);var Po=(o,t,r,i)=>({set _(s){ue(o,t,s,r)},get _(){return y(o,t,i)}});import{a5 as tn,am as Lh,ah as bt,aj as pt,az as Zl,ak as Mu,S as oi,i as ai,s as ui,a9 as ur,J as We,T as qe,V as G,c as De,L as pn,a8 as Ti,e as re,f as He,W as Y,a4 as Nh,P as Nr,u as O,q as $t,t as q,r as wt,h as ie,Z as K,A as Yu,ad as si,a0 as xt,Q as Oi,R as mp,Y as _p,D as hr,n as Ve,af as ar,al as jh,a1 as cr,ai as vu,aA as zi,$ as eh,ac as vp,_ as Ku,ae as Dh,an as yp,a2 as qh,ag as bp,U as $p}from"./SpinnerAugment-BUJasFTo.js";import"./design-system-init-BKdwvVur.js";import{g as wp}from"./globals-D0QH3NT1.js";import{e as Ko}from"./each-DUdYBCJG.js";import{A as xp,I as Cp}from"./IconButtonAugment-DFy7vWkh.js";import{S as Sp,W as it,h as Is,D as yu}from"./BaseButton-ci_067e0.js";import{i as sr,a as Ri,b as Kt,c as Es,S as ws,d as xs,e as na,C as Ip,E as Ep,D as Mp,f as kp,g as Ap,s as bu,h as Qu,j as Qo,k as Jo,l as Ju,m as Xo,n as Xu,o as Zo,A as Fp,p as $u,q as Tp,r as ks,t as zh,U as Op,u as Rp,v as Lp,w as Np}from"./chat-flags-model-pSBfdnEi.js";import{F as Ph,b as jp,c as nn,d as Dp,e as Uh}from"./folder-opened-C1X7jSw2.js";import{P as Mt,C as Ms,c as ku,a as An,I as $s,E as qp}from"./file-base64-RhZyEMB8.js";import{M as Wh,T as zp}from"./TextTooltipAugment-UDQF2J4S.js";import{S as Pp,a as th}from"./types-e72Yl75f.js";import{K as ci,C as Up,A as nh,R as Wp,B as Hp,P as Vp,T as Bp,a as Gp,b as Yp,c as Kp,G as Qp,d as Jp,M as Au,e as Hh,f as Xp,g as Zp,h as eg}from"./Keybindings-CJ37aOb-.js";import{B as jr}from"./ButtonAugment-DbAwCSeR.js";import{T as tg}from"./Content-CSmc2GUv.js";import{D as wu}from"./index-yg8vr2DA.js";import{M as Pi}from"./MaterialIcon-d9y4vLnQ.js";import{E as ng}from"./exclamation-triangle-uzqmF3G7.js";import{a as rg,M as ig,g as sg,C as og}from"./index-B-fP3g4F.js";import"./file-paths-BcSg4gks.js";import"./CardAugment-DvO45c5p.js";import"./pen-to-square-DxHNIIBu.js";import"./augment-logo-CSOE_v2f.js";import"./BaseTextInput-BYcZ2XaJ.js";var rh=NaN,ag="[object Symbol]",ug=/^\s+|\s+$/g,cg=/^[-+]0x[0-9a-f]+$/i,lg=/^0b[01]+$/i,hg=/^0o[0-7]+$/i,fg=parseInt,dg=typeof tn=="object"&&tn&&tn.Object===Object&&tn,pg=typeof self=="object"&&self&&self.Object===Object&&self,gg=dg||pg||Function("return this")(),mg=Object.prototype.toString,_g=Math.max,vg=Math.min,xu=function(){return gg.Date.now()};function Fu(o){var t=typeof o;return!!o&&(t=="object"||t=="function")}function ih(o){if(typeof o=="number")return o;if(function(i){return typeof i=="symbol"||function(s){return!!s&&typeof s=="object"}(i)&&mg.call(i)==ag}(o))return rh;if(Fu(o)){var t=typeof o.valueOf=="function"?o.valueOf():o;o=Fu(t)?t+"":t}if(typeof o!="string")return o===0?o:+o;o=o.replace(ug,"");var r=lg.test(o);return r||hg.test(o)?fg(o.slice(2),r?2:8):cg.test(o)?rh:+o}const Tu=Lh(function(o,t,r){var i,s,u,c,h,f,v=0,m=!1,b=!1,x=!0;if(typeof o!="function")throw new TypeError("Expected a function");function E(T){var ne=i,de=s;return i=s=void 0,v=T,c=o.apply(de,ne)}function F(T){var ne=T-f;return f===void 0||ne>=t||ne<0||b&&T-v>=u}function z(){var T=xu();if(F(T))return R(T);h=setTimeout(z,function(ne){var de=t-(ne-f);return b?vg(de,u-(ne-v)):de}(T))}function R(T){return h=void 0,x&&i?E(T):(i=s=void 0,c)}function S(){var T=xu(),ne=F(T);if(i=arguments,s=this,f=T,ne){if(h===void 0)return function(de){return v=de,h=setTimeout(z,t),m?E(de):c}(f);if(b)return h=setTimeout(z,t),E(f)}return h===void 0&&(h=setTimeout(z,t)),c}return t=ih(t)||0,Fu(r)&&(m=!!r.leading,u=(b="maxWait"in r)?_g(ih(r.maxWait)||0,t):u,x="trailing"in r?!!r.trailing:x),S.cancel=function(){h!==void 0&&clearTimeout(h),v=0,i=f=s=h=void 0},S.flush=function(){return h===void 0?c:R(xu())},S});var Ou={exports:{}};(function(o,t){var r="__lodash_hash_undefined__",i=1,s=2,u=9007199254740991,c="[object Arguments]",h="[object Array]",f="[object AsyncFunction]",v="[object Boolean]",m="[object Date]",b="[object Error]",x="[object Function]",E="[object GeneratorFunction]",F="[object Map]",z="[object Number]",R="[object Null]",S="[object Object]",T="[object Promise]",ne="[object Proxy]",de="[object RegExp]",oe="[object Set]",Ne="[object String]",pe="[object Symbol]",Ie="[object Undefined]",me="[object WeakMap]",Te="[object ArrayBuffer]",Ue="[object DataView]",st=/^\[object .+?Constructor\]$/,Se=/^(?:0|[1-9]\d*)$/,we={};we["[object Float32Array]"]=we["[object Float64Array]"]=we["[object Int8Array]"]=we["[object Int16Array]"]=we["[object Int32Array]"]=we["[object Uint8Array]"]=we["[object Uint8ClampedArray]"]=we["[object Uint16Array]"]=we["[object Uint32Array]"]=!0,we[c]=we[h]=we[Te]=we[v]=we[Ue]=we[m]=we[b]=we[x]=we[F]=we[z]=we[S]=we[de]=we[oe]=we[Ne]=we[me]=!1;var at=typeof tn=="object"&&tn&&tn.Object===Object&&tn,gn=typeof self=="object"&&self&&self.Object===Object&&self,Ct=at||gn||Function("return this")(),fe=t&&!t.nodeType&&t,mn=fe&&o&&!o.nodeType&&o,Fn=mn&&mn.exports===fe,Vn=Fn&&at.process,fr=function(){try{return Vn&&Vn.binding&&Vn.binding("util")}catch{}}(),li=fr&&fr.isTypedArray;function Ui(C,k){for(var W=-1,ee=C==null?0:C.length;++W<ee;)if(k(C[W],W,C))return!0;return!1}function ra(C){var k=-1,W=Array(C.size);return C.forEach(function(ee,ze){W[++k]=[ze,ee]}),W}function ia(C){var k=-1,W=Array(C.size);return C.forEach(function(ee){W[++k]=ee}),W}var Ts,Wi,Hi,sa=Array.prototype,oa=Function.prototype,hi=Object.prototype,Vi=Ct["__core-js_shared__"],Bi=oa.toString,rn=hi.hasOwnProperty,Os=(Ts=/[^.]+$/.exec(Vi&&Vi.keys&&Vi.keys.IE_PROTO||""))?"Symbol(src)_1."+Ts:"",Rs=hi.toString,Gi=RegExp("^"+Bi.call(rn).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),Ls=Fn?Ct.Buffer:void 0,dr=Ct.Symbol,Ns=Ct.Uint8Array,js=hi.propertyIsEnumerable,aa=sa.splice,Bn=dr?dr.toStringTag:void 0,Ds=Object.getOwnPropertySymbols,ua=Ls?Ls.isBuffer:void 0,ca=(Wi=Object.keys,Hi=Object,function(C){return Wi(Hi(C))}),Yi=Nn(Ct,"DataView"),pr=Nn(Ct,"Map"),Ki=Nn(Ct,"Promise"),Qi=Nn(Ct,"Set"),Ji=Nn(Ct,"WeakMap"),qr=Nn(Object,"create"),la=Gn(Yi),ha=Gn(pr),fi=Gn(Ki),fa=Gn(Qi),di=Gn(Ji),Xi=dr?dr.prototype:void 0,pi=Xi?Xi.valueOf:void 0;function Tn(C){var k=-1,W=C==null?0:C.length;for(this.clear();++k<W;){var ee=C[k];this.set(ee[0],ee[1])}}function sn(C){var k=-1,W=C==null?0:C.length;for(this.clear();++k<W;){var ee=C[k];this.set(ee[0],ee[1])}}function On(C){var k=-1,W=C==null?0:C.length;for(this.clear();++k<W;){var ee=C[k];this.set(ee[0],ee[1])}}function zr(C){var k=-1,W=C==null?0:C.length;for(this.__data__=new On;++k<W;)this.add(C[k])}function Rn(C){var k=this.__data__=new sn(C);this.size=k.size}function da(C,k){var W=Ur(C),ee=!W&&Ws(C),ze=!W&&!ee&&mi(C),X=!W&&!ee&&!ze&&Bs(C),he=W||ee||ze||X,Je=he?function(Xe,on){for(var Pe=-1,ut=Array(Xe);++Pe<Xe;)ut[Pe]=on(Pe);return ut}(C.length,String):[],Ht=Je.length;for(var tt in C)!rn.call(C,tt)||he&&(tt=="length"||ze&&(tt=="offset"||tt=="parent")||X&&(tt=="buffer"||tt=="byteLength"||tt=="byteOffset")||Us(tt,Ht))||Je.push(tt);return Je}function Pr(C,k){for(var W=C.length;W--;)if(ts(C[W][0],k))return W;return-1}function Ln(C){return C==null?C===void 0?Ie:R:Bn&&Bn in Object(C)?function(k){var W=rn.call(k,Bn),ee=k[Bn];try{k[Bn]=void 0;var ze=!0}catch{}var X=Rs.call(k);return ze&&(W?k[Bn]=ee:delete k[Bn]),X}(C):function(k){return Rs.call(k)}(C)}function Zi(C){return Wr(C)&&Ln(C)==c}function qs(C,k,W,ee,ze){return C===k||(C==null||k==null||!Wr(C)&&!Wr(k)?C!=C&&k!=k:function(X,he,Je,Ht,tt,Xe){var on=Ur(X),Pe=Ur(he),ut=on?h:_n(X),St=Pe?h:_n(he),Yn=(ut=ut==c?S:ut)==S,_r=(St=St==c?S:St)==S,ct=ut==St;if(ct&&mi(X)){if(!mi(he))return!1;on=!0,Yn=!1}if(ct&&!Yn)return Xe||(Xe=new Rn),on||Bs(X)?gi(X,he,Je,Ht,tt,Xe):function(Oe,Ae,gt,jn,kt,qt,an){switch(gt){case Ue:if(Oe.byteLength!=Ae.byteLength||Oe.byteOffset!=Ae.byteOffset)return!1;Oe=Oe.buffer,Ae=Ae.buffer;case Te:return!(Oe.byteLength!=Ae.byteLength||!qt(new Ns(Oe),new Ns(Ae)));case v:case m:case z:return ts(+Oe,+Ae);case b:return Oe.name==Ae.name&&Oe.message==Ae.message;case de:case Ne:return Oe==Ae+"";case F:var It=ra;case oe:var Dn=jn&i;if(It||(It=ia),Oe.size!=Ae.size&&!Dn)return!1;var yr=an.get(Oe);if(yr)return yr==Ae;jn|=s,an.set(Oe,Ae);var je=gi(It(Oe),It(Ae),jn,kt,qt,an);return an.delete(Oe),je;case pe:if(pi)return pi.call(Oe)==pi.call(Ae)}return!1}(X,he,ut,Je,Ht,tt,Xe);if(!(Je&i)){var vr=Yn&&rn.call(X,"__wrapped__"),is=_r&&rn.call(he,"__wrapped__");if(vr||is){var Gs=vr?X.value():X,Ys=is?he.value():he;return Xe||(Xe=new Rn),tt(Gs,Ys,Je,Ht,Xe)}}return ct?(Xe||(Xe=new Rn),function(Oe,Ae,gt,jn,kt,qt){var an=gt&i,It=es(Oe),Dn=It.length,yr=es(Ae),je=yr.length;if(Dn!=je&&!an)return!1;for(var Vt=Dn;Vt--;){var vn=It[Vt];if(!(an?vn in Ae:rn.call(Ae,vn)))return!1}var Ks=qt.get(Oe);if(Ks&&qt.get(Ae))return Ks==Ae;var br=!0;qt.set(Oe,Ae),qt.set(Ae,Oe);for(var ss=an;++Vt<Dn;){var Hr=Oe[vn=It[Vt]],Kn=Ae[vn];if(jn)var Qn=an?jn(Kn,Hr,vn,Ae,Oe,qt):jn(Hr,Kn,vn,Oe,Ae,qt);if(!(Qn===void 0?Hr===Kn||kt(Hr,Kn,gt,jn,qt):Qn)){br=!1;break}ss||(ss=vn=="constructor")}if(br&&!ss){var _i=Oe.constructor,Vr=Ae.constructor;_i==Vr||!("constructor"in Oe)||!("constructor"in Ae)||typeof _i=="function"&&_i instanceof _i&&typeof Vr=="function"&&Vr instanceof Vr||(br=!1)}return qt.delete(Oe),qt.delete(Ae),br}(X,he,Je,Ht,tt,Xe)):!1}(C,k,W,ee,qs,ze))}function zs(C){return!(!Vs(C)||function(k){return!!Os&&Os in k}(C))&&(ns(C)?Gi:st).test(Gn(C))}function Ps(C){if(W=(k=C)&&k.constructor,ee=typeof W=="function"&&W.prototype||hi,k!==ee)return ca(C);var k,W,ee,ze=[];for(var X in Object(C))rn.call(C,X)&&X!="constructor"&&ze.push(X);return ze}function gi(C,k,W,ee,ze,X){var he=W&i,Je=C.length,Ht=k.length;if(Je!=Ht&&!(he&&Ht>Je))return!1;var tt=X.get(C);if(tt&&X.get(k))return tt==k;var Xe=-1,on=!0,Pe=W&s?new zr:void 0;for(X.set(C,k),X.set(k,C);++Xe<Je;){var ut=C[Xe],St=k[Xe];if(ee)var Yn=he?ee(St,ut,Xe,k,C,X):ee(ut,St,Xe,C,k,X);if(Yn!==void 0){if(Yn)continue;on=!1;break}if(Pe){if(!Ui(k,function(_r,ct){if(vr=ct,!Pe.has(vr)&&(ut===_r||ze(ut,_r,W,ee,X)))return Pe.push(ct);var vr})){on=!1;break}}else if(ut!==St&&!ze(ut,St,W,ee,X)){on=!1;break}}return X.delete(C),X.delete(k),on}function es(C){return function(k,W,ee){var ze=W(k);return Ur(k)?ze:function(X,he){for(var Je=-1,Ht=he.length,tt=X.length;++Je<Ht;)X[tt+Je]=he[Je];return X}(ze,ee(k))}(C,rs,mr)}function gr(C,k){var W,ee,ze=C.__data__;return((ee=typeof(W=k))=="string"||ee=="number"||ee=="symbol"||ee=="boolean"?W!=="__proto__":W===null)?ze[typeof k=="string"?"string":"hash"]:ze.map}function Nn(C,k){var W=function(ee,ze){return ee==null?void 0:ee[ze]}(C,k);return zs(W)?W:void 0}Tn.prototype.clear=function(){this.__data__=qr?qr(null):{},this.size=0},Tn.prototype.delete=function(C){var k=this.has(C)&&delete this.__data__[C];return this.size-=k?1:0,k},Tn.prototype.get=function(C){var k=this.__data__;if(qr){var W=k[C];return W===r?void 0:W}return rn.call(k,C)?k[C]:void 0},Tn.prototype.has=function(C){var k=this.__data__;return qr?k[C]!==void 0:rn.call(k,C)},Tn.prototype.set=function(C,k){var W=this.__data__;return this.size+=this.has(C)?0:1,W[C]=qr&&k===void 0?r:k,this},sn.prototype.clear=function(){this.__data__=[],this.size=0},sn.prototype.delete=function(C){var k=this.__data__,W=Pr(k,C);return!(W<0)&&(W==k.length-1?k.pop():aa.call(k,W,1),--this.size,!0)},sn.prototype.get=function(C){var k=this.__data__,W=Pr(k,C);return W<0?void 0:k[W][1]},sn.prototype.has=function(C){return Pr(this.__data__,C)>-1},sn.prototype.set=function(C,k){var W=this.__data__,ee=Pr(W,C);return ee<0?(++this.size,W.push([C,k])):W[ee][1]=k,this},On.prototype.clear=function(){this.size=0,this.__data__={hash:new Tn,map:new(pr||sn),string:new Tn}},On.prototype.delete=function(C){var k=gr(this,C).delete(C);return this.size-=k?1:0,k},On.prototype.get=function(C){return gr(this,C).get(C)},On.prototype.has=function(C){return gr(this,C).has(C)},On.prototype.set=function(C,k){var W=gr(this,C),ee=W.size;return W.set(C,k),this.size+=W.size==ee?0:1,this},zr.prototype.add=zr.prototype.push=function(C){return this.__data__.set(C,r),this},zr.prototype.has=function(C){return this.__data__.has(C)},Rn.prototype.clear=function(){this.__data__=new sn,this.size=0},Rn.prototype.delete=function(C){var k=this.__data__,W=k.delete(C);return this.size=k.size,W},Rn.prototype.get=function(C){return this.__data__.get(C)},Rn.prototype.has=function(C){return this.__data__.has(C)},Rn.prototype.set=function(C,k){var W=this.__data__;if(W instanceof sn){var ee=W.__data__;if(!pr||ee.length<199)return ee.push([C,k]),this.size=++W.size,this;W=this.__data__=new On(ee)}return W.set(C,k),this.size=W.size,this};var mr=Ds?function(C){return C==null?[]:(C=Object(C),function(k,W){for(var ee=-1,ze=k==null?0:k.length,X=0,he=[];++ee<ze;){var Je=k[ee];W(Je,ee,k)&&(he[X++]=Je)}return he}(Ds(C),function(k){return js.call(C,k)}))}:function(){return[]},_n=Ln;function Us(C,k){return!!(k=k??u)&&(typeof C=="number"||Se.test(C))&&C>-1&&C%1==0&&C<k}function Gn(C){if(C!=null){try{return Bi.call(C)}catch{}try{return C+""}catch{}}return""}function ts(C,k){return C===k||C!=C&&k!=k}(Yi&&_n(new Yi(new ArrayBuffer(1)))!=Ue||pr&&_n(new pr)!=F||Ki&&_n(Ki.resolve())!=T||Qi&&_n(new Qi)!=oe||Ji&&_n(new Ji)!=me)&&(_n=function(C){var k=Ln(C),W=k==S?C.constructor:void 0,ee=W?Gn(W):"";if(ee)switch(ee){case la:return Ue;case ha:return F;case fi:return T;case fa:return oe;case di:return me}return k});var Ws=Zi(function(){return arguments}())?Zi:function(C){return Wr(C)&&rn.call(C,"callee")&&!js.call(C,"callee")},Ur=Array.isArray,mi=ua||function(){return!1};function ns(C){if(!Vs(C))return!1;var k=Ln(C);return k==x||k==E||k==f||k==ne}function Hs(C){return typeof C=="number"&&C>-1&&C%1==0&&C<=u}function Vs(C){var k=typeof C;return C!=null&&(k=="object"||k=="function")}function Wr(C){return C!=null&&typeof C=="object"}var Bs=li?function(C){return function(k){return C(k)}}(li):function(C){return Wr(C)&&Hs(C.length)&&!!we[Ln(C)]};function rs(C){return(k=C)!=null&&Hs(k.length)&&!ns(k)?da(C):Ps(C);var k}o.exports=function(C,k){return qs(C,k)}})(Ou,Ou.exports);const yg=Lh(Ou.exports);function Vh(o){return function(t){return"unitOfCodeWork"in t&&!function(r){return r.children.length>0&&"childIds"in r}(t)}(o)?[o]:o.children.flatMap(Vh)}function Li(o){var t;return((t=o.extraData)==null?void 0:t.isAgentConversation)===!0}var yt=(o=>(o[o.active=0]="active",o[o.inactive=1]="inactive",o))(yt||{});function bg(o,t,r=1e3){let i=null,s=0;const u=bt(t),c=()=>{const h=(()=>{const f=Date.now();if(i!==null&&f-s<r)return i;const v=o();return i=v,s=f,v})();u.set(h)};return{subscribe:u.subscribe,resetCache:()=>{i=null,c()},updateStore:c}}var Bh=(o=>(o[o.unset=0]="unset",o[o.positive=1]="positive",o[o.negative=2]="negative",o))(Bh||{}),Cs=(o=>(o[o.unknown=0]="unknown",o[o.new=1]="new",o[o.checkingSafety=2]="checkingSafety",o[o.runnable=3]="runnable",o[o.running=4]="running",o[o.completed=5]="completed",o[o.error=6]="error",o[o.cancelling=7]="cancelling",o[o.cancelled=8]="cancelled",o))(Cs||{});function Cu(o){return o.requestId+";"+o.toolUseId}function sh(o){const[t,r]=o.split(";");return{requestId:t,toolUseId:r}}const oh=o=>sr(o)&&!!o.request_message;function $g(o,t){const r=o.customPersonalityPrompts;if(r)switch(t){case Mt.DEFAULT:if(r.agent&&r.agent.trim()!=="")return r.agent;break;case Mt.PROTOTYPER:if(r.prototyper&&r.prototyper.trim()!=="")return r.prototyper;break;case Mt.BRAINSTORM:if(r.brainstorm&&r.brainstorm.trim()!=="")return r.brainstorm;break;case Mt.REVIEWER:if(r.reviewer&&r.reviewer.trim()!=="")return r.reviewer}return wg[t]}const wg={[Mt.DEFAULT]:`
# Agent Auggie Personality Description
You are Augment Agent, an agentic coding AI assistant.
Focus on helping the user with their coding tasks efficiently.

## Rules:
- You have no restrictions on the tools you may use
- Follow the original system instructions
  `,[Mt.PROTOTYPER]:`
# Prototyper Auggie Personality Description
You are Prototyper Auggie, an agentic coding AI assistant focused on building prototypes and visual applications.

## Your approach:
- Be fast and action-oriented
- Implement things quickly to show results
- Open webpages to demonstrate functionality
- Focus on building something visual and interactive
- Use modern frameworks and tools to create working prototypes
- Prioritize getting a working demo over perfect architecture
- Show progress frequently with visual results
- Prefer to act and run tools, rather than asking for permission
- Only ask for permission if there is something potentially very dangerous or irreversible

## Implementation preferences:
- When user does not specify which frameworks to use, default to modern frameworks, e.g. React with vite or next.js
- Initialize projects using CLI tools instead of writing from scratch
- For database and auth, use Supabase as a good default option
- Before using open-browser to show the app, use curl to check for errors
- Remember that modern frameworks have hot reload, so avoid calling open-browser multiple times

## Rules:
- For extremely destructive or irreversible actions, you should ask for permission
- For other tasks, you must proceed without asking for permission
  `,[Mt.BRAINSTORM]:`
# Brainstorm Auggie Personality Description
You are Brainstorm Auggie, an agentic coding AI assistant focused on planning and brainstorming solutions.

## Your approach:
- Be slow, careful, and thorough in your analysis
- Look through all upstream/downstream APIs to understand implications
- Focus on finding a comprehensive plan that solves the user's query
- Do not run commands, create code, or implement solutions directly
- Your job is to be introspective and think deeply about the problem
- Brainstorm multiple approaches and evaluate their tradeoffs
- Consider edge cases and potential issues with each approach

## Planning preferences:
- Analyze the codebase thoroughly before suggesting changes
- Consider multiple implementation options with pros and cons
- Identify potential risks and challenges for each approach
- Create detailed, step-by-step plans for implementation
- Provide reasoning for architectural decisions
- Consider performance, maintainability, and scalability
- Do not execute the plan - your role is to provide guidance only

## Rules:
- Prefer information gathering and non-destructive tools
- Prefer non-destructive and non-modifying tools
- You must never execute code, modify the codebase, or make changes
- Consider using Mermaid diagrams to help visualize complex concepts
- Once you have a proposal, please examine it critically, and do a revision before finalizing
  `,[Mt.REVIEWER]:`
# Reviewer Auggie Personality Description
You are Reviewer Auggie, an agentic coding AI assistant focused on reviewing code changes and identifying potential issues.

## Your approach:
- Act like a code detective to find potential bugs and issues
- Use git commands to analyze changes against the merge base
- Be super inquisitive and look for anything suspicious
- Build a mental model of what is happening in the code change
- Analyze API implications and downstream effects
- Guard the codebase from potential negative side effects
- Focus on understanding the changes from first principles

## Review preferences:
- Use git and GitHub tools to get code history information
- Compare changes against the logical base or merge base
- Look for edge cases and potential bugs
- Analyze API contracts and potential breaking changes
- Consider performance implications
- Check for security vulnerabilities
- Verify test coverage for the changes

## Rules:
- Use git commands and GitHub API to analyze code changes
- Be thorough and methodical in your analysis
- Focus on finding potential issues rather than implementing solutions
- Provide constructive feedback with specific examples
- Consider both the technical implementation and the broader impact
  `};class et{constructor(t,r,i,s){_(this,"_state");_(this,"_subscribers",new Set);_(this,"_focusModel",new Ph);_(this,"_onSendExchangeListeners",[]);_(this,"_onNewConversationListeners",[]);_(this,"_onHistoryDeleteListeners",[]);_(this,"_onBeforeChangeConversationListeners",[]);_(this,"_totalCharactersCacheThrottleMs",1e3);_(this,"_totalCharactersStore");_(this,"subscribe",t=>(this._subscribers.add(t),t(this),()=>{this._subscribers.delete(t)}));_(this,"setConversation",(t,r=!0,i=!0)=>{const s=t.id!==this._state.id;s&&i&&(t.toolUseStates=Object.fromEntries(Object.entries(t.toolUseStates??{}).map(([c,h])=>{if(h.requestId&&h.toolUseId){const{requestId:f,toolUseId:v}=sh(c);return f===h.requestId&&v===h.toolUseId||console.warn("Tool use state key does not match request and tool use IDs. Got key ",c,"but object has ",Cu(h)),[c,h]}return[c,{...h,...sh(c)}]})),(t=this._notifyBeforeChangeConversation(this._state,t)).lastInteractedAtIso=new Date().toISOString()),r&&s&&this.isValid&&(this.saveDraftActiveContextIds(),this._unloadContextFromConversation(this._state));const u=et.isEmpty(t);if(s&&u){const c=this._state.draftExchange;c&&(t.draftExchange=c)}return this._state=t,this._focusModel.setItems(this._state.chatHistory.filter(sr)),this._focusModel.initFocusIdx(-1),this._subscribers.forEach(c=>c(this)),this._saveConversation(this._state),s&&(this._loadContextFromConversation(t),this.loadDraftActiveContextIds(),this._onNewConversationListeners.forEach(c=>c())),!0});_(this,"update",t=>{this.setConversation({...this._state,...t}),this._totalCharactersStore.updateStore()});_(this,"toggleIsPinned",()=>{this.update({isPinned:!this.isPinned})});_(this,"setName",t=>{this.update({name:t})});_(this,"setSelectedModelId",t=>{this.update({selectedModelId:t})});_(this,"updateFeedback",(t,r)=>{this.update({feedbackStates:{...this._state.feedbackStates,[t]:r}})});_(this,"updateToolUseState",t=>{this.update({toolUseStates:{...this._state.toolUseStates,[Cu(t)]:t}})});_(this,"getToolUseState",(t,r)=>t===void 0||r===void 0||this.toolUseStates===void 0?{phase:Cs.unknown,requestId:t??"",toolUseId:r??""}:this.toolUseStates[Cu({requestId:t,toolUseId:r})]||{phase:Cs.new});_(this,"getLastToolUseState",()=>{var i,s;const t=this.lastExchange;if(!t)return{phase:Cs.unknown};const r=(((i=t==null?void 0:t.structured_output_nodes)==null?void 0:i.filter(u=>u.type===Ms.TOOL_USE))??[]).at(-1);return r?this.getToolUseState(t.request_id,(s=r.tool_use)==null?void 0:s.tool_use_id):{phase:Cs.unknown}});_(this,"addExchange",t=>{const r=[...this._state.chatHistory,t];let i;sr(t)&&(i=t.request_id?{...this._state.feedbackStates,[t.request_id]:{selectedRating:Bh.unset,feedbackNote:""}}:void 0),this.update({chatHistory:r,...i?{feedbackStates:i}:{},lastUrl:void 0})});_(this,"resetShareUrl",()=>{this.update({lastUrl:void 0})});_(this,"updateExchangeById",(t,r,i=!1)=>{var h;const s=this.exchangeWithRequestId(r);if(s===null)return console.warn("No exchange with this request ID found."),!1;i&&t.response_text!==void 0&&(t.response_text=(s.response_text??"")+(t.response_text??"")),i&&(t.structured_output_nodes=[...s.structured_output_nodes??[],...t.structured_output_nodes??[]]),i&&t.workspace_file_chunks!==void 0&&(t.workspace_file_chunks=[...s.workspace_file_chunks??[],...t.workspace_file_chunks??[]]);const u=(h=(t.structured_output_nodes||[]).find(f=>f.type===Ms.MAIN_TEXT_FINISHED))==null?void 0:h.content;u&&u!==t.response_text&&(t.response_text=u);let c=this._state.isShareable||Ri({...s,...t});return this.update({chatHistory:this.chatHistory.map(f=>f.request_id===r?{...f,...t}:f),isShareable:c}),!0});_(this,"clearMessagesFromHistory",t=>{this.update({chatHistory:this.chatHistory.filter(r=>!r.request_id||!t.has(r.request_id))}),this._extensionClient.clearMetadataFor({requestIds:Array.from(t)})});_(this,"clearHistory",()=>{this._extensionClient.clearMetadataFor({requestIds:this.requestIds}),this.update({chatHistory:[]})});_(this,"clearHistoryFrom",async(t,r=!0)=>{const i=this.historyFrom(t,r),s=i.map(u=>u.request_id).filter(u=>u!==void 0);this.update({chatHistory:this.historyTo(t,!r)}),this._extensionClient.clearMetadataFor({requestIds:s}),i.forEach(u=>{this._onHistoryDeleteListeners.forEach(c=>c(u))})});_(this,"clearMessageFromHistory",t=>{this.update({chatHistory:this.chatHistory.filter(r=>r.request_id!==t)}),this._extensionClient.clearMetadataFor({requestIds:[t]})});_(this,"historyTo",(t,r=!1)=>{const i=this.chatHistory.findIndex(s=>s.request_id===t);return i===-1?[]:this.chatHistory.slice(0,r?i+1:i)});_(this,"historyFrom",(t,r=!0)=>{const i=this.chatHistory.findIndex(s=>s.request_id===t);return i===-1?[]:this.chatHistory.slice(r?i:i+1)});_(this,"resendLastExchange",async()=>{const t=this.lastExchange;if(t&&!this.awaitingReply)return this.resendTurn(t)});_(this,"resendTurn",t=>this.awaitingReply?Promise.resolve():(this._removeTurn(t),this.sendExchange({chatItemType:t.chatItemType,request_message:t.request_message,rich_text_json_repr:t.rich_text_json_repr,status:Kt.draft,mentioned_items:t.mentioned_items,structured_request_nodes:t.structured_request_nodes,disableSelectedCodeDetails:t.disableSelectedCodeDetails,disableHistory:t.disableHistory})));_(this,"_removeTurn",t=>{this.update({chatHistory:this.chatHistory.filter(r=>r!==t&&(!t.request_id||r.request_id!==t.request_id))})});_(this,"exchangeWithRequestId",t=>this.chatHistory.find(r=>r.request_id===t)||null);_(this,"resetTotalCharactersCache",()=>{this._totalCharactersStore.resetCache()});_(this,"markSeen",async t=>{if(!t.request_id||!this.chatHistory.find(i=>i.request_id===t.request_id))return;const r={seen_state:ws.seen};this.update({chatHistory:this.chatHistory.map(i=>i.request_id===t.request_id?{...i,...r}:i)})});_(this,"createStructuredRequestNodes",t=>this._jsonToStructuredRequest(t));_(this,"saveDraftMentions",t=>{if(!this.draftExchange)return;const r=t.filter(i=>!i.personality);this.update({draftExchange:{...this.draftExchange,mentioned_items:r}})});_(this,"saveDraftActiveContextIds",()=>{const t=this._specialContextInputModel.recentActiveItems.map(r=>r.id);this.update({draftActiveContextIds:t})});_(this,"loadDraftActiveContextIds",()=>{const t=new Set(this.draftActiveContextIds??[]),r=this._specialContextInputModel.recentItems.filter(s=>t.has(s.id)||s.recentFile||s.selection||s.sourceFolder),i=this._specialContextInputModel.recentItems.filter(s=>!(t.has(s.id)||s.recentFile||s.selection||s.sourceFolder));this._specialContextInputModel.markItemsActive(r.reverse()),this._specialContextInputModel.markItemsInactive(i.reverse())});_(this,"saveDraftExchange",(t,r)=>{var c,h,f;const i=t!==((c=this.draftExchange)==null?void 0:c.request_message),s=r!==((h=this.draftExchange)==null?void 0:h.rich_text_json_repr);if(!i&&!s)return;const u=(f=this.draftExchange)==null?void 0:f.mentioned_items;this.update({draftExchange:{request_message:t,rich_text_json_repr:r,mentioned_items:u,status:Kt.draft}})});_(this,"clearDraftExchange",()=>{const t=this.draftExchange;return this.update({draftExchange:void 0}),t});_(this,"sendDraftExchange",()=>{if(this._extensionClient.triggerUsedChatMetric(),!this.canSendDraft||!this.draftExchange)return!1;const t=this.clearDraftExchange();if(!t)return!1;const r=this._chatFlagModel.enableChatMultimodal&&t.rich_text_json_repr?this._jsonToStructuredRequest(t.rich_text_json_repr):void 0;return this.sendExchange({...t,structured_request_nodes:r,model_id:this.selectedModelId??void 0}).then(()=>{var c,h;const i=!this.name&&this.chatHistory.length===1&&((c=this.firstExchange)==null?void 0:c.request_id)===this.chatHistory[0].request_id,s=Li(this)&&((h=this._state.extraData)==null?void 0:h.hasAgentOnboarded)&&(u=this.chatHistory,u.filter(f=>oh(f))).length===2;var u;this._chatFlagModel.summaryTitles&&(i||s)&&this.updateConversationTitle()}).finally(()=>{var i;Li(this)&&this._extensionClient.reportAgentRequestEvent({eventName:jp.sentUserMessage,conversationId:this.id,requestId:((i=this.lastExchange)==null?void 0:i.request_id)??"UNKNOWN_REQUEST_ID",chatHistoryLength:this.chatHistory.length})}),this.focusModel.setFocusIdx(void 0),!0});_(this,"cancelMessage",async()=>{var t;this.canCancelMessage&&((t=this.lastExchange)!=null&&t.request_id)&&(this.updateExchangeById({status:Kt.cancelled},this.lastExchange.request_id),await this._extensionClient.cancelChatStream(this.lastExchange.request_id))});_(this,"sendInstructionExchange",async(t,r)=>{let i=`temp-fe-${crypto.randomUUID()}`;const s={status:Kt.sent,request_id:i,request_message:t,model_id:this.selectedModelId??void 0,structured_output_nodes:[],seen_state:ws.unseen,timestamp:new Date().toISOString()};this.addExchange(s);for await(const u of this._extensionClient.sendInstructionMessage(s,r)){if(!this.updateExchangeById(u,i,!0))return;i=u.request_id||i}});_(this,"updateConversationTitle",async()=>{const{responseText:t}=await this.sendSummaryExchange();this.update({name:t})});_(this,"sendSummaryExchange",()=>{const t={status:Kt.sent,request_message:"Please provide a clear and concise summary of our conversation so far. The summary must be less than 6 words long. The summary must contain the key points of the conversation. The summary must be in the form of a title which will represent the conversation. The response should not include any additional formatting such as wrapping the response with quotation marks.",model_id:this.selectedModelId??void 0,chatItemType:xs.summaryTitle,disableRetrieval:!0,disableSelectedCodeDetails:!0};return this.sendSilentExchange(t)});_(this,"generateCommitMessage",async()=>{let t=`temp-fe-${crypto.randomUUID()}`;const r={status:Kt.sent,request_id:t,request_message:"Please generate a commit message based on the diff of my staged and unstaged changes.",model_id:this.selectedModelId??void 0,mentioned_items:[],seen_state:ws.unseen,chatItemType:xs.generateCommitMessage,disableSelectedCodeDetails:!0,disableHistory:!0,timestamp:new Date().toISOString()};this.addExchange(r);for await(const i of this._extensionClient.generateCommitMessage()){if(!this.updateExchangeById(i,t,!0))return;t=i.request_id||t}});_(this,"sendExchange",async t=>{var u;this.updateLastInteraction();let r=`temp-fe-${crypto.randomUUID()}`,i=this._chatFlagModel.isModelIdValid(t.model_id)?t.model_id:void 0;t=await this._addIdeStateNode(ah(t));const s={status:Kt.sent,request_id:r,request_message:t.request_message,rich_text_json_repr:t.rich_text_json_repr,model_id:i,mentioned_items:t.mentioned_items,structured_output_nodes:t.structured_output_nodes,seen_state:ws.unseen,chatItemType:t.chatItemType,disableSelectedCodeDetails:t.disableSelectedCodeDetails,disableHistory:t.disableHistory,structured_request_nodes:t.structured_request_nodes,timestamp:new Date().toISOString()};this.addExchange(s),this._loadContextFromExchange(s),this._onSendExchangeListeners.forEach(c=>c(s));for await(const c of this.sendUserMessage(r,s,!1)){if(((u=this.exchangeWithRequestId(r))==null?void 0:u.status)!==Kt.sent||!this.updateExchangeById(c,r,!0))return;r=c.request_id||r}});_(this,"sendSuggestedQuestion",t=>{this.sendExchange({request_message:t,status:Kt.draft}),this._extensionClient.triggerUsedChatMetric(),this._extensionClient.reportWebviewClientEvent(ku.chatUseSuggestedQuestion)});_(this,"recoverAllExchanges",async()=>{await Promise.all(this.recoverableExchanges.map(this.recoverExchange))});_(this,"recoverExchange",async t=>{var s;if(!t.request_id||t.status!==Kt.sent)return;let r=t.request_id;const i=(s=t.structured_output_nodes)==null?void 0:s.filter(u=>u.type===Ms.AGENT_MEMORY);this.updateExchangeById({...t,response_text:"",structured_output_nodes:i??[]},r);for await(const u of this.getChatStream(t)){if(!this.updateExchangeById(u,r,!0))return;r=u.request_id||r}});_(this,"_loadContextFromConversation",t=>{t.chatHistory.forEach(r=>{sr(r)&&this._loadContextFromExchange(r)})});_(this,"_loadContextFromExchange",t=>{t.mentioned_items&&(this._specialContextInputModel.updateItems(t.mentioned_items,[]),this._specialContextInputModel.markItemsActive(t.mentioned_items))});_(this,"_unloadContextFromConversation",t=>{t.chatHistory.forEach(r=>{sr(r)&&this._unloadContextFromExchange(r)})});_(this,"_unloadContextFromExchange",t=>{t.mentioned_items&&this._specialContextInputModel.updateItems([],t.mentioned_items)});_(this,"updateLastInteraction",()=>{this.update({lastInteractedAtIso:new Date().toISOString()})});_(this,"_jsonToStructuredRequest",t=>{const r=[],i=u=>{var h;const c=r.at(-1);if((c==null?void 0:c.type)===An.TEXT){const f=((h=c.text_node)==null?void 0:h.content)??"",v={...c,text_node:{content:f+u}};r[r.length-1]=v}else r.push({id:r.length,type:An.TEXT,text_node:{content:u}})},s=u=>{var c,h,f,v;if(u.type==="doc"||u.type==="paragraph")for(const m of u.content??[])s(m);else if(u.type==="hardBreak")i(`
`);else if(u.type==="text")i(u.text??"");else if(u.type==="image"){if(typeof((c=u.attrs)==null?void 0:c.src)!="string")return void console.error("Image source is not a string: ",(h=u.attrs)==null?void 0:h.src);if(u.attrs.isLoading)return;const m=(f=u.attrs)==null?void 0:f.title,b=this._fileNameToImageFormat(m);r.push({id:r.length,type:An.IMAGE_ID,image_id_node:{image_id:u.attrs.src,format:b}})}else if(u.type==="mention"){const m=(v=u.attrs)==null?void 0:v.data;m&&na(m)?r.push({id:r.length,type:An.TEXT,text_node:{content:$g(this._chatFlagModel,m.personality.type)}}):i(`@${m==null?void 0:m.name}`)}};return s(t),r});this._extensionClient=t,this._chatFlagModel=r,this._specialContextInputModel=i,this._saveConversation=s,this._state={...et.create()},this._totalCharactersStore=this._createTotalCharactersStore()}_createTotalCharactersStore(){return bg(()=>{let t=0;const r=this._state.chatHistory;return this._convertHistoryToExchanges(r).forEach(i=>{t+=JSON.stringify(i).length}),this._state.draftExchange&&(t+=JSON.stringify(this._state.draftExchange).length),t},0,this._totalCharactersCacheThrottleMs)}async decidePersonaType(){var t;try{return(((t=(await this._extensionClient.getWorkspaceInfo()).trackedFileCount)==null?void 0:t.reduce((i,s)=>i+s,0))||0)<=4?Mt.PROTOTYPER:Mt.DEFAULT}catch(r){return console.error("Error determining persona type:",r),Mt.DEFAULT}}static create(t={}){const r=new Date().toISOString();return{id:crypto.randomUUID(),name:void 0,createdAtIso:r,lastInteractedAtIso:r,chatHistory:[],feedbackStates:{},toolUseStates:{},draftExchange:void 0,draftActiveContextIds:void 0,selectedModelId:void 0,requestIds:[],isPinned:!1,lastUrl:void 0,isShareable:!1,extraData:{},personaType:Mt.DEFAULT,...t}}static toSentenceCase(t){return t.charAt(0).toUpperCase()+t.slice(1)}static getDisplayName(t){var u,c;const r=this._filterToExchanges(t);let i;var s;return s=t,i=((u=s.extraData)==null?void 0:u.isAutofix)===!0?"Autofix Chat":Li(t)?"New Agent":"New Chat",et.toSentenceCase(t.name||((c=r[0])==null?void 0:c.request_message)||i)}static _filterToExchanges(t){return t.chatHistory.filter(r=>sr(r))}static isEmpty(t){var r;return t.chatHistory.filter(i=>sr(i)).length===0&&!((r=t.draftExchange)!=null&&r.request_message)}static isNamed(t){return t.name!==void 0&&t.name!==""}static getTime(t,r){return r==="lastMessageTimestamp"?et.lastMessageTimestamp(t):r==="lastInteractedAt"?et.lastInteractedAt(t):et.createdAt(t)}static createdAt(t){return new Date(t.createdAtIso)}static lastInteractedAt(t){return new Date(t.lastInteractedAtIso)}static lastMessageTimestamp(t){const r=this._filterToExchanges(t);if(r.length===0)return this.createdAt(t);const i=r[r.length-1];return i.timestamp?new Date(i.timestamp):this.createdAt(t)}static isValid(t){return t.id!==void 0&&(!et.isEmpty(t)||et.isNamed(t))}onBeforeChangeConversation(t){return this._onBeforeChangeConversationListeners.push(t),()=>{this._onBeforeChangeConversationListeners=this._onBeforeChangeConversationListeners.filter(r=>r!==t)}}_notifyBeforeChangeConversation(t,r){let i=r;for(const s of this._onBeforeChangeConversationListeners){const u=s(t,i);u!==void 0&&(i=u)}return i}get extraData(){return this._state.extraData}set extraData(t){this.update({extraData:t})}get focusModel(){return this._focusModel}get isValid(){return et.isValid(this._state)}get id(){return this._state.id}get name(){return this._state.name}get personaType(){return this._state.personaType??Mt.DEFAULT}get displayName(){return et.getDisplayName(this._state)}get createdAtIso(){return this._state.createdAtIso}get createdAt(){return et.createdAt(this._state)}get chatHistory(){return this._state.chatHistory}get feedbackStates(){return this._state.feedbackStates}get toolUseStates(){return this._state.toolUseStates}get draftExchange(){return this._state.draftExchange}get selectedModelId(){return this._state.selectedModelId}get isPinned(){return!!this._state.isPinned}get extensionClient(){return this._extensionClient}addChatItem(t){this.addExchange(t)}get requestIds(){return this._state.chatHistory.map(t=>t.request_id).filter(t=>t!==void 0)}get hasDraft(){var i;const t=(((i=this.draftExchange)==null?void 0:i.request_message)??"").trim()!=="",r=this.hasImagesInDraft();return t||r}hasImagesInDraft(){var i;const t=(i=this.draftExchange)==null?void 0:i.rich_text_json_repr;if(!t)return!1;const r=s=>Array.isArray(s)?s.some(r):!!s&&(s.type==="image"||!(!s.content||!Array.isArray(s.content))&&s.content.some(r));return r(t)}get canSendDraft(){return this.hasDraft&&!this.awaitingReply}get canCancelMessage(){return this.awaitingReply}get firstExchange(){const t=et._filterToExchanges(this);return t.length===0?null:t[0]}get lastExchange(){const t=et._filterToExchanges(this);return t.length===0?null:t[t.length-1]}get canClearHistory(){return this._state.chatHistory.length!==0&&!this.awaitingReply}get recoverableExchanges(){return this._state.chatHistory.filter(t=>sr(t)&&t.status===Kt.sent)}get successfulMessages(){return this._state.chatHistory.filter(t=>Ri(t)||Es(t))}get totalCharactersStore(){return this._totalCharactersStore}_convertHistoryToExchanges(t){if(t.length===0)return[];const r=[];for(const i of t)if(Ri(i))r.push(Cg(i));else if(Es(i)&&i.fromTimestamp!==void 0&&i.toTimestamp!==void 0&&i.revertTarget){const s=xg(i,1),u={request_message:"",response_text:"",request_id:i.request_id||crypto.randomUUID(),request_nodes:[s],response_nodes:[]};r.push(u)}return r}get awaitingReply(){return this.lastExchange!==null&&this.lastExchange.status===Kt.sent}get lastInteractedAtIso(){return this._state.lastInteractedAtIso}get draftActiveContextIds(){return this._state.draftActiveContextIds}async sendSilentExchange(t){const r=crypto.randomUUID();let i,s="";const u=await this._addIdeStateNode(ah({...t,request_id:r,status:Kt.sent,timestamp:new Date().toISOString()}));for await(const c of this.sendUserMessage(r,u,!0))c.response_text&&(s+=c.response_text),c.request_id&&(i=c.request_id);return{responseText:s,requestId:i}}async*getChatStream(t){t.request_id&&(yield*this._extensionClient.getExistingChatStream(t,{flags:this._chatFlagModel}))}async*sendUserMessage(t,r,i){var m;const s=this._specialContextInputModel.chatActiveContext;let u=this.successfulMessages;if(r.chatItemType===xs.summaryTitle){const b=u.findIndex(x=>x.chatItemType!==xs.agentOnboarding&&oh(x));b!==-1&&(u=u.slice(b))}const c=r.disableHistory===!0?[]:u;let h=this._convertHistoryToExchanges(c),f=this.personaType;if(r.structured_request_nodes){const b=r.structured_request_nodes.find(x=>x.type===An.CHANGE_PERSONALITY);b&&b.change_personality_node&&(f=b.change_personality_node.personality_type)}const v={text:r.request_message,chatHistory:h,silent:i,modelId:r.model_id,context:s,userSpecifiedFiles:s.userSpecifiedFiles,externalSourceIds:(m=s.externalSources)==null?void 0:m.map(b=>b.id),disableRetrieval:r.disableRetrieval??!1,disableSelectedCodeDetails:r.disableSelectedCodeDetails??!1,nodes:r.structured_request_nodes,memoriesInfo:r.memoriesInfo,personaType:f,conversationId:this.id};yield*this._extensionClient.startChatStreamWithRetry(t,v,{flags:this._chatFlagModel})}onSendExchange(t){return this._onSendExchangeListeners.push(t),()=>{this._onSendExchangeListeners=this._onSendExchangeListeners.filter(r=>r!==t)}}onNewConversation(t){return this._onNewConversationListeners.push(t),()=>{this._onNewConversationListeners=this._onNewConversationListeners.filter(r=>r!==t)}}onHistoryDelete(t){return this._onHistoryDeleteListeners.push(t),()=>{this._onHistoryDeleteListeners=this._onHistoryDeleteListeners.filter(r=>r!==t)}}updateChatItem(t,r){return this.chatHistory.find(i=>i.request_id===t)===null?(console.warn("No exchange with this request ID found."),!1):(this.update({chatHistory:this.chatHistory.map(i=>i.request_id===t?{...i,...r}:i)}),!0)}_fileNameToImageFormat(t){var i;switch((i=t.split(".").at(-1))==null?void 0:i.toLowerCase()){case"jpeg":case"jpg":return $s.JPEG;case"png":return $s.PNG;case"gif":return $s.GIF;case"webp":return $s.WEBP;default:return $s.IMAGE_FORMAT_UNSPECIFIED}}async _addIdeStateNode(t){let r=(t.structured_request_nodes??[]).filter(s=>s.type!==An.IDE_STATE);const i=await this._extensionClient.getChatRequestIdeState();return i?(r=[...r,{id:Gh(r)+1,type:An.IDE_STATE,ide_state_node:i}],{...t,structured_request_nodes:r}):t}}function xg(o,t){const r=(Es(o),o.fromTimestamp),i=(Es(o),o.toTimestamp),s=Es(o)&&o.revertTarget!==void 0;return{id:t,type:An.CHECKPOINT_REF,checkpoint_ref_node:{request_id:o.request_id||"",from_timestamp:r,to_timestamp:i,source:s?qp.CHECKPOINT_REVERT:void 0}}}function Cg(o){const t=(o.structured_output_nodes??[]).filter(r=>r.type===Ms.RAW_RESPONSE||r.type===Ms.TOOL_USE);return{request_message:o.request_message,response_text:o.response_text??"",request_id:o.request_id||"",request_nodes:o.structured_request_nodes??[],response_nodes:t}}function Gh(o){return o.length>0?Math.max(...o.map(t=>t.id)):0}function ah(o){var t;if(o.request_message.length>0&&!((t=o.structured_request_nodes)!=null&&t.some(r=>r.type===An.TEXT))){let r=o.structured_request_nodes??[];return r=[...r,{id:Gh(r)+1,type:An.TEXT,text_node:{content:o.request_message}}],{...o,structured_request_nodes:r}}return o}class Sg{constructor(t=!0,r=setTimeout){_(this,"_notify",new Set);_(this,"_clearTimeout",t=>{t.timeoutId&&clearTimeout(t.timeoutId)});_(this,"_schedule",t=>{if(!this._started||t.date&&(t.timeout=t.date.getTime()-Date.now(),t.timeout<0))return;const r=this._setTimeout;t.timeoutId=r(this._handle,t.timeout,t)});_(this,"_handle",t=>{t.notify(),t.date?this._notify.delete(t):t.once||this._schedule(t)});_(this,"dispose",()=>{this._notify.forEach(this._clearTimeout),this._notify.clear()});this._started=t,this._setTimeout=r}start(){return this._started||(this._started=!0,this._notify.forEach(this._schedule)),this}stop(){return this._started=!1,this._notify.forEach(this._clearTimeout),this}get isStarted(){return this._started}set isStarted(t){t?this.start():this.stop()}once(t,r){return this._register(t,r,!0)}interval(t,r){return this._register(t,r,!1)}at(t,r){return this._register(0,r,!1,typeof t=="number"?new Date(Date.now()+t):t)}reschedule(){this._notify.forEach(t=>{this._clearTimeout(t),this._schedule(t)})}_register(t,r,i,s){if(!t&&!s)return()=>{};const u={timeout:t,notify:r,once:i,date:s};return this._notify.add(u),this._schedule(u),()=>{this._clearTimeout(u),this._notify.delete(u)}}}class Ig{constructor(t=0,r=0,i=new Sg,s=bt("busy"),u=bt(!1)){_(this,"unsubNotify");_(this,"unsubMessage");_(this,"activity",()=>{this.idleStatus.set("busy"),this.idleScheduler.reschedule()});_(this,"focus",t=>{this.focusAfterIdle.set(t)});this._idleNotifyTimeout=t,this._idleMessageTimeout=r,this.idleScheduler=i,this.idleStatus=s,this.focusAfterIdle=u,this.idleNotifyTimeout=t,this.idleMessageTimeout=r}set idleMessageTimeout(t){var r;this._idleMessageTimeout!==t&&(this._idleMessageTimeout=t,(r=this.unsubMessage)==null||r.call(this),this.unsubMessage=this.idleScheduler.once(t,()=>{this.idleStatus.set("idle-message")}))}set idleNotifyTimeout(t){var r;this._idleNotifyTimeout!==t&&(this._idleNotifyTimeout=t,(r=this.unsubNotify)==null||r.call(this),this.unsubNotify=this.idleScheduler.once(t,()=>{this.idleStatus.set("idle-notify")}))}get idleMessageTimeout(){return this._idleMessageTimeout}get idleNotifyTimeout(){return this._idleNotifyTimeout}get notifyEnabled(){return this._idleNotifyTimeout>0}get messageEnabled(){return this._idleMessageTimeout>0}dispose(){var t,r;(t=this.unsubNotify)==null||t.call(this),(r=this.unsubMessage)==null||r.call(this),this.idleScheduler.dispose(),this.idleStatus.set("busy"),this.focusAfterIdle.set(!1)}}const Uo=bt("idle");class Yh{constructor(t,r,i,s={}){_(this,"_state",{currentConversationId:void 0,conversations:{},agentExecutionMode:"manual",isAgentEditsCollapsed:!0});_(this,"extensionClient");_(this,"_chatFlagsModel");_(this,"_currConversationModel");_(this,"subscribers",new Set);_(this,"idleMessageModel",new Ig);_(this,"isAgentEditsCollapsed");_(this,"agentExecutionMode");_(this,"sortConversationsBy");_(this,"onLoaded",async()=>{var r,i;const t=await this.extensionClient.getChatInitData();this._chatFlagsModel.update({enableEditableHistory:t.enableEditableHistory??!1,enablePreferenceCollection:t.enablePreferenceCollection??!1,enableRetrievalDataCollection:t.enableRetrievalDataCollection??!1,enableDebugFeatures:t.enableDebugFeatures??!1,enableRichTextHistory:t.useRichTextHistory??!0,modelDisplayNameToId:t.modelDisplayNameToId??{},fullFeatured:t.fullFeatured??!0,isRemoteAgentWindow:!1,remoteAgentId:void 0,smallSyncThreshold:t.smallSyncThreshold??Mp,bigSyncThreshold:t.bigSyncThreshold??kp,enableExternalSourcesInChat:t.enableExternalSourcesInChat??!1,enableSmartPaste:t.enableSmartPaste??!1,enableDirectApply:t.enableDirectApply??!1,summaryTitles:t.summaryTitles??!1,suggestedEditsAvailable:t.suggestedEditsAvailable??!1,enableShareService:t.enableShareService??!1,maxTrackableFileCount:t.maxTrackableFileCount??Ap,enableDesignSystemRichTextEditor:t.enableDesignSystemRichTextEditor??!1,enableSources:t.enableSources??!1,enableChatMermaidDiagrams:t.enableChatMermaidDiagrams??!1,smartPastePrecomputeMode:t.smartPastePrecomputeMode??Sp.visibleHover,useNewThreadsMenu:t.useNewThreadsMenu??!1,enableChatMermaidDiagramsMinVersion:t.enableChatMermaidDiagramsMinVersion??!1,idleNewSessionMessageTimeoutMs:t.idleNewSessionMessageTimeoutMs,idleNewSessionNotificationTimeoutMs:t.idleNewSessionNotificationTimeoutMs,enableChatMultimodal:t.enableChatMultimodal??!1,enableAgentMode:t.enableAgentMode??!1,agentMemoriesFilePathName:t.agentMemoriesFilePathName,enableRichCheckpointInfo:t.enableRichCheckpointInfo??!1,userTier:t.userTier??"unknown",truncateChatHistory:t.truncateChatHistory??!1,enableBackgroundAgents:t.enableBackgroundAgents??!1,enableVirtualizedMessageList:t.enableVirtualizedMessageList??!1,customPersonalityPrompts:t.customPersonalityPrompts??{},enablePersonalities:t.enablePersonalities??!1,memoryClassificationOnFirstToken:t.memoryClassificationOnFirstToken??!1,enableGenerateCommitMessage:t.enableGenerateCommitMessage??!1,enablePromptEnhancer:t.enablePromptEnhancer??!1,modelRegistry:t.modelRegistry??{},enableModelRegistry:t.enableModelRegistry??!1}),(i=(r=this.options).onLoaded)==null||i.call(r),this.notifySubscribers(),(t.enableBackgroundAgents??!1)&&this.extensionClient.getRemoteAgentStatus().then(s=>{this._chatFlagsModel.update({isRemoteAgentWindow:s.isRemoteAgentWindow,remoteAgentId:s.remoteAgentId}),this.notifySubscribers()})});_(this,"subscribe",t=>(this.subscribers.add(t),t(this),()=>{this.subscribers.delete(t)}));_(this,"initialize",t=>{this._state={...this._state,...this._host.getState()},t&&(this._state.conversations[t==null?void 0:t.id]=t),this._chatFlagsModel.fullFeatured&&((t==null?void 0:t.id)!==bu&&this.currentConversationId!==bu||(delete this._state.conversations[bu],this.setCurrentConversationToWelcome())),this._chatFlagsModel.subscribe(r=>{this.idleMessageModel.idleNotifyTimeout=r.idleNewSessionNotificationTimeoutMs,this.idleMessageModel.idleMessageTimeout=r.idleNewSessionMessageTimeoutMs}),this._state.conversations=Object.fromEntries(Object.entries(this._state.conversations).filter(([r,i])=>et.isValid(i))),this.initializeIsShareableState(),t?this.setCurrentConversation(t.id):this.setCurrentConversation(this.currentConversationId),this.subscribe(()=>this.idleMessageModel.activity()),this.setState(this._state)});_(this,"initializeIsShareableState",()=>{const t={...this._state.conversations};for(const[r,i]of Object.entries(t)){if(i.isShareable)continue;const s=i.chatHistory.some(u=>Ri(u));t[r]={...i,isShareable:s}}this._state.conversations=t});_(this,"updateChatState",t=>{this._state={...this._state,...t};const r=this._state.conversations,i=new Set;for(const[s,u]of Object.entries(r))u.isPinned&&i.add(s);this.setState(this._state),this.notifySubscribers()});_(this,"saveImmediate",()=>{this._host.setState(this._state)});_(this,"setState",Tu(t=>{this._host.setState({...t,isAgentEditsCollapsed:pt(this.isAgentEditsCollapsed),agentExecutionMode:pt(this.agentExecutionMode),sortConversationsBy:pt(this.sortConversationsBy)})},1e3,{maxWait:15e3}));_(this,"notifySubscribers",()=>{this.subscribers.forEach(t=>t(this))});_(this,"withWebviewClientEvent",(t,r)=>(...i)=>(this.extensionClient.reportWebviewClientEvent(t),r(...i)));_(this,"setCurrentConversationToWelcome",()=>{this.setCurrentConversation(),this._currConversationModel.setName("Welcome to Augment"),this._currConversationModel.addChatItem({chatItemType:xs.educateFeatures,request_id:crypto.randomUUID(),seen_state:ws.seen})});_(this,"popCurrentConversation",async()=>{var r,i;const t=this.currentConversationId;t&&await this.deleteConversation(t,((r=this.nextConversation)==null?void 0:r.id)??((i=this.previousConversation)==null?void 0:i.id))});_(this,"setCurrentConversation",async(t,r=!0)=>{let i;t===void 0?(this.deleteInvalidConversations(Li(this._currConversationModel)?"agent":"chat"),i=et.create({personaType:await this._currConversationModel.decidePersonaType()})):i=this._state.conversations[t]??et.create({personaType:await this._currConversationModel.decidePersonaType()});const s=this.conversations[this._currConversationModel.id]===void 0;this._currConversationModel.setConversation(i,!s,r),this._currConversationModel.recoverAllExchanges(),this._currConversationModel.resetTotalCharactersCache()});_(this,"saveConversation",t=>{this.updateChatState({conversations:{...this._state.conversations,[t.id]:t},currentConversationId:t.id})});_(this,"isConversationShareable",t=>{var r;return((r=this._state.conversations[t])==null?void 0:r.isShareable)??!0});_(this,"setSortConversationsBy",t=>{this.sortConversationsBy.set(t),this.updateChatState({})});_(this,"getConversationUrl",async t=>{const r=this._state.conversations[t];if(r.lastUrl)return r.lastUrl;Uo.set("copying");const i=r==null?void 0:r.chatHistory,s=i.reduce((h,f)=>(Ri(f)&&h.push({request_id:f.request_id||"",request_message:f.request_message,response_text:f.response_text||""}),h),[]);if(s.length===0)throw new Error("No chat history to share");const u=et.getDisplayName(r),c=await this.extensionClient.saveChat(t,s,u);if(c.data){let h=c.data.url;return this.updateChatState({conversations:{...this._state.conversations,[t]:{...r,lastUrl:h}}}),h}throw new Error("Failed to create URL")});_(this,"shareConversation",async t=>{if(t!==void 0)try{const r=await this.getConversationUrl(t);if(!r)return void Uo.set("idle");navigator.clipboard.writeText(r),Uo.set("copied")}catch{Uo.set("failed")}});_(this,"deleteConversations",async(t,r=void 0)=>{if(await this.extensionClient.openConfirmationModal({title:"Delete Conversation",message:`Are you sure you want to delete ${t.length>1?"these conversations":"this conversation"}?`,confirmButtonText:"Delete",cancelButtonText:"Cancel"})){const i=new Set(t);this.deleteConversationIds(i),this.currentConversationId&&i.has(this.currentConversationId)&&this.setCurrentConversation(r)}});_(this,"deleteConversation",async(t,r=void 0)=>{await this.deleteConversations([t],r)});_(this,"deleteConversationIds",async t=>{var i;const r=[];for(const s of t){const u=((i=this._state.conversations[s])==null?void 0:i.requestIds)??[];r.push(...u)}for(const s of Object.values(this._state.conversations))if(t.has(s.id)){for(const c of s.chatHistory)sr(c)&&this.deleteImagesInExchange(c);const u=s.draftExchange;u&&this.deleteImagesInExchange(u)}this.updateChatState({conversations:Object.fromEntries(Object.entries(this._state.conversations).filter(([s])=>!t.has(s)))}),this.extensionClient.clearMetadataFor({requestIds:r,conversationIds:Array.from(t)})});_(this,"deleteImagesInExchange",t=>{const r=new Set([...t.rich_text_json_repr?this.findImagesInJson(t.rich_text_json_repr):[],...t.structured_request_nodes?this.findImagesInStructuredRequest(t.structured_request_nodes):[]]);for(const i of r)this.deleteImage(i)});_(this,"findImagesInJson",t=>{const r=[],i=s=>{var u;if(s.type==="image"&&((u=s.attrs)!=null&&u.src))r.push(s.attrs.src);else if((s.type==="doc"||s.type==="paragraph")&&s.content)for(const c of s.content)i(c)};return i(t),r});_(this,"findImagesInStructuredRequest",t=>t.reduce((r,i)=>(i.type===An.IMAGE_ID&&i.image_id_node&&r.push(i.image_id_node.image_id),r),[]));_(this,"toggleConversationPinned",t=>{const r=this._state.conversations[t],i={...r,isPinned:!r.isPinned};this.updateChatState({conversations:{...this._state.conversations,[t]:i}}),t===this.currentConversationId&&this._currConversationModel.toggleIsPinned()});_(this,"renameConversation",(t,r)=>{const i={...this._state.conversations[t],name:r};this.updateChatState({conversations:{...this._state.conversations,[t]:i}}),t===this.currentConversationId&&this._currConversationModel.setName(r)});_(this,"smartPaste",(t,r,i,s)=>{const u=this._currConversationModel.historyTo(t,!0).filter(c=>Ri(c)).map(c=>({request_message:c.request_message,response_text:c.response_text||"",request_id:c.request_id||""}));this.extensionClient.smartPaste({generatedCode:r,chatHistory:u,targetFile:i??void 0,options:s})});_(this,"saveImage",async t=>await this.extensionClient.saveImage(t));_(this,"deleteImage",async t=>await this.extensionClient.deleteImage(t));_(this,"renderImage",async t=>await this.extensionClient.loadImage(t));this._asyncMsgSender=t,this._host=r,this._specialContextInputModel=i,this.options=s,this._chatFlagsModel=new Ip(s.initialFlags),this.extensionClient=new Ep(this._host,this._asyncMsgSender,this._chatFlagsModel),this._currConversationModel=new et(this.extensionClient,this._chatFlagsModel,this._specialContextInputModel,this.saveConversation),this.initialize(s.initialConversation),this.isAgentEditsCollapsed=bt(this._state.isAgentEditsCollapsed),this.agentExecutionMode=bt(this._state.agentExecutionMode??"manual"),this.sortConversationsBy=bt(this._state.sortConversationsBy??"lastMessageTimestamp"),this.onLoaded()}get flags(){return this._chatFlagsModel}get specialContextInputModel(){return this._specialContextInputModel}get currentConversationId(){return this._state.currentConversationId}get currentConversationModel(){return this._currConversationModel}get conversations(){return this._state.conversations}orderedConversations(t,r="desc",i){const s=t||this._state.sortConversationsBy||"lastMessageTimestamp";let u=Object.values(this._state.conversations);return i&&(u=u.filter(i)),u.sort((c,h)=>{const f=et.getTime(c,s).getTime(),v=et.getTime(h,s).getTime();return r==="asc"?f-v:v-f})}get nextConversation(){if(!this.currentConversationId)return;const t=this.orderedConversations(),r=t.findIndex(i=>i.id===this.currentConversationId);return t.length>r+1?t[r+1]:void 0}get previousConversation(){if(!this.currentConversationId)return;const t=this.orderedConversations(),r=t.findIndex(i=>i.id===this.currentConversationId);return r>0?t[r-1]:void 0}get host(){return this._host}deleteInvalidConversations(t="all"){const r=Object.keys(this.conversations).filter(i=>{const s=!et.isValid(this.conversations[i]),u=Li(this.conversations[i]);return s&&(t==="agent"&&u||t==="chat"&&!u||t==="all")});r.length&&this.deleteConversationIds(new Set(r))}get lastMessageTimestamp(){const t=this.currentConversationModel.lastExchange;return t==null?void 0:t.timestamp}handleMessageFromExtension(t){return t.data.type===it.newThread&&(this.setCurrentConversation(),!0)}}function uh(o,t){let r,i,s=t;const u=()=>s.editor.getModifiedEditor(),c=()=>{const{afterLineNumber:h}=s,f=u();if(h===void 0)return void f.changeViewZones(m=>{r&&f&&i&&m.removeZone(i)});const v={...s,afterLineNumber:h,domNode:o,suppressMouseDown:!0};f==null||f.changeViewZones(m=>{r&&i&&m.removeZone(i),i=m.addZone(v),r=v})};return c(),{update:h=>{s=h,c()},destroy:()=>{const h=u();h.changeViewZones(f=>{if(r&&h&&i)try{f.removeZone(i)}catch(v){if(v instanceof Error){if(v.message.includes("Cannot read properties of null (reading 'removeChild')"))return}else console.warn(`Failed to remove view zone: ${v}`)}})}}}var Dr=(o=>(o.edit="edit",o.instruction="instruction",o))(Dr||{}),Ru=(o=>(o[o.instructionDrawer=0]="instructionDrawer",o[o.chunkActionPanel=1]="chunkActionPanel",o))(Ru||{});const Ai=typeof performance=="object"&&performance&&typeof performance.now=="function"?performance:Date,ch=new Set,Lu=typeof process=="object"&&process?process:{},Kh=(o,t,r,i)=>{typeof Lu.emitWarning=="function"?Lu.emitWarning(o,t,r,i):console.error(`[${r}] ${t}: ${o}`)};let ea=globalThis.AbortController,lh=globalThis.AbortSignal;var Th;if(ea===void 0){lh=class{constructor(){_(this,"onabort");_(this,"_onabort",[]);_(this,"reason");_(this,"aborted",!1)}addEventListener(r,i){this._onabort.push(i)}},ea=class{constructor(){_(this,"signal",new lh);t()}abort(r){var i,s;if(!this.signal.aborted){this.signal.reason=r,this.signal.aborted=!0;for(const u of this.signal._onabort)u(r);(s=(i=this.signal).onabort)==null||s.call(i,r)}}};let o=((Th=Lu.env)==null?void 0:Th.LRU_CACHE_IGNORE_AC_WARNING)!=="1";const t=()=>{o&&(o=!1,Kh("AbortController is not defined. If using lru-cache in node 14, load an AbortController polyfill from the `node-abort-controller` package. A minimal polyfill is provided for use by LRUCache.fetch(), but it should not be relied upon in other contexts (eg, passing it to other APIs that use AbortController/AbortSignal might have undesirable effects). You may disable this with LRU_CACHE_IGNORE_AC_WARNING=1 in the env.","NO_ABORT_CONTROLLER","ENOTSUP",t))}}const kr=o=>o&&o===Math.floor(o)&&o>0&&isFinite(o),Qh=o=>kr(o)?o<=Math.pow(2,8)?Uint8Array:o<=Math.pow(2,16)?Uint16Array:o<=Math.pow(2,32)?Uint32Array:o<=Number.MAX_SAFE_INTEGER?Bo:null:null;class Bo extends Array{constructor(t){super(t),this.fill(0)}}var Ni;const ni=class ni{constructor(t,r){_(this,"heap");_(this,"length");if(!y(ni,Ni))throw new TypeError("instantiate Stack using Stack.create(n)");this.heap=new r(t),this.length=0}static create(t){const r=Qh(t);if(!r)return[];ue(ni,Ni,!0);const i=new ni(t,r);return ue(ni,Ni,!1),i}push(t){this.heap[this.length++]=t}pop(){return this.heap[--this.length]}};Ni=new WeakMap,ke(ni,Ni,!1);let Nu=ni;var Oh,Rh,wn,Qt,xn,Cn,ji,Di,dt,Sn,ot,Ke,be,jt,Jt,Ot,_t,In,vt,En,Mn,Xt,kn,Lr,Dt,H,Du,ri,or,As,Zt,Jh,ii,qi,Fs,Ar,Fr,qu,Go,Yo,Ye,zu,Ss,Tr,Pu;const tc=class tc{constructor(t){ke(this,H);ke(this,wn);ke(this,Qt);ke(this,xn);ke(this,Cn);ke(this,ji);ke(this,Di);_(this,"ttl");_(this,"ttlResolution");_(this,"ttlAutopurge");_(this,"updateAgeOnGet");_(this,"updateAgeOnHas");_(this,"allowStale");_(this,"noDisposeOnSet");_(this,"noUpdateTTL");_(this,"maxEntrySize");_(this,"sizeCalculation");_(this,"noDeleteOnFetchRejection");_(this,"noDeleteOnStaleGet");_(this,"allowStaleOnFetchAbort");_(this,"allowStaleOnFetchRejection");_(this,"ignoreFetchAbort");ke(this,dt);ke(this,Sn);ke(this,ot);ke(this,Ke);ke(this,be);ke(this,jt);ke(this,Jt);ke(this,Ot);ke(this,_t);ke(this,In);ke(this,vt);ke(this,En);ke(this,Mn);ke(this,Xt);ke(this,kn);ke(this,Lr);ke(this,Dt);ke(this,ri,()=>{});ke(this,or,()=>{});ke(this,As,()=>{});ke(this,Zt,()=>!1);ke(this,ii,t=>{});ke(this,qi,(t,r,i)=>{});ke(this,Fs,(t,r,i,s)=>{if(i||s)throw new TypeError("cannot set size without setting maxSize or maxEntrySize on cache");return 0});_(this,Oh,"LRUCache");const{max:r=0,ttl:i,ttlResolution:s=1,ttlAutopurge:u,updateAgeOnGet:c,updateAgeOnHas:h,allowStale:f,dispose:v,disposeAfter:m,noDisposeOnSet:b,noUpdateTTL:x,maxSize:E=0,maxEntrySize:F=0,sizeCalculation:z,fetchMethod:R,memoMethod:S,noDeleteOnFetchRejection:T,noDeleteOnStaleGet:ne,allowStaleOnFetchRejection:de,allowStaleOnFetchAbort:oe,ignoreFetchAbort:Ne}=t;if(r!==0&&!kr(r))throw new TypeError("max option must be a nonnegative integer");const pe=r?Qh(r):Array;if(!pe)throw new Error("invalid max value: "+r);if(ue(this,wn,r),ue(this,Qt,E),this.maxEntrySize=F||y(this,Qt),this.sizeCalculation=z,this.sizeCalculation){if(!y(this,Qt)&&!this.maxEntrySize)throw new TypeError("cannot set sizeCalculation without setting maxSize or maxEntrySize");if(typeof this.sizeCalculation!="function")throw new TypeError("sizeCalculation set to non-function")}if(S!==void 0&&typeof S!="function")throw new TypeError("memoMethod must be a function if defined");if(ue(this,Di,S),R!==void 0&&typeof R!="function")throw new TypeError("fetchMethod must be a function if specified");if(ue(this,ji,R),ue(this,Lr,!!R),ue(this,ot,new Map),ue(this,Ke,new Array(r).fill(void 0)),ue(this,be,new Array(r).fill(void 0)),ue(this,jt,new pe(r)),ue(this,Jt,new pe(r)),ue(this,Ot,0),ue(this,_t,0),ue(this,In,Nu.create(r)),ue(this,dt,0),ue(this,Sn,0),typeof v=="function"&&ue(this,xn,v),typeof m=="function"?(ue(this,Cn,m),ue(this,vt,[])):(ue(this,Cn,void 0),ue(this,vt,void 0)),ue(this,kn,!!y(this,xn)),ue(this,Dt,!!y(this,Cn)),this.noDisposeOnSet=!!b,this.noUpdateTTL=!!x,this.noDeleteOnFetchRejection=!!T,this.allowStaleOnFetchRejection=!!de,this.allowStaleOnFetchAbort=!!oe,this.ignoreFetchAbort=!!Ne,this.maxEntrySize!==0){if(y(this,Qt)!==0&&!kr(y(this,Qt)))throw new TypeError("maxSize must be a positive integer if specified");if(!kr(this.maxEntrySize))throw new TypeError("maxEntrySize must be a positive integer if specified");Q(this,H,Jh).call(this)}if(this.allowStale=!!f,this.noDeleteOnStaleGet=!!ne,this.updateAgeOnGet=!!c,this.updateAgeOnHas=!!h,this.ttlResolution=kr(s)||s===0?s:1,this.ttlAutopurge=!!u,this.ttl=i||0,this.ttl){if(!kr(this.ttl))throw new TypeError("ttl must be a positive integer if specified");Q(this,H,Du).call(this)}if(y(this,wn)===0&&this.ttl===0&&y(this,Qt)===0)throw new TypeError("At least one of max, maxSize, or ttl is required");if(!this.ttlAutopurge&&!y(this,wn)&&!y(this,Qt)){const Ie="LRU_CACHE_UNBOUNDED";(me=>!ch.has(me))(Ie)&&(ch.add(Ie),Kh("TTL caching without ttlAutopurge, max, or maxSize can result in unbounded memory consumption.","UnboundedCacheWarning",Ie,tc))}}static unsafeExposeInternals(t){return{starts:y(t,Mn),ttls:y(t,Xt),sizes:y(t,En),keyMap:y(t,ot),keyList:y(t,Ke),valList:y(t,be),next:y(t,jt),prev:y(t,Jt),get head(){return y(t,Ot)},get tail(){return y(t,_t)},free:y(t,In),isBackgroundFetch:r=>{var i;return Q(i=t,H,Ye).call(i,r)},backgroundFetch:(r,i,s,u)=>{var c;return Q(c=t,H,Yo).call(c,r,i,s,u)},moveToTail:r=>{var i;return Q(i=t,H,Ss).call(i,r)},indexes:r=>{var i;return Q(i=t,H,Ar).call(i,r)},rindexes:r=>{var i;return Q(i=t,H,Fr).call(i,r)},isStale:r=>{var i;return y(i=t,Zt).call(i,r)}}}get max(){return y(this,wn)}get maxSize(){return y(this,Qt)}get calculatedSize(){return y(this,Sn)}get size(){return y(this,dt)}get fetchMethod(){return y(this,ji)}get memoMethod(){return y(this,Di)}get dispose(){return y(this,xn)}get disposeAfter(){return y(this,Cn)}getRemainingTTL(t){return y(this,ot).has(t)?1/0:0}*entries(){for(const t of Q(this,H,Ar).call(this))y(this,be)[t]===void 0||y(this,Ke)[t]===void 0||Q(this,H,Ye).call(this,y(this,be)[t])||(yield[y(this,Ke)[t],y(this,be)[t]])}*rentries(){for(const t of Q(this,H,Fr).call(this))y(this,be)[t]===void 0||y(this,Ke)[t]===void 0||Q(this,H,Ye).call(this,y(this,be)[t])||(yield[y(this,Ke)[t],y(this,be)[t]])}*keys(){for(const t of Q(this,H,Ar).call(this)){const r=y(this,Ke)[t];r===void 0||Q(this,H,Ye).call(this,y(this,be)[t])||(yield r)}}*rkeys(){for(const t of Q(this,H,Fr).call(this)){const r=y(this,Ke)[t];r===void 0||Q(this,H,Ye).call(this,y(this,be)[t])||(yield r)}}*values(){for(const t of Q(this,H,Ar).call(this))y(this,be)[t]===void 0||Q(this,H,Ye).call(this,y(this,be)[t])||(yield y(this,be)[t])}*rvalues(){for(const t of Q(this,H,Fr).call(this))y(this,be)[t]===void 0||Q(this,H,Ye).call(this,y(this,be)[t])||(yield y(this,be)[t])}[(Rh=Symbol.iterator,Oh=Symbol.toStringTag,Rh)](){return this.entries()}find(t,r={}){for(const i of Q(this,H,Ar).call(this)){const s=y(this,be)[i],u=Q(this,H,Ye).call(this,s)?s.__staleWhileFetching:s;if(u!==void 0&&t(u,y(this,Ke)[i],this))return this.get(y(this,Ke)[i],r)}}forEach(t,r=this){for(const i of Q(this,H,Ar).call(this)){const s=y(this,be)[i],u=Q(this,H,Ye).call(this,s)?s.__staleWhileFetching:s;u!==void 0&&t.call(r,u,y(this,Ke)[i],this)}}rforEach(t,r=this){for(const i of Q(this,H,Fr).call(this)){const s=y(this,be)[i],u=Q(this,H,Ye).call(this,s)?s.__staleWhileFetching:s;u!==void 0&&t.call(r,u,y(this,Ke)[i],this)}}purgeStale(){let t=!1;for(const r of Q(this,H,Fr).call(this,{allowStale:!0}))y(this,Zt).call(this,r)&&(Q(this,H,Tr).call(this,y(this,Ke)[r],"expire"),t=!0);return t}info(t){const r=y(this,ot).get(t);if(r===void 0)return;const i=y(this,be)[r],s=Q(this,H,Ye).call(this,i)?i.__staleWhileFetching:i;if(s===void 0)return;const u={value:s};if(y(this,Xt)&&y(this,Mn)){const c=y(this,Xt)[r],h=y(this,Mn)[r];if(c&&h){const f=c-(Ai.now()-h);u.ttl=f,u.start=Date.now()}}return y(this,En)&&(u.size=y(this,En)[r]),u}dump(){const t=[];for(const r of Q(this,H,Ar).call(this,{allowStale:!0})){const i=y(this,Ke)[r],s=y(this,be)[r],u=Q(this,H,Ye).call(this,s)?s.__staleWhileFetching:s;if(u===void 0||i===void 0)continue;const c={value:u};if(y(this,Xt)&&y(this,Mn)){c.ttl=y(this,Xt)[r];const h=Ai.now()-y(this,Mn)[r];c.start=Math.floor(Date.now()-h)}y(this,En)&&(c.size=y(this,En)[r]),t.unshift([i,c])}return t}load(t){this.clear();for(const[r,i]of t){if(i.start){const s=Date.now()-i.start;i.start=Ai.now()-s}this.set(r,i.value,i)}}set(t,r,i={}){var x,E,F,z,R;if(r===void 0)return this.delete(t),this;const{ttl:s=this.ttl,start:u,noDisposeOnSet:c=this.noDisposeOnSet,sizeCalculation:h=this.sizeCalculation,status:f}=i;let{noUpdateTTL:v=this.noUpdateTTL}=i;const m=y(this,Fs).call(this,t,r,i.size||0,h);if(this.maxEntrySize&&m>this.maxEntrySize)return f&&(f.set="miss",f.maxEntrySizeExceeded=!0),Q(this,H,Tr).call(this,t,"set"),this;let b=y(this,dt)===0?void 0:y(this,ot).get(t);if(b===void 0)b=y(this,dt)===0?y(this,_t):y(this,In).length!==0?y(this,In).pop():y(this,dt)===y(this,wn)?Q(this,H,Go).call(this,!1):y(this,dt),y(this,Ke)[b]=t,y(this,be)[b]=r,y(this,ot).set(t,b),y(this,jt)[y(this,_t)]=b,y(this,Jt)[b]=y(this,_t),ue(this,_t,b),Po(this,dt)._++,y(this,qi).call(this,b,m,f),f&&(f.set="add"),v=!1;else{Q(this,H,Ss).call(this,b);const S=y(this,be)[b];if(r!==S){if(y(this,Lr)&&Q(this,H,Ye).call(this,S)){S.__abortController.abort(new Error("replaced"));const{__staleWhileFetching:T}=S;T===void 0||c||(y(this,kn)&&((x=y(this,xn))==null||x.call(this,T,t,"set")),y(this,Dt)&&((E=y(this,vt))==null||E.push([T,t,"set"])))}else c||(y(this,kn)&&((F=y(this,xn))==null||F.call(this,S,t,"set")),y(this,Dt)&&((z=y(this,vt))==null||z.push([S,t,"set"])));if(y(this,ii).call(this,b),y(this,qi).call(this,b,m,f),y(this,be)[b]=r,f){f.set="replace";const T=S&&Q(this,H,Ye).call(this,S)?S.__staleWhileFetching:S;T!==void 0&&(f.oldValue=T)}}else f&&(f.set="update")}if(s===0||y(this,Xt)||Q(this,H,Du).call(this),y(this,Xt)&&(v||y(this,As).call(this,b,s,u),f&&y(this,or).call(this,f,b)),!c&&y(this,Dt)&&y(this,vt)){const S=y(this,vt);let T;for(;T=S==null?void 0:S.shift();)(R=y(this,Cn))==null||R.call(this,...T)}return this}pop(){var t;try{for(;y(this,dt);){const r=y(this,be)[y(this,Ot)];if(Q(this,H,Go).call(this,!0),Q(this,H,Ye).call(this,r)){if(r.__staleWhileFetching)return r.__staleWhileFetching}else if(r!==void 0)return r}}finally{if(y(this,Dt)&&y(this,vt)){const r=y(this,vt);let i;for(;i=r==null?void 0:r.shift();)(t=y(this,Cn))==null||t.call(this,...i)}}}has(t,r={}){const{updateAgeOnHas:i=this.updateAgeOnHas,status:s}=r,u=y(this,ot).get(t);if(u!==void 0){const c=y(this,be)[u];if(Q(this,H,Ye).call(this,c)&&c.__staleWhileFetching===void 0)return!1;if(!y(this,Zt).call(this,u))return i&&y(this,ri).call(this,u),s&&(s.has="hit",y(this,or).call(this,s,u)),!0;s&&(s.has="stale",y(this,or).call(this,s,u))}else s&&(s.has="miss");return!1}peek(t,r={}){const{allowStale:i=this.allowStale}=r,s=y(this,ot).get(t);if(s===void 0||!i&&y(this,Zt).call(this,s))return;const u=y(this,be)[s];return Q(this,H,Ye).call(this,u)?u.__staleWhileFetching:u}async fetch(t,r={}){const{allowStale:i=this.allowStale,updateAgeOnGet:s=this.updateAgeOnGet,noDeleteOnStaleGet:u=this.noDeleteOnStaleGet,ttl:c=this.ttl,noDisposeOnSet:h=this.noDisposeOnSet,size:f=0,sizeCalculation:v=this.sizeCalculation,noUpdateTTL:m=this.noUpdateTTL,noDeleteOnFetchRejection:b=this.noDeleteOnFetchRejection,allowStaleOnFetchRejection:x=this.allowStaleOnFetchRejection,ignoreFetchAbort:E=this.ignoreFetchAbort,allowStaleOnFetchAbort:F=this.allowStaleOnFetchAbort,context:z,forceRefresh:R=!1,status:S,signal:T}=r;if(!y(this,Lr))return S&&(S.fetch="get"),this.get(t,{allowStale:i,updateAgeOnGet:s,noDeleteOnStaleGet:u,status:S});const ne={allowStale:i,updateAgeOnGet:s,noDeleteOnStaleGet:u,ttl:c,noDisposeOnSet:h,size:f,sizeCalculation:v,noUpdateTTL:m,noDeleteOnFetchRejection:b,allowStaleOnFetchRejection:x,allowStaleOnFetchAbort:F,ignoreFetchAbort:E,status:S,signal:T};let de=y(this,ot).get(t);if(de===void 0){S&&(S.fetch="miss");const oe=Q(this,H,Yo).call(this,t,de,ne,z);return oe.__returned=oe}{const oe=y(this,be)[de];if(Q(this,H,Ye).call(this,oe)){const me=i&&oe.__staleWhileFetching!==void 0;return S&&(S.fetch="inflight",me&&(S.returnedStale=!0)),me?oe.__staleWhileFetching:oe.__returned=oe}const Ne=y(this,Zt).call(this,de);if(!R&&!Ne)return S&&(S.fetch="hit"),Q(this,H,Ss).call(this,de),s&&y(this,ri).call(this,de),S&&y(this,or).call(this,S,de),oe;const pe=Q(this,H,Yo).call(this,t,de,ne,z),Ie=pe.__staleWhileFetching!==void 0&&i;return S&&(S.fetch=Ne?"stale":"refresh",Ie&&Ne&&(S.returnedStale=!0)),Ie?pe.__staleWhileFetching:pe.__returned=pe}}async forceFetch(t,r={}){const i=await this.fetch(t,r);if(i===void 0)throw new Error("fetch() returned undefined");return i}memo(t,r={}){const i=y(this,Di);if(!i)throw new Error("no memoMethod provided to constructor");const{context:s,forceRefresh:u,...c}=r,h=this.get(t,c);if(!u&&h!==void 0)return h;const f=i(t,h,{options:c,context:s});return this.set(t,f,c),f}get(t,r={}){const{allowStale:i=this.allowStale,updateAgeOnGet:s=this.updateAgeOnGet,noDeleteOnStaleGet:u=this.noDeleteOnStaleGet,status:c}=r,h=y(this,ot).get(t);if(h!==void 0){const f=y(this,be)[h],v=Q(this,H,Ye).call(this,f);return c&&y(this,or).call(this,c,h),y(this,Zt).call(this,h)?(c&&(c.get="stale"),v?(c&&i&&f.__staleWhileFetching!==void 0&&(c.returnedStale=!0),i?f.__staleWhileFetching:void 0):(u||Q(this,H,Tr).call(this,t,"expire"),c&&i&&(c.returnedStale=!0),i?f:void 0)):(c&&(c.get="hit"),v?f.__staleWhileFetching:(Q(this,H,Ss).call(this,h),s&&y(this,ri).call(this,h),f))}c&&(c.get="miss")}delete(t){return Q(this,H,Tr).call(this,t,"delete")}clear(){return Q(this,H,Pu).call(this,"delete")}};wn=new WeakMap,Qt=new WeakMap,xn=new WeakMap,Cn=new WeakMap,ji=new WeakMap,Di=new WeakMap,dt=new WeakMap,Sn=new WeakMap,ot=new WeakMap,Ke=new WeakMap,be=new WeakMap,jt=new WeakMap,Jt=new WeakMap,Ot=new WeakMap,_t=new WeakMap,In=new WeakMap,vt=new WeakMap,En=new WeakMap,Mn=new WeakMap,Xt=new WeakMap,kn=new WeakMap,Lr=new WeakMap,Dt=new WeakMap,H=new WeakSet,Du=function(){const t=new Bo(y(this,wn)),r=new Bo(y(this,wn));ue(this,Xt,t),ue(this,Mn,r),ue(this,As,(u,c,h=Ai.now())=>{if(r[u]=c!==0?h:0,t[u]=c,c!==0&&this.ttlAutopurge){const f=setTimeout(()=>{y(this,Zt).call(this,u)&&Q(this,H,Tr).call(this,y(this,Ke)[u],"expire")},c+1);f.unref&&f.unref()}}),ue(this,ri,u=>{r[u]=t[u]!==0?Ai.now():0}),ue(this,or,(u,c)=>{if(t[c]){const h=t[c],f=r[c];if(!h||!f)return;u.ttl=h,u.start=f,u.now=i||s();const v=u.now-f;u.remainingTTL=h-v}});let i=0;const s=()=>{const u=Ai.now();if(this.ttlResolution>0){i=u;const c=setTimeout(()=>i=0,this.ttlResolution);c.unref&&c.unref()}return u};this.getRemainingTTL=u=>{const c=y(this,ot).get(u);if(c===void 0)return 0;const h=t[c],f=r[c];return!h||!f?1/0:h-((i||s())-f)},ue(this,Zt,u=>{const c=r[u],h=t[u];return!!h&&!!c&&(i||s())-c>h})},ri=new WeakMap,or=new WeakMap,As=new WeakMap,Zt=new WeakMap,Jh=function(){const t=new Bo(y(this,wn));ue(this,Sn,0),ue(this,En,t),ue(this,ii,r=>{ue(this,Sn,y(this,Sn)-t[r]),t[r]=0}),ue(this,Fs,(r,i,s,u)=>{if(Q(this,H,Ye).call(this,i))return 0;if(!kr(s)){if(!u)throw new TypeError("invalid size value (must be positive integer). When maxSize or maxEntrySize is used, sizeCalculation or size must be set.");if(typeof u!="function")throw new TypeError("sizeCalculation must be a function");if(s=u(i,r),!kr(s))throw new TypeError("sizeCalculation return invalid (expect positive integer)")}return s}),ue(this,qi,(r,i,s)=>{if(t[r]=i,y(this,Qt)){const u=y(this,Qt)-t[r];for(;y(this,Sn)>u;)Q(this,H,Go).call(this,!0)}ue(this,Sn,y(this,Sn)+t[r]),s&&(s.entrySize=i,s.totalCalculatedSize=y(this,Sn))})},ii=new WeakMap,qi=new WeakMap,Fs=new WeakMap,Ar=function*({allowStale:t=this.allowStale}={}){if(y(this,dt))for(let r=y(this,_t);Q(this,H,qu).call(this,r)&&(!t&&y(this,Zt).call(this,r)||(yield r),r!==y(this,Ot));)r=y(this,Jt)[r]},Fr=function*({allowStale:t=this.allowStale}={}){if(y(this,dt))for(let r=y(this,Ot);Q(this,H,qu).call(this,r)&&(!t&&y(this,Zt).call(this,r)||(yield r),r!==y(this,_t));)r=y(this,jt)[r]},qu=function(t){return t!==void 0&&y(this,ot).get(y(this,Ke)[t])===t},Go=function(t){var u,c;const r=y(this,Ot),i=y(this,Ke)[r],s=y(this,be)[r];return y(this,Lr)&&Q(this,H,Ye).call(this,s)?s.__abortController.abort(new Error("evicted")):(y(this,kn)||y(this,Dt))&&(y(this,kn)&&((u=y(this,xn))==null||u.call(this,s,i,"evict")),y(this,Dt)&&((c=y(this,vt))==null||c.push([s,i,"evict"]))),y(this,ii).call(this,r),t&&(y(this,Ke)[r]=void 0,y(this,be)[r]=void 0,y(this,In).push(r)),y(this,dt)===1?(ue(this,Ot,ue(this,_t,0)),y(this,In).length=0):ue(this,Ot,y(this,jt)[r]),y(this,ot).delete(i),Po(this,dt)._--,r},Yo=function(t,r,i,s){const u=r===void 0?void 0:y(this,be)[r];if(Q(this,H,Ye).call(this,u))return u;const c=new ea,{signal:h}=i;h==null||h.addEventListener("abort",()=>c.abort(h.reason),{signal:c.signal});const f={signal:c.signal,options:i,context:s},v=(E,F=!1)=>{const{aborted:z}=c.signal,R=i.ignoreFetchAbort&&E!==void 0;if(i.status&&(z&&!F?(i.status.fetchAborted=!0,i.status.fetchError=c.signal.reason,R&&(i.status.fetchAbortIgnored=!0)):i.status.fetchResolved=!0),z&&!R&&!F)return m(c.signal.reason);const S=b;return y(this,be)[r]===b&&(E===void 0?S.__staleWhileFetching?y(this,be)[r]=S.__staleWhileFetching:Q(this,H,Tr).call(this,t,"fetch"):(i.status&&(i.status.fetchUpdated=!0),this.set(t,E,f.options))),E},m=E=>{const{aborted:F}=c.signal,z=F&&i.allowStaleOnFetchAbort,R=z||i.allowStaleOnFetchRejection,S=R||i.noDeleteOnFetchRejection,T=b;if(y(this,be)[r]===b&&(!S||T.__staleWhileFetching===void 0?Q(this,H,Tr).call(this,t,"fetch"):z||(y(this,be)[r]=T.__staleWhileFetching)),R)return i.status&&T.__staleWhileFetching!==void 0&&(i.status.returnedStale=!0),T.__staleWhileFetching;if(T.__returned===T)throw E};i.status&&(i.status.fetchDispatched=!0);const b=new Promise((E,F)=>{var R;const z=(R=y(this,ji))==null?void 0:R.call(this,t,u,f);z&&z instanceof Promise&&z.then(S=>E(S===void 0?void 0:S),F),c.signal.addEventListener("abort",()=>{i.ignoreFetchAbort&&!i.allowStaleOnFetchAbort||(E(void 0),i.allowStaleOnFetchAbort&&(E=S=>v(S,!0)))})}).then(v,E=>(i.status&&(i.status.fetchRejected=!0,i.status.fetchError=E),m(E))),x=Object.assign(b,{__abortController:c,__staleWhileFetching:u,__returned:void 0});return r===void 0?(this.set(t,x,{...f.options,status:void 0}),r=y(this,ot).get(t)):y(this,be)[r]=x,x},Ye=function(t){if(!y(this,Lr))return!1;const r=t;return!!r&&r instanceof Promise&&r.hasOwnProperty("__staleWhileFetching")&&r.__abortController instanceof ea},zu=function(t,r){y(this,Jt)[r]=t,y(this,jt)[t]=r},Ss=function(t){t!==y(this,_t)&&(t===y(this,Ot)?ue(this,Ot,y(this,jt)[t]):Q(this,H,zu).call(this,y(this,Jt)[t],y(this,jt)[t]),Q(this,H,zu).call(this,y(this,_t),t),ue(this,_t,t))},Tr=function(t,r){var s,u,c,h;let i=!1;if(y(this,dt)!==0){const f=y(this,ot).get(t);if(f!==void 0)if(i=!0,y(this,dt)===1)Q(this,H,Pu).call(this,r);else{y(this,ii).call(this,f);const v=y(this,be)[f];if(Q(this,H,Ye).call(this,v)?v.__abortController.abort(new Error("deleted")):(y(this,kn)||y(this,Dt))&&(y(this,kn)&&((s=y(this,xn))==null||s.call(this,v,t,r)),y(this,Dt)&&((u=y(this,vt))==null||u.push([v,t,r]))),y(this,ot).delete(t),y(this,Ke)[f]=void 0,y(this,be)[f]=void 0,f===y(this,_t))ue(this,_t,y(this,Jt)[f]);else if(f===y(this,Ot))ue(this,Ot,y(this,jt)[f]);else{const m=y(this,Jt)[f];y(this,jt)[m]=y(this,jt)[f];const b=y(this,jt)[f];y(this,Jt)[b]=y(this,Jt)[f]}Po(this,dt)._--,y(this,In).push(f)}}if(y(this,Dt)&&((c=y(this,vt))!=null&&c.length)){const f=y(this,vt);let v;for(;v=f==null?void 0:f.shift();)(h=y(this,Cn))==null||h.call(this,...v)}return i},Pu=function(t){var r,i,s;for(const u of Q(this,H,Fr).call(this,{allowStale:!0})){const c=y(this,be)[u];if(Q(this,H,Ye).call(this,c))c.__abortController.abort(new Error("deleted"));else{const h=y(this,Ke)[u];y(this,kn)&&((r=y(this,xn))==null||r.call(this,c,h,t)),y(this,Dt)&&((i=y(this,vt))==null||i.push([c,h,t]))}}if(y(this,ot).clear(),y(this,be).fill(void 0),y(this,Ke).fill(void 0),y(this,Xt)&&y(this,Mn)&&(y(this,Xt).fill(0),y(this,Mn).fill(0)),y(this,En)&&y(this,En).fill(0),ue(this,Ot,0),ue(this,_t,0),y(this,In).length=0,ue(this,Sn,0),ue(this,dt,0),y(this,Dt)&&y(this,vt)){const u=y(this,vt);let c;for(;c=u==null?void 0:u.shift();)(s=y(this,Cn))==null||s.call(this,...c)}};let ju=tc;class Xh{constructor(){_(this,"_syncStatus",{status:Pp.done,foldersProgress:[]});_(this,"_syncEnabledState",th.initializing);_(this,"_workspaceGuidelines",[]);_(this,"_openUserGuidelinesInput",!1);_(this,"_userGuidelines");_(this,"_contextStore",new Eg);_(this,"_prevOpenFiles",[]);_(this,"_disableContext",!1);_(this,"_enableAgentMemories",!1);_(this,"subscribers",new Set);_(this,"subscribe",t=>(this.subscribers.add(t),t(this),()=>{this.subscribers.delete(t)}));_(this,"handleMessageFromExtension",t=>{const r=t.data;switch(r.type){case it.sourceFoldersUpdated:this.onSourceFoldersUpdated(r.data.sourceFolders);break;case it.sourceFoldersSyncStatus:this.onSyncStatusUpdated(r.data);break;case it.fileRangesSelected:this.updateSelections(r.data);break;case it.currentlyOpenFiles:this.setCurrentlyOpenFiles(r.data);break;case it.syncEnabledState:this.onSyncEnabledStateUpdate(r.data);break;case it.updateGuidelinesState:this.onGuidelinesStateUpdate(r.data);break;default:return!1}return!0});_(this,"onSourceFoldersUpdated",t=>{const r=this.sourceFolders;t=this.updateSourceFoldersWithGuidelines(t),this._contextStore.update(t.map(i=>({sourceFolder:i,status:yt.active,label:i.folderRoot,showWarning:i.guidelinesOverLimit,id:i.folderRoot+String(i.guidelinesEnabled)+String(i.guidelinesOverLimit)})),r,i=>i.id),this.notifySubscribers()});_(this,"onSyncStatusUpdated",t=>{this._syncStatus=t,this.notifySubscribers()});_(this,"disableContext",()=>{this._disableContext=!0,this.notifySubscribers()});_(this,"enableContext",()=>{this._disableContext=!1,this.notifySubscribers()});_(this,"addFile",t=>{this.addFiles([t])});_(this,"addFiles",t=>{this.updateFiles(t,[])});_(this,"removeFile",t=>{this.removeFiles([t])});_(this,"removeFiles",t=>{this.updateFiles([],t)});_(this,"updateItems",(t,r)=>{this.updateItemsInplace(t,r),this.notifySubscribers()});_(this,"updateItemsInplace",(t,r)=>{this._contextStore.update(t,r,i=>i.id)});_(this,"updateFiles",(t,r)=>{const i=c=>({file:c,...$u(c)}),s=t.map(i),u=r.map(i);this._contextStore.update(s,u,c=>c.id),this.notifySubscribers()});_(this,"enableAgentMemories",()=>{this._enableAgentMemories=!0,this.notifySubscribers()});_(this,"disableAgentMemories",()=>{this._enableAgentMemories=!1,this.notifySubscribers()});_(this,"setCurrentlyOpenFiles",t=>{const r=t.map(s=>({recentFile:s,...$u(s)})),i=this._prevOpenFiles;this._prevOpenFiles=r,this._contextStore.update(r,i,s=>s.id),i.forEach(s=>{const u=this._contextStore.peekKey(s.id);u!=null&&u.recentFile&&(u.file=u.recentFile,delete u.recentFile)}),r.forEach(s=>{const u=this._contextStore.peekKey(s.id);u!=null&&u.file&&(u.recentFile=u.file,delete u.file)}),this.notifySubscribers()});_(this,"onSyncEnabledStateUpdate",t=>{this._syncEnabledState=t,this.notifySubscribers()});_(this,"updateUserGuidelines",t=>{const r=this.userGuidelines,i={userGuidelines:t,label:"User Guidelines",id:"userGuidelines",status:yt.active,referenceCount:1,showWarning:t.overLimit};this._contextStore.update([i],r,s=>{var u,c;return s.id+String((u=s.userGuidelines)==null?void 0:u.enabled)+String((c=s.userGuidelines)==null?void 0:c.overLimit)}),this.notifySubscribers()});_(this,"onGuidelinesStateUpdate",t=>{this._userGuidelines=t.userGuidelines,this._workspaceGuidelines=t.workspaceGuidelines??[];const r=t.userGuidelines;r&&this.updateUserGuidelines(r),this.onSourceFoldersUpdated(this.sourceFolders.map(i=>i.sourceFolder))});_(this,"updateSourceFoldersWithGuidelines",t=>t.map(r=>{const i=this._workspaceGuidelines.find(s=>s.workspaceFolder===r.folderRoot);return{...r,guidelinesEnabled:(i==null?void 0:i.enabled)??!1,guidelinesOverLimit:(i==null?void 0:i.overLimit)??!1,guidelinesLengthLimit:(i==null?void 0:i.lengthLimit)??2e3}}));_(this,"toggleStatus",t=>{this._contextStore.toggleStatus(t.id),this.notifySubscribers()});_(this,"updateExternalSources",(t,r)=>{this._contextStore.update(t,r,i=>i.id),this.notifySubscribers()});_(this,"clearFiles",()=>{this._contextStore.update([],this.files,t=>t.id),this.notifySubscribers()});_(this,"updateSelections",t=>{const r=this._contextStore.values.filter(Jo);this._contextStore.update(t.map(i=>({selection:i,...$u(i)})),r,i=>i.id),this.notifySubscribers()});_(this,"maybeHandleDelete",({editor:t})=>{if(t.state.selection.empty&&t.state.selection.$anchor.pos===1&&this.recentActiveItems.length>0){const r=this.recentActiveItems[0];return this.markInactive(r),!0}return!1});_(this,"markInactive",t=>{this.markItemsInactive([t])});_(this,"markItemsInactive",t=>{t.forEach(r=>{this._contextStore.setStatus(r.id,yt.inactive)}),this.notifySubscribers()});_(this,"markAllInactive",()=>{this.markItemsInactive(this.recentActiveItems)});_(this,"markActive",t=>{this.markItemsActive([t])});_(this,"markItemsActive",t=>{t.forEach(r=>{this._contextStore.setStatus(r.id,yt.active)}),this.notifySubscribers()});_(this,"markAllActive",()=>{this.markItemsActive(this.recentInactiveItems)});_(this,"unpin",t=>{this._contextStore.unpin(t.id),this.notifySubscribers()});_(this,"togglePinned",t=>{this._contextStore.togglePinned(t.id),this.notifySubscribers()});_(this,"notifySubscribers",()=>{this.subscribers.forEach(t=>t(this))});this.clearFiles()}get files(){return this._disableContext?[]:this._contextStore.values.filter(t=>Qu(t)&&!Qo(t))}get recentFiles(){return this._disableContext?[]:this._contextStore.values.filter(Qo)}get userGuidelinesText(){var t;return((t=this._userGuidelines)==null?void 0:t.contents)??""}get selections(){return this._disableContext?[]:this._contextStore.values.filter(Jo)}get folders(){return this._disableContext?[]:this._contextStore.values.filter(Ju)}get sourceFolders(){return this._disableContext?[]:this._contextStore.values.filter(Xo)}get externalSources(){return this._disableContext?[]:this._contextStore.values.filter(Xu)}get userGuidelines(){return this._contextStore.values.filter(Zo)}get agentMemories(){return[{...Fp,status:this._enableAgentMemories?yt.active:yt.inactive,referenceCount:1}]}get activeFiles(){return this._disableContext?[]:this.files.filter(t=>t.status===yt.active)}get activeRecentFiles(){return this._disableContext?[]:this.recentFiles.filter(t=>t.status===yt.active)}get activeExternalSources(){return this._disableContext?[]:this.externalSources.filter(t=>t.status===yt.active)}get activeSelections(){return this._disableContext?[]:this.selections.filter(t=>t.status===yt.active)}get activeSourceFolders(){return this._disableContext?[]:this.sourceFolders.filter(t=>t.status===yt.active)}get syncStatus(){return this._syncStatus.status}get syncEnabledState(){return this._syncEnabledState}get syncProgress(){var f;if(this.syncEnabledState===th.disabled||!this._syncStatus.foldersProgress)return;const t=this._syncStatus.foldersProgress.filter(v=>v.progress!==void 0);if(t.length===0)return;const r=t.reduce((v,m)=>{var b;return v+(((b=m==null?void 0:m.progress)==null?void 0:b.trackedFiles)??0)},0),i=t.reduce((v,m)=>{var b;return v+(((b=m==null?void 0:m.progress)==null?void 0:b.backlogSize)??0)},0),s=Math.max(r,0),u=Math.min(Math.max(i,0),s),c=s-u,h=[];for(const v of t)(f=v==null?void 0:v.progress)!=null&&f.newlyTracked&&h.push(v.folderRoot);return{status:this._syncStatus.status,totalFiles:s,syncedCount:c,backlogSize:u,newlyTrackedFolders:h}}get contextCounts(){return this._contextStore.values.length??0}get chatActiveContext(){return{userSpecifiedFiles:this.activeFiles.map(t=>({rootPath:t.file.repoRoot,relPath:t.file.pathName})),recentFiles:this.activeRecentFiles.map(t=>({rootPath:t.recentFile.repoRoot,relPath:t.recentFile.pathName})),externalSources:this.activeExternalSources.map(t=>t.externalSource),selections:this.activeSelections.map(t=>t.selection),sourceFolders:this.activeSourceFolders.map(t=>({rootPath:t.sourceFolder.folderRoot,relPath:""}))}}get recentItems(){return this._disableContext?this.userGuidelines:[...this._contextStore.values.filter(t=>!Xo(t)&&!Zo(t)&&!na(t)),...this.sourceFolders,...this.userGuidelines,...this.agentMemories]}get recentActiveItems(){return this.recentItems.filter(t=>t.status===yt.active)}get recentInactiveItems(){return this.recentItems.filter(t=>t.status===yt.inactive)}get isContextDisabled(){return this._disableContext}}class Eg{constructor(){_(this,"_cache",new ju({max:1e3}));_(this,"peekKey",t=>this._cache.get(t,{updateAgeOnGet:!1}));_(this,"clear",()=>{this._cache.clear()});_(this,"update",(t,r,i)=>{t.forEach(s=>this.addInPlace(s,i)),r.forEach(s=>this.removeInPlace(s,i))});_(this,"removeFromStore",(t,r)=>{const i=r(t);this._cache.delete(i)});_(this,"addInPlace",(t,r)=>{const i=r(t),s=t.referenceCount??1,u=this._cache.get(i),c=t.status??(u==null?void 0:u.status)??yt.active;u?(u.referenceCount+=s,u.status=c,u.pinned=t.pinned??u.pinned,u.showWarning=t.showWarning??u.showWarning):this._cache.set(i,{...t,pinned:void 0,referenceCount:s,status:c})});_(this,"removeInPlace",(t,r)=>{const i=r(t),s=this._cache.get(i);s&&(s.referenceCount-=1,s.referenceCount===0&&this._cache.delete(i))});_(this,"setStatus",(t,r)=>{const i=this._cache.get(t);i&&(i.status=r)});_(this,"togglePinned",t=>{const r=this._cache.peek(t);r&&(r.pinned?this.unpin(t):this.pin(t))});_(this,"pin",t=>{const r=this._cache.peek(t);r&&!r.pinned&&(r.pinned=!0,r.referenceCount+=1)});_(this,"unpin",t=>{const r=this._cache.peek(t);r&&r.pinned&&(r.pinned=!1,r.referenceCount-=1,r.referenceCount===0&&this._cache.delete(t))});_(this,"toggleStatus",t=>{const r=this._cache.get(t);r&&(r.status=r.status===yt.active?yt.inactive:yt.active)})}get store(){return Object.fromEntries(this._cache.entries())}get values(){return[...this._cache.values()]}}class Mg{constructor(t,r,i){_(this,"_originalModel");_(this,"_modifiedModel");_(this,"_fullEdits",[]);_(this,"_currEdit");_(this,"_currOriginalEdit");_(this,"swapBaseModel",t=>{this._originalModel.setValue(t),this._modifiedModel.setValue(t),this._fullEdits.forEach(r=>{this._modifiedModel.applyEdits([r])}),this._currEdit&&this._modifiedModel.applyEdits([this._currEdit]),this._currOriginalEdit&&this._originalModel.applyEdits([this._currOriginalEdit])});_(this,"finish",()=>this._completeCurrEdit());_(this,"onReceiveChunk",t=>t.data.newChunkStart?this._startNewEdit(t.data.newChunkStart):t.data.chunkContinue&&this._currEdit?this._continueEdit(t.data.chunkContinue):t.data.chunkEnd&&this._currEdit?this._completeCurrEdit(t.data.chunkEnd):void 0);_(this,"_completeCurrEdit",t=>{const r={resetOriginal:[],original:[],modified:[]};if(!t)return r;if(this._currEdit){this._currEdit.range=new this._monaco.Range(t.stagedStartLine,0,t.stagedEndLine,0);const i=this._nextModifiedInsertPosition(),s=t.stagedEndLine-t.stagedStartLine,u={range:new this._monaco.Range(i.lineNumber,0,i.lineNumber+s,0),text:""};r.modified.push(u),this._modifiedModel.applyEdits([u]),this._fullEdits.push(this._currEdit),this._currEdit=void 0}return r});_(this,"_startNewEdit",t=>{const r={resetOriginal:[],original:[],modified:[]};return this._currOriginalEdit=void 0,this._currEdit={range:new this._monaco.Range(t.stagedStartLine,0,t.stagedStartLine,0),text:""},r.modified.push(this._currEdit),this._modifiedModel.applyEdits([this._currEdit]),r});_(this,"_continueEdit",t=>{if(!this._currEdit)throw new Error("No current edit");const r=this._nextModifiedInsertPosition(),i={...this._currEdit,text:t.newText,range:new this._monaco.Range(r.lineNumber,r.column,r.lineNumber,r.column)};return this._modifiedModel.applyEdits([i]),this._currEdit.text+=t.newText,{resetOriginal:[],original:[],modified:t.newText.length>0?[i]:[]}});_(this,"_nextModifiedInsertPosition",()=>{var r;if(!this._currEdit)throw new Error("No current edit");const t=this._modifiedModel.getOffsetAt({lineNumber:this._currEdit.range.startLineNumber,column:this._currEdit.range.startColumn})+(((r=this._currEdit.text)==null?void 0:r.length)??0);return this._modifiedModel.getPositionAt(t)});this.id=t,this.originalCode=r,this._monaco=i,this._originalModel=this._monaco.editor.createModel(r),this._modifiedModel=this._monaco.editor.createModel(r)}get hasReceivedFirstChunk(){return this._currEdit!==void 0||this._fullEdits.length>0}get originalValue(){return this._originalModel.getValue()}get modifiedValue(){return this._modifiedModel.getValue()}get currEdit(){return this._currEdit}}class kg{constructor(t,r,i){_(this,"_asyncMsgSender");_(this,"_editor");_(this,"_chatModel");_(this,"_focusModel",new Ph);_(this,"_hasScrolledOnInit",!1);_(this,"_markHasScrolledOnInit",Tu(()=>{this._hasScrolledOnInit=!0},200));_(this,"_resetScrollOnInit",()=>{this._markHasScrolledOnInit.cancel(),this._hasScrolledOnInit=!1});_(this,"_subscribers",new Set);_(this,"_disposables",[]);_(this,"_rootChunk");_(this,"_keybindings",bt({}));_(this,"_requestId",bt(void 0));_(this,"requestId",this._requestId);_(this,"_disableResolution",bt(!1));_(this,"disableResolution",Zl(this._disableResolution));_(this,"_disableApply",bt(!1));_(this,"disableApply",Zl(this._disableApply));_(this,"_currStream");_(this,"_isLoadingDiffChunks",bt(!1));_(this,"_selectionLines",bt(void 0));_(this,"_mode",bt(Dr.edit));_(this,"initializeEditor",t=>{var r,i,s,u,c,h,f,v,m,b,x,E;this._editor=this._monaco.editor.createDiffEditor(this._editorContainer,{automaticLayout:!0,theme:t,readOnly:!0,contextmenu:!1,renderSideBySide:!1,renderIndicators:!0,renderMarginRevertIcon:!1,originalEditable:!1,diffCodeLens:!1,renderOverviewRuler:!1,ignoreTrimWhitespace:!1,scrollBeyondLastLine:!0,maxComputationTime:0,minimap:{enabled:!1},padding:{top:16}}),this._editor.getOriginalEditor().updateOptions({lineNumbers:"off"}),this._chatModel=new Yh(new Wh(Is),Is,new Xh),(i=(r=this._monaco.editor).registerCommand)==null||i.call(r,"acceptFocusedChunk",this.acceptFocusedChunk),(u=(s=this._monaco.editor).registerCommand)==null||u.call(s,"rejectFocusedChunk",this.rejectFocusedChunk),(h=(c=this._monaco.editor).registerCommand)==null||h.call(c,"acceptAllChunks",this.acceptAllChunks),(v=(f=this._monaco.editor).registerCommand)==null||v.call(f,"rejectAllChunks",this.rejectAllChunks),(b=(m=this._monaco.editor).registerCommand)==null||b.call(m,"focusNextChunk",this.focusNextChunk),(E=(x=this._monaco.editor).registerCommand)==null||E.call(x,"focusPrevChunk",this.focusPrevChunk),this._disposables.push(this._editor,this._editor.onDidUpdateDiff(this.onDidUpdateDiff),this._editor.getModifiedEditor().onMouseMove(this.onMouseMoveModified),{dispose:this._focusModel.subscribe(F=>this.notifySubscribers())}),this.initialize()});_(this,"subscribe",t=>(this._subscribers.add(t),t(this),()=>{this._subscribers.delete(t)}));_(this,"dispose",()=>{this._editor.dispose(),this._subscribers.clear(),this._disposables.forEach(t=>t.dispose())});_(this,"notifySubscribers",()=>{this._subscribers.forEach(t=>t(this))});_(this,"onDidUpdateDiff",()=>{var t;if(this.updateCodeChunk(),!this._hasScrolledOnInit&&((t=this.leaves)==null?void 0:t.length)){this._markHasScrolledOnInit();const r=this.leaves[0];this.revealChunk(r)}this.notifyDiffViewUpdated(),this.notifySubscribers()});_(this,"onMouseMoveModified",t=>{var s,u,c,h,f,v;if(((s=t.target.position)==null?void 0:s.lineNumber)===void 0||this.leaves===void 0)return;const r=this.editorOffset,i=(u=t.target.position)==null?void 0:u.lineNumber;for(let m=0;m<this.leaves.length;m++){const b=this.leaves[m],x=(c=b.unitOfCodeWork.lineChanges)==null?void 0:c.lineChanges[0].modifiedStart,E=(h=b.unitOfCodeWork.lineChanges)==null?void 0:h.lineChanges[0].modifiedEnd,F=(f=b.unitOfCodeWork.lineChanges)==null?void 0:f.lineChanges[0].originalStart,z=(v=b.unitOfCodeWork.lineChanges)==null?void 0:v.lineChanges[0].originalEnd;if(x!==void 0&&E!==void 0&&F!==void 0&&z!==void 0){if(x!==E||i!==x){if(x<=i&&i<E){this.setCurrFocusedChunkIdx(m,!1);break}}else if(t.target.type===this._monaco.editor.MouseTargetType.CONTENT_VIEW_ZONE){const R=this._editor.getOriginalEditor(),S=R.getOption(this._monaco.editor.EditorOption.lineHeight),T=R.getScrolledVisiblePosition({lineNumber:F,column:0}),ne=R.getScrolledVisiblePosition({lineNumber:z+1,column:0});if(T===null||ne===null)continue;const de=T.top-S/2+r,oe=ne.top-S/2+r;if(t.event.posy>=de&&t.event.posy<=oe){this.setCurrFocusedChunkIdx(m,!1);break}break}}}});_(this,"updateIsWebviewFocused",async t=>{await this._asyncMsgSender.send({type:it.diffViewWindowFocusChange,data:t})});_(this,"setCurrFocusedChunkIdx",(t,r=!0)=>{this._focusModel.focusedItemIdx!==t&&(this._focusModel.setFocusIdx(t),r&&this.revealCurrFocusedChunk(),this.notifySubscribers())});_(this,"revealCurrFocusedChunk",()=>{const t=this._focusModel.focusedItem;t&&this.revealChunk(t)});_(this,"revealChunk",t=>{var s;const r=(s=t.unitOfCodeWork.lineChanges)==null?void 0:s.lineChanges[0],i=r==null?void 0:r.modifiedStart;i!==void 0&&this._editor.revealLineNearTop(i-1)});_(this,"renderCentralOverlayWidget",t=>{const r=()=>({editor:this._editor,id:"central-overlay-widget"}),i=function(s,u,c){let h,f=u;const v=()=>f.editor.getModifiedEditor(),m=()=>{const b=v();if(!b)return;const x={getDomNode:()=>s,getId:()=>f.id,getPosition:()=>({preference:c.monaco.editor.OverlayWidgetPositionPreference.TOP_CENTER})};h&&b.removeOverlayWidget(h),b.addOverlayWidget(x),h=x};return m(),{update:b=>{f=b,m()},destroy:()=>{const b=v();b&&h&&b.removeOverlayWidget(h)}}}(t,r(),{monaco:this._monaco});return{update:()=>{i.update(r())},destroy:i.destroy}});_(this,"renderInstructionsDrawerViewZone",(t,r)=>{let i=!1,s=r;const u=r.autoFocus??!0,c=m=>{u&&!i&&(this._editor.revealLineNearTop(m),i=!0)},h=m=>({...m,ordinal:Ru.instructionDrawer,editor:this._editor,afterLineNumber:m.line}),f=uh(t,h(r)),v=[];return u&&v.push(this._editor.onDidUpdateDiff(()=>{c(s.line)})),{update:m=>{const b={...s,...m};yg(b,s)||(f.update(h(b)),s=b,c(b.line))},destroy:()=>{f.destroy(),v.forEach(m=>m.dispose())}}});_(this,"renderActionsViewZone",(t,r)=>{const i=u=>{var h;let c;return c=u.chunk?(h=u.chunk.unitOfCodeWork.lineChanges)==null?void 0:h.lineChanges[0].modifiedStart:1,{...u,ordinal:Ru.chunkActionPanel,editor:this._editor,afterLineNumber:c?c-1:void 0}},s=uh(t,i(r));return{update:u=>{s.update(i(u))},destroy:s.destroy}});_(this,"acceptAllChunks",()=>{this.leaves&&this.acceptChunks(this.leaves,!0)});_(this,"rejectAllChunks",()=>{this.leaves&&this.rejectChunks(this.leaves,!0)});_(this,"acceptFocusedChunk",()=>{const t=this._focusModel.focusedItem;t&&this.acceptChunk(t)});_(this,"rejectFocusedChunk",()=>{const t=this._focusModel.focusedItem;t&&this.rejectChunk(t)});_(this,"focusNextChunk",()=>{this._focusModel.focusNext(),this.revealCurrFocusedChunk()});_(this,"focusPrevChunk",()=>{this._focusModel.focusPrev(),this.revealCurrFocusedChunk()});_(this,"initialize",async()=>{var f;const t=await this._asyncMsgSender.send({type:it.diffViewLoaded},2e3);this._resetScrollOnInit();const{file:r,instruction:i,keybindings:s,editable:u}=t.data;this._editor.updateOptions({readOnly:!u});const c=pt(this._keybindings);this._keybindings.set(s??c);const h=i==null?void 0:i.selection;h&&(h.start.line===h.end.line&&h.start.character===h.end.character&&this._mode.set(Dr.instruction),pt(this.selectionLines)===void 0&&this._selectionLines.set({start:h.start.line,end:h.end.line})),this.updateModels(r.originalCode??"",r.modifiedCode??"",{rootPath:r.repoRoot,relPath:r.pathName}),(f=this._currStream)==null||f.finish(),this._currStream=void 0,this._disableResolution.set(!!t.data.disableResolution),this._disableApply.set(!!t.data.disableApply),await this._tryFetchStream(),this._syncStreamToModels()});_(this,"disposeDiffViewPanel",async()=>{await this._asyncMsgSender.send({type:it.disposeDiffView})});_(this,"_tryFetchStream",async()=>{var r,i,s;const t=this._asyncMsgSender.stream({type:it.diffViewFetchPendingStream},15e3,6e4);for await(const u of t)switch(u.type){case it.diffViewDiffStreamStarted:{this.setLoading(!0),this._requestId.set(u.data.requestId);const c=this._editor.getOriginalEditor().getValue();this._currStream=new Mg(u.data.streamId,c,this._monaco),this._syncStreamToModels();break}case it.diffViewDiffStreamEnded:if(((r=this._currStream)==null?void 0:r.id)!==u.data.streamId)return;this.setLoading(!1),this._cleanupStream();break;case it.diffViewDiffStreamChunk:{if(((i=this._currStream)==null?void 0:i.id)!==u.data.streamId)return;const c=this._editor.getOriginalEditor().getModel();if(!this._editor.getModifiedEditor().getModel()||!c)return this.setLoading(!1),void this._cleanupStream();const h=(s=this._currStream)==null?void 0:s.onReceiveChunk(u);h&&(this._applyDeltaDiff(h),pt(this._selectionLines)!=null&&this._selectionLines.set(null));break}}});_(this,"handleMessageFromExtension",async t=>{switch(t.data.type){case it.diffViewNotifyReinit:this.setLoading(!1),this._cleanupStream(),this.initialize();break;case it.diffViewAcceptAllChunks:this.acceptAllChunks();break;case it.diffViewAcceptFocusedChunk:this.acceptFocusedChunk();break;case it.diffViewRejectFocusedChunk:this.rejectFocusedChunk();break;case it.diffViewFocusPrevChunk:this.focusPrevChunk();break;case it.diffViewFocusNextChunk:this.focusNextChunk()}});_(this,"_applyDeltaDiff",t=>{const r=this._editor.getOriginalEditor().getModel(),i=this._editor.getModifiedEditor().getModel();r&&i&&(r.pushEditOperations([],t.resetOriginal,()=>[]),t.original.forEach(s=>{r.pushEditOperations([],[s],()=>[])}),t.modified.forEach(s=>{i.pushEditOperations([],[s],()=>[])}))});_(this,"_cleanupStream",()=>{var t;if(this._currStream){const r=(t=this._currStream)==null?void 0:t.finish();this._applyDeltaDiff(r),this._currStream=void 0,this._resetScrollOnInit()}});_(this,"_syncStreamToModels",()=>{var i,s;const t=(i=this._currStream)==null?void 0:i.originalValue,r=(s=this._currStream)==null?void 0:s.modifiedValue;t&&t!==this._editor.getOriginalEditor().getValue()&&this._editor.getOriginalEditor().setValue(t),r&&r!==this._editor.getModifiedEditor().getValue()&&this._editor.getModifiedEditor().setValue(r)});_(this,"acceptChunk",async t=>{pt(this._disableApply)||this.acceptChunks([t])});_(this,"acceptChunks",async(t,r=!1)=>{pt(this._disableApply)||(this.executeDiffChunks(t,!0),this.notifyResolvedChunks(t,yu.accept,r),await Mu(),this.areModelsEqual()&&!pt(this.isLoading)&&this.disposeDiffViewPanel())});_(this,"areModelsEqual",()=>{var i,s;const t=(i=this._editor.getModel())==null?void 0:i.original,r=(s=this._editor.getModel())==null?void 0:s.modified;return(t==null?void 0:t.getValue())===(r==null?void 0:r.getValue())});_(this,"rejectChunk",async t=>{this.rejectChunks([t])});_(this,"rejectChunks",async(t,r=!1)=>{this.executeDiffChunks(t,!1),this.notifyResolvedChunks(t,yu.reject,r),await Mu(),this.areModelsEqual()&&!pt(this.isLoading)&&this.disposeDiffViewPanel()});_(this,"notifyDiffViewUpdated",Tu(()=>{this.notifyResolvedChunks([],yu.accept)},1e3));_(this,"notifyResolvedChunks",async(t,r,i=!1)=>{var u;const s=(u=this._editor.getModel())==null?void 0:u.original.uri.path;s&&await this._asyncMsgSender.send({type:it.diffViewResolveChunk,data:{file:{repoRoot:"",pathName:s,originalCode:this._originalCode,modifiedCode:this._modifiedCode},changes:t.map(c=>c.unitOfCodeWork),resolveType:r,shouldApplyToAll:i}},2e3)});_(this,"executeDiffChunks",(t,r)=>{var m,b,x;if(pt(this._disableResolution)||r&&pt(this._disableApply))return;const i=(m=this._editor.getModel())==null?void 0:m.original,s=(b=this._editor.getModel())==null?void 0:b.modified;if(!i||!s||this._currStream!==void 0)return;const u=[],c=[];for(const E of t){const F=(x=E.unitOfCodeWork.lineChanges)==null?void 0:x.lineChanges[0];if(!F||E.unitOfCodeWork.originalCode===void 0||E.unitOfCodeWork.modifiedCode===void 0)continue;let z={startLineNumber:F.originalStart,startColumn:1,endLineNumber:F.originalEnd,endColumn:1},R={startLineNumber:F.modifiedStart,startColumn:1,endLineNumber:F.modifiedEnd,endColumn:1};const S=r?E.unitOfCodeWork.modifiedCode:E.unitOfCodeWork.originalCode;S!==void 0&&(u.push({range:z,text:S}),c.push({range:R,text:S}))}i.pushEditOperations([],u,()=>[]),s.pushEditOperations([],c,()=>[]);const h=this._focusModel.nextIdx({nowrap:!0});if(h===void 0)return;const f=h===this._focusModel.focusedItemIdx?h-1:h,v=this._focusModel.items[f];v&&this.revealChunk(v)});_(this,"updateCodeChunk",()=>{this._rootChunk=this.computeCodeChunk(),this._focusModel.setItems(this.leaves??[]),this._focusModel.initFocusIdx(0),this.notifySubscribers()});_(this,"handleInstructionSubmit",t=>{const r=this._editor.getModifiedEditor(),i=this.getSelectedCodeDetails(r);if(!i)throw Error("No selected code details found");this._chatModel.currentConversationModel.sendInstructionExchange(t,i)});_(this,"updateModels",(t,r,i)=>{var c,h;const s=(h=(c=this._editor.getModel())==null?void 0:c.original)==null?void 0:h.uri,u=(i&&this._monaco.Uri.file(i.relPath))??s;if(u)if((s==null?void 0:s.fsPath)!==u.fsPath||(s==null?void 0:s.authority)!==u.authority){const f=u.with({fragment:crypto.randomUUID()}),v=u.with({fragment:crypto.randomUUID()});this._editor.setModel({original:this._monaco.editor.createModel(t,void 0,f),modified:this._monaco.editor.createModel(r??"",void 0,v)})}else this._originalCode!==t&&this.getOriginalEditor().setValue(t),this._modifiedCode!==r&&this.getModifiedEditor().setValue(r??"");else console.warn("No URI found for diff view. Not updating models.")});_(this,"updateTheme",t=>{this._monaco.editor.setTheme(t)});this._editorContainer=t,this._monaco=i,this._asyncMsgSender=new xp(s=>Is.postMessage(s)),this.initializeEditor(r)}get editorOffset(){return this._editorContainer.getBoundingClientRect().top}get currFocusedChunkIdx(){return this._focusModel.focusedItemIdx}get selectionLines(){return this._selectionLines}get mode(){return this._mode}get keybindings(){return this._keybindings}getOriginalEditor(){return this._editor.getOriginalEditor()}getModifiedEditor(){return this._editor.getModifiedEditor()}get isLoading(){return{subscribe:this._isLoadingDiffChunks.subscribe}}setLoading(t){this._isLoadingDiffChunks.set(t)}get _originalCode(){var t;return((t=this._currStream)==null?void 0:t.originalCode)??this._editor.getOriginalEditor().getValue()}get _modifiedCode(){return this._editor.getModifiedEditor().getValue()}get leaves(){const t=this.codeChunk;if(t)return Vh(t)}get codeChunk(){return this._rootChunk}computeCodeChunk(){var u,c;const t=[],r=this._editor.getLineChanges(),i=(u=this._editor.getModel())==null?void 0:u.original,s=(c=this._editor.getModel())==null?void 0:c.modified;if(r&&i&&s){for(const h of r){const f=hh({startLineNumber:h.originalStartLineNumber,startColumn:1,endLineNumber:h.originalEndLineNumber,endColumn:1}),v=hh({startLineNumber:h.modifiedStartLineNumber,startColumn:1,endLineNumber:h.modifiedEndLineNumber,endColumn:1}),m=Ag(this._editor,f,v);t.push(m)}return{id:crypto.randomUUID(),name:"",title:"",description:"",generationSource:"",supportedActions:[],children:t,childIds:t.map(h=>h.id)}}}getSelectedCodeDetails(t){const r=t.getModel();if(!r)return null;const i=r.getLanguageId(),s=1,u=1,c={lineNumber:r.getLineCount(),column:r.getLineMaxColumn(r.getLineCount())},h=pt(this._selectionLines);if(!h)throw new Error("No selection lines found");const f=Math.min(h.end+1,c.lineNumber),v=new this._monaco.Range(h.start+1,1,f,r.getLineMaxColumn(f));let m=r.getValueInRange(v);f<r.getLineCount()&&(m+=r.getEOL());const b=new this._monaco.Range(s,u,v.startLineNumber,v.startColumn),x=Math.min(v.endLineNumber+1,c.lineNumber),E=new this._monaco.Range(x,1,c.lineNumber,c.column);return{selectedCode:m,prefix:r.getValueInRange(b),suffix:r.getValueInRange(E),path:r.uri.path,language:i,prefixBegin:b.startLineNumber-1,suffixEnd:E.endLineNumber-1}}}function Ag(o,t,r){var u,c;const i=(u=o.getModel())==null?void 0:u.original,s=(c=o.getModel())==null?void 0:c.modified;if(!i||!s)throw new Error("No models found");return function(h,f,v,m){return{id:crypto.randomUUID(),name:"",title:"",description:"",generationSource:"",supportedActions:[],unitOfCodeWork:{repoRoot:"",pathName:"",originalCode:h,modifiedCode:f,lineChanges:{lineChanges:[{originalStart:v.startLineNumber,originalEnd:v.endLineNumber,modifiedStart:m.startLineNumber,modifiedEnd:m.endLineNumber}],lineOffset:0}},children:[],childIds:[]}}(i.getValueInRange(t),s.getValueInRange(r),t,r)}function hh(o){return o.endLineNumber===0?{startLineNumber:o.startLineNumber+1,startColumn:1,endLineNumber:o.startLineNumber+1,endColumn:1}:{startLineNumber:o.startLineNumber,startColumn:1,endLineNumber:o.endLineNumber+1,endColumn:1}}function fh(o){let t,r;return t=new jr({props:{size:1,variant:"ghost",color:"success",$$slots:{default:[Fg]},$$scope:{ctx:o}}}),t.$on("click",function(){ur(o[4])&&o[4].apply(this,arguments)}),{c(){G(t.$$.fragment)},m(i,s){Y(t,i,s),r=!0},p(i,s){o=i;const u={};132096&s&&(u.$$scope={dirty:s,ctx:o}),t.$set(u)},i(i){r||(O(t.$$.fragment,i),r=!0)},o(i){q(t.$$.fragment,i),r=!1},d(i){K(t,i)}}}function Fg(o){let t,r,i;return t=new ci({props:{keybinding:o[10].acceptFocusedChunk}}),{c(){G(t.$$.fragment),r=xt(`
        Accept`)},m(s,u){Y(t,s,u),re(s,r,u),i=!0},p(s,u){const c={};1024&u&&(c.keybinding=s[10].acceptFocusedChunk),t.$set(c)},i(s){i||(O(t.$$.fragment,s),i=!0)},o(s){q(t.$$.fragment,s),i=!1},d(s){s&&ie(r),K(t,s)}}}function Tg(o){let t,r,i;return t=new ci({props:{keybinding:o[10].rejectFocusedChunk}}),{c(){G(t.$$.fragment),r=xt(`
      Reject`)},m(s,u){Y(t,s,u),re(s,r,u),i=!0},p(s,u){const c={};1024&u&&(c.keybinding=s[10].rejectFocusedChunk),t.$set(c)},i(s){i||(O(t.$$.fragment,s),i=!0)},o(s){q(t.$$.fragment,s),i=!1},d(s){s&&ie(r),K(t,s)}}}function Og(o){let t,r,i,s,u,c,h,f,v,m,b=!o[3]&&fh(o);return h=new jr({props:{size:1,variant:"ghost",color:"error",$$slots:{default:[Tg]},$$scope:{ctx:o}}}),h.$on("click",function(){ur(o[5])&&o[5].apply(this,arguments)}),{c(){t=We("div"),i=qe(),s=We("div"),u=We("div"),b&&b.c(),c=qe(),G(h.$$.fragment),De(t,"class","svelte-zm1705"),pn(t,"c-chunk-diff-border--focused",!!o[7]&&o[1]),De(u,"class","c-button-container svelte-zm1705"),pn(u,"c-button-container--focused",o[1]),pn(u,"c-button-container--transparent",o[9]),De(s,"class","c-chunk-action-panel-anchor svelte-zm1705"),Ti(s,"top",o[8]+"px"),pn(s,"c-chunk-action-panel-anchor--left",o[0]==="left"),pn(s,"c-chunk-action-panel-anchor--right",o[0]==="right"),pn(s,"c-chunk-action-panel-anchor--focused",o[1])},m(x,E){re(x,t,E),re(x,i,E),re(x,s,E),He(s,u),b&&b.m(u,null),He(u,c),Y(h,u,null),f=!0,v||(m=[Nh(r=o[6].renderActionsViewZone(t,{chunk:o[7],heightInPx:o[2],onDomNodeTop:o[12]})),Nr(s,"mouseenter",o[13]),Nr(s,"mousemove",o[13]),Nr(s,"mouseleave",o[13])],v=!0)},p(x,[E]){o=x,r&&ur(r.update)&&132&E&&r.update.call(null,{chunk:o[7],heightInPx:o[2],onDomNodeTop:o[12]}),(!f||130&E)&&pn(t,"c-chunk-diff-border--focused",!!o[7]&&o[1]),o[3]?b&&($t(),q(b,1,1,()=>{b=null}),wt()):b?(b.p(o,E),8&E&&O(b,1)):(b=fh(o),b.c(),O(b,1),b.m(u,c));const F={};132096&E&&(F.$$scope={dirty:E,ctx:o}),h.$set(F),(!f||2&E)&&pn(u,"c-button-container--focused",o[1]),(!f||512&E)&&pn(u,"c-button-container--transparent",o[9]),(!f||256&E)&&Ti(s,"top",o[8]+"px"),(!f||1&E)&&pn(s,"c-chunk-action-panel-anchor--left",o[0]==="left"),(!f||1&E)&&pn(s,"c-chunk-action-panel-anchor--right",o[0]==="right"),(!f||2&E)&&pn(s,"c-chunk-action-panel-anchor--focused",o[1])},i(x){f||(O(b),O(h.$$.fragment,x),f=!0)},o(x){q(b),q(h.$$.fragment,x),f=!1},d(x){x&&(ie(t),ie(i),ie(s)),b&&b.d(),K(h),v=!1,Yu(m)}}}function Rg(o,t,r){let i,{align:s="right"}=t,{isFocused:u}=t,{heightInPx:c=1}=t,{disableApply:h=!1}=t,{onAccept:f}=t,{onReject:v}=t,{diffViewModel:m}=t,{leaf:b}=t;const x=m.keybindings;si(o,x,S=>r(10,i=S));let E=0,F,z=!1;function R(){F&&(clearTimeout(F),F=void 0),r(9,z=!1)}return o.$$set=S=>{"align"in S&&r(0,s=S.align),"isFocused"in S&&r(1,u=S.isFocused),"heightInPx"in S&&r(2,c=S.heightInPx),"disableApply"in S&&r(3,h=S.disableApply),"onAccept"in S&&r(4,f=S.onAccept),"onReject"in S&&r(5,v=S.onReject),"diffViewModel"in S&&r(6,m=S.diffViewModel),"leaf"in S&&r(7,b=S.leaf)},[s,u,c,h,f,v,m,b,E,z,i,x,S=>{r(8,E=S)},function(S){S.target.closest(".c-button-container")?R():S.type==="mouseenter"||S.type==="mousemove"?(R(),F=setTimeout(()=>{r(9,z=!0)},400)):S.type==="mouseleave"&&R()}]}class Lg extends oi{constructor(t){super(),ai(this,t,Rg,Og,ui,{align:0,isFocused:1,heightInPx:2,disableApply:3,onAccept:4,onReject:5,diffViewModel:6,leaf:7})}}function dh(o){let t,r,i;function s(c){o[18](c)}let u={onOpenChange:o[16],content:o[3],triggerOn:[tg.Hover],$$slots:{default:[jg]},$$scope:{ctx:o}};return o[4]!==void 0&&(u.requestClose=o[4]),t=new zp({props:u}),Oi.push(()=>mp(t,"requestClose",s)),{c(){G(t.$$.fragment)},m(c,h){Y(t,c,h),i=!0},p(c,h){const f={};8&h&&(f.content=c[3]),1048576&h&&(f.$$scope={dirty:h,ctx:c}),!r&&16&h&&(r=!0,f.requestClose=c[4],_p(()=>r=!1)),t.$set(f)},i(c){i||(O(t.$$.fragment,c),i=!0)},o(c){q(t.$$.fragment,c),i=!1},d(c){K(t,c)}}}function Ng(o){let t,r;return t=new Up({}),{c(){G(t.$$.fragment)},m(i,s){Y(t,i,s),r=!0},i(i){r||(O(t.$$.fragment,i),r=!0)},o(i){q(t.$$.fragment,i),r=!1},d(i){K(t,i)}}}function jg(o){let t,r;return t=new Cp({props:{variant:"ghost",color:"neutral",size:1,$$slots:{default:[Ng]},$$scope:{ctx:o}}}),t.$on("click",o[17]),{c(){G(t.$$.fragment)},m(i,s){Y(t,i,s),r=!0},p(i,s){const u={};1048576&s&&(u.$$scope={dirty:s,ctx:i}),t.$set(u)},i(i){r||(O(t.$$.fragment,i),r=!0)},o(i){q(t.$$.fragment,i),r=!1},d(i){K(t,i)}}}function Dg(o){let t;return{c(){t=We("span"),t.textContent="No changes",De(t,"class","c-diff-page-counter svelte-4zjwll")},m(r,i){re(r,t,i)},p:Ve,i:Ve,o:Ve,d(r){r&&ie(t)}}}function qg(o){var v,m;let t,r,i,s,u,c,h,f=((m=(v=o[1])==null?void 0:v.leaves)==null?void 0:m.length)+"";return c=new jh({props:{size:1,loading:o[10]}}),{c(){t=We("span"),r=xt(o[2]),i=xt(" of "),s=xt(f),u=qe(),G(c.$$.fragment),De(t,"class","c-diff-page-counter svelte-4zjwll")},m(b,x){re(b,t,x),He(t,r),He(t,i),He(t,s),He(t,u),Y(c,t,null),h=!0},p(b,x){var F,z;(!h||4&x)&&cr(r,b[2]),(!h||2&x)&&f!==(f=((z=(F=b[1])==null?void 0:F.leaves)==null?void 0:z.length)+"")&&cr(s,f);const E={};1024&x&&(E.loading=b[10]),c.$set(E)},i(b){h||(O(c.$$.fragment,b),h=!0)},o(b){q(c.$$.fragment,b),h=!1},d(b){b&&ie(t),K(c)}}}function zg(o){let t,r,i,s;return i=new jh({props:{size:1,loading:o[10]}}),{c(){t=We("span"),r=xt(`Generating changes
        `),G(i.$$.fragment),De(t,"class","c-diff-page-counter svelte-4zjwll")},m(u,c){re(u,t,c),He(t,r),Y(i,t,null),s=!0},p(u,c){const h={};1024&c&&(h.loading=u[10]),i.$set(h)},i(u){s||(O(i.$$.fragment,u),s=!0)},o(u){q(i.$$.fragment,u),s=!1},d(u){u&&ie(t),K(i)}}}function ph(o){let t,r,i,s,u,c;t=new jr({props:{size:1,variant:"ghost",color:"neutral",$$slots:{default:[Pg]},$$scope:{ctx:o}}}),t.$on("click",function(){ur(o[0].focusPrevChunk)&&o[0].focusPrevChunk.apply(this,arguments)}),i=new jr({props:{size:1,variant:"ghost",color:"neutral",$$slots:{default:[Ug]},$$scope:{ctx:o}}}),i.$on("click",function(){ur(o[0].focusNextChunk)&&o[0].focusNextChunk.apply(this,arguments)});let h=!o[12]&&gh(o);return{c(){G(t.$$.fragment),r=qe(),G(i.$$.fragment),s=qe(),h&&h.c(),u=hr()},m(f,v){Y(t,f,v),re(f,r,v),Y(i,f,v),re(f,s,v),h&&h.m(f,v),re(f,u,v),c=!0},p(f,v){o=f;const m={};1050624&v&&(m.$$scope={dirty:v,ctx:o}),t.$set(m);const b={};1050624&v&&(b.$$scope={dirty:v,ctx:o}),i.$set(b),o[12]?h&&($t(),q(h,1,1,()=>{h=null}),wt()):h?(h.p(o,v),4096&v&&O(h,1)):(h=gh(o),h.c(),O(h,1),h.m(u.parentNode,u))},i(f){c||(O(t.$$.fragment,f),O(i.$$.fragment,f),O(h),c=!0)},o(f){q(t.$$.fragment,f),q(i.$$.fragment,f),q(h),c=!1},d(f){f&&(ie(r),ie(s),ie(u)),K(t,f),K(i,f),h&&h.d(f)}}}function Pg(o){let t,r,i;return t=new ci({props:{keybinding:o[11].focusPrevChunk}}),{c(){G(t.$$.fragment),r=xt(`
        Back`)},m(s,u){Y(t,s,u),re(s,r,u),i=!0},p(s,u){const c={};2048&u&&(c.keybinding=s[11].focusPrevChunk),t.$set(c)},i(s){i||(O(t.$$.fragment,s),i=!0)},o(s){q(t.$$.fragment,s),i=!1},d(s){s&&ie(r),K(t,s)}}}function Ug(o){let t,r,i;return t=new ci({props:{keybinding:o[11].focusNextChunk}}),{c(){G(t.$$.fragment),r=xt(`
        Next`)},m(s,u){Y(t,s,u),re(s,r,u),i=!0},p(s,u){const c={};2048&u&&(c.keybinding=s[11].focusNextChunk),t.$set(c)},i(s){i||(O(t.$$.fragment,s),i=!0)},o(s){q(t.$$.fragment,s),i=!1},d(s){s&&ie(r),K(t,s)}}}function gh(o){let t,r,i,s=!o[13]&&mh(o);return r=new jr({props:{size:1,variant:"ghost",color:"error",$$slots:{default:[Hg]},$$scope:{ctx:o}}}),r.$on("click",function(){ur(o[0].rejectAllChunks)&&o[0].rejectAllChunks.apply(this,arguments)}),{c(){s&&s.c(),t=qe(),G(r.$$.fragment)},m(u,c){s&&s.m(u,c),re(u,t,c),Y(r,u,c),i=!0},p(u,c){(o=u)[13]?s&&($t(),q(s,1,1,()=>{s=null}),wt()):s?(s.p(o,c),8192&c&&O(s,1)):(s=mh(o),s.c(),O(s,1),s.m(t.parentNode,t));const h={};1050624&c&&(h.$$scope={dirty:c,ctx:o}),r.$set(h)},i(u){i||(O(s),O(r.$$.fragment,u),i=!0)},o(u){q(s),q(r.$$.fragment,u),i=!1},d(u){u&&ie(t),s&&s.d(u),K(r,u)}}}function mh(o){let t,r;return t=new jr({props:{size:1,variant:"ghost",color:"success",$$slots:{default:[Wg]},$$scope:{ctx:o}}}),t.$on("click",function(){ur(o[0].acceptAllChunks)&&o[0].acceptAllChunks.apply(this,arguments)}),{c(){G(t.$$.fragment)},m(i,s){Y(t,i,s),r=!0},p(i,s){o=i;const u={};1050624&s&&(u.$$scope={dirty:s,ctx:o}),t.$set(u)},i(i){r||(O(t.$$.fragment,i),r=!0)},o(i){q(t.$$.fragment,i),r=!1},d(i){K(t,i)}}}function Wg(o){let t,r,i;return t=new ci({props:{keybinding:o[11].acceptAllChunks}}),{c(){G(t.$$.fragment),r=xt(`
            Accept All`)},m(s,u){Y(t,s,u),re(s,r,u),i=!0},p(s,u){const c={};2048&u&&(c.keybinding=s[11].acceptAllChunks),t.$set(c)},i(s){i||(O(t.$$.fragment,s),i=!0)},o(s){q(t.$$.fragment,s),i=!1},d(s){s&&ie(r),K(t,s)}}}function Hg(o){let t,r,i;return t=new ci({props:{keybinding:o[11].rejectAllChunks}}),{c(){G(t.$$.fragment),r=xt(`
          Reject All`)},m(s,u){Y(t,s,u),re(s,r,u),i=!0},p(s,u){const c={};2048&u&&(c.keybinding=s[11].rejectAllChunks),t.$set(c)},i(s){i||(O(t.$$.fragment,s),i=!0)},o(s){q(t.$$.fragment,s),i=!1},d(s){s&&ie(r),K(t,s)}}}function Vg(o){let t,r,i,s,u,c,h,f=o[9]&&dh(o);const v=[zg,qg,Dg],m=[];function b(E,F){return!E[5]&&E[10]?0:E[5]?1:2}s=b(o),u=m[s]=v[s](o);let x=o[5]&&ph(o);return{c(){t=We("div"),r=We("div"),f&&f.c(),i=qe(),u.c(),c=qe(),x&&x.c(),De(r,"class","c-button-container svelte-4zjwll"),De(t,"class","c-top-action-panel-anchor svelte-4zjwll")},m(E,F){re(E,t,F),He(t,r),f&&f.m(r,null),He(r,i),m[s].m(r,null),He(r,c),x&&x.m(r,null),h=!0},p(E,[F]){E[9]?f?(f.p(E,F),512&F&&O(f,1)):(f=dh(E),f.c(),O(f,1),f.m(r,i)):f&&($t(),q(f,1,1,()=>{f=null}),wt());let z=s;s=b(E),s===z?m[s].p(E,F):($t(),q(m[z],1,1,()=>{m[z]=null}),wt(),u=m[s],u?u.p(E,F):(u=m[s]=v[s](E),u.c()),O(u,1),u.m(r,c)),E[5]?x?(x.p(E,F),32&F&&O(x,1)):(x=ph(E),x.c(),O(x,1),x.m(r,null)):x&&($t(),q(x,1,1,()=>{x=null}),wt())},i(E){h||(O(f),O(u),O(x),h=!0)},o(E){q(f),q(u),q(x),h=!1},d(E){E&&ie(t),f&&f.d(),m[s].d(),x&&x.d()}}}function Bg(o,t,r){let i,s,u,c,h,f,v,m,b,x,E=Ve,F=()=>(E(),E=ar(T,me=>r(1,f=me)),T),z=Ve,R=Ve,S=Ve;o.$$.on_destroy.push(()=>E()),o.$$.on_destroy.push(()=>z()),o.$$.on_destroy.push(()=>R()),o.$$.on_destroy.push(()=>S());let{diffViewModel:T}=t;F();const ne=T.keybindings;si(o,ne,me=>r(11,m=me));const de=T.requestId;si(o,de,me=>r(9,h=me));let oe,Ne="x",pe="Copy request ID",Ie=()=>{};return o.$$set=me=>{"diffViewModel"in me&&F(r(0,T=me.diffViewModel))},o.$$.update=()=>{var me;2&o.$$.dirty&&(r(8,i=f.disableResolution),R(),R=ar(i,Te=>r(12,b=Te))),2&o.$$.dirty&&(r(7,s=f.disableApply),S(),S=ar(s,Te=>r(13,x=Te))),2&o.$$.dirty&&(f.currFocusedChunkIdx!==void 0?r(2,Ne=(f.currFocusedChunkIdx+1).toString()):r(2,Ne="x")),2&o.$$.dirty&&(r(6,u=f.isLoading),z(),z=ar(u,Te=>r(10,v=Te))),2&o.$$.dirty&&r(5,c=!!((me=f.leaves)!=null&&me.length))},[T,f,Ne,pe,Ie,c,u,s,i,h,v,m,b,x,ne,de,function(me){me||(clearTimeout(oe),oe=void 0,r(3,pe="Copy request ID"))},async function(){h&&(await navigator.clipboard.writeText(h),r(3,pe="Copied!"),clearTimeout(oe),oe=setTimeout(Ie,1500))},function(me){Ie=me,r(4,Ie)}]}class Gg extends oi{constructor(t){super(),ai(this,t,Bg,Vg,ui,{diffViewModel:0})}}var Wo,Ho,Uu={exports:{}};Wo=Uu,Ho=Uu.exports,(function(){var o,t="Expected a function",r="__lodash_hash_undefined__",i="__lodash_placeholder__",s=16,u=32,c=64,h=128,f=256,v=1/0,m=9007199254740991,b=NaN,x=4294967295,E=[["ary",h],["bind",1],["bindKey",2],["curry",8],["curryRight",s],["flip",512],["partial",u],["partialRight",c],["rearg",f]],F="[object Arguments]",z="[object Array]",R="[object Boolean]",S="[object Date]",T="[object Error]",ne="[object Function]",de="[object GeneratorFunction]",oe="[object Map]",Ne="[object Number]",pe="[object Object]",Ie="[object Promise]",me="[object RegExp]",Te="[object Set]",Ue="[object String]",st="[object Symbol]",Se="[object WeakMap]",we="[object ArrayBuffer]",at="[object DataView]",gn="[object Float32Array]",Ct="[object Float64Array]",fe="[object Int8Array]",mn="[object Int16Array]",Fn="[object Int32Array]",Vn="[object Uint8Array]",fr="[object Uint8ClampedArray]",li="[object Uint16Array]",Ui="[object Uint32Array]",ra=/\b__p \+= '';/g,ia=/\b(__p \+=) '' \+/g,Ts=/(__e\(.*?\)|\b__t\)) \+\n'';/g,Wi=/&(?:amp|lt|gt|quot|#39);/g,Hi=/[&<>"']/g,sa=RegExp(Wi.source),oa=RegExp(Hi.source),hi=/<%-([\s\S]+?)%>/g,Vi=/<%([\s\S]+?)%>/g,Bi=/<%=([\s\S]+?)%>/g,rn=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Os=/^\w*$/,Rs=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,Gi=/[\\^$.*+?()[\]{}|]/g,Ls=RegExp(Gi.source),dr=/^\s+/,Ns=/\s/,js=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,aa=/\{\n\/\* \[wrapped with (.+)\] \*/,Bn=/,? & /,Ds=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,ua=/[()=,{}\[\]\/\s]/,ca=/\\(\\)?/g,Yi=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,pr=/\w*$/,Ki=/^[-+]0x[0-9a-f]+$/i,Qi=/^0b[01]+$/i,Ji=/^\[object .+?Constructor\]$/,qr=/^0o[0-7]+$/i,la=/^(?:0|[1-9]\d*)$/,ha=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,fi=/($^)/,fa=/['\n\r\u2028\u2029\\]/g,di="\\ud800-\\udfff",Xi="\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff",pi="\\u2700-\\u27bf",Tn="a-z\\xdf-\\xf6\\xf8-\\xff",sn="A-Z\\xc0-\\xd6\\xd8-\\xde",On="\\ufe0e\\ufe0f",zr="\\xac\\xb1\\xd7\\xf7\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf\\u2000-\\u206f \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",Rn="['’]",da="["+di+"]",Pr="["+zr+"]",Ln="["+Xi+"]",Zi="\\d+",qs="["+pi+"]",zs="["+Tn+"]",Ps="[^"+di+zr+Zi+pi+Tn+sn+"]",gi="\\ud83c[\\udffb-\\udfff]",es="[^"+di+"]",gr="(?:\\ud83c[\\udde6-\\uddff]){2}",Nn="[\\ud800-\\udbff][\\udc00-\\udfff]",mr="["+sn+"]",_n="\\u200d",Us="(?:"+zs+"|"+Ps+")",Gn="(?:"+mr+"|"+Ps+")",ts="(?:['’](?:d|ll|m|re|s|t|ve))?",Ws="(?:['’](?:D|LL|M|RE|S|T|VE))?",Ur="(?:"+Ln+"|"+gi+")?",mi="["+On+"]?",ns=mi+Ur+"(?:"+_n+"(?:"+[es,gr,Nn].join("|")+")"+mi+Ur+")*",Hs="(?:"+[qs,gr,Nn].join("|")+")"+ns,Vs="(?:"+[es+Ln+"?",Ln,gr,Nn,da].join("|")+")",Wr=RegExp(Rn,"g"),Bs=RegExp(Ln,"g"),rs=RegExp(gi+"(?="+gi+")|"+Vs+ns,"g"),C=RegExp([mr+"?"+zs+"+"+ts+"(?="+[Pr,mr,"$"].join("|")+")",Gn+"+"+Ws+"(?="+[Pr,mr+Us,"$"].join("|")+")",mr+"?"+Us+"+"+ts,mr+"+"+Ws,"\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])","\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",Zi,Hs].join("|"),"g"),k=RegExp("["+_n+di+Xi+On+"]"),W=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,ee=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],ze=-1,X={};X[gn]=X[Ct]=X[fe]=X[mn]=X[Fn]=X[Vn]=X[fr]=X[li]=X[Ui]=!0,X[F]=X[z]=X[we]=X[R]=X[at]=X[S]=X[T]=X[ne]=X[oe]=X[Ne]=X[pe]=X[me]=X[Te]=X[Ue]=X[Se]=!1;var he={};he[F]=he[z]=he[we]=he[at]=he[R]=he[S]=he[gn]=he[Ct]=he[fe]=he[mn]=he[Fn]=he[oe]=he[Ne]=he[pe]=he[me]=he[Te]=he[Ue]=he[st]=he[Vn]=he[fr]=he[li]=he[Ui]=!0,he[T]=he[ne]=he[Se]=!1;var Je={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},Ht=parseFloat,tt=parseInt,Xe=typeof tn=="object"&&tn&&tn.Object===Object&&tn,on=typeof self=="object"&&self&&self.Object===Object&&self,Pe=Xe||on||Function("return this")(),ut=Ho&&!Ho.nodeType&&Ho,St=ut&&Wo&&!Wo.nodeType&&Wo,Yn=St&&St.exports===ut,_r=Yn&&Xe.process,ct=function(){try{var M=St&&St.require&&St.require("util").types;return M||_r&&_r.binding&&_r.binding("util")}catch{}}(),vr=ct&&ct.isArrayBuffer,is=ct&&ct.isDate,Gs=ct&&ct.isMap,Ys=ct&&ct.isRegExp,Oe=ct&&ct.isSet,Ae=ct&&ct.isTypedArray;function gt(M,j,D){switch(D.length){case 0:return M.call(j);case 1:return M.call(j,D[0]);case 2:return M.call(j,D[0],D[1]);case 3:return M.call(j,D[0],D[1],D[2])}return M.apply(j,D)}function jn(M,j,D,B){for(var _e=-1,Me=M==null?0:M.length;++_e<Me;){var lt=M[_e];j(B,lt,D(lt),M)}return B}function kt(M,j){for(var D=-1,B=M==null?0:M.length;++D<B&&j(M[D],D,M)!==!1;);return M}function qt(M,j){for(var D=M==null?0:M.length;D--&&j(M[D],D,M)!==!1;);return M}function an(M,j){for(var D=-1,B=M==null?0:M.length;++D<B;)if(!j(M[D],D,M))return!1;return!0}function It(M,j){for(var D=-1,B=M==null?0:M.length,_e=0,Me=[];++D<B;){var lt=M[D];j(lt,D,M)&&(Me[_e++]=lt)}return Me}function Dn(M,j){return!(M==null||!M.length)&&Qn(M,j,0)>-1}function yr(M,j,D){for(var B=-1,_e=M==null?0:M.length;++B<_e;)if(D(j,M[B]))return!0;return!1}function je(M,j){for(var D=-1,B=M==null?0:M.length,_e=Array(B);++D<B;)_e[D]=j(M[D],D,M);return _e}function Vt(M,j){for(var D=-1,B=j.length,_e=M.length;++D<B;)M[_e+D]=j[D];return M}function vn(M,j,D,B){var _e=-1,Me=M==null?0:M.length;for(B&&Me&&(D=M[++_e]);++_e<Me;)D=j(D,M[_e],_e,M);return D}function Ks(M,j,D,B){var _e=M==null?0:M.length;for(B&&_e&&(D=M[--_e]);_e--;)D=j(D,M[_e],_e,M);return D}function br(M,j){for(var D=-1,B=M==null?0:M.length;++D<B;)if(j(M[D],D,M))return!0;return!1}var ss=pa("length");function Hr(M,j,D){var B;return D(M,function(_e,Me,lt){if(j(_e,Me,lt))return B=Me,!1}),B}function Kn(M,j,D,B){for(var _e=M.length,Me=D+(B?1:-1);B?Me--:++Me<_e;)if(j(M[Me],Me,M))return Me;return-1}function Qn(M,j,D){return j==j?function(B,_e,Me){for(var lt=Me-1,qn=B.length;++lt<qn;)if(B[lt]===_e)return lt;return-1}(M,j,D):Kn(M,Vr,D)}function _i(M,j,D,B){for(var _e=D-1,Me=M.length;++_e<Me;)if(B(M[_e],j))return _e;return-1}function Vr(M){return M!=M}function nc(M,j){var D=M==null?0:M.length;return D?ma(M,j)/D:b}function pa(M){return function(j){return j==null?o:j[M]}}function ga(M){return function(j){return M==null?o:M[j]}}function rc(M,j,D,B,_e){return _e(M,function(Me,lt,qn){D=B?(B=!1,Me):j(D,Me,lt,qn)}),D}function ma(M,j){for(var D,B=-1,_e=M.length;++B<_e;){var Me=j(M[B]);Me!==o&&(D=D===o?Me:D+Me)}return D}function _a(M,j){for(var D=-1,B=Array(M);++D<M;)B[D]=j(D);return B}function ic(M){return M&&M.slice(0,uc(M)+1).replace(dr,"")}function Bt(M){return function(j){return M(j)}}function va(M,j){return je(j,function(D){return M[D]})}function os(M,j){return M.has(j)}function sc(M,j){for(var D=-1,B=M.length;++D<B&&Qn(j,M[D],0)>-1;);return D}function oc(M,j){for(var D=M.length;D--&&Qn(j,M[D],0)>-1;);return D}var cf=ga({À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",Ç:"C",ç:"c",Ð:"D",ð:"d",È:"E",É:"E",Ê:"E",Ë:"E",è:"e",é:"e",ê:"e",ë:"e",Ì:"I",Í:"I",Î:"I",Ï:"I",ì:"i",í:"i",î:"i",ï:"i",Ñ:"N",ñ:"n",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",Ù:"U",Ú:"U",Û:"U",Ü:"U",ù:"u",ú:"u",û:"u",ü:"u",Ý:"Y",ý:"y",ÿ:"y",Æ:"Ae",æ:"ae",Þ:"Th",þ:"th",ß:"ss",Ā:"A",Ă:"A",Ą:"A",ā:"a",ă:"a",ą:"a",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",ć:"c",ĉ:"c",ċ:"c",č:"c",Ď:"D",Đ:"D",ď:"d",đ:"d",Ē:"E",Ĕ:"E",Ė:"E",Ę:"E",Ě:"E",ē:"e",ĕ:"e",ė:"e",ę:"e",ě:"e",Ĝ:"G",Ğ:"G",Ġ:"G",Ģ:"G",ĝ:"g",ğ:"g",ġ:"g",ģ:"g",Ĥ:"H",Ħ:"H",ĥ:"h",ħ:"h",Ĩ:"I",Ī:"I",Ĭ:"I",Į:"I",İ:"I",ĩ:"i",ī:"i",ĭ:"i",į:"i",ı:"i",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",ĸ:"k",Ĺ:"L",Ļ:"L",Ľ:"L",Ŀ:"L",Ł:"L",ĺ:"l",ļ:"l",ľ:"l",ŀ:"l",ł:"l",Ń:"N",Ņ:"N",Ň:"N",Ŋ:"N",ń:"n",ņ:"n",ň:"n",ŋ:"n",Ō:"O",Ŏ:"O",Ő:"O",ō:"o",ŏ:"o",ő:"o",Ŕ:"R",Ŗ:"R",Ř:"R",ŕ:"r",ŗ:"r",ř:"r",Ś:"S",Ŝ:"S",Ş:"S",Š:"S",ś:"s",ŝ:"s",ş:"s",š:"s",Ţ:"T",Ť:"T",Ŧ:"T",ţ:"t",ť:"t",ŧ:"t",Ũ:"U",Ū:"U",Ŭ:"U",Ů:"U",Ű:"U",Ų:"U",ũ:"u",ū:"u",ŭ:"u",ů:"u",ű:"u",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",Ż:"Z",Ž:"Z",ź:"z",ż:"z",ž:"z",Ĳ:"IJ",ĳ:"ij",Œ:"Oe",œ:"oe",ŉ:"'n",ſ:"s"}),lf=ga({"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"});function hf(M){return"\\"+Je[M]}function vi(M){return k.test(M)}function ya(M){var j=-1,D=Array(M.size);return M.forEach(function(B,_e){D[++j]=[_e,B]}),D}function ac(M,j){return function(D){return M(j(D))}}function $r(M,j){for(var D=-1,B=M.length,_e=0,Me=[];++D<B;){var lt=M[D];lt!==j&&lt!==i||(M[D]=i,Me[_e++]=D)}return Me}function Qs(M){var j=-1,D=Array(M.size);return M.forEach(function(B){D[++j]=B}),D}function ff(M){var j=-1,D=Array(M.size);return M.forEach(function(B){D[++j]=[B,B]}),D}function yi(M){return vi(M)?function(j){for(var D=rs.lastIndex=0;rs.test(j);)++D;return D}(M):ss(M)}function yn(M){return vi(M)?function(j){return j.match(rs)||[]}(M):function(j){return j.split("")}(M)}function uc(M){for(var j=M.length;j--&&Ns.test(M.charAt(j)););return j}var df=ga({"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"}),bi=function M(j){var D,B=(j=j==null?Pe:bi.defaults(Pe.Object(),j,bi.pick(Pe,ee))).Array,_e=j.Date,Me=j.Error,lt=j.Function,qn=j.Math,Be=j.Object,ba=j.RegExp,pf=j.String,un=j.TypeError,Js=B.prototype,gf=lt.prototype,$i=Be.prototype,Xs=j["__core-js_shared__"],Zs=gf.toString,Le=$i.hasOwnProperty,mf=0,cc=(D=/[^.]+$/.exec(Xs&&Xs.keys&&Xs.keys.IE_PROTO||""))?"Symbol(src)_1."+D:"",eo=$i.toString,_f=Zs.call(Be),vf=Pe._,yf=ba("^"+Zs.call(Le).replace(Gi,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),to=Yn?j.Buffer:o,wr=j.Symbol,no=j.Uint8Array,lc=to?to.allocUnsafe:o,ro=ac(Be.getPrototypeOf,Be),hc=Be.create,fc=$i.propertyIsEnumerable,io=Js.splice,dc=wr?wr.isConcatSpreadable:o,as=wr?wr.iterator:o,Br=wr?wr.toStringTag:o,so=function(){try{var e=Jr(Be,"defineProperty");return e({},"",{}),e}catch{}}(),bf=j.clearTimeout!==Pe.clearTimeout&&j.clearTimeout,$f=_e&&_e.now!==Pe.Date.now&&_e.now,wf=j.setTimeout!==Pe.setTimeout&&j.setTimeout,oo=qn.ceil,ao=qn.floor,$a=Be.getOwnPropertySymbols,xf=to?to.isBuffer:o,pc=j.isFinite,Cf=Js.join,Sf=ac(Be.keys,Be),ht=qn.max,At=qn.min,If=_e.now,Ef=j.parseInt,gc=qn.random,Mf=Js.reverse,wa=Jr(j,"DataView"),us=Jr(j,"Map"),xa=Jr(j,"Promise"),wi=Jr(j,"Set"),cs=Jr(j,"WeakMap"),ls=Jr(Be,"create"),uo=cs&&new cs,xi={},kf=Xr(wa),Af=Xr(us),Ff=Xr(xa),Tf=Xr(wi),Of=Xr(cs),co=wr?wr.prototype:o,hs=co?co.valueOf:o,mc=co?co.toString:o;function p(e){if(Ze(e)&&!$e(e)&&!(e instanceof Ee)){if(e instanceof cn)return e;if(Le.call(e,"__wrapped__"))return _l(e)}return new cn(e)}var Ci=function(){function e(){}return function(n){if(!Qe(n))return{};if(hc)return hc(n);e.prototype=n;var a=new e;return e.prototype=o,a}}();function lo(){}function cn(e,n){this.__wrapped__=e,this.__actions__=[],this.__chain__=!!n,this.__index__=0,this.__values__=o}function Ee(e){this.__wrapped__=e,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=x,this.__views__=[]}function Gr(e){var n=-1,a=e==null?0:e.length;for(this.clear();++n<a;){var l=e[n];this.set(l[0],l[1])}}function Jn(e){var n=-1,a=e==null?0:e.length;for(this.clear();++n<a;){var l=e[n];this.set(l[0],l[1])}}function Xn(e){var n=-1,a=e==null?0:e.length;for(this.clear();++n<a;){var l=e[n];this.set(l[0],l[1])}}function Yr(e){var n=-1,a=e==null?0:e.length;for(this.__data__=new Xn;++n<a;)this.add(e[n])}function bn(e){var n=this.__data__=new Jn(e);this.size=n.size}function _c(e,n){var a=$e(e),l=!a&&Zr(e),d=!a&&!l&&Er(e),g=!a&&!l&&!d&&Mi(e),$=a||l||d||g,w=$?_a(e.length,pf):[],I=w.length;for(var L in e)!n&&!Le.call(e,L)||$&&(L=="length"||d&&(L=="offset"||L=="parent")||g&&(L=="buffer"||L=="byteLength"||L=="byteOffset")||nr(L,I))||w.push(L);return w}function vc(e){var n=e.length;return n?e[Ra(0,n-1)]:o}function Rf(e,n){return So(zt(e),Kr(n,0,e.length))}function Lf(e){return So(zt(e))}function Ca(e,n,a){(a!==o&&!$n(e[n],a)||a===o&&!(n in e))&&Zn(e,n,a)}function fs(e,n,a){var l=e[n];Le.call(e,n)&&$n(l,a)&&(a!==o||n in e)||Zn(e,n,a)}function ho(e,n){for(var a=e.length;a--;)if($n(e[a][0],n))return a;return-1}function Nf(e,n,a,l){return xr(e,function(d,g,$){n(l,d,a(d),$)}),l}function yc(e,n){return e&&Pn(n,mt(n),e)}function Zn(e,n,a){n=="__proto__"&&so?so(e,n,{configurable:!0,enumerable:!0,value:a,writable:!0}):e[n]=a}function Sa(e,n){for(var a=-1,l=n.length,d=B(l),g=e==null;++a<l;)d[a]=g?o:su(e,n[a]);return d}function Kr(e,n,a){return e==e&&(a!==o&&(e=e<=a?e:a),n!==o&&(e=e>=n?e:n)),e}function ln(e,n,a,l,d,g){var $,w=1&n,I=2&n,L=4&n;if(a&&($=d?a(e,l,d,g):a(e)),$!==o)return $;if(!Qe(e))return e;var A=$e(e);if(A){if($=function(N){var U=N.length,ce=new N.constructor(U);return U&&typeof N[0]=="string"&&Le.call(N,"index")&&(ce.index=N.index,ce.input=N.input),ce}(e),!w)return zt(e,$)}else{var P=Ft(e),J=P==ne||P==de;if(Er(e))return Wc(e,w);if(P==pe||P==F||J&&!d){if($=I||J?{}:ul(e),!w)return I?function(N,U){return Pn(N,ol(N),U)}(e,function(N,U){return N&&Pn(U,Ut(U),N)}($,e)):function(N,U){return Pn(N,Ya(N),U)}(e,yc($,e))}else{if(!he[P])return d?e:{};$=function(N,U,ce){var V,ye=N.constructor;switch(U){case we:return Pa(N);case R:case S:return new ye(+N);case at:return function(ge,Fe){var te=Fe?Pa(ge.buffer):ge.buffer;return new ge.constructor(te,ge.byteOffset,ge.byteLength)}(N,ce);case gn:case Ct:case fe:case mn:case Fn:case Vn:case fr:case li:case Ui:return Hc(N,ce);case oe:return new ye;case Ne:case Ue:return new ye(N);case me:return function(ge){var Fe=new ge.constructor(ge.source,pr.exec(ge));return Fe.lastIndex=ge.lastIndex,Fe}(N);case Te:return new ye;case st:return V=N,hs?Be(hs.call(V)):{}}}(e,P,w)}}g||(g=new bn);var Z=g.get(e);if(Z)return Z;g.set(e,$),Ll(e)?e.forEach(function(N){$.add(ln(N,n,a,N,e,g))}):Ol(e)&&e.forEach(function(N,U){$.set(U,ln(N,n,a,U,e,g))});var se=A?o:(L?I?Va:Ha:I?Ut:mt)(e);return kt(se||e,function(N,U){se&&(N=e[U=N]),fs($,U,ln(N,n,a,U,e,g))}),$}function bc(e,n,a){var l=a.length;if(e==null)return!l;for(e=Be(e);l--;){var d=a[l],g=n[d],$=e[d];if($===o&&!(d in e)||!g($))return!1}return!0}function $c(e,n,a){if(typeof e!="function")throw new un(t);return ys(function(){e.apply(o,a)},n)}function ds(e,n,a,l){var d=-1,g=Dn,$=!0,w=e.length,I=[],L=n.length;if(!w)return I;a&&(n=je(n,Bt(a))),l?(g=yr,$=!1):n.length>=200&&(g=os,$=!1,n=new Yr(n));e:for(;++d<w;){var A=e[d],P=a==null?A:a(A);if(A=l||A!==0?A:0,$&&P==P){for(var J=L;J--;)if(n[J]===P)continue e;I.push(A)}else g(n,P,l)||I.push(A)}return I}p.templateSettings={escape:hi,evaluate:Vi,interpolate:Bi,variable:"",imports:{_:p}},p.prototype=lo.prototype,p.prototype.constructor=p,cn.prototype=Ci(lo.prototype),cn.prototype.constructor=cn,Ee.prototype=Ci(lo.prototype),Ee.prototype.constructor=Ee,Gr.prototype.clear=function(){this.__data__=ls?ls(null):{},this.size=0},Gr.prototype.delete=function(e){var n=this.has(e)&&delete this.__data__[e];return this.size-=n?1:0,n},Gr.prototype.get=function(e){var n=this.__data__;if(ls){var a=n[e];return a===r?o:a}return Le.call(n,e)?n[e]:o},Gr.prototype.has=function(e){var n=this.__data__;return ls?n[e]!==o:Le.call(n,e)},Gr.prototype.set=function(e,n){var a=this.__data__;return this.size+=this.has(e)?0:1,a[e]=ls&&n===o?r:n,this},Jn.prototype.clear=function(){this.__data__=[],this.size=0},Jn.prototype.delete=function(e){var n=this.__data__,a=ho(n,e);return!(a<0||(a==n.length-1?n.pop():io.call(n,a,1),--this.size,0))},Jn.prototype.get=function(e){var n=this.__data__,a=ho(n,e);return a<0?o:n[a][1]},Jn.prototype.has=function(e){return ho(this.__data__,e)>-1},Jn.prototype.set=function(e,n){var a=this.__data__,l=ho(a,e);return l<0?(++this.size,a.push([e,n])):a[l][1]=n,this},Xn.prototype.clear=function(){this.size=0,this.__data__={hash:new Gr,map:new(us||Jn),string:new Gr}},Xn.prototype.delete=function(e){var n=Co(this,e).delete(e);return this.size-=n?1:0,n},Xn.prototype.get=function(e){return Co(this,e).get(e)},Xn.prototype.has=function(e){return Co(this,e).has(e)},Xn.prototype.set=function(e,n){var a=Co(this,e),l=a.size;return a.set(e,n),this.size+=a.size==l?0:1,this},Yr.prototype.add=Yr.prototype.push=function(e){return this.__data__.set(e,r),this},Yr.prototype.has=function(e){return this.__data__.has(e)},bn.prototype.clear=function(){this.__data__=new Jn,this.size=0},bn.prototype.delete=function(e){var n=this.__data__,a=n.delete(e);return this.size=n.size,a},bn.prototype.get=function(e){return this.__data__.get(e)},bn.prototype.has=function(e){return this.__data__.has(e)},bn.prototype.set=function(e,n){var a=this.__data__;if(a instanceof Jn){var l=a.__data__;if(!us||l.length<199)return l.push([e,n]),this.size=++a.size,this;a=this.__data__=new Xn(l)}return a.set(e,n),this.size=a.size,this};var xr=Yc(zn),wc=Yc(Ea,!0);function jf(e,n){var a=!0;return xr(e,function(l,d,g){return a=!!n(l,d,g)}),a}function fo(e,n,a){for(var l=-1,d=e.length;++l<d;){var g=e[l],$=n(g);if($!=null&&(w===o?$==$&&!Yt($):a($,w)))var w=$,I=g}return I}function xc(e,n){var a=[];return xr(e,function(l,d,g){n(l,d,g)&&a.push(l)}),a}function Et(e,n,a,l,d){var g=-1,$=e.length;for(a||(a=Kf),d||(d=[]);++g<$;){var w=e[g];n>0&&a(w)?n>1?Et(w,n-1,a,l,d):Vt(d,w):l||(d[d.length]=w)}return d}var Ia=Kc(),Cc=Kc(!0);function zn(e,n){return e&&Ia(e,n,mt)}function Ea(e,n){return e&&Cc(e,n,mt)}function po(e,n){return It(n,function(a){return rr(e[a])})}function Qr(e,n){for(var a=0,l=(n=Sr(n,e)).length;e!=null&&a<l;)e=e[Un(n[a++])];return a&&a==l?e:o}function Sc(e,n,a){var l=n(e);return $e(e)?l:Vt(l,a(e))}function Rt(e){return e==null?e===o?"[object Undefined]":"[object Null]":Br&&Br in Be(e)?function(n){var a=Le.call(n,Br),l=n[Br];try{n[Br]=o;var d=!0}catch{}var g=eo.call(n);return d&&(a?n[Br]=l:delete n[Br]),g}(e):function(n){return eo.call(n)}(e)}function Ma(e,n){return e>n}function Df(e,n){return e!=null&&Le.call(e,n)}function qf(e,n){return e!=null&&n in Be(e)}function ka(e,n,a){for(var l=a?yr:Dn,d=e[0].length,g=e.length,$=g,w=B(g),I=1/0,L=[];$--;){var A=e[$];$&&n&&(A=je(A,Bt(n))),I=At(A.length,I),w[$]=!a&&(n||d>=120&&A.length>=120)?new Yr($&&A):o}A=e[0];var P=-1,J=w[0];e:for(;++P<d&&L.length<I;){var Z=A[P],se=n?n(Z):Z;if(Z=a||Z!==0?Z:0,!(J?os(J,se):l(L,se,a))){for($=g;--$;){var N=w[$];if(!(N?os(N,se):l(e[$],se,a)))continue e}J&&J.push(se),L.push(Z)}}return L}function ps(e,n,a){var l=(e=fl(e,n=Sr(n,e)))==null?e:e[Un(fn(n))];return l==null?o:gt(l,e,a)}function Ic(e){return Ze(e)&&Rt(e)==F}function gs(e,n,a,l,d){return e===n||(e==null||n==null||!Ze(e)&&!Ze(n)?e!=e&&n!=n:function(g,$,w,I,L,A){var P=$e(g),J=$e($),Z=P?z:Ft(g),se=J?z:Ft($),N=(Z=Z==F?pe:Z)==pe,U=(se=se==F?pe:se)==pe,ce=Z==se;if(ce&&Er(g)){if(!Er($))return!1;P=!0,N=!1}if(ce&&!N)return A||(A=new bn),P||Mi(g)?sl(g,$,w,I,L,A):function(te,le,ft,rt,Nt,Ge,Tt){switch(ft){case at:if(te.byteLength!=le.byteLength||te.byteOffset!=le.byteOffset)return!1;te=te.buffer,le=le.buffer;case we:return!(te.byteLength!=le.byteLength||!Ge(new no(te),new no(le)));case R:case S:case Ne:return $n(+te,+le);case T:return te.name==le.name&&te.message==le.message;case me:case Ue:return te==le+"";case oe:var Wn=ya;case Te:var Mr=1&rt;if(Wn||(Wn=Qs),te.size!=le.size&&!Mr)return!1;var Ro=Tt.get(te);if(Ro)return Ro==le;rt|=2,Tt.set(te,le);var gu=sl(Wn(te),Wn(le),rt,Nt,Ge,Tt);return Tt.delete(te),gu;case st:if(hs)return hs.call(te)==hs.call(le)}return!1}(g,$,Z,w,I,L,A);if(!(1&w)){var V=N&&Le.call(g,"__wrapped__"),ye=U&&Le.call($,"__wrapped__");if(V||ye){var ge=V?g.value():g,Fe=ye?$.value():$;return A||(A=new bn),L(ge,Fe,w,I,A)}}return!!ce&&(A||(A=new bn),function(te,le,ft,rt,Nt,Ge){var Tt=1&ft,Wn=Ha(te),Mr=Wn.length,Ro=Ha(le),gu=Ro.length;if(Mr!=gu&&!Tt)return!1;for(var Lo=Mr;Lo--;){var ei=Wn[Lo];if(!(Tt?ei in le:Le.call(le,ei)))return!1}var Kl=Ge.get(te),Ql=Ge.get(le);if(Kl&&Ql)return Kl==le&&Ql==te;var No=!0;Ge.set(te,le),Ge.set(le,te);for(var mu=Tt;++Lo<Mr;){var jo=te[ei=Wn[Lo]],Do=le[ei];if(rt)var Jl=Tt?rt(Do,jo,ei,le,te,Ge):rt(jo,Do,ei,te,le,Ge);if(!(Jl===o?jo===Do||Nt(jo,Do,ft,rt,Ge):Jl)){No=!1;break}mu||(mu=ei=="constructor")}if(No&&!mu){var qo=te.constructor,zo=le.constructor;qo==zo||!("constructor"in te)||!("constructor"in le)||typeof qo=="function"&&qo instanceof qo&&typeof zo=="function"&&zo instanceof zo||(No=!1)}return Ge.delete(te),Ge.delete(le),No}(g,$,w,I,L,A))}(e,n,a,l,gs,d))}function Aa(e,n,a,l){var d=a.length,g=d,$=!l;if(e==null)return!g;for(e=Be(e);d--;){var w=a[d];if($&&w[2]?w[1]!==e[w[0]]:!(w[0]in e))return!1}for(;++d<g;){var I=(w=a[d])[0],L=e[I],A=w[1];if($&&w[2]){if(L===o&&!(I in e))return!1}else{var P=new bn;if(l)var J=l(L,A,I,e,n,P);if(!(J===o?gs(A,L,3,l,P):J))return!1}}return!0}function Ec(e){return!(!Qe(e)||(n=e,cc&&cc in n))&&(rr(e)?yf:Ji).test(Xr(e));var n}function Mc(e){return typeof e=="function"?e:e==null?Wt:typeof e=="object"?$e(e)?Fc(e[0],e[1]):Ac(e):Yl(e)}function Fa(e){if(!vs(e))return Sf(e);var n=[];for(var a in Be(e))Le.call(e,a)&&a!="constructor"&&n.push(a);return n}function zf(e){if(!Qe(e))return function(d){var g=[];if(d!=null)for(var $ in Be(d))g.push($);return g}(e);var n=vs(e),a=[];for(var l in e)(l!="constructor"||!n&&Le.call(e,l))&&a.push(l);return a}function Ta(e,n){return e<n}function kc(e,n){var a=-1,l=Pt(e)?B(e.length):[];return xr(e,function(d,g,$){l[++a]=n(d,g,$)}),l}function Ac(e){var n=Ga(e);return n.length==1&&n[0][2]?ll(n[0][0],n[0][1]):function(a){return a===e||Aa(a,e,n)}}function Fc(e,n){return Ka(e)&&cl(n)?ll(Un(e),n):function(a){var l=su(a,e);return l===o&&l===n?ou(a,e):gs(n,l,3)}}function go(e,n,a,l,d){e!==n&&Ia(n,function(g,$){if(d||(d=new bn),Qe(g))(function(I,L,A,P,J,Z,se){var N=Ja(I,A),U=Ja(L,A),ce=se.get(U);if(ce)Ca(I,A,ce);else{var V=Z?Z(N,U,A+"",I,L,se):o,ye=V===o;if(ye){var ge=$e(U),Fe=!ge&&Er(U),te=!ge&&!Fe&&Mi(U);V=U,ge||Fe||te?$e(N)?V=N:nt(N)?V=zt(N):Fe?(ye=!1,V=Wc(U,!0)):te?(ye=!1,V=Hc(U,!0)):V=[]:bs(U)||Zr(U)?(V=N,Zr(N)?V=Dl(N):Qe(N)&&!rr(N)||(V=ul(U))):ye=!1}ye&&(se.set(U,V),J(V,U,P,Z,se),se.delete(U)),Ca(I,A,V)}})(e,n,$,a,go,l,d);else{var w=l?l(Ja(e,$),g,$+"",e,n,d):o;w===o&&(w=g),Ca(e,$,w)}},Ut)}function Tc(e,n){var a=e.length;if(a)return nr(n+=n<0?a:0,a)?e[n]:o}function Oc(e,n,a){n=n.length?je(n,function(g){return $e(g)?function($){return Qr($,g.length===1?g[0]:g)}:g}):[Wt];var l=-1;n=je(n,Bt(ae()));var d=kc(e,function(g,$,w){var I=je(n,function(L){return L(g)});return{criteria:I,index:++l,value:g}});return function(g,$){var w=g.length;for(g.sort($);w--;)g[w]=g[w].value;return g}(d,function(g,$){return function(w,I,L){for(var A=-1,P=w.criteria,J=I.criteria,Z=P.length,se=L.length;++A<Z;){var N=Vc(P[A],J[A]);if(N)return A>=se?N:N*(L[A]=="desc"?-1:1)}return w.index-I.index}(g,$,a)})}function Rc(e,n,a){for(var l=-1,d=n.length,g={};++l<d;){var $=n[l],w=Qr(e,$);a(w,$)&&ms(g,Sr($,e),w)}return g}function Oa(e,n,a,l){var d=l?_i:Qn,g=-1,$=n.length,w=e;for(e===n&&(n=zt(n)),a&&(w=je(e,Bt(a)));++g<$;)for(var I=0,L=n[g],A=a?a(L):L;(I=d(w,A,I,l))>-1;)w!==e&&io.call(w,I,1),io.call(e,I,1);return e}function Lc(e,n){for(var a=e?n.length:0,l=a-1;a--;){var d=n[a];if(a==l||d!==g){var g=d;nr(d)?io.call(e,d,1):ja(e,d)}}return e}function Ra(e,n){return e+ao(gc()*(n-e+1))}function La(e,n){var a="";if(!e||n<1||n>m)return a;do n%2&&(a+=e),(n=ao(n/2))&&(e+=e);while(n);return a}function Ce(e,n){return Xa(hl(e,n,Wt),e+"")}function Pf(e){return vc(ki(e))}function Uf(e,n){var a=ki(e);return So(a,Kr(n,0,a.length))}function ms(e,n,a,l){if(!Qe(e))return e;for(var d=-1,g=(n=Sr(n,e)).length,$=g-1,w=e;w!=null&&++d<g;){var I=Un(n[d]),L=a;if(I==="__proto__"||I==="constructor"||I==="prototype")return e;if(d!=$){var A=w[I];(L=l?l(A,I,w):o)===o&&(L=Qe(A)?A:nr(n[d+1])?[]:{})}fs(w,I,L),w=w[I]}return e}var Nc=uo?function(e,n){return uo.set(e,n),e}:Wt,Wf=so?function(e,n){return so(e,"toString",{configurable:!0,enumerable:!1,value:uu(n),writable:!0})}:Wt;function Hf(e){return So(ki(e))}function hn(e,n,a){var l=-1,d=e.length;n<0&&(n=-n>d?0:d+n),(a=a>d?d:a)<0&&(a+=d),d=n>a?0:a-n>>>0,n>>>=0;for(var g=B(d);++l<d;)g[l]=e[l+n];return g}function Vf(e,n){var a;return xr(e,function(l,d,g){return!(a=n(l,d,g))}),!!a}function mo(e,n,a){var l=0,d=e==null?l:e.length;if(typeof n=="number"&&n==n&&d<=2147483647){for(;l<d;){var g=l+d>>>1,$=e[g];$!==null&&!Yt($)&&(a?$<=n:$<n)?l=g+1:d=g}return d}return Na(e,n,Wt,a)}function Na(e,n,a,l){var d=0,g=e==null?0:e.length;if(g===0)return 0;for(var $=(n=a(n))!=n,w=n===null,I=Yt(n),L=n===o;d<g;){var A=ao((d+g)/2),P=a(e[A]),J=P!==o,Z=P===null,se=P==P,N=Yt(P);if($)var U=l||se;else U=L?se&&(l||J):w?se&&J&&(l||!Z):I?se&&J&&!Z&&(l||!N):!Z&&!N&&(l?P<=n:P<n);U?d=A+1:g=A}return At(g,4294967294)}function jc(e,n){for(var a=-1,l=e.length,d=0,g=[];++a<l;){var $=e[a],w=n?n($):$;if(!a||!$n(w,I)){var I=w;g[d++]=$===0?0:$}}return g}function Dc(e){return typeof e=="number"?e:Yt(e)?b:+e}function Gt(e){if(typeof e=="string")return e;if($e(e))return je(e,Gt)+"";if(Yt(e))return mc?mc.call(e):"";var n=e+"";return n=="0"&&1/e==-1/0?"-0":n}function Cr(e,n,a){var l=-1,d=Dn,g=e.length,$=!0,w=[],I=w;if(a)$=!1,d=yr;else if(g>=200){var L=n?null:Gf(e);if(L)return Qs(L);$=!1,d=os,I=new Yr}else I=n?[]:w;e:for(;++l<g;){var A=e[l],P=n?n(A):A;if(A=a||A!==0?A:0,$&&P==P){for(var J=I.length;J--;)if(I[J]===P)continue e;n&&I.push(P),w.push(A)}else d(I,P,a)||(I!==w&&I.push(P),w.push(A))}return w}function ja(e,n){return(e=fl(e,n=Sr(n,e)))==null||delete e[Un(fn(n))]}function qc(e,n,a,l){return ms(e,n,a(Qr(e,n)),l)}function _o(e,n,a,l){for(var d=e.length,g=l?d:-1;(l?g--:++g<d)&&n(e[g],g,e););return a?hn(e,l?0:g,l?g+1:d):hn(e,l?g+1:0,l?d:g)}function zc(e,n){var a=e;return a instanceof Ee&&(a=a.value()),vn(n,function(l,d){return d.func.apply(d.thisArg,Vt([l],d.args))},a)}function Da(e,n,a){var l=e.length;if(l<2)return l?Cr(e[0]):[];for(var d=-1,g=B(l);++d<l;)for(var $=e[d],w=-1;++w<l;)w!=d&&(g[d]=ds(g[d]||$,e[w],n,a));return Cr(Et(g,1),n,a)}function Pc(e,n,a){for(var l=-1,d=e.length,g=n.length,$={};++l<d;){var w=l<g?n[l]:o;a($,e[l],w)}return $}function qa(e){return nt(e)?e:[]}function za(e){return typeof e=="function"?e:Wt}function Sr(e,n){return $e(e)?e:Ka(e,n)?[e]:ml(Re(e))}var Bf=Ce;function Ir(e,n,a){var l=e.length;return a=a===o?l:a,!n&&a>=l?e:hn(e,n,a)}var Uc=bf||function(e){return Pe.clearTimeout(e)};function Wc(e,n){if(n)return e.slice();var a=e.length,l=lc?lc(a):new e.constructor(a);return e.copy(l),l}function Pa(e){var n=new e.constructor(e.byteLength);return new no(n).set(new no(e)),n}function Hc(e,n){var a=n?Pa(e.buffer):e.buffer;return new e.constructor(a,e.byteOffset,e.length)}function Vc(e,n){if(e!==n){var a=e!==o,l=e===null,d=e==e,g=Yt(e),$=n!==o,w=n===null,I=n==n,L=Yt(n);if(!w&&!L&&!g&&e>n||g&&$&&I&&!w&&!L||l&&$&&I||!a&&I||!d)return 1;if(!l&&!g&&!L&&e<n||L&&a&&d&&!l&&!g||w&&a&&d||!$&&d||!I)return-1}return 0}function Bc(e,n,a,l){for(var d=-1,g=e.length,$=a.length,w=-1,I=n.length,L=ht(g-$,0),A=B(I+L),P=!l;++w<I;)A[w]=n[w];for(;++d<$;)(P||d<g)&&(A[a[d]]=e[d]);for(;L--;)A[w++]=e[d++];return A}function Gc(e,n,a,l){for(var d=-1,g=e.length,$=-1,w=a.length,I=-1,L=n.length,A=ht(g-w,0),P=B(A+L),J=!l;++d<A;)P[d]=e[d];for(var Z=d;++I<L;)P[Z+I]=n[I];for(;++$<w;)(J||d<g)&&(P[Z+a[$]]=e[d++]);return P}function zt(e,n){var a=-1,l=e.length;for(n||(n=B(l));++a<l;)n[a]=e[a];return n}function Pn(e,n,a,l){var d=!a;a||(a={});for(var g=-1,$=n.length;++g<$;){var w=n[g],I=l?l(a[w],e[w],w,a,e):o;I===o&&(I=e[w]),d?Zn(a,w,I):fs(a,w,I)}return a}function vo(e,n){return function(a,l){var d=$e(a)?jn:Nf,g=n?n():{};return d(a,e,ae(l,2),g)}}function Si(e){return Ce(function(n,a){var l=-1,d=a.length,g=d>1?a[d-1]:o,$=d>2?a[2]:o;for(g=e.length>3&&typeof g=="function"?(d--,g):o,$&&Lt(a[0],a[1],$)&&(g=d<3?o:g,d=1),n=Be(n);++l<d;){var w=a[l];w&&e(n,w,l,g)}return n})}function Yc(e,n){return function(a,l){if(a==null)return a;if(!Pt(a))return e(a,l);for(var d=a.length,g=n?d:-1,$=Be(a);(n?g--:++g<d)&&l($[g],g,$)!==!1;);return a}}function Kc(e){return function(n,a,l){for(var d=-1,g=Be(n),$=l(n),w=$.length;w--;){var I=$[e?w:++d];if(a(g[I],I,g)===!1)break}return n}}function Qc(e){return function(n){var a=vi(n=Re(n))?yn(n):o,l=a?a[0]:n.charAt(0),d=a?Ir(a,1).join(""):n.slice(1);return l[e]()+d}}function Ii(e){return function(n){return vn(Bl(Vl(n).replace(Wr,"")),e,"")}}function _s(e){return function(){var n=arguments;switch(n.length){case 0:return new e;case 1:return new e(n[0]);case 2:return new e(n[0],n[1]);case 3:return new e(n[0],n[1],n[2]);case 4:return new e(n[0],n[1],n[2],n[3]);case 5:return new e(n[0],n[1],n[2],n[3],n[4]);case 6:return new e(n[0],n[1],n[2],n[3],n[4],n[5]);case 7:return new e(n[0],n[1],n[2],n[3],n[4],n[5],n[6])}var a=Ci(e.prototype),l=e.apply(a,n);return Qe(l)?l:a}}function Jc(e){return function(n,a,l){var d=Be(n);if(!Pt(n)){var g=ae(a,3);n=mt(n),a=function(w){return g(d[w],w,d)}}var $=e(n,a,l);return $>-1?d[g?n[$]:$]:o}}function Xc(e){return tr(function(n){var a=n.length,l=a,d=cn.prototype.thru;for(e&&n.reverse();l--;){var g=n[l];if(typeof g!="function")throw new un(t);if(d&&!$&&xo(g)=="wrapper")var $=new cn([],!0)}for(l=$?l:a;++l<a;){var w=xo(g=n[l]),I=w=="wrapper"?Ba(g):o;$=I&&Qa(I[0])&&I[1]==424&&!I[4].length&&I[9]==1?$[xo(I[0])].apply($,I[3]):g.length==1&&Qa(g)?$[w]():$.thru(g)}return function(){var L=arguments,A=L[0];if($&&L.length==1&&$e(A))return $.plant(A).value();for(var P=0,J=a?n[P].apply(this,L):A;++P<a;)J=n[P].call(this,J);return J}})}function yo(e,n,a,l,d,g,$,w,I,L){var A=n&h,P=1&n,J=2&n,Z=24&n,se=512&n,N=J?o:_s(e);return function U(){for(var ce=arguments.length,V=B(ce),ye=ce;ye--;)V[ye]=arguments[ye];if(Z)var ge=Ei(U),Fe=function(rt,Nt){for(var Ge=rt.length,Tt=0;Ge--;)rt[Ge]===Nt&&++Tt;return Tt}(V,ge);if(l&&(V=Bc(V,l,d,Z)),g&&(V=Gc(V,g,$,Z)),ce-=Fe,Z&&ce<L){var te=$r(V,ge);return tl(e,n,yo,U.placeholder,a,V,te,w,I,L-ce)}var le=P?a:this,ft=J?le[e]:e;return ce=V.length,w?V=function(rt,Nt){for(var Ge=rt.length,Tt=At(Nt.length,Ge),Wn=zt(rt);Tt--;){var Mr=Nt[Tt];rt[Tt]=nr(Mr,Ge)?Wn[Mr]:o}return rt}(V,w):se&&ce>1&&V.reverse(),A&&I<ce&&(V.length=I),this&&this!==Pe&&this instanceof U&&(ft=N||_s(ft)),ft.apply(le,V)}}function Zc(e,n){return function(a,l){return function(d,g,$,w){return zn(d,function(I,L,A){g(w,$(I),L,A)}),w}(a,e,n(l),{})}}function bo(e,n){return function(a,l){var d;if(a===o&&l===o)return n;if(a!==o&&(d=a),l!==o){if(d===o)return l;typeof a=="string"||typeof l=="string"?(a=Gt(a),l=Gt(l)):(a=Dc(a),l=Dc(l)),d=e(a,l)}return d}}function Ua(e){return tr(function(n){return n=je(n,Bt(ae())),Ce(function(a){var l=this;return e(n,function(d){return gt(d,l,a)})})})}function $o(e,n){var a=(n=n===o?" ":Gt(n)).length;if(a<2)return a?La(n,e):n;var l=La(n,oo(e/yi(n)));return vi(n)?Ir(yn(l),0,e).join(""):l.slice(0,e)}function el(e){return function(n,a,l){return l&&typeof l!="number"&&Lt(n,a,l)&&(a=l=o),n=ir(n),a===o?(a=n,n=0):a=ir(a),function(d,g,$,w){for(var I=-1,L=ht(oo((g-d)/($||1)),0),A=B(L);L--;)A[w?L:++I]=d,d+=$;return A}(n,a,l=l===o?n<a?1:-1:ir(l),e)}}function wo(e){return function(n,a){return typeof n=="string"&&typeof a=="string"||(n=dn(n),a=dn(a)),e(n,a)}}function tl(e,n,a,l,d,g,$,w,I,L){var A=8&n;n|=A?u:c,4&(n&=~(A?c:u))||(n&=-4);var P=[e,n,d,A?g:o,A?$:o,A?o:g,A?o:$,w,I,L],J=a.apply(o,P);return Qa(e)&&dl(J,P),J.placeholder=l,pl(J,e,n)}function Wa(e){var n=qn[e];return function(a,l){if(a=dn(a),(l=l==null?0:At(xe(l),292))&&pc(a)){var d=(Re(a)+"e").split("e");return+((d=(Re(n(d[0]+"e"+(+d[1]+l)))+"e").split("e"))[0]+"e"+(+d[1]-l))}return n(a)}}var Gf=wi&&1/Qs(new wi([,-0]))[1]==v?function(e){return new wi(e)}:hu;function nl(e){return function(n){var a=Ft(n);return a==oe?ya(n):a==Te?ff(n):function(l,d){return je(d,function(g){return[g,l[g]]})}(n,e(n))}}function er(e,n,a,l,d,g,$,w){var I=2&n;if(!I&&typeof e!="function")throw new un(t);var L=l?l.length:0;if(L||(n&=-97,l=d=o),$=$===o?$:ht(xe($),0),w=w===o?w:xe(w),L-=d?d.length:0,n&c){var A=l,P=d;l=d=o}var J=I?o:Ba(e),Z=[e,n,a,l,d,A,P,g,$,w];if(J&&function(N,U){var ce=N[1],V=U[1],ye=ce|V,ge=ye<131,Fe=V==h&&ce==8||V==h&&ce==f&&N[7].length<=U[8]||V==384&&U[7].length<=U[8]&&ce==8;if(!ge&&!Fe)return N;1&V&&(N[2]=U[2],ye|=1&ce?0:4);var te=U[3];if(te){var le=N[3];N[3]=le?Bc(le,te,U[4]):te,N[4]=le?$r(N[3],i):U[4]}(te=U[5])&&(le=N[5],N[5]=le?Gc(le,te,U[6]):te,N[6]=le?$r(N[5],i):U[6]),(te=U[7])&&(N[7]=te),V&h&&(N[8]=N[8]==null?U[8]:At(N[8],U[8])),N[9]==null&&(N[9]=U[9]),N[0]=U[0],N[1]=ye}(Z,J),e=Z[0],n=Z[1],a=Z[2],l=Z[3],d=Z[4],!(w=Z[9]=Z[9]===o?I?0:e.length:ht(Z[9]-L,0))&&24&n&&(n&=-25),n&&n!=1)se=n==8||n==s?function(N,U,ce){var V=_s(N);return function ye(){for(var ge=arguments.length,Fe=B(ge),te=ge,le=Ei(ye);te--;)Fe[te]=arguments[te];var ft=ge<3&&Fe[0]!==le&&Fe[ge-1]!==le?[]:$r(Fe,le);return(ge-=ft.length)<ce?tl(N,U,yo,ye.placeholder,o,Fe,ft,o,o,ce-ge):gt(this&&this!==Pe&&this instanceof ye?V:N,this,Fe)}}(e,n,w):n!=u&&n!=33||d.length?yo.apply(o,Z):function(N,U,ce,V){var ye=1&U,ge=_s(N);return function Fe(){for(var te=-1,le=arguments.length,ft=-1,rt=V.length,Nt=B(rt+le),Ge=this&&this!==Pe&&this instanceof Fe?ge:N;++ft<rt;)Nt[ft]=V[ft];for(;le--;)Nt[ft++]=arguments[++te];return gt(Ge,ye?ce:this,Nt)}}(e,n,a,l);else var se=function(N,U,ce){var V=1&U,ye=_s(N);return function ge(){return(this&&this!==Pe&&this instanceof ge?ye:N).apply(V?ce:this,arguments)}}(e,n,a);return pl((J?Nc:dl)(se,Z),e,n)}function rl(e,n,a,l){return e===o||$n(e,$i[a])&&!Le.call(l,a)?n:e}function il(e,n,a,l,d,g){return Qe(e)&&Qe(n)&&(g.set(n,e),go(e,n,o,il,g),g.delete(n)),e}function Yf(e){return bs(e)?o:e}function sl(e,n,a,l,d,g){var $=1&a,w=e.length,I=n.length;if(w!=I&&!($&&I>w))return!1;var L=g.get(e),A=g.get(n);if(L&&A)return L==n&&A==e;var P=-1,J=!0,Z=2&a?new Yr:o;for(g.set(e,n),g.set(n,e);++P<w;){var se=e[P],N=n[P];if(l)var U=$?l(N,se,P,n,e,g):l(se,N,P,e,n,g);if(U!==o){if(U)continue;J=!1;break}if(Z){if(!br(n,function(ce,V){if(!os(Z,V)&&(se===ce||d(se,ce,a,l,g)))return Z.push(V)})){J=!1;break}}else if(se!==N&&!d(se,N,a,l,g)){J=!1;break}}return g.delete(e),g.delete(n),J}function tr(e){return Xa(hl(e,o,bl),e+"")}function Ha(e){return Sc(e,mt,Ya)}function Va(e){return Sc(e,Ut,ol)}var Ba=uo?function(e){return uo.get(e)}:hu;function xo(e){for(var n=e.name+"",a=xi[n],l=Le.call(xi,n)?a.length:0;l--;){var d=a[l],g=d.func;if(g==null||g==e)return d.name}return n}function Ei(e){return(Le.call(p,"placeholder")?p:e).placeholder}function ae(){var e=p.iteratee||cu;return e=e===cu?Mc:e,arguments.length?e(arguments[0],arguments[1]):e}function Co(e,n){var a,l,d=e.__data__;return((l=typeof(a=n))=="string"||l=="number"||l=="symbol"||l=="boolean"?a!=="__proto__":a===null)?d[typeof n=="string"?"string":"hash"]:d.map}function Ga(e){for(var n=mt(e),a=n.length;a--;){var l=n[a],d=e[l];n[a]=[l,d,cl(d)]}return n}function Jr(e,n){var a=function(l,d){return l==null?o:l[d]}(e,n);return Ec(a)?a:o}var Ya=$a?function(e){return e==null?[]:(e=Be(e),It($a(e),function(n){return fc.call(e,n)}))}:fu,ol=$a?function(e){for(var n=[];e;)Vt(n,Ya(e)),e=ro(e);return n}:fu,Ft=Rt;function al(e,n,a){for(var l=-1,d=(n=Sr(n,e)).length,g=!1;++l<d;){var $=Un(n[l]);if(!(g=e!=null&&a(e,$)))break;e=e[$]}return g||++l!=d?g:!!(d=e==null?0:e.length)&&Fo(d)&&nr($,d)&&($e(e)||Zr(e))}function ul(e){return typeof e.constructor!="function"||vs(e)?{}:Ci(ro(e))}function Kf(e){return $e(e)||Zr(e)||!!(dc&&e&&e[dc])}function nr(e,n){var a=typeof e;return!!(n=n??m)&&(a=="number"||a!="symbol"&&la.test(e))&&e>-1&&e%1==0&&e<n}function Lt(e,n,a){if(!Qe(a))return!1;var l=typeof n;return!!(l=="number"?Pt(a)&&nr(n,a.length):l=="string"&&n in a)&&$n(a[n],e)}function Ka(e,n){if($e(e))return!1;var a=typeof e;return!(a!="number"&&a!="symbol"&&a!="boolean"&&e!=null&&!Yt(e))||Os.test(e)||!rn.test(e)||n!=null&&e in Be(n)}function Qa(e){var n=xo(e),a=p[n];if(typeof a!="function"||!(n in Ee.prototype))return!1;if(e===a)return!0;var l=Ba(a);return!!l&&e===l[0]}(wa&&Ft(new wa(new ArrayBuffer(1)))!=at||us&&Ft(new us)!=oe||xa&&Ft(xa.resolve())!=Ie||wi&&Ft(new wi)!=Te||cs&&Ft(new cs)!=Se)&&(Ft=function(e){var n=Rt(e),a=n==pe?e.constructor:o,l=a?Xr(a):"";if(l)switch(l){case kf:return at;case Af:return oe;case Ff:return Ie;case Tf:return Te;case Of:return Se}return n});var Qf=Xs?rr:du;function vs(e){var n=e&&e.constructor;return e===(typeof n=="function"&&n.prototype||$i)}function cl(e){return e==e&&!Qe(e)}function ll(e,n){return function(a){return a!=null&&a[e]===n&&(n!==o||e in Be(a))}}function hl(e,n,a){return n=ht(n===o?e.length-1:n,0),function(){for(var l=arguments,d=-1,g=ht(l.length-n,0),$=B(g);++d<g;)$[d]=l[n+d];d=-1;for(var w=B(n+1);++d<n;)w[d]=l[d];return w[n]=a($),gt(e,this,w)}}function fl(e,n){return n.length<2?e:Qr(e,hn(n,0,-1))}function Ja(e,n){if((n!=="constructor"||typeof e[n]!="function")&&n!="__proto__")return e[n]}var dl=gl(Nc),ys=wf||function(e,n){return Pe.setTimeout(e,n)},Xa=gl(Wf);function pl(e,n,a){var l=n+"";return Xa(e,function(d,g){var $=g.length;if(!$)return d;var w=$-1;return g[w]=($>1?"& ":"")+g[w],g=g.join($>2?", ":" "),d.replace(js,`{
/* [wrapped with `+g+`] */
`)}(l,function(d,g){return kt(E,function($){var w="_."+$[0];g&$[1]&&!Dn(d,w)&&d.push(w)}),d.sort()}(function(d){var g=d.match(aa);return g?g[1].split(Bn):[]}(l),a)))}function gl(e){var n=0,a=0;return function(){var l=If(),d=16-(l-a);if(a=l,d>0){if(++n>=800)return arguments[0]}else n=0;return e.apply(o,arguments)}}function So(e,n){var a=-1,l=e.length,d=l-1;for(n=n===o?l:n;++a<n;){var g=Ra(a,d),$=e[g];e[g]=e[a],e[a]=$}return e.length=n,e}var ml=function(e){var n=ko(e,function(l){return a.size===500&&a.clear(),l}),a=n.cache;return n}(function(e){var n=[];return e.charCodeAt(0)===46&&n.push(""),e.replace(Rs,function(a,l,d,g){n.push(d?g.replace(ca,"$1"):l||a)}),n});function Un(e){if(typeof e=="string"||Yt(e))return e;var n=e+"";return n=="0"&&1/e==-1/0?"-0":n}function Xr(e){if(e!=null){try{return Zs.call(e)}catch{}try{return e+""}catch{}}return""}function _l(e){if(e instanceof Ee)return e.clone();var n=new cn(e.__wrapped__,e.__chain__);return n.__actions__=zt(e.__actions__),n.__index__=e.__index__,n.__values__=e.__values__,n}var Jf=Ce(function(e,n){return nt(e)?ds(e,Et(n,1,nt,!0)):[]}),Xf=Ce(function(e,n){var a=fn(n);return nt(a)&&(a=o),nt(e)?ds(e,Et(n,1,nt,!0),ae(a,2)):[]}),Zf=Ce(function(e,n){var a=fn(n);return nt(a)&&(a=o),nt(e)?ds(e,Et(n,1,nt,!0),o,a):[]});function vl(e,n,a){var l=e==null?0:e.length;if(!l)return-1;var d=a==null?0:xe(a);return d<0&&(d=ht(l+d,0)),Kn(e,ae(n,3),d)}function yl(e,n,a){var l=e==null?0:e.length;if(!l)return-1;var d=l-1;return a!==o&&(d=xe(a),d=a<0?ht(l+d,0):At(d,l-1)),Kn(e,ae(n,3),d,!0)}function bl(e){return e!=null&&e.length?Et(e,1):[]}function $l(e){return e&&e.length?e[0]:o}var ed=Ce(function(e){var n=je(e,qa);return n.length&&n[0]===e[0]?ka(n):[]}),td=Ce(function(e){var n=fn(e),a=je(e,qa);return n===fn(a)?n=o:a.pop(),a.length&&a[0]===e[0]?ka(a,ae(n,2)):[]}),nd=Ce(function(e){var n=fn(e),a=je(e,qa);return(n=typeof n=="function"?n:o)&&a.pop(),a.length&&a[0]===e[0]?ka(a,o,n):[]});function fn(e){var n=e==null?0:e.length;return n?e[n-1]:o}var rd=Ce(wl);function wl(e,n){return e&&e.length&&n&&n.length?Oa(e,n):e}var id=tr(function(e,n){var a=e==null?0:e.length,l=Sa(e,n);return Lc(e,je(n,function(d){return nr(d,a)?+d:d}).sort(Vc)),l});function Za(e){return e==null?e:Mf.call(e)}var sd=Ce(function(e){return Cr(Et(e,1,nt,!0))}),od=Ce(function(e){var n=fn(e);return nt(n)&&(n=o),Cr(Et(e,1,nt,!0),ae(n,2))}),ad=Ce(function(e){var n=fn(e);return n=typeof n=="function"?n:o,Cr(Et(e,1,nt,!0),o,n)});function eu(e){if(!e||!e.length)return[];var n=0;return e=It(e,function(a){if(nt(a))return n=ht(a.length,n),!0}),_a(n,function(a){return je(e,pa(a))})}function xl(e,n){if(!e||!e.length)return[];var a=eu(e);return n==null?a:je(a,function(l){return gt(n,o,l)})}var ud=Ce(function(e,n){return nt(e)?ds(e,n):[]}),cd=Ce(function(e){return Da(It(e,nt))}),ld=Ce(function(e){var n=fn(e);return nt(n)&&(n=o),Da(It(e,nt),ae(n,2))}),hd=Ce(function(e){var n=fn(e);return n=typeof n=="function"?n:o,Da(It(e,nt),o,n)}),fd=Ce(eu),dd=Ce(function(e){var n=e.length,a=n>1?e[n-1]:o;return a=typeof a=="function"?(e.pop(),a):o,xl(e,a)});function Cl(e){var n=p(e);return n.__chain__=!0,n}function Io(e,n){return n(e)}var pd=tr(function(e){var n=e.length,a=n?e[0]:0,l=this.__wrapped__,d=function(g){return Sa(g,e)};return!(n>1||this.__actions__.length)&&l instanceof Ee&&nr(a)?((l=l.slice(a,+a+(n?1:0))).__actions__.push({func:Io,args:[d],thisArg:o}),new cn(l,this.__chain__).thru(function(g){return n&&!g.length&&g.push(o),g})):this.thru(d)}),gd=vo(function(e,n,a){Le.call(e,a)?++e[a]:Zn(e,a,1)}),md=Jc(vl),_d=Jc(yl);function Sl(e,n){return($e(e)?kt:xr)(e,ae(n,3))}function Il(e,n){return($e(e)?qt:wc)(e,ae(n,3))}var vd=vo(function(e,n,a){Le.call(e,a)?e[a].push(n):Zn(e,a,[n])}),yd=Ce(function(e,n,a){var l=-1,d=typeof n=="function",g=Pt(e)?B(e.length):[];return xr(e,function($){g[++l]=d?gt(n,$,a):ps($,n,a)}),g}),bd=vo(function(e,n,a){Zn(e,a,n)});function Eo(e,n){return($e(e)?je:kc)(e,ae(n,3))}var $d=vo(function(e,n,a){e[a?0:1].push(n)},function(){return[[],[]]}),wd=Ce(function(e,n){if(e==null)return[];var a=n.length;return a>1&&Lt(e,n[0],n[1])?n=[]:a>2&&Lt(n[0],n[1],n[2])&&(n=[n[0]]),Oc(e,Et(n,1),[])}),Mo=$f||function(){return Pe.Date.now()};function El(e,n,a){return n=a?o:n,n=e&&n==null?e.length:n,er(e,h,o,o,o,o,n)}function Ml(e,n){var a;if(typeof n!="function")throw new un(t);return e=xe(e),function(){return--e>0&&(a=n.apply(this,arguments)),e<=1&&(n=o),a}}var tu=Ce(function(e,n,a){var l=1;if(a.length){var d=$r(a,Ei(tu));l|=u}return er(e,l,n,a,d)}),kl=Ce(function(e,n,a){var l=3;if(a.length){var d=$r(a,Ei(kl));l|=u}return er(n,l,e,a,d)});function Al(e,n,a){var l,d,g,$,w,I,L=0,A=!1,P=!1,J=!0;if(typeof e!="function")throw new un(t);function Z(V){var ye=l,ge=d;return l=d=o,L=V,$=e.apply(ge,ye)}function se(V){var ye=V-I;return I===o||ye>=n||ye<0||P&&V-L>=g}function N(){var V=Mo();if(se(V))return U(V);w=ys(N,function(ye){var ge=n-(ye-I);return P?At(ge,g-(ye-L)):ge}(V))}function U(V){return w=o,J&&l?Z(V):(l=d=o,$)}function ce(){var V=Mo(),ye=se(V);if(l=arguments,d=this,I=V,ye){if(w===o)return function(ge){return L=ge,w=ys(N,n),A?Z(ge):$}(I);if(P)return Uc(w),w=ys(N,n),Z(I)}return w===o&&(w=ys(N,n)),$}return n=dn(n)||0,Qe(a)&&(A=!!a.leading,g=(P="maxWait"in a)?ht(dn(a.maxWait)||0,n):g,J="trailing"in a?!!a.trailing:J),ce.cancel=function(){w!==o&&Uc(w),L=0,l=I=d=w=o},ce.flush=function(){return w===o?$:U(Mo())},ce}var xd=Ce(function(e,n){return $c(e,1,n)}),Cd=Ce(function(e,n,a){return $c(e,dn(n)||0,a)});function ko(e,n){if(typeof e!="function"||n!=null&&typeof n!="function")throw new un(t);var a=function(){var l=arguments,d=n?n.apply(this,l):l[0],g=a.cache;if(g.has(d))return g.get(d);var $=e.apply(this,l);return a.cache=g.set(d,$)||g,$};return a.cache=new(ko.Cache||Xn),a}function Ao(e){if(typeof e!="function")throw new un(t);return function(){var n=arguments;switch(n.length){case 0:return!e.call(this);case 1:return!e.call(this,n[0]);case 2:return!e.call(this,n[0],n[1]);case 3:return!e.call(this,n[0],n[1],n[2])}return!e.apply(this,n)}}ko.Cache=Xn;var Sd=Bf(function(e,n){var a=(n=n.length==1&&$e(n[0])?je(n[0],Bt(ae())):je(Et(n,1),Bt(ae()))).length;return Ce(function(l){for(var d=-1,g=At(l.length,a);++d<g;)l[d]=n[d].call(this,l[d]);return gt(e,this,l)})}),nu=Ce(function(e,n){var a=$r(n,Ei(nu));return er(e,u,o,n,a)}),Fl=Ce(function(e,n){var a=$r(n,Ei(Fl));return er(e,c,o,n,a)}),Id=tr(function(e,n){return er(e,f,o,o,o,n)});function $n(e,n){return e===n||e!=e&&n!=n}var Ed=wo(Ma),Md=wo(function(e,n){return e>=n}),Zr=Ic(function(){return arguments}())?Ic:function(e){return Ze(e)&&Le.call(e,"callee")&&!fc.call(e,"callee")},$e=B.isArray,kd=vr?Bt(vr):function(e){return Ze(e)&&Rt(e)==we};function Pt(e){return e!=null&&Fo(e.length)&&!rr(e)}function nt(e){return Ze(e)&&Pt(e)}var Er=xf||du,Ad=is?Bt(is):function(e){return Ze(e)&&Rt(e)==S};function ru(e){if(!Ze(e))return!1;var n=Rt(e);return n==T||n=="[object DOMException]"||typeof e.message=="string"&&typeof e.name=="string"&&!bs(e)}function rr(e){if(!Qe(e))return!1;var n=Rt(e);return n==ne||n==de||n=="[object AsyncFunction]"||n=="[object Proxy]"}function Tl(e){return typeof e=="number"&&e==xe(e)}function Fo(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=m}function Qe(e){var n=typeof e;return e!=null&&(n=="object"||n=="function")}function Ze(e){return e!=null&&typeof e=="object"}var Ol=Gs?Bt(Gs):function(e){return Ze(e)&&Ft(e)==oe};function Rl(e){return typeof e=="number"||Ze(e)&&Rt(e)==Ne}function bs(e){if(!Ze(e)||Rt(e)!=pe)return!1;var n=ro(e);if(n===null)return!0;var a=Le.call(n,"constructor")&&n.constructor;return typeof a=="function"&&a instanceof a&&Zs.call(a)==_f}var iu=Ys?Bt(Ys):function(e){return Ze(e)&&Rt(e)==me},Ll=Oe?Bt(Oe):function(e){return Ze(e)&&Ft(e)==Te};function To(e){return typeof e=="string"||!$e(e)&&Ze(e)&&Rt(e)==Ue}function Yt(e){return typeof e=="symbol"||Ze(e)&&Rt(e)==st}var Mi=Ae?Bt(Ae):function(e){return Ze(e)&&Fo(e.length)&&!!X[Rt(e)]},Fd=wo(Ta),Td=wo(function(e,n){return e<=n});function Nl(e){if(!e)return[];if(Pt(e))return To(e)?yn(e):zt(e);if(as&&e[as])return function(a){for(var l,d=[];!(l=a.next()).done;)d.push(l.value);return d}(e[as]());var n=Ft(e);return(n==oe?ya:n==Te?Qs:ki)(e)}function ir(e){return e?(e=dn(e))===v||e===-1/0?17976931348623157e292*(e<0?-1:1):e==e?e:0:e===0?e:0}function xe(e){var n=ir(e),a=n%1;return n==n?a?n-a:n:0}function jl(e){return e?Kr(xe(e),0,x):0}function dn(e){if(typeof e=="number")return e;if(Yt(e))return b;if(Qe(e)){var n=typeof e.valueOf=="function"?e.valueOf():e;e=Qe(n)?n+"":n}if(typeof e!="string")return e===0?e:+e;e=ic(e);var a=Qi.test(e);return a||qr.test(e)?tt(e.slice(2),a?2:8):Ki.test(e)?b:+e}function Dl(e){return Pn(e,Ut(e))}function Re(e){return e==null?"":Gt(e)}var Od=Si(function(e,n){if(vs(n)||Pt(n))Pn(n,mt(n),e);else for(var a in n)Le.call(n,a)&&fs(e,a,n[a])}),ql=Si(function(e,n){Pn(n,Ut(n),e)}),Oo=Si(function(e,n,a,l){Pn(n,Ut(n),e,l)}),Rd=Si(function(e,n,a,l){Pn(n,mt(n),e,l)}),Ld=tr(Sa),Nd=Ce(function(e,n){e=Be(e);var a=-1,l=n.length,d=l>2?n[2]:o;for(d&&Lt(n[0],n[1],d)&&(l=1);++a<l;)for(var g=n[a],$=Ut(g),w=-1,I=$.length;++w<I;){var L=$[w],A=e[L];(A===o||$n(A,$i[L])&&!Le.call(e,L))&&(e[L]=g[L])}return e}),jd=Ce(function(e){return e.push(o,il),gt(zl,o,e)});function su(e,n,a){var l=e==null?o:Qr(e,n);return l===o?a:l}function ou(e,n){return e!=null&&al(e,n,qf)}var Dd=Zc(function(e,n,a){n!=null&&typeof n.toString!="function"&&(n=eo.call(n)),e[n]=a},uu(Wt)),qd=Zc(function(e,n,a){n!=null&&typeof n.toString!="function"&&(n=eo.call(n)),Le.call(e,n)?e[n].push(a):e[n]=[a]},ae),zd=Ce(ps);function mt(e){return Pt(e)?_c(e):Fa(e)}function Ut(e){return Pt(e)?_c(e,!0):zf(e)}var Pd=Si(function(e,n,a){go(e,n,a)}),zl=Si(function(e,n,a,l){go(e,n,a,l)}),Ud=tr(function(e,n){var a={};if(e==null)return a;var l=!1;n=je(n,function(g){return g=Sr(g,e),l||(l=g.length>1),g}),Pn(e,Va(e),a),l&&(a=ln(a,7,Yf));for(var d=n.length;d--;)ja(a,n[d]);return a}),Wd=tr(function(e,n){return e==null?{}:function(a,l){return Rc(a,l,function(d,g){return ou(a,g)})}(e,n)});function Pl(e,n){if(e==null)return{};var a=je(Va(e),function(l){return[l]});return n=ae(n),Rc(e,a,function(l,d){return n(l,d[0])})}var Ul=nl(mt),Wl=nl(Ut);function ki(e){return e==null?[]:va(e,mt(e))}var Hd=Ii(function(e,n,a){return n=n.toLowerCase(),e+(a?Hl(n):n)});function Hl(e){return au(Re(e).toLowerCase())}function Vl(e){return(e=Re(e))&&e.replace(ha,cf).replace(Bs,"")}var Vd=Ii(function(e,n,a){return e+(a?"-":"")+n.toLowerCase()}),Bd=Ii(function(e,n,a){return e+(a?" ":"")+n.toLowerCase()}),Gd=Qc("toLowerCase"),Yd=Ii(function(e,n,a){return e+(a?"_":"")+n.toLowerCase()}),Kd=Ii(function(e,n,a){return e+(a?" ":"")+au(n)}),Qd=Ii(function(e,n,a){return e+(a?" ":"")+n.toUpperCase()}),au=Qc("toUpperCase");function Bl(e,n,a){return e=Re(e),(n=a?o:n)===o?function(l){return W.test(l)}(e)?function(l){return l.match(C)||[]}(e):function(l){return l.match(Ds)||[]}(e):e.match(n)||[]}var Gl=Ce(function(e,n){try{return gt(e,o,n)}catch(a){return ru(a)?a:new Me(a)}}),Jd=tr(function(e,n){return kt(n,function(a){a=Un(a),Zn(e,a,tu(e[a],e))}),e});function uu(e){return function(){return e}}var Xd=Xc(),Zd=Xc(!0);function Wt(e){return e}function cu(e){return Mc(typeof e=="function"?e:ln(e,1))}var ep=Ce(function(e,n){return function(a){return ps(a,e,n)}}),tp=Ce(function(e,n){return function(a){return ps(e,a,n)}});function lu(e,n,a){var l=mt(n),d=po(n,l);a!=null||Qe(n)&&(d.length||!l.length)||(a=n,n=e,e=this,d=po(n,mt(n)));var g=!(Qe(a)&&"chain"in a&&!a.chain),$=rr(e);return kt(d,function(w){var I=n[w];e[w]=I,$&&(e.prototype[w]=function(){var L=this.__chain__;if(g||L){var A=e(this.__wrapped__);return(A.__actions__=zt(this.__actions__)).push({func:I,args:arguments,thisArg:e}),A.__chain__=L,A}return I.apply(e,Vt([this.value()],arguments))})}),e}function hu(){}var np=Ua(je),rp=Ua(an),ip=Ua(br);function Yl(e){return Ka(e)?pa(Un(e)):function(n){return function(a){return Qr(a,n)}}(e)}var sp=el(),op=el(!0);function fu(){return[]}function du(){return!1}var pu,ap=bo(function(e,n){return e+n},0),up=Wa("ceil"),cp=bo(function(e,n){return e/n},1),lp=Wa("floor"),hp=bo(function(e,n){return e*n},1),fp=Wa("round"),dp=bo(function(e,n){return e-n},0);return p.after=function(e,n){if(typeof n!="function")throw new un(t);return e=xe(e),function(){if(--e<1)return n.apply(this,arguments)}},p.ary=El,p.assign=Od,p.assignIn=ql,p.assignInWith=Oo,p.assignWith=Rd,p.at=Ld,p.before=Ml,p.bind=tu,p.bindAll=Jd,p.bindKey=kl,p.castArray=function(){if(!arguments.length)return[];var e=arguments[0];return $e(e)?e:[e]},p.chain=Cl,p.chunk=function(e,n,a){n=(a?Lt(e,n,a):n===o)?1:ht(xe(n),0);var l=e==null?0:e.length;if(!l||n<1)return[];for(var d=0,g=0,$=B(oo(l/n));d<l;)$[g++]=hn(e,d,d+=n);return $},p.compact=function(e){for(var n=-1,a=e==null?0:e.length,l=0,d=[];++n<a;){var g=e[n];g&&(d[l++]=g)}return d},p.concat=function(){var e=arguments.length;if(!e)return[];for(var n=B(e-1),a=arguments[0],l=e;l--;)n[l-1]=arguments[l];return Vt($e(a)?zt(a):[a],Et(n,1))},p.cond=function(e){var n=e==null?0:e.length,a=ae();return e=n?je(e,function(l){if(typeof l[1]!="function")throw new un(t);return[a(l[0]),l[1]]}):[],Ce(function(l){for(var d=-1;++d<n;){var g=e[d];if(gt(g[0],this,l))return gt(g[1],this,l)}})},p.conforms=function(e){return function(n){var a=mt(n);return function(l){return bc(l,n,a)}}(ln(e,1))},p.constant=uu,p.countBy=gd,p.create=function(e,n){var a=Ci(e);return n==null?a:yc(a,n)},p.curry=function e(n,a,l){var d=er(n,8,o,o,o,o,o,a=l?o:a);return d.placeholder=e.placeholder,d},p.curryRight=function e(n,a,l){var d=er(n,s,o,o,o,o,o,a=l?o:a);return d.placeholder=e.placeholder,d},p.debounce=Al,p.defaults=Nd,p.defaultsDeep=jd,p.defer=xd,p.delay=Cd,p.difference=Jf,p.differenceBy=Xf,p.differenceWith=Zf,p.drop=function(e,n,a){var l=e==null?0:e.length;return l?hn(e,(n=a||n===o?1:xe(n))<0?0:n,l):[]},p.dropRight=function(e,n,a){var l=e==null?0:e.length;return l?hn(e,0,(n=l-(n=a||n===o?1:xe(n)))<0?0:n):[]},p.dropRightWhile=function(e,n){return e&&e.length?_o(e,ae(n,3),!0,!0):[]},p.dropWhile=function(e,n){return e&&e.length?_o(e,ae(n,3),!0):[]},p.fill=function(e,n,a,l){var d=e==null?0:e.length;return d?(a&&typeof a!="number"&&Lt(e,n,a)&&(a=0,l=d),function(g,$,w,I){var L=g.length;for((w=xe(w))<0&&(w=-w>L?0:L+w),(I=I===o||I>L?L:xe(I))<0&&(I+=L),I=w>I?0:jl(I);w<I;)g[w++]=$;return g}(e,n,a,l)):[]},p.filter=function(e,n){return($e(e)?It:xc)(e,ae(n,3))},p.flatMap=function(e,n){return Et(Eo(e,n),1)},p.flatMapDeep=function(e,n){return Et(Eo(e,n),v)},p.flatMapDepth=function(e,n,a){return a=a===o?1:xe(a),Et(Eo(e,n),a)},p.flatten=bl,p.flattenDeep=function(e){return e!=null&&e.length?Et(e,v):[]},p.flattenDepth=function(e,n){return e!=null&&e.length?Et(e,n=n===o?1:xe(n)):[]},p.flip=function(e){return er(e,512)},p.flow=Xd,p.flowRight=Zd,p.fromPairs=function(e){for(var n=-1,a=e==null?0:e.length,l={};++n<a;){var d=e[n];l[d[0]]=d[1]}return l},p.functions=function(e){return e==null?[]:po(e,mt(e))},p.functionsIn=function(e){return e==null?[]:po(e,Ut(e))},p.groupBy=vd,p.initial=function(e){return e!=null&&e.length?hn(e,0,-1):[]},p.intersection=ed,p.intersectionBy=td,p.intersectionWith=nd,p.invert=Dd,p.invertBy=qd,p.invokeMap=yd,p.iteratee=cu,p.keyBy=bd,p.keys=mt,p.keysIn=Ut,p.map=Eo,p.mapKeys=function(e,n){var a={};return n=ae(n,3),zn(e,function(l,d,g){Zn(a,n(l,d,g),l)}),a},p.mapValues=function(e,n){var a={};return n=ae(n,3),zn(e,function(l,d,g){Zn(a,d,n(l,d,g))}),a},p.matches=function(e){return Ac(ln(e,1))},p.matchesProperty=function(e,n){return Fc(e,ln(n,1))},p.memoize=ko,p.merge=Pd,p.mergeWith=zl,p.method=ep,p.methodOf=tp,p.mixin=lu,p.negate=Ao,p.nthArg=function(e){return e=xe(e),Ce(function(n){return Tc(n,e)})},p.omit=Ud,p.omitBy=function(e,n){return Pl(e,Ao(ae(n)))},p.once=function(e){return Ml(2,e)},p.orderBy=function(e,n,a,l){return e==null?[]:($e(n)||(n=n==null?[]:[n]),$e(a=l?o:a)||(a=a==null?[]:[a]),Oc(e,n,a))},p.over=np,p.overArgs=Sd,p.overEvery=rp,p.overSome=ip,p.partial=nu,p.partialRight=Fl,p.partition=$d,p.pick=Wd,p.pickBy=Pl,p.property=Yl,p.propertyOf=function(e){return function(n){return e==null?o:Qr(e,n)}},p.pull=rd,p.pullAll=wl,p.pullAllBy=function(e,n,a){return e&&e.length&&n&&n.length?Oa(e,n,ae(a,2)):e},p.pullAllWith=function(e,n,a){return e&&e.length&&n&&n.length?Oa(e,n,o,a):e},p.pullAt=id,p.range=sp,p.rangeRight=op,p.rearg=Id,p.reject=function(e,n){return($e(e)?It:xc)(e,Ao(ae(n,3)))},p.remove=function(e,n){var a=[];if(!e||!e.length)return a;var l=-1,d=[],g=e.length;for(n=ae(n,3);++l<g;){var $=e[l];n($,l,e)&&(a.push($),d.push(l))}return Lc(e,d),a},p.rest=function(e,n){if(typeof e!="function")throw new un(t);return Ce(e,n=n===o?n:xe(n))},p.reverse=Za,p.sampleSize=function(e,n,a){return n=(a?Lt(e,n,a):n===o)?1:xe(n),($e(e)?Rf:Uf)(e,n)},p.set=function(e,n,a){return e==null?e:ms(e,n,a)},p.setWith=function(e,n,a,l){return l=typeof l=="function"?l:o,e==null?e:ms(e,n,a,l)},p.shuffle=function(e){return($e(e)?Lf:Hf)(e)},p.slice=function(e,n,a){var l=e==null?0:e.length;return l?(a&&typeof a!="number"&&Lt(e,n,a)?(n=0,a=l):(n=n==null?0:xe(n),a=a===o?l:xe(a)),hn(e,n,a)):[]},p.sortBy=wd,p.sortedUniq=function(e){return e&&e.length?jc(e):[]},p.sortedUniqBy=function(e,n){return e&&e.length?jc(e,ae(n,2)):[]},p.split=function(e,n,a){return a&&typeof a!="number"&&Lt(e,n,a)&&(n=a=o),(a=a===o?x:a>>>0)?(e=Re(e))&&(typeof n=="string"||n!=null&&!iu(n))&&!(n=Gt(n))&&vi(e)?Ir(yn(e),0,a):e.split(n,a):[]},p.spread=function(e,n){if(typeof e!="function")throw new un(t);return n=n==null?0:ht(xe(n),0),Ce(function(a){var l=a[n],d=Ir(a,0,n);return l&&Vt(d,l),gt(e,this,d)})},p.tail=function(e){var n=e==null?0:e.length;return n?hn(e,1,n):[]},p.take=function(e,n,a){return e&&e.length?hn(e,0,(n=a||n===o?1:xe(n))<0?0:n):[]},p.takeRight=function(e,n,a){var l=e==null?0:e.length;return l?hn(e,(n=l-(n=a||n===o?1:xe(n)))<0?0:n,l):[]},p.takeRightWhile=function(e,n){return e&&e.length?_o(e,ae(n,3),!1,!0):[]},p.takeWhile=function(e,n){return e&&e.length?_o(e,ae(n,3)):[]},p.tap=function(e,n){return n(e),e},p.throttle=function(e,n,a){var l=!0,d=!0;if(typeof e!="function")throw new un(t);return Qe(a)&&(l="leading"in a?!!a.leading:l,d="trailing"in a?!!a.trailing:d),Al(e,n,{leading:l,maxWait:n,trailing:d})},p.thru=Io,p.toArray=Nl,p.toPairs=Ul,p.toPairsIn=Wl,p.toPath=function(e){return $e(e)?je(e,Un):Yt(e)?[e]:zt(ml(Re(e)))},p.toPlainObject=Dl,p.transform=function(e,n,a){var l=$e(e),d=l||Er(e)||Mi(e);if(n=ae(n,4),a==null){var g=e&&e.constructor;a=d?l?new g:[]:Qe(e)&&rr(g)?Ci(ro(e)):{}}return(d?kt:zn)(e,function($,w,I){return n(a,$,w,I)}),a},p.unary=function(e){return El(e,1)},p.union=sd,p.unionBy=od,p.unionWith=ad,p.uniq=function(e){return e&&e.length?Cr(e):[]},p.uniqBy=function(e,n){return e&&e.length?Cr(e,ae(n,2)):[]},p.uniqWith=function(e,n){return n=typeof n=="function"?n:o,e&&e.length?Cr(e,o,n):[]},p.unset=function(e,n){return e==null||ja(e,n)},p.unzip=eu,p.unzipWith=xl,p.update=function(e,n,a){return e==null?e:qc(e,n,za(a))},p.updateWith=function(e,n,a,l){return l=typeof l=="function"?l:o,e==null?e:qc(e,n,za(a),l)},p.values=ki,p.valuesIn=function(e){return e==null?[]:va(e,Ut(e))},p.without=ud,p.words=Bl,p.wrap=function(e,n){return nu(za(n),e)},p.xor=cd,p.xorBy=ld,p.xorWith=hd,p.zip=fd,p.zipObject=function(e,n){return Pc(e||[],n||[],fs)},p.zipObjectDeep=function(e,n){return Pc(e||[],n||[],ms)},p.zipWith=dd,p.entries=Ul,p.entriesIn=Wl,p.extend=ql,p.extendWith=Oo,lu(p,p),p.add=ap,p.attempt=Gl,p.camelCase=Hd,p.capitalize=Hl,p.ceil=up,p.clamp=function(e,n,a){return a===o&&(a=n,n=o),a!==o&&(a=(a=dn(a))==a?a:0),n!==o&&(n=(n=dn(n))==n?n:0),Kr(dn(e),n,a)},p.clone=function(e){return ln(e,4)},p.cloneDeep=function(e){return ln(e,5)},p.cloneDeepWith=function(e,n){return ln(e,5,n=typeof n=="function"?n:o)},p.cloneWith=function(e,n){return ln(e,4,n=typeof n=="function"?n:o)},p.conformsTo=function(e,n){return n==null||bc(e,n,mt(n))},p.deburr=Vl,p.defaultTo=function(e,n){return e==null||e!=e?n:e},p.divide=cp,p.endsWith=function(e,n,a){e=Re(e),n=Gt(n);var l=e.length,d=a=a===o?l:Kr(xe(a),0,l);return(a-=n.length)>=0&&e.slice(a,d)==n},p.eq=$n,p.escape=function(e){return(e=Re(e))&&oa.test(e)?e.replace(Hi,lf):e},p.escapeRegExp=function(e){return(e=Re(e))&&Ls.test(e)?e.replace(Gi,"\\$&"):e},p.every=function(e,n,a){var l=$e(e)?an:jf;return a&&Lt(e,n,a)&&(n=o),l(e,ae(n,3))},p.find=md,p.findIndex=vl,p.findKey=function(e,n){return Hr(e,ae(n,3),zn)},p.findLast=_d,p.findLastIndex=yl,p.findLastKey=function(e,n){return Hr(e,ae(n,3),Ea)},p.floor=lp,p.forEach=Sl,p.forEachRight=Il,p.forIn=function(e,n){return e==null?e:Ia(e,ae(n,3),Ut)},p.forInRight=function(e,n){return e==null?e:Cc(e,ae(n,3),Ut)},p.forOwn=function(e,n){return e&&zn(e,ae(n,3))},p.forOwnRight=function(e,n){return e&&Ea(e,ae(n,3))},p.get=su,p.gt=Ed,p.gte=Md,p.has=function(e,n){return e!=null&&al(e,n,Df)},p.hasIn=ou,p.head=$l,p.identity=Wt,p.includes=function(e,n,a,l){e=Pt(e)?e:ki(e),a=a&&!l?xe(a):0;var d=e.length;return a<0&&(a=ht(d+a,0)),To(e)?a<=d&&e.indexOf(n,a)>-1:!!d&&Qn(e,n,a)>-1},p.indexOf=function(e,n,a){var l=e==null?0:e.length;if(!l)return-1;var d=a==null?0:xe(a);return d<0&&(d=ht(l+d,0)),Qn(e,n,d)},p.inRange=function(e,n,a){return n=ir(n),a===o?(a=n,n=0):a=ir(a),function(l,d,g){return l>=At(d,g)&&l<ht(d,g)}(e=dn(e),n,a)},p.invoke=zd,p.isArguments=Zr,p.isArray=$e,p.isArrayBuffer=kd,p.isArrayLike=Pt,p.isArrayLikeObject=nt,p.isBoolean=function(e){return e===!0||e===!1||Ze(e)&&Rt(e)==R},p.isBuffer=Er,p.isDate=Ad,p.isElement=function(e){return Ze(e)&&e.nodeType===1&&!bs(e)},p.isEmpty=function(e){if(e==null)return!0;if(Pt(e)&&($e(e)||typeof e=="string"||typeof e.splice=="function"||Er(e)||Mi(e)||Zr(e)))return!e.length;var n=Ft(e);if(n==oe||n==Te)return!e.size;if(vs(e))return!Fa(e).length;for(var a in e)if(Le.call(e,a))return!1;return!0},p.isEqual=function(e,n){return gs(e,n)},p.isEqualWith=function(e,n,a){var l=(a=typeof a=="function"?a:o)?a(e,n):o;return l===o?gs(e,n,o,a):!!l},p.isError=ru,p.isFinite=function(e){return typeof e=="number"&&pc(e)},p.isFunction=rr,p.isInteger=Tl,p.isLength=Fo,p.isMap=Ol,p.isMatch=function(e,n){return e===n||Aa(e,n,Ga(n))},p.isMatchWith=function(e,n,a){return a=typeof a=="function"?a:o,Aa(e,n,Ga(n),a)},p.isNaN=function(e){return Rl(e)&&e!=+e},p.isNative=function(e){if(Qf(e))throw new Me("Unsupported core-js use. Try https://npms.io/search?q=ponyfill.");return Ec(e)},p.isNil=function(e){return e==null},p.isNull=function(e){return e===null},p.isNumber=Rl,p.isObject=Qe,p.isObjectLike=Ze,p.isPlainObject=bs,p.isRegExp=iu,p.isSafeInteger=function(e){return Tl(e)&&e>=-9007199254740991&&e<=m},p.isSet=Ll,p.isString=To,p.isSymbol=Yt,p.isTypedArray=Mi,p.isUndefined=function(e){return e===o},p.isWeakMap=function(e){return Ze(e)&&Ft(e)==Se},p.isWeakSet=function(e){return Ze(e)&&Rt(e)=="[object WeakSet]"},p.join=function(e,n){return e==null?"":Cf.call(e,n)},p.kebabCase=Vd,p.last=fn,p.lastIndexOf=function(e,n,a){var l=e==null?0:e.length;if(!l)return-1;var d=l;return a!==o&&(d=(d=xe(a))<0?ht(l+d,0):At(d,l-1)),n==n?function(g,$,w){for(var I=w+1;I--;)if(g[I]===$)return I;return I}(e,n,d):Kn(e,Vr,d,!0)},p.lowerCase=Bd,p.lowerFirst=Gd,p.lt=Fd,p.lte=Td,p.max=function(e){return e&&e.length?fo(e,Wt,Ma):o},p.maxBy=function(e,n){return e&&e.length?fo(e,ae(n,2),Ma):o},p.mean=function(e){return nc(e,Wt)},p.meanBy=function(e,n){return nc(e,ae(n,2))},p.min=function(e){return e&&e.length?fo(e,Wt,Ta):o},p.minBy=function(e,n){return e&&e.length?fo(e,ae(n,2),Ta):o},p.stubArray=fu,p.stubFalse=du,p.stubObject=function(){return{}},p.stubString=function(){return""},p.stubTrue=function(){return!0},p.multiply=hp,p.nth=function(e,n){return e&&e.length?Tc(e,xe(n)):o},p.noConflict=function(){return Pe._===this&&(Pe._=vf),this},p.noop=hu,p.now=Mo,p.pad=function(e,n,a){e=Re(e);var l=(n=xe(n))?yi(e):0;if(!n||l>=n)return e;var d=(n-l)/2;return $o(ao(d),a)+e+$o(oo(d),a)},p.padEnd=function(e,n,a){e=Re(e);var l=(n=xe(n))?yi(e):0;return n&&l<n?e+$o(n-l,a):e},p.padStart=function(e,n,a){e=Re(e);var l=(n=xe(n))?yi(e):0;return n&&l<n?$o(n-l,a)+e:e},p.parseInt=function(e,n,a){return a||n==null?n=0:n&&(n=+n),Ef(Re(e).replace(dr,""),n||0)},p.random=function(e,n,a){if(a&&typeof a!="boolean"&&Lt(e,n,a)&&(n=a=o),a===o&&(typeof n=="boolean"?(a=n,n=o):typeof e=="boolean"&&(a=e,e=o)),e===o&&n===o?(e=0,n=1):(e=ir(e),n===o?(n=e,e=0):n=ir(n)),e>n){var l=e;e=n,n=l}if(a||e%1||n%1){var d=gc();return At(e+d*(n-e+Ht("1e-"+((d+"").length-1))),n)}return Ra(e,n)},p.reduce=function(e,n,a){var l=$e(e)?vn:rc,d=arguments.length<3;return l(e,ae(n,4),a,d,xr)},p.reduceRight=function(e,n,a){var l=$e(e)?Ks:rc,d=arguments.length<3;return l(e,ae(n,4),a,d,wc)},p.repeat=function(e,n,a){return n=(a?Lt(e,n,a):n===o)?1:xe(n),La(Re(e),n)},p.replace=function(){var e=arguments,n=Re(e[0]);return e.length<3?n:n.replace(e[1],e[2])},p.result=function(e,n,a){var l=-1,d=(n=Sr(n,e)).length;for(d||(d=1,e=o);++l<d;){var g=e==null?o:e[Un(n[l])];g===o&&(l=d,g=a),e=rr(g)?g.call(e):g}return e},p.round=fp,p.runInContext=M,p.sample=function(e){return($e(e)?vc:Pf)(e)},p.size=function(e){if(e==null)return 0;if(Pt(e))return To(e)?yi(e):e.length;var n=Ft(e);return n==oe||n==Te?e.size:Fa(e).length},p.snakeCase=Yd,p.some=function(e,n,a){var l=$e(e)?br:Vf;return a&&Lt(e,n,a)&&(n=o),l(e,ae(n,3))},p.sortedIndex=function(e,n){return mo(e,n)},p.sortedIndexBy=function(e,n,a){return Na(e,n,ae(a,2))},p.sortedIndexOf=function(e,n){var a=e==null?0:e.length;if(a){var l=mo(e,n);if(l<a&&$n(e[l],n))return l}return-1},p.sortedLastIndex=function(e,n){return mo(e,n,!0)},p.sortedLastIndexBy=function(e,n,a){return Na(e,n,ae(a,2),!0)},p.sortedLastIndexOf=function(e,n){if(e!=null&&e.length){var a=mo(e,n,!0)-1;if($n(e[a],n))return a}return-1},p.startCase=Kd,p.startsWith=function(e,n,a){return e=Re(e),a=a==null?0:Kr(xe(a),0,e.length),n=Gt(n),e.slice(a,a+n.length)==n},p.subtract=dp,p.sum=function(e){return e&&e.length?ma(e,Wt):0},p.sumBy=function(e,n){return e&&e.length?ma(e,ae(n,2)):0},p.template=function(e,n,a){var l=p.templateSettings;a&&Lt(e,n,a)&&(n=o),e=Re(e),n=Oo({},n,l,rl);var d,g,$=Oo({},n.imports,l.imports,rl),w=mt($),I=va($,w),L=0,A=n.interpolate||fi,P="__p += '",J=ba((n.escape||fi).source+"|"+A.source+"|"+(A===Bi?Yi:fi).source+"|"+(n.evaluate||fi).source+"|$","g"),Z="//# sourceURL="+(Le.call(n,"sourceURL")?(n.sourceURL+"").replace(/\s/g," "):"lodash.templateSources["+ ++ze+"]")+`
`;e.replace(J,function(U,ce,V,ye,ge,Fe){return V||(V=ye),P+=e.slice(L,Fe).replace(fa,hf),ce&&(d=!0,P+=`' +
__e(`+ce+`) +
'`),ge&&(g=!0,P+=`';
`+ge+`;
__p += '`),V&&(P+=`' +
((__t = (`+V+`)) == null ? '' : __t) +
'`),L=Fe+U.length,U}),P+=`';
`;var se=Le.call(n,"variable")&&n.variable;if(se){if(ua.test(se))throw new Me("Invalid `variable` option passed into `_.template`")}else P=`with (obj) {
`+P+`
}
`;P=(g?P.replace(ra,""):P).replace(ia,"$1").replace(Ts,"$1;"),P="function("+(se||"obj")+`) {
`+(se?"":`obj || (obj = {});
`)+"var __t, __p = ''"+(d?", __e = _.escape":"")+(g?`, __j = Array.prototype.join;
function print() { __p += __j.call(arguments, '') }
`:`;
`)+P+`return __p
}`;var N=Gl(function(){return lt(w,Z+"return "+P).apply(o,I)});if(N.source=P,ru(N))throw N;return N},p.times=function(e,n){if((e=xe(e))<1||e>m)return[];var a=x,l=At(e,x);n=ae(n),e-=x;for(var d=_a(l,n);++a<e;)n(a);return d},p.toFinite=ir,p.toInteger=xe,p.toLength=jl,p.toLower=function(e){return Re(e).toLowerCase()},p.toNumber=dn,p.toSafeInteger=function(e){return e?Kr(xe(e),-9007199254740991,m):e===0?e:0},p.toString=Re,p.toUpper=function(e){return Re(e).toUpperCase()},p.trim=function(e,n,a){if((e=Re(e))&&(a||n===o))return ic(e);if(!e||!(n=Gt(n)))return e;var l=yn(e),d=yn(n);return Ir(l,sc(l,d),oc(l,d)+1).join("")},p.trimEnd=function(e,n,a){if((e=Re(e))&&(a||n===o))return e.slice(0,uc(e)+1);if(!e||!(n=Gt(n)))return e;var l=yn(e);return Ir(l,0,oc(l,yn(n))+1).join("")},p.trimStart=function(e,n,a){if((e=Re(e))&&(a||n===o))return e.replace(dr,"");if(!e||!(n=Gt(n)))return e;var l=yn(e);return Ir(l,sc(l,yn(n))).join("")},p.truncate=function(e,n){var a=30,l="...";if(Qe(n)){var d="separator"in n?n.separator:d;a="length"in n?xe(n.length):a,l="omission"in n?Gt(n.omission):l}var g=(e=Re(e)).length;if(vi(e)){var $=yn(e);g=$.length}if(a>=g)return e;var w=a-yi(l);if(w<1)return l;var I=$?Ir($,0,w).join(""):e.slice(0,w);if(d===o)return I+l;if($&&(w+=I.length-w),iu(d)){if(e.slice(w).search(d)){var L,A=I;for(d.global||(d=ba(d.source,Re(pr.exec(d))+"g")),d.lastIndex=0;L=d.exec(A);)var P=L.index;I=I.slice(0,P===o?w:P)}}else if(e.indexOf(Gt(d),w)!=w){var J=I.lastIndexOf(d);J>-1&&(I=I.slice(0,J))}return I+l},p.unescape=function(e){return(e=Re(e))&&sa.test(e)?e.replace(Wi,df):e},p.uniqueId=function(e){var n=++mf;return Re(e)+n},p.upperCase=Qd,p.upperFirst=au,p.each=Sl,p.eachRight=Il,p.first=$l,lu(p,(pu={},zn(p,function(e,n){Le.call(p.prototype,n)||(pu[n]=e)}),pu),{chain:!1}),p.VERSION="4.17.21",kt(["bind","bindKey","curry","curryRight","partial","partialRight"],function(e){p[e].placeholder=p}),kt(["drop","take"],function(e,n){Ee.prototype[e]=function(a){a=a===o?1:ht(xe(a),0);var l=this.__filtered__&&!n?new Ee(this):this.clone();return l.__filtered__?l.__takeCount__=At(a,l.__takeCount__):l.__views__.push({size:At(a,x),type:e+(l.__dir__<0?"Right":"")}),l},Ee.prototype[e+"Right"]=function(a){return this.reverse()[e](a).reverse()}}),kt(["filter","map","takeWhile"],function(e,n){var a=n+1,l=a==1||a==3;Ee.prototype[e]=function(d){var g=this.clone();return g.__iteratees__.push({iteratee:ae(d,3),type:a}),g.__filtered__=g.__filtered__||l,g}}),kt(["head","last"],function(e,n){var a="take"+(n?"Right":"");Ee.prototype[e]=function(){return this[a](1).value()[0]}}),kt(["initial","tail"],function(e,n){var a="drop"+(n?"":"Right");Ee.prototype[e]=function(){return this.__filtered__?new Ee(this):this[a](1)}}),Ee.prototype.compact=function(){return this.filter(Wt)},Ee.prototype.find=function(e){return this.filter(e).head()},Ee.prototype.findLast=function(e){return this.reverse().find(e)},Ee.prototype.invokeMap=Ce(function(e,n){return typeof e=="function"?new Ee(this):this.map(function(a){return ps(a,e,n)})}),Ee.prototype.reject=function(e){return this.filter(Ao(ae(e)))},Ee.prototype.slice=function(e,n){e=xe(e);var a=this;return a.__filtered__&&(e>0||n<0)?new Ee(a):(e<0?a=a.takeRight(-e):e&&(a=a.drop(e)),n!==o&&(a=(n=xe(n))<0?a.dropRight(-n):a.take(n-e)),a)},Ee.prototype.takeRightWhile=function(e){return this.reverse().takeWhile(e).reverse()},Ee.prototype.toArray=function(){return this.take(x)},zn(Ee.prototype,function(e,n){var a=/^(?:filter|find|map|reject)|While$/.test(n),l=/^(?:head|last)$/.test(n),d=p[l?"take"+(n=="last"?"Right":""):n],g=l||/^find/.test(n);d&&(p.prototype[n]=function(){var $=this.__wrapped__,w=l?[1]:arguments,I=$ instanceof Ee,L=w[0],A=I||$e($),P=function(ce){var V=d.apply(p,Vt([ce],w));return l&&J?V[0]:V};A&&a&&typeof L=="function"&&L.length!=1&&(I=A=!1);var J=this.__chain__,Z=!!this.__actions__.length,se=g&&!J,N=I&&!Z;if(!g&&A){$=N?$:new Ee(this);var U=e.apply($,w);return U.__actions__.push({func:Io,args:[P],thisArg:o}),new cn(U,J)}return se&&N?e.apply(this,w):(U=this.thru(P),se?l?U.value()[0]:U.value():U)})}),kt(["pop","push","shift","sort","splice","unshift"],function(e){var n=Js[e],a=/^(?:push|sort|unshift)$/.test(e)?"tap":"thru",l=/^(?:pop|shift)$/.test(e);p.prototype[e]=function(){var d=arguments;if(l&&!this.__chain__){var g=this.value();return n.apply($e(g)?g:[],d)}return this[a](function($){return n.apply($e($)?$:[],d)})}}),zn(Ee.prototype,function(e,n){var a=p[n];if(a){var l=a.name+"";Le.call(xi,l)||(xi[l]=[]),xi[l].push({name:n,func:a})}}),xi[yo(o,2).name]=[{name:"wrapper",func:o}],Ee.prototype.clone=function(){var e=new Ee(this.__wrapped__);return e.__actions__=zt(this.__actions__),e.__dir__=this.__dir__,e.__filtered__=this.__filtered__,e.__iteratees__=zt(this.__iteratees__),e.__takeCount__=this.__takeCount__,e.__views__=zt(this.__views__),e},Ee.prototype.reverse=function(){if(this.__filtered__){var e=new Ee(this);e.__dir__=-1,e.__filtered__=!0}else(e=this.clone()).__dir__*=-1;return e},Ee.prototype.value=function(){var e=this.__wrapped__.value(),n=this.__dir__,a=$e(e),l=n<0,d=a?e.length:0,g=function(Fe,te,le){for(var ft=-1,rt=le.length;++ft<rt;){var Nt=le[ft],Ge=Nt.size;switch(Nt.type){case"drop":Fe+=Ge;break;case"dropRight":te-=Ge;break;case"take":te=At(te,Fe+Ge);break;case"takeRight":Fe=ht(Fe,te-Ge)}}return{start:Fe,end:te}}(0,d,this.__views__),$=g.start,w=g.end,I=w-$,L=l?w:$-1,A=this.__iteratees__,P=A.length,J=0,Z=At(I,this.__takeCount__);if(!a||!l&&d==I&&Z==I)return zc(e,this.__actions__);var se=[];e:for(;I--&&J<Z;){for(var N=-1,U=e[L+=n];++N<P;){var ce=A[N],V=ce.iteratee,ye=ce.type,ge=V(U);if(ye==2)U=ge;else if(!ge){if(ye==1)continue e;break e}}se[J++]=U}return se},p.prototype.at=pd,p.prototype.chain=function(){return Cl(this)},p.prototype.commit=function(){return new cn(this.value(),this.__chain__)},p.prototype.next=function(){this.__values__===o&&(this.__values__=Nl(this.value()));var e=this.__index__>=this.__values__.length;return{done:e,value:e?o:this.__values__[this.__index__++]}},p.prototype.plant=function(e){for(var n,a=this;a instanceof lo;){var l=_l(a);l.__index__=0,l.__values__=o,n?d.__wrapped__=l:n=l;var d=l;a=a.__wrapped__}return d.__wrapped__=e,n},p.prototype.reverse=function(){var e=this.__wrapped__;if(e instanceof Ee){var n=e;return this.__actions__.length&&(n=new Ee(this)),(n=n.reverse()).__actions__.push({func:Io,args:[Za],thisArg:o}),new cn(n,this.__chain__)}return this.thru(Za)},p.prototype.toJSON=p.prototype.valueOf=p.prototype.value=function(){return zc(this.__wrapped__,this.__actions__)},p.prototype.first=p.prototype.head,as&&(p.prototype[as]=function(){return this}),p}();St?((St.exports=bi)._=bi,ut._=bi):Pe._=bi}).call(tn);var Yg=Uu.exports;function lr(o){return Array.isArray?Array.isArray(o):tf(o)==="[object Array]"}const Kg=1/0;function Qg(o){return o==null?"":function(t){if(typeof t=="string")return t;let r=t+"";return r=="0"&&1/t==-Kg?"-0":r}(o)}function Hn(o){return typeof o=="string"}function Zh(o){return typeof o=="number"}function Jg(o){return o===!0||o===!1||function(t){return ef(t)&&t!==null}(o)&&tf(o)=="[object Boolean]"}function ef(o){return typeof o=="object"}function en(o){return o!=null}function Su(o){return!o.trim().length}function tf(o){return o==null?o===void 0?"[object Undefined]":"[object Null]":Object.prototype.toString.call(o)}const Xg=o=>`Missing ${o} property in key`,Zg=o=>`Property 'weight' in key '${o}' must be a positive integer`,_h=Object.prototype.hasOwnProperty;class em{constructor(t){this._keys=[],this._keyMap={};let r=0;t.forEach(i=>{let s=nf(i);this._keys.push(s),this._keyMap[s.id]=s,r+=s.weight}),this._keys.forEach(i=>{i.weight/=r})}get(t){return this._keyMap[t]}keys(){return this._keys}toJSON(){return JSON.stringify(this._keys)}}function nf(o){let t=null,r=null,i=null,s=1,u=null;if(Hn(o)||lr(o))i=o,t=vh(o),r=Wu(o);else{if(!_h.call(o,"name"))throw new Error(Xg("name"));const c=o.name;if(i=c,_h.call(o,"weight")&&(s=o.weight,s<=0))throw new Error(Zg(c));t=vh(c),r=Wu(c),u=o.getFn}return{path:t,id:r,weight:s,src:i,getFn:u}}function vh(o){return lr(o)?o:o.split(".")}function Wu(o){return lr(o)?o.join("."):o}var ve={isCaseSensitive:!1,includeScore:!1,keys:[],shouldSort:!0,sortFn:(o,t)=>o.score===t.score?o.idx<t.idx?-1:1:o.score<t.score?-1:1,includeMatches:!1,findAllMatches:!1,minMatchCharLength:1,location:0,threshold:.6,distance:100,useExtendedSearch:!1,getFn:function(o,t){let r=[],i=!1;const s=(u,c,h)=>{if(en(u))if(c[h]){const f=u[c[h]];if(!en(f))return;if(h===c.length-1&&(Hn(f)||Zh(f)||Jg(f)))r.push(Qg(f));else if(lr(f)){i=!0;for(let v=0,m=f.length;v<m;v+=1)s(f[v],c,h+1)}else c.length&&s(f,c,h+1)}else r.push(u)};return s(o,Hn(t)?t.split("."):t,0),i?r:r[0]},ignoreLocation:!1,ignoreFieldNorm:!1,fieldNormWeight:1};const tm=/[^ ]+/g;class Zu{constructor({getFn:t=ve.getFn,fieldNormWeight:r=ve.fieldNormWeight}={}){this.norm=function(i=1,s=3){const u=new Map,c=Math.pow(10,s);return{get(h){const f=h.match(tm).length;if(u.has(f))return u.get(f);const v=1/Math.pow(f,.5*i),m=parseFloat(Math.round(v*c)/c);return u.set(f,m),m},clear(){u.clear()}}}(r,3),this.getFn=t,this.isCreated=!1,this.setIndexRecords()}setSources(t=[]){this.docs=t}setIndexRecords(t=[]){this.records=t}setKeys(t=[]){this.keys=t,this._keysMap={},t.forEach((r,i)=>{this._keysMap[r.id]=i})}create(){!this.isCreated&&this.docs.length&&(this.isCreated=!0,Hn(this.docs[0])?this.docs.forEach((t,r)=>{this._addString(t,r)}):this.docs.forEach((t,r)=>{this._addObject(t,r)}),this.norm.clear())}add(t){const r=this.size();Hn(t)?this._addString(t,r):this._addObject(t,r)}removeAt(t){this.records.splice(t,1);for(let r=t,i=this.size();r<i;r+=1)this.records[r].i-=1}getValueForItemAtKeyId(t,r){return t[this._keysMap[r]]}size(){return this.records.length}_addString(t,r){if(!en(t)||Su(t))return;let i={v:t,i:r,n:this.norm.get(t)};this.records.push(i)}_addObject(t,r){let i={i:r,$:{}};this.keys.forEach((s,u)=>{let c=s.getFn?s.getFn(t):this.getFn(t,s.path);if(en(c)){if(lr(c)){let h=[];const f=[{nestedArrIndex:-1,value:c}];for(;f.length;){const{nestedArrIndex:v,value:m}=f.pop();if(en(m))if(Hn(m)&&!Su(m)){let b={v:m,i:v,n:this.norm.get(m)};h.push(b)}else lr(m)&&m.forEach((b,x)=>{f.push({nestedArrIndex:x,value:b})})}i.$[u]=h}else if(Hn(c)&&!Su(c)){let h={v:c,n:this.norm.get(c)};i.$[u]=h}}}),this.records.push(i)}toJSON(){return{keys:this.keys,records:this.records}}}function rf(o,t,{getFn:r=ve.getFn,fieldNormWeight:i=ve.fieldNormWeight}={}){const s=new Zu({getFn:r,fieldNormWeight:i});return s.setKeys(o.map(nf)),s.setSources(t),s.create(),s}function Vo(o,{errors:t=0,currentLocation:r=0,expectedLocation:i=0,distance:s=ve.distance,ignoreLocation:u=ve.ignoreLocation}={}){const c=t/o.length;if(u)return c;const h=Math.abs(i-r);return s?c+h/s:h?1:c}const ti=32;function nm(o,t,r,{location:i=ve.location,distance:s=ve.distance,threshold:u=ve.threshold,findAllMatches:c=ve.findAllMatches,minMatchCharLength:h=ve.minMatchCharLength,includeMatches:f=ve.includeMatches,ignoreLocation:v=ve.ignoreLocation}={}){if(t.length>ti)throw new Error(`Pattern length exceeds max of ${ti}.`);const m=t.length,b=o.length,x=Math.max(0,Math.min(i,b));let E=u,F=x;const z=h>1||f,R=z?Array(b):[];let S;for(;(S=o.indexOf(t,F))>-1;){let pe=Vo(t,{currentLocation:S,expectedLocation:x,distance:s,ignoreLocation:v});if(E=Math.min(pe,E),F=S+m,z){let Ie=0;for(;Ie<m;)R[S+Ie]=1,Ie+=1}}F=-1;let T=[],ne=1,de=m+b;const oe=1<<m-1;for(let pe=0;pe<m;pe+=1){let Ie=0,me=de;for(;Ie<me;)Vo(t,{errors:pe,currentLocation:x+me,expectedLocation:x,distance:s,ignoreLocation:v})<=E?Ie=me:de=me,me=Math.floor((de-Ie)/2+Ie);de=me;let Te=Math.max(1,x-me+1),Ue=c?b:Math.min(x+me,b)+m,st=Array(Ue+2);st[Ue+1]=(1<<pe)-1;for(let Se=Ue;Se>=Te;Se-=1){let we=Se-1,at=r[o.charAt(we)];if(z&&(R[we]=+!!at),st[Se]=(st[Se+1]<<1|1)&at,pe&&(st[Se]|=(T[Se+1]|T[Se])<<1|1|T[Se+1]),st[Se]&oe&&(ne=Vo(t,{errors:pe,currentLocation:we,expectedLocation:x,distance:s,ignoreLocation:v}),ne<=E)){if(E=ne,F=we,F<=x)break;Te=Math.max(1,2*x-F)}}if(Vo(t,{errors:pe+1,currentLocation:x,expectedLocation:x,distance:s,ignoreLocation:v})>E)break;T=st}const Ne={isMatch:F>=0,score:Math.max(.001,ne)};if(z){const pe=function(Ie=[],me=ve.minMatchCharLength){let Te=[],Ue=-1,st=-1,Se=0;for(let we=Ie.length;Se<we;Se+=1){let at=Ie[Se];at&&Ue===-1?Ue=Se:at||Ue===-1||(st=Se-1,st-Ue+1>=me&&Te.push([Ue,st]),Ue=-1)}return Ie[Se-1]&&Se-Ue>=me&&Te.push([Ue,Se-1]),Te}(R,h);pe.length?f&&(Ne.indices=pe):Ne.isMatch=!1}return Ne}function rm(o){let t={};for(let r=0,i=o.length;r<i;r+=1){const s=o.charAt(r);t[s]=(t[s]||0)|1<<i-r-1}return t}class sf{constructor(t,{location:r=ve.location,threshold:i=ve.threshold,distance:s=ve.distance,includeMatches:u=ve.includeMatches,findAllMatches:c=ve.findAllMatches,minMatchCharLength:h=ve.minMatchCharLength,isCaseSensitive:f=ve.isCaseSensitive,ignoreLocation:v=ve.ignoreLocation}={}){if(this.options={location:r,threshold:i,distance:s,includeMatches:u,findAllMatches:c,minMatchCharLength:h,isCaseSensitive:f,ignoreLocation:v},this.pattern=f?t:t.toLowerCase(),this.chunks=[],!this.pattern.length)return;const m=(x,E)=>{this.chunks.push({pattern:x,alphabet:rm(x),startIndex:E})},b=this.pattern.length;if(b>ti){let x=0;const E=b%ti,F=b-E;for(;x<F;)m(this.pattern.substr(x,ti),x),x+=ti;if(E){const z=b-ti;m(this.pattern.substr(z),z)}}else m(this.pattern,0)}searchIn(t){const{isCaseSensitive:r,includeMatches:i}=this.options;if(r||(t=t.toLowerCase()),this.pattern===t){let F={isMatch:!0,score:0};return i&&(F.indices=[[0,t.length-1]]),F}const{location:s,distance:u,threshold:c,findAllMatches:h,minMatchCharLength:f,ignoreLocation:v}=this.options;let m=[],b=0,x=!1;this.chunks.forEach(({pattern:F,alphabet:z,startIndex:R})=>{const{isMatch:S,score:T,indices:ne}=nm(t,F,z,{location:s+R,distance:u,threshold:c,findAllMatches:h,minMatchCharLength:f,includeMatches:i,ignoreLocation:v});S&&(x=!0),b+=T,S&&ne&&(m=[...m,...ne])});let E={isMatch:x,score:x?b/this.chunks.length:1};return x&&i&&(E.indices=m),E}}class Or{constructor(t){this.pattern=t}static isMultiMatch(t){return yh(t,this.multiRegex)}static isSingleMatch(t){return yh(t,this.singleRegex)}search(){}}function yh(o,t){const r=o.match(t);return r?r[1]:null}class of extends Or{constructor(t,{location:r=ve.location,threshold:i=ve.threshold,distance:s=ve.distance,includeMatches:u=ve.includeMatches,findAllMatches:c=ve.findAllMatches,minMatchCharLength:h=ve.minMatchCharLength,isCaseSensitive:f=ve.isCaseSensitive,ignoreLocation:v=ve.ignoreLocation}={}){super(t),this._bitapSearch=new sf(t,{location:r,threshold:i,distance:s,includeMatches:u,findAllMatches:c,minMatchCharLength:h,isCaseSensitive:f,ignoreLocation:v})}static get type(){return"fuzzy"}static get multiRegex(){return/^"(.*)"$/}static get singleRegex(){return/^(.*)$/}search(t){return this._bitapSearch.searchIn(t)}}class af extends Or{constructor(t){super(t)}static get type(){return"include"}static get multiRegex(){return/^'"(.*)"$/}static get singleRegex(){return/^'(.*)$/}search(t){let r,i=0;const s=[],u=this.pattern.length;for(;(r=t.indexOf(this.pattern,i))>-1;)i=r+u,s.push([r,i-1]);const c=!!s.length;return{isMatch:c,score:c?0:1,indices:s}}}const Hu=[class extends Or{constructor(o){super(o)}static get type(){return"exact"}static get multiRegex(){return/^="(.*)"$/}static get singleRegex(){return/^=(.*)$/}search(o){const t=o===this.pattern;return{isMatch:t,score:t?0:1,indices:[0,this.pattern.length-1]}}},af,class extends Or{constructor(o){super(o)}static get type(){return"prefix-exact"}static get multiRegex(){return/^\^"(.*)"$/}static get singleRegex(){return/^\^(.*)$/}search(o){const t=o.startsWith(this.pattern);return{isMatch:t,score:t?0:1,indices:[0,this.pattern.length-1]}}},class extends Or{constructor(o){super(o)}static get type(){return"inverse-prefix-exact"}static get multiRegex(){return/^!\^"(.*)"$/}static get singleRegex(){return/^!\^(.*)$/}search(o){const t=!o.startsWith(this.pattern);return{isMatch:t,score:t?0:1,indices:[0,o.length-1]}}},class extends Or{constructor(o){super(o)}static get type(){return"inverse-suffix-exact"}static get multiRegex(){return/^!"(.*)"\$$/}static get singleRegex(){return/^!(.*)\$$/}search(o){const t=!o.endsWith(this.pattern);return{isMatch:t,score:t?0:1,indices:[0,o.length-1]}}},class extends Or{constructor(o){super(o)}static get type(){return"suffix-exact"}static get multiRegex(){return/^"(.*)"\$$/}static get singleRegex(){return/^(.*)\$$/}search(o){const t=o.endsWith(this.pattern);return{isMatch:t,score:t?0:1,indices:[o.length-this.pattern.length,o.length-1]}}},class extends Or{constructor(o){super(o)}static get type(){return"inverse-exact"}static get multiRegex(){return/^!"(.*)"$/}static get singleRegex(){return/^!(.*)$/}search(o){const t=o.indexOf(this.pattern)===-1;return{isMatch:t,score:t?0:1,indices:[0,o.length-1]}}},of],bh=Hu.length,im=/ +(?=(?:[^\"]*\"[^\"]*\")*[^\"]*$)/,sm=new Set([of.type,af.type]);class om{constructor(t,{isCaseSensitive:r=ve.isCaseSensitive,includeMatches:i=ve.includeMatches,minMatchCharLength:s=ve.minMatchCharLength,ignoreLocation:u=ve.ignoreLocation,findAllMatches:c=ve.findAllMatches,location:h=ve.location,threshold:f=ve.threshold,distance:v=ve.distance}={}){this.query=null,this.options={isCaseSensitive:r,includeMatches:i,minMatchCharLength:s,findAllMatches:c,ignoreLocation:u,location:h,threshold:f,distance:v},this.pattern=r?t:t.toLowerCase(),this.query=function(m,b={}){return m.split("|").map(x=>{let E=x.trim().split(im).filter(z=>z&&!!z.trim()),F=[];for(let z=0,R=E.length;z<R;z+=1){const S=E[z];let T=!1,ne=-1;for(;!T&&++ne<bh;){const de=Hu[ne];let oe=de.isMultiMatch(S);oe&&(F.push(new de(oe,b)),T=!0)}if(!T)for(ne=-1;++ne<bh;){const de=Hu[ne];let oe=de.isSingleMatch(S);if(oe){F.push(new de(oe,b));break}}}return F})}(this.pattern,this.options)}static condition(t,r){return r.useExtendedSearch}searchIn(t){const r=this.query;if(!r)return{isMatch:!1,score:1};const{includeMatches:i,isCaseSensitive:s}=this.options;t=s?t:t.toLowerCase();let u=0,c=[],h=0;for(let f=0,v=r.length;f<v;f+=1){const m=r[f];c.length=0,u=0;for(let b=0,x=m.length;b<x;b+=1){const E=m[b],{isMatch:F,indices:z,score:R}=E.search(t);if(!F){h=0,u=0,c.length=0;break}if(u+=1,h+=R,i){const S=E.constructor.type;sm.has(S)?c=[...c,...z]:c.push(z)}}if(u){let b={isMatch:!0,score:h/u};return i&&(b.indices=c),b}}return{isMatch:!1,score:1}}}const Vu=[];function Bu(o,t){for(let r=0,i=Vu.length;r<i;r+=1){let s=Vu[r];if(s.condition(o,t))return new s(o,t)}return new sf(o,t)}const ec="$and",am="$or",$h="$path",um="$val",Iu=o=>!(!o[ec]&&!o[am]),wh=o=>({[ec]:Object.keys(o).map(t=>({[t]:o[t]}))});function uf(o,t,{auto:r=!0}={}){const i=s=>{let u=Object.keys(s);const c=(f=>!!f[$h])(s);if(!c&&u.length>1&&!Iu(s))return i(wh(s));if((f=>!lr(f)&&ef(f)&&!Iu(f))(s)){const f=c?s[$h]:u[0],v=c?s[um]:s[f];if(!Hn(v))throw new Error((b=>`Invalid value for key ${b}`)(f));const m={keyId:Wu(f),pattern:v};return r&&(m.searcher=Bu(v,t)),m}let h={children:[],operator:u[0]};return u.forEach(f=>{const v=s[f];lr(v)&&v.forEach(m=>{h.children.push(i(m))})}),h};return Iu(o)||(o=wh(o)),i(o)}function cm(o,t){const r=o.matches;t.matches=[],en(r)&&r.forEach(i=>{if(!en(i.indices)||!i.indices.length)return;const{indices:s,value:u}=i;let c={indices:s,value:u};i.key&&(c.key=i.key.src),i.idx>-1&&(c.refIndex=i.idx),t.matches.push(c)})}function lm(o,t){t.score=o.score}class Fi{constructor(t,r={},i){this.options={...ve,...r},this.options.useExtendedSearch,this._keyStore=new em(this.options.keys),this.setCollection(t,i)}setCollection(t,r){if(this._docs=t,r&&!(r instanceof Zu))throw new Error("Incorrect 'index' type");this._myIndex=r||rf(this.options.keys,this._docs,{getFn:this.options.getFn,fieldNormWeight:this.options.fieldNormWeight})}add(t){en(t)&&(this._docs.push(t),this._myIndex.add(t))}remove(t=()=>!1){const r=[];for(let i=0,s=this._docs.length;i<s;i+=1){const u=this._docs[i];t(u,i)&&(this.removeAt(i),i-=1,s-=1,r.push(u))}return r}removeAt(t){this._docs.splice(t,1),this._myIndex.removeAt(t)}getIndex(){return this._myIndex}search(t,{limit:r=-1}={}){const{includeMatches:i,includeScore:s,shouldSort:u,sortFn:c,ignoreFieldNorm:h}=this.options;let f=Hn(t)?Hn(this._docs[0])?this._searchStringList(t):this._searchObjectList(t):this._searchLogical(t);return function(v,{ignoreFieldNorm:m=ve.ignoreFieldNorm}){v.forEach(b=>{let x=1;b.matches.forEach(({key:E,norm:F,score:z})=>{const R=E?E.weight:null;x*=Math.pow(z===0&&R?Number.EPSILON:z,(R||1)*(m?1:F))}),b.score=x})}(f,{ignoreFieldNorm:h}),u&&f.sort(c),Zh(r)&&r>-1&&(f=f.slice(0,r)),function(v,m,{includeMatches:b=ve.includeMatches,includeScore:x=ve.includeScore}={}){const E=[];return b&&E.push(cm),x&&E.push(lm),v.map(F=>{const{idx:z}=F,R={item:m[z],refIndex:z};return E.length&&E.forEach(S=>{S(F,R)}),R})}(f,this._docs,{includeMatches:i,includeScore:s})}_searchStringList(t){const r=Bu(t,this.options),{records:i}=this._myIndex,s=[];return i.forEach(({v:u,i:c,n:h})=>{if(!en(u))return;const{isMatch:f,score:v,indices:m}=r.searchIn(u);f&&s.push({item:u,idx:c,matches:[{score:v,value:u,norm:h,indices:m}]})}),s}_searchLogical(t){const r=uf(t,this.options),i=(h,f,v)=>{if(!h.children){const{keyId:b,searcher:x}=h,E=this._findMatches({key:this._keyStore.get(b),value:this._myIndex.getValueForItemAtKeyId(f,b),searcher:x});return E&&E.length?[{idx:v,item:f,matches:E}]:[]}const m=[];for(let b=0,x=h.children.length;b<x;b+=1){const E=h.children[b],F=i(E,f,v);if(F.length)m.push(...F);else if(h.operator===ec)return[]}return m},s=this._myIndex.records,u={},c=[];return s.forEach(({$:h,i:f})=>{if(en(h)){let v=i(r,h,f);v.length&&(u[f]||(u[f]={idx:f,item:h,matches:[]},c.push(u[f])),v.forEach(({matches:m})=>{u[f].matches.push(...m)}))}}),c}_searchObjectList(t){const r=Bu(t,this.options),{keys:i,records:s}=this._myIndex,u=[];return s.forEach(({$:c,i:h})=>{if(!en(c))return;let f=[];i.forEach((v,m)=>{f.push(...this._findMatches({key:v,value:c[m],searcher:r}))}),f.length&&u.push({idx:h,item:c,matches:f})}),u}_findMatches({key:t,value:r,searcher:i}){if(!en(r))return[];let s=[];if(lr(r))r.forEach(({v:u,i:c,n:h})=>{if(!en(u))return;const{isMatch:f,score:v,indices:m}=i.searchIn(u);f&&s.push({score:v,key:t,value:u,idx:c,norm:h,indices:m})});else{const{v:u,n:c}=r,{isMatch:h,score:f,indices:v}=i.searchIn(u);h&&s.push({score:f,key:t,value:u,norm:c,indices:v})}return s}}Fi.version="7.0.0",Fi.createIndex=rf,Fi.parseIndex=function(o,{getFn:t=ve.getFn,fieldNormWeight:r=ve.fieldNormWeight}={}){const{keys:i,records:s}=o,u=new Zu({getFn:t,fieldNormWeight:r});return u.setKeys(i),u.setIndexRecords(s),u},Fi.config=ve,Fi.parseQuery=uf,function(...o){Vu.push(...o)}(om);const Rr=class Rr{constructor(t,r){_(this,"_disposers",[]);_(this,"_allMentionables",bt([]));_(this,"_breadcrumbIds",bt([]));_(this,"_userQuery",bt(""));_(this,"_active",bt(!1));_(this,"_allGroups",vu([this._active,this._allMentionables],([t,r])=>t?Tp(r):[]));_(this,"_currentGroup",vu([this._breadcrumbIds,this._allGroups],([t,r])=>{if(t.length===0)return;const i=t[t.length-1];return r.find(s=>ks(s)&&s.id===i)}));_(this,"dispose",()=>{for(const t of this._disposers)t()});_(this,"openDropdown",()=>{this._active.set(!0)});_(this,"closeDropdown",()=>{this._active.set(!1),this._resetState()});_(this,"toggleDropdown",()=>pt(this._active)?(this.closeDropdown(),!1):(this.openDropdown(),!0));_(this,"pushBreadcrumb",t=>{pt(this._active)&&this._breadcrumbIds.update(r=>[...r,t.id])});_(this,"popBreadcrumb",()=>{pt(this._active)&&this._breadcrumbIds.update(t=>t.slice(0,-1))});_(this,"selectMentionable",t=>{var s;const r=this._chatModel.extensionClient,i=this._chatModel.specialContextInputModel;return ks(t)&&t.type==="breadcrumb"?(this.pushBreadcrumb(t),!0):t.type==="breadcrumb-back"?(this.popBreadcrumb(),!0):zh(t)?(i.markAllActive(),this.closeDropdown(),r.reportWebviewClientEvent(ku.chatRestoreDefaultContext),!0):t.clearContext?(i.markAllInactive(),this.closeDropdown(),r.reportWebviewClientEvent(ku.chatClearContext),!0):t.userGuidelines?(r.openSettingsPage("userGuidelines"),this.closeDropdown(),!0):((s=this._insertMentionNode)==null||s.call(this,t),this.closeDropdown(),!0)});_(this,"_displayItems",vu([this._active,this._breadcrumbIds,this._userQuery,this._currentGroup,this.allGroups],([t,r,i,s,u])=>t?r.length>0&&s?[{...s,type:"breadcrumb-back"},...s.group.items.slice(0,Rr.SINGLE_GROUP_MAX_ITEMS).map(c=>({...c,type:"item"}))]:i.length>0?u.flatMap(c=>[{...c,type:"breadcrumb"},...c.group.items.slice(0,Rr.MULTI_GROUP_MAX_ITEMS).map(h=>({...h,type:"item"}))]):[{...Op,type:"item"},...u.map(c=>({...c,type:"breadcrumb"})),{...Rp,type:"item"},{...Lp,type:"item"}]:[]));_(this,"_refreshSeqNum",0);_(this,"_refreshMentionables",Yg.throttle(async()=>{if(!pt(this._active))return;this._refreshSeqNum++;const t=this._refreshSeqNum,r=this._chatModel.currentConversationModel&&Li(this._chatModel.currentConversationModel),i=pt(this._userQuery),s=await this._chatModel.extensionClient.getSuggestions(i,r);t===this._refreshSeqNum&&this._allMentionables.set(hm(i,s))},Rr.REFRESH_THROTTLE_MS,{leading:!0,trailing:!0}));this._chatModel=t,this._insertMentionNode=r,this._disposers.push(this._userQuery.subscribe(this._refreshMentionables)),this._disposers.push(this._active.subscribe(this._refreshMentionables))}get allGroups(){return this._allGroups}get currentGroup(){return this._currentGroup}get breadcrumbIds(){return this._breadcrumbIds}get displayItems(){return this._displayItems}get active(){return this._active}get userQuery(){return this._userQuery}_resetState(){this._breadcrumbIds.set([]),this._userQuery.set("")}};_(Rr,"REFRESH_THROTTLE_MS",600),_(Rr,"SINGLE_GROUP_MAX_ITEMS",12),_(Rr,"MULTI_GROUP_MAX_ITEMS",6);let Gu=Rr;const hm=(o,t)=>{if(o.length<=1)return t;const r=new Fi(t,{keys:["label"],threshold:1,minMatchCharLength:0,ignoreLocation:!0,includeScore:!0,useExtendedSearch:!1,shouldSort:!0,findAllMatches:!0}).search(o);return r.length===0?t:r.map(i=>i.item)};function ta(o){switch(o){case Mt.DEFAULT:return nh;case Mt.PROTOTYPER:return Vp;case Mt.BRAINSTORM:return Hp;case Mt.REVIEWER:return Wp;default:return nh}}function fm(o){let t,r,i,s=o[0].label+"";return{c(){t=We("span"),r=We("span"),i=xt(s),De(r,"class","c-mentionable-group-label__text right"),De(t,"class","c-mentionable-group-label")},m(u,c){re(u,t,c),He(t,r),He(r,i)},p(u,c){1&c&&s!==(s=u[0].label+"")&&cr(i,s)},i:Ve,o:Ve,d(u){u&&ie(t)}}}function dm(o){let t,r;return t=new Bp({props:{$$slots:{text:[wm],leftIcon:[$m]},$$scope:{ctx:o}}}),{c(){G(t.$$.fragment)},m(i,s){Y(t,i,s),r=!0},p(i,s){const u={};17&s&&(u.$$scope={dirty:s,ctx:i}),t.$set(u)},i(i){r||(O(t.$$.fragment,i),r=!0)},o(i){q(t.$$.fragment,i),r=!1},d(i){K(t,i)}}}function pm(o){let t,r=o[0].label+"";return{c(){t=xt(r)},m(i,s){re(i,t,s)},p(i,s){1&s&&r!==(r=i[0].label+"")&&cr(t,r)},i:Ve,o:Ve,d(i){i&&ie(t)}}}function gm(o){let t,r;return t=new nn({props:{filepath:o[0].recentFile.pathName,$$slots:{leftIcon:[xm]},$$scope:{ctx:o}}}),{c(){G(t.$$.fragment)},m(i,s){Y(t,i,s),r=!0},p(i,s){const u={};1&s&&(u.filepath=i[0].recentFile.pathName),16&s&&(u.$$scope={dirty:s,ctx:i}),t.$set(u)},i(i){r||(O(t.$$.fragment,i),r=!0)},o(i){q(t.$$.fragment,i),r=!1},d(i){K(t,i)}}}function mm(o){let t,r;return t=new nn({props:{filepath:o[0].selection.pathName,$$slots:{leftIcon:[Cm]},$$scope:{ctx:o}}}),{c(){G(t.$$.fragment)},m(i,s){Y(t,i,s),r=!0},p(i,s){const u={};1&s&&(u.filepath=i[0].selection.pathName),16&s&&(u.$$scope={dirty:s,ctx:i}),t.$set(u)},i(i){r||(O(t.$$.fragment,i),r=!0)},o(i){q(t.$$.fragment,i),r=!1},d(i){K(t,i)}}}function _m(o){let t,r;return t=new nn({props:{filepath:o[0].sourceFolder.folderRoot,$$slots:{leftIcon:[Sm]},$$scope:{ctx:o}}}),{c(){G(t.$$.fragment)},m(i,s){Y(t,i,s),r=!0},p(i,s){const u={};1&s&&(u.filepath=i[0].sourceFolder.folderRoot),16&s&&(u.$$scope={dirty:s,ctx:i}),t.$set(u)},i(i){r||(O(t.$$.fragment,i),r=!0)},o(i){q(t.$$.fragment,i),r=!1},d(i){K(t,i)}}}function vm(o){let t,r;return t=new nn({props:{filepath:o[0].externalSource.name,$$slots:{leftIcon:[Im]},$$scope:{ctx:o}}}),{c(){G(t.$$.fragment)},m(i,s){Y(t,i,s),r=!0},p(i,s){const u={};1&s&&(u.filepath=i[0].externalSource.name),16&s&&(u.$$scope={dirty:s,ctx:i}),t.$set(u)},i(i){r||(O(t.$$.fragment,i),r=!0)},o(i){q(t.$$.fragment,i),r=!1},d(i){K(t,i)}}}function ym(o){let t,r;return t=new nn({props:{filepath:o[0].folder.pathName,$$slots:{leftIcon:[Em]},$$scope:{ctx:o}}}),{c(){G(t.$$.fragment)},m(i,s){Y(t,i,s),r=!0},p(i,s){const u={};1&s&&(u.filepath=i[0].folder.pathName),16&s&&(u.$$scope={dirty:s,ctx:i}),t.$set(u)},i(i){r||(O(t.$$.fragment,i),r=!0)},o(i){q(t.$$.fragment,i),r=!1},d(i){K(t,i)}}}function bm(o){let t,r;return t=new nn({props:{filepath:o[0].file.pathName,$$slots:{leftIcon:[Mm]},$$scope:{ctx:o}}}),{c(){G(t.$$.fragment)},m(i,s){Y(t,i,s),r=!0},p(i,s){const u={};1&s&&(u.filepath=i[0].file.pathName),16&s&&(u.$$scope={dirty:s,ctx:i}),t.$set(u)},i(i){r||(O(t.$$.fragment,i),r=!0)},o(i){q(t.$$.fragment,i),r=!1},d(i){K(t,i)}}}function $m(o){let t,r,i;var s=ta(o[0].personality.type);return s&&(r=zi(s,{})),{c(){t=We("span"),r&&G(r.$$.fragment),De(t,"slot","leftIcon"),De(t,"class","c-context-menu-item__icon svelte-1b93fdw")},m(u,c){re(u,t,c),r&&Y(r,t,null),i=!0},p(u,c){if(1&c&&s!==(s=ta(u[0].personality.type))){if(r){$t();const h=r;q(h.$$.fragment,1,0,()=>{K(h,1)}),wt()}s?(r=zi(s,{}),G(r.$$.fragment),O(r.$$.fragment,1),Y(r,t,null)):r=null}},i(u){i||(r&&O(r.$$.fragment,u),i=!0)},o(u){r&&q(r.$$.fragment,u),i=!1},d(u){u&&ie(t),r&&K(r)}}}function wm(o){let t,r,i=o[0].label+"";return{c(){t=We("span"),r=xt(i),De(t,"slot","text")},m(s,u){re(s,t,u),He(t,r)},p(s,u){1&u&&i!==(i=s[0].label+"")&&cr(r,i)},d(s){s&&ie(t)}}}function xm(o){let t,r;return t=new Pi({props:{slot:"leftIcon",iconName:"description"}}),{c(){G(t.$$.fragment)},m(i,s){Y(t,i,s),r=!0},p:Ve,i(i){r||(O(t.$$.fragment,i),r=!0)},o(i){q(t.$$.fragment,i),r=!1},d(i){K(t,i)}}}function Cm(o){let t,r;return t=new Pi({props:{slot:"leftIcon",iconName:"text_select_start"}}),{c(){G(t.$$.fragment)},m(i,s){Y(t,i,s),r=!0},p:Ve,i(i){r||(O(t.$$.fragment,i),r=!0)},o(i){q(t.$$.fragment,i),r=!1},d(i){K(t,i)}}}function Sm(o){let t,r;return t=new Pi({props:{slot:"leftIcon",iconName:"folder_managed"}}),{c(){G(t.$$.fragment)},m(i,s){Y(t,i,s),r=!0},p:Ve,i(i){r||(O(t.$$.fragment,i),r=!0)},o(i){q(t.$$.fragment,i),r=!1},d(i){K(t,i)}}}function Im(o){let t,r;return t=new Pi({props:{slot:"leftIcon",iconName:"import_contacts"}}),{c(){G(t.$$.fragment)},m(i,s){Y(t,i,s),r=!0},p:Ve,i(i){r||(O(t.$$.fragment,i),r=!0)},o(i){q(t.$$.fragment,i),r=!1},d(i){K(t,i)}}}function Em(o){let t,r;return t=new Pi({props:{slot:"leftIcon",iconName:"folder_open"}}),{c(){G(t.$$.fragment)},m(i,s){Y(t,i,s),r=!0},p:Ve,i(i){r||(O(t.$$.fragment,i),r=!0)},o(i){q(t.$$.fragment,i),r=!1},d(i){K(t,i)}}}function Mm(o){let t,r;return t=new Pi({props:{slot:"leftIcon",iconName:"description"}}),{c(){G(t.$$.fragment)},m(i,s){Y(t,i,s),r=!0},p:Ve,i(i){r||(O(t.$$.fragment,i),r=!0)},o(i){q(t.$$.fragment,i),r=!1},d(i){K(t,i)}}}function km(o){let t,r,i,s,u,c,h,f,v,m,b,x,E;const F=[bm,ym,vm,_m,mm,gm,pm,dm,fm],z=[];function R(S,T){return 1&T&&(t=null),1&T&&(r=null),1&T&&(i=null),1&T&&(s=null),1&T&&(u=null),1&T&&(c=null),1&T&&(h=null),1&T&&(f=null),1&T&&(v=null),t==null&&(t=!!Qu(S[0])),t?0:(r==null&&(r=!!Ju(S[0])),r?1:(i==null&&(i=!!Xu(S[0])),i?2:(s==null&&(s=!!Xo(S[0])),s?3:(u==null&&(u=!!Jo(S[0])),u?4:(c==null&&(c=!!Qo(S[0])),c?5:(h==null&&(h=!!ks(S[0])),h?6:(f==null&&(f=!!na(S[0])),f?7:(v==null&&(v=!!(zh(S[0])||Np(S[0])||Zo(S[0]))),v?8:-1))))))))}return~(m=R(o,-1))&&(b=z[m]=F[m](o)),{c(){b&&b.c(),x=hr()},m(S,T){~m&&z[m].m(S,T),re(S,x,T),E=!0},p(S,T){let ne=m;m=R(S,T),m===ne?~m&&z[m].p(S,T):(b&&($t(),q(z[ne],1,1,()=>{z[ne]=null}),wt()),~m?(b=z[m],b?b.p(S,T):(b=z[m]=F[m](S),b.c()),O(b,1),b.m(x.parentNode,x)):b=null)},i(S){E||(O(b),E=!0)},o(S){q(b),E=!1},d(S){S&&ie(x),~m&&z[m].d(S)}}}function Am(o){let t,r,i;var s=o[3];function u(c,h){return{props:{highlight:c[2],onSelect:c[1],$$slots:{default:[km]},$$scope:{ctx:c}}}}return s&&(t=zi(s,u(o))),{c(){t&&G(t.$$.fragment),r=hr()},m(c,h){t&&Y(t,c,h),re(c,r,h),i=!0},p(c,[h]){if(8&h&&s!==(s=c[3])){if(t){$t();const f=t;q(f.$$.fragment,1,0,()=>{K(f,1)}),wt()}s?(t=zi(s,u(c)),G(t.$$.fragment),O(t.$$.fragment,1),Y(t,r.parentNode,r)):t=null}else if(s){const f={};4&h&&(f.highlight=c[2]),2&h&&(f.onSelect=c[1]),17&h&&(f.$$scope={dirty:h,ctx:c}),t.$set(f)}},i(c){i||(t&&O(t.$$.fragment,c),i=!0)},o(c){t&&q(t.$$.fragment,c),i=!1},d(c){c&&ie(r),t&&K(t,c)}}}function Fm(o,t,r){let i,{item:s}=t,{onSelect:u}=t,{highlight:c}=t;return o.$$set=h=>{"item"in h&&r(0,s=h.item),"onSelect"in h&&r(1,u=h.onSelect),"highlight"in h&&r(2,c=h.highlight)},o.$$.update=()=>{1&o.$$.dirty&&(s.type==="breadcrumb-back"?r(3,i=wu.BreadcrumbBackItem):s.type==="breadcrumb"&&ks(s)?r(3,i=wu.BreadcrumbItem):s.type!=="item"||ks(s)||r(3,i=wu.Item))},[s,u,c,i]}class Tm extends oi{constructor(t){super(),ai(this,t,Fm,Am,ui,{item:0,onSelect:1,highlight:2})}}function Om(o){let t,r=o[0].label+"";return{c(){t=xt(r)},m(i,s){re(i,t,s)},p(i,s){1&s&&r!==(r=i[0].label+"")&&cr(t,r)},i:Ve,o:Ve,d(i){i&&ie(t)}}}function Rm(o){let t,r,i,s,u,c,h,f;return r=new Gp({props:{heightPx:32,floatHeight:4,animationDuration:2.25,$$slots:{default:[Um]},$$scope:{ctx:o}}}),u=new eh({props:{size:2,weight:"medium",$$slots:{default:[Wm]},$$scope:{ctx:o}}}),h=new eh({props:{size:1,$$slots:{default:[Hm]},$$scope:{ctx:o}}}),{c(){t=We("div"),G(r.$$.fragment),i=qe(),s=We("div"),G(u.$$.fragment),c=qe(),G(h.$$.fragment),De(t,"class","c-mention-hover-contents__personality-icon svelte-11069rs"),De(s,"class","c-mention-hover-contents__personality svelte-11069rs")},m(v,m){re(v,t,m),Y(r,t,null),re(v,i,m),re(v,s,m),Y(u,s,null),He(s,c),Y(h,s,null),f=!0},p(v,m){const b={};3&m&&(b.$$scope={dirty:m,ctx:v}),r.$set(b);const x={};3&m&&(x.$$scope={dirty:m,ctx:v}),u.$set(x);const E={};3&m&&(E.$$scope={dirty:m,ctx:v}),h.$set(E)},i(v){f||(O(r.$$.fragment,v),O(u.$$.fragment,v),O(h.$$.fragment,v),f=!0)},o(v){q(r.$$.fragment,v),q(u.$$.fragment,v),q(h.$$.fragment,v),f=!1},d(v){v&&(ie(t),ie(i),ie(s)),K(r),K(u),K(h)}}}function Lm(o){var u,c;let t,r,i,s;return t=new Yp({}),i=new nn({props:{filepath:`${o[0].selection.pathName}:L${(u=o[0].selection.fullRange)==null?void 0:u.startLineNumber}-${(c=o[0].selection.fullRange)==null?void 0:c.endLineNumber}`}}),{c(){G(t.$$.fragment),r=qe(),G(i.$$.fragment)},m(h,f){Y(t,h,f),re(h,r,f),Y(i,h,f),s=!0},p(h,f){var m,b;const v={};1&f&&(v.filepath=`${h[0].selection.pathName}:L${(m=h[0].selection.fullRange)==null?void 0:m.startLineNumber}-${(b=h[0].selection.fullRange)==null?void 0:b.endLineNumber}`),i.$set(v)},i(h){s||(O(t.$$.fragment,h),O(i.$$.fragment,h),s=!0)},o(h){q(t.$$.fragment,h),q(i.$$.fragment,h),s=!1},d(h){h&&ie(r),K(t,h),K(i,h)}}}function Nm(o){let t,r,i=o[0].userGuidelines.overLimit&&xh(o);return{c(){i&&i.c(),t=hr()},m(s,u){i&&i.m(s,u),re(s,t,u),r=!0},p(s,u){s[0].userGuidelines.overLimit?i?(i.p(s,u),1&u&&O(i,1)):(i=xh(s),i.c(),O(i,1),i.m(t.parentNode,t)):i&&($t(),q(i,1,1,()=>{i=null}),wt())},i(s){r||(O(i),r=!0)},o(s){q(i),r=!1},d(s){s&&ie(t),i&&i.d(s)}}}function jm(o){let t,r,i,s,u,c,h,f;return i=new Kp({}),u=new nn({props:{class:"c-source-folder-item",filepath:o[0].sourceFolder.folderRoot}}),h=new Qp({props:{class:"guidelines-filespan",sourceFolder:o[0].sourceFolder}}),{c(){t=We("div"),r=We("div"),G(i.$$.fragment),s=qe(),G(u.$$.fragment),c=qe(),G(h.$$.fragment),De(r,"class","l-source-folder-name svelte-11069rs"),De(t,"class","l-mention-hover-contents__source-folder")},m(v,m){re(v,t,m),He(t,r),Y(i,r,null),He(r,s),Y(u,r,null),He(t,c),Y(h,t,null),f=!0},p(v,m){const b={};1&m&&(b.filepath=v[0].sourceFolder.folderRoot),u.$set(b);const x={};1&m&&(x.sourceFolder=v[0].sourceFolder),h.$set(x)},i(v){f||(O(i.$$.fragment,v),O(u.$$.fragment,v),O(h.$$.fragment,v),f=!0)},o(v){q(i.$$.fragment,v),q(u.$$.fragment,v),q(h.$$.fragment,v),f=!1},d(v){v&&ie(t),K(i),K(u),K(h)}}}function Dm(o){let t,r,i,s;return t=new Jp({}),i=new nn({props:{filepath:o[0].externalSource.name}}),{c(){G(t.$$.fragment),r=qe(),G(i.$$.fragment)},m(u,c){Y(t,u,c),re(u,r,c),Y(i,u,c),s=!0},p(u,c){const h={};1&c&&(h.filepath=u[0].externalSource.name),i.$set(h)},i(u){s||(O(t.$$.fragment,u),O(i.$$.fragment,u),s=!0)},o(u){q(t.$$.fragment,u),q(i.$$.fragment,u),s=!1},d(u){u&&ie(r),K(t,u),K(i,u)}}}function qm(o){let t,r,i,s;return t=new Dp({}),i=new nn({props:{filepath:o[0].folder.pathName}}),{c(){G(t.$$.fragment),r=qe(),G(i.$$.fragment)},m(u,c){Y(t,u,c),re(u,r,c),Y(i,u,c),s=!0},p(u,c){const h={};1&c&&(h.filepath=u[0].folder.pathName),i.$set(h)},i(u){s||(O(t.$$.fragment,u),O(i.$$.fragment,u),s=!0)},o(u){q(t.$$.fragment,u),q(i.$$.fragment,u),s=!1},d(u){u&&ie(r),K(t,u),K(i,u)}}}function zm(o){let t,r,i,s;return t=new Uh({}),i=new nn({props:{filepath:o[0].recentFile.pathName}}),{c(){G(t.$$.fragment),r=qe(),G(i.$$.fragment)},m(u,c){Y(t,u,c),re(u,r,c),Y(i,u,c),s=!0},p(u,c){const h={};1&c&&(h.filepath=u[0].recentFile.pathName),i.$set(h)},i(u){s||(O(t.$$.fragment,u),O(i.$$.fragment,u),s=!0)},o(u){q(t.$$.fragment,u),q(i.$$.fragment,u),s=!1},d(u){u&&ie(r),K(t,u),K(i,u)}}}function Pm(o){let t,r,i,s;return t=new Uh({}),i=new nn({props:{filepath:o[0].file.pathName}}),{c(){G(t.$$.fragment),r=qe(),G(i.$$.fragment)},m(u,c){Y(t,u,c),re(u,r,c),Y(i,u,c),s=!0},p(u,c){const h={};1&c&&(h.filepath=u[0].file.pathName),i.$set(h)},i(u){s||(O(t.$$.fragment,u),O(i.$$.fragment,u),s=!0)},o(u){q(t.$$.fragment,u),q(i.$$.fragment,u),s=!1},d(u){u&&ie(r),K(t,u),K(i,u)}}}function Um(o){let t,r,i;var s=ta(o[0].personality.type);return s&&(t=zi(s,{})),{c(){t&&G(t.$$.fragment),r=hr()},m(u,c){t&&Y(t,u,c),re(u,r,c),i=!0},p(u,c){if(1&c&&s!==(s=ta(u[0].personality.type))){if(t){$t();const h=t;q(h.$$.fragment,1,0,()=>{K(h,1)}),wt()}s?(t=zi(s,{}),G(t.$$.fragment),O(t.$$.fragment,1),Y(t,r.parentNode,r)):t=null}},i(u){i||(t&&O(t.$$.fragment,u),i=!0)},o(u){t&&q(t.$$.fragment,u),i=!1},d(u){u&&ie(r),t&&K(t,u)}}}function Wm(o){let t,r=o[0].label+"";return{c(){t=xt(r)},m(i,s){re(i,t,s)},p(i,s){1&s&&r!==(r=i[0].label+"")&&cr(t,r)},d(i){i&&ie(t)}}}function Hm(o){let t,r=o[0].personality.description+"";return{c(){t=xt(r)},m(i,s){re(i,t,s)},p(i,s){1&s&&r!==(r=i[0].personality.description+"")&&cr(t,r)},d(i){i&&ie(t)}}}function xh(o){let t,r,i,s;return t=new ng({props:{class:"c-mention-hover-contents__guidelines-warning-icon"}}),i=new nn({props:{filepath:`Guidelines exceeded length limit of ${o[0].userGuidelines.lengthLimit} characters`}}),{c(){G(t.$$.fragment),r=qe(),G(i.$$.fragment)},m(u,c){Y(t,u,c),re(u,r,c),Y(i,u,c),s=!0},p(u,c){const h={};1&c&&(h.filepath=`Guidelines exceeded length limit of ${u[0].userGuidelines.lengthLimit} characters`),i.$set(h)},i(u){s||(O(t.$$.fragment,u),O(i.$$.fragment,u),s=!0)},o(u){q(t.$$.fragment,u),q(i.$$.fragment,u),s=!1},d(u){u&&ie(r),K(t,u),K(i,u)}}}function Vm(o){let t,r,i,s,u,c,h,f,v,m,b,x;const E=[Pm,zm,qm,Dm,jm,Nm,Lm,Rm,Om],F=[];function z(R,S){return 1&S&&(r=null),1&S&&(i=null),1&S&&(s=null),1&S&&(u=null),1&S&&(c=null),1&S&&(h=null),1&S&&(f=null),1&S&&(v=null),r==null&&(r=!(!R[0]||!Qu(R[0]))),r?0:(i==null&&(i=!(!R[0]||!Qo(R[0]))),i?1:(s==null&&(s=!(!R[0]||!Ju(R[0]))),s?2:(u==null&&(u=!(!R[0]||!Xu(R[0]))),u?3:(c==null&&(c=!(!R[0]||!Xo(R[0]))),c?4:(h==null&&(h=!!(R[0]&&Zo(R[0])&&R[0].userGuidelines.enabled)),h?5:(f==null&&(f=!(!R[0]||!Jo(R[0]))),f?6:(v==null&&(v=!(!R[0]||!na(R[0]))),v?7:8)))))))}return m=z(o,-1),b=F[m]=E[m](o),{c(){t=We("div"),b.c(),De(t,"class","c-mention-hover-contents svelte-11069rs")},m(R,S){re(R,t,S),F[m].m(t,null),x=!0},p(R,[S]){let T=m;m=z(R,S),m===T?F[m].p(R,S):($t(),q(F[T],1,1,()=>{F[T]=null}),wt(),b=F[m],b?b.p(R,S):(b=F[m]=E[m](R),b.c()),O(b,1),b.m(t,null))},i(R){x||(O(b),x=!0)},o(R){q(b),x=!1},d(R){R&&ie(t),F[m].d()}}}function Bm(o,t,r){let{option:i}=t;return o.$$set=s=>{"option"in s&&r(0,i=s.option)},[i]}class Gm extends oi{constructor(t){super(),ai(this,t,Bm,Vm,ui,{option:0})}}function Ch(o,t,r){const i=o.slice();return i[15]=t[r],i}function Sh(o){let t,r;function i(){return o[8](o[15])}return t=new Tm({props:{item:o[15],highlight:o[15]===o[14],onSelect:i}}),{c(){G(t.$$.fragment)},m(s,u){Y(t,s,u),r=!0},p(s,u){o=s;const c={};4&u&&(c.item=o[15]),16388&u&&(c.highlight=o[15]===o[14]),4&u&&(c.onSelect=i),t.$set(c)},i(s){r||(O(t.$$.fragment,s),r=!0)},o(s){q(t.$$.fragment,s),r=!1},d(s){K(t,s)}}}function Ym(o){let t,r,i=Ko(o[2]),s=[];for(let c=0;c<i.length;c+=1)s[c]=Sh(Ch(o,i,c));const u=c=>q(s[c],1,1,()=>{s[c]=null});return{c(){for(let c=0;c<s.length;c+=1)s[c].c();t=hr()},m(c,h){for(let f=0;f<s.length;f+=1)s[f]&&s[f].m(c,h);re(c,t,h),r=!0},p(c,h){if(16420&h){let f;for(i=Ko(c[2]),f=0;f<i.length;f+=1){const v=Ch(c,i,f);s[f]?(s[f].p(v,h),O(s[f],1)):(s[f]=Sh(v),s[f].c(),O(s[f],1),s[f].m(t.parentNode,t))}for($t(),f=i.length;f<s.length;f+=1)u(f);wt()}},i(c){if(!r){for(let h=0;h<i.length;h+=1)O(s[h]);r=!0}},o(c){s=s.filter(Boolean);for(let h=0;h<s.length;h+=1)q(s[h]);r=!1},d(c){c&&ie(t),Dh(s,c)}}}function Km(o){let t,r;return t=new Gm({props:{slot:"mentionable",option:o[13]}}),{c(){G(t.$$.fragment)},m(i,s){Y(t,i,s),r=!0},p(i,s){const u={};8192&s&&(u.option=i[13]),t.$set(u)},i(i){r||(O(t.$$.fragment,i),r=!0)},o(i){q(t.$$.fragment,i),r=!1},d(i){K(t,i)}}}function Qm(o){let t,r,i,s;return t=new Au.Menu.Root({props:{mentionables:o[2],onQueryUpdate:o[4],onSelectMentionable:o[5],$$slots:{default:[Ym,({activeItem:u})=>({14:u}),({activeItem:u})=>u?16384:0]},$$scope:{ctx:o}}}),i=new Au.ChipTooltip({props:{$$slots:{mentionable:[Km,({mentionable:u})=>({13:u}),({mentionable:u})=>u?8192:0]},$$scope:{ctx:o}}}),{c(){G(t.$$.fragment),r=qe(),G(i.$$.fragment)},m(u,c){Y(t,u,c),re(u,r,c),Y(i,u,c),s=!0},p(u,c){const h={};4&c&&(h.mentionables=u[2]),278532&c&&(h.$$scope={dirty:c,ctx:u}),t.$set(h);const f={};270336&c&&(f.$$scope={dirty:c,ctx:u}),i.$set(f)},i(u){s||(O(t.$$.fragment,u),O(i.$$.fragment,u),s=!0)},o(u){q(t.$$.fragment,u),q(i.$$.fragment,u),s=!1},d(u){u&&ie(r),K(t,u),K(i,u)}}}function Jm(o){let t,r,i={triggerCharacter:"@",onMentionItemsUpdated:o[0],$$slots:{default:[Qm]},$$scope:{ctx:o}};return t=new Au.Root({props:i}),o[9](t),{c(){G(t.$$.fragment)},m(s,u){Y(t,s,u),r=!0},p(s,[u]){const c={};1&u&&(c.onMentionItemsUpdated=s[0]),262148&u&&(c.$$scope={dirty:u,ctx:s}),t.$set(c)},i(s){r||(O(t.$$.fragment,s),r=!0)},o(s){q(t.$$.fragment,s),r=!1},d(s){o[9](null),K(t,s)}}}function Xm(o,t,r){let i,{requestEditorFocus:s}=t,{onMentionItemsUpdated:u}=t;const c=vp("chatModel");if(!c)throw new Error("ChatModel not found in context");const h=new Gu(c,m),f=h.displayItems;let v;function m(x){return!!v&&(v.insertMention(x),h.closeDropdown(),!0)}function b(x){const E=h.selectMentionable(x);return s(),E}return si(o,f,x=>r(2,i=x)),Ku(()=>{h.dispose()}),o.$$set=x=>{"requestEditorFocus"in x&&r(6,s=x.requestEditorFocus),"onMentionItemsUpdated"in x&&r(0,u=x.onMentionItemsUpdated)},[u,v,i,f,function(x){x===void 0?h.closeDropdown():(h.openDropdown(),h.userQuery.set(x))},b,s,x=>m(x),x=>b(x),function(x){Oi[x?"unshift":"push"](()=>{v=x,r(1,v)})}]}class Zm extends oi{constructor(t){super(),ai(this,t,Xm,Jm,ui,{requestEditorFocus:6,onMentionItemsUpdated:0,insertMentionNode:7})}get insertMentionNode(){return this.$$.ctx[7]}}function Ih(o){let t,r,i,s,u,c,h,f,v,m,b,x,E,F,z={focusOnInit:!0,$$slots:{default:[e_]},$$scope:{ctx:o}};return c=new Hh.Root({props:z}),o[25](c),v=new jr({props:{id:"close",size:1,variant:"soft",color:"neutral",title:"Close",$$slots:{default:[t_]},$$scope:{ctx:o}}}),v.$on("click",function(){ur(o[0].disposeDiffViewPanel)&&o[0].disposeDiffViewPanel.apply(this,arguments)}),b=new jr({props:{id:"send",size:1,variant:"solid",color:"accent",title:o[3]===Dr.instruction?"Instruct Augment":"Edit with Augment",disabled:!o[4].trim()||o[11],$$slots:{iconRight:[r_],default:[n_]},$$scope:{ctx:o}}}),b.$on("click",o[14]),{c(){t=We("div"),r=qe(),i=We("div"),s=We("div"),u=We("div"),G(c.$$.fragment),h=qe(),f=We("div"),G(v.$$.fragment),m=qe(),G(b.$$.fragment),De(u,"class","l-input-area__input svelte-1cxscce"),De(f,"class","c-instruction-drawer-panel__btn-container svelte-1cxscce"),De(s,"class","instruction-drawer-panel__contents svelte-1cxscce"),De(s,"tabindex","0"),De(s,"role","button"),De(i,"class","instruction-drawer-panel svelte-1cxscce"),Ti(i,"top",o[5]+"px"),Ti(i,"height",o[6]+"px")},m(R,S){re(R,t,S),re(R,r,S),re(R,i,S),He(i,s),He(s,u),Y(c,u,null),o[26](u),He(s,h),He(s,f),Y(v,f,null),He(f,m),Y(b,f,null),x=!0,E||(F=[Nh(o[15].call(null,t)),Nr(s,"click",o[17]),Nr(s,"keydown",o[27])],E=!0)},p(R,S){o=R;const T={};1296&S[0]|256&S[1]&&(T.$$scope={dirty:S,ctx:o}),c.$set(T);const ne={};256&S[1]&&(ne.$$scope={dirty:S,ctx:o}),v.$set(ne);const de={};8&S[0]&&(de.title=o[3]===Dr.instruction?"Instruct Augment":"Edit with Augment"),2064&S[0]&&(de.disabled=!o[4].trim()||o[11]),8&S[0]|256&S[1]&&(de.$$scope={dirty:S,ctx:o}),b.$set(de),(!x||32&S[0])&&Ti(i,"top",o[5]+"px"),(!x||64&S[0])&&Ti(i,"height",o[6]+"px")},i(R){x||(O(c.$$.fragment,R),O(v.$$.fragment,R),O(b.$$.fragment,R),x=!0)},o(R){q(c.$$.fragment,R),q(v.$$.fragment,R),q(b.$$.fragment,R),x=!1},d(R){R&&(ie(t),ie(r),ie(i)),o[25](null),K(c),o[26](null),K(v),K(b),E=!1,Yu(F)}}}function e_(o){let t,r,i,s,u,c,h,f;t=new Zp({props:{shortcuts:{Enter:o[23]}}});let v={requestEditorFocus:o[16],onMentionItemsUpdated:o[18]};return i=new Zm({props:v}),o[24](i),u=new Hh.Content({props:{content:o[4],onContentChanged:o[19]}}),h=new eg({props:{placeholder:o[10]}}),{c(){G(t.$$.fragment),r=qe(),G(i.$$.fragment),s=qe(),G(u.$$.fragment),c=qe(),G(h.$$.fragment)},m(m,b){Y(t,m,b),re(m,r,b),Y(i,m,b),re(m,s,b),Y(u,m,b),re(m,c,b),Y(h,m,b),f=!0},p(m,b){i.$set({});const x={};16&b[0]&&(x.content=m[4]),u.$set(x);const E={};1024&b[0]&&(E.placeholder=m[10]),h.$set(E)},i(m){f||(O(t.$$.fragment,m),O(i.$$.fragment,m),O(u.$$.fragment,m),O(h.$$.fragment,m),f=!0)},o(m){q(t.$$.fragment,m),q(i.$$.fragment,m),q(u.$$.fragment,m),q(h.$$.fragment,m),f=!1},d(m){m&&(ie(r),ie(s),ie(c)),K(t,m),o[24](null),K(i,m),K(u,m),K(h,m)}}}function t_(o){let t,r,i;return r=new ci({props:{keybinding:"esc"}}),{c(){t=xt(`Close
          `),G(r.$$.fragment)},m(s,u){re(s,t,u),Y(r,s,u),i=!0},p:Ve,i(s){i||(O(r.$$.fragment,s),i=!0)},o(s){q(r.$$.fragment,s),i=!1},d(s){s&&ie(t),K(r,s)}}}function n_(o){let t,r=o[3]===Dr.instruction?"Instruct":"Edit";return{c(){t=xt(r)},m(i,s){re(i,t,s)},p(i,s){8&s[0]&&r!==(r=i[3]===Dr.instruction?"Instruct":"Edit")&&cr(t,r)},d(i){i&&ie(t)}}}function r_(o){let t,r;return t=new Xp({props:{slot:"iconRight"}}),{c(){G(t.$$.fragment)},m(i,s){Y(t,i,s),r=!0},p:Ve,i(i){r||(O(t.$$.fragment,i),r=!0)},o(i){q(t.$$.fragment,i),r=!1},d(i){K(t,i)}}}function i_(o){let t,r,i=o[2]&&Ih(o);return{c(){i&&i.c(),t=hr()},m(s,u){i&&i.m(s,u),re(s,t,u),r=!0},p(s,u){s[2]?i?(i.p(s,u),4&u[0]&&O(i,1)):(i=Ih(s),i.c(),O(i,1),i.m(t.parentNode,t)):i&&($t(),q(i,1,1,()=>{i=null}),wt())},i(s){r||(O(i),r=!0)},o(s){q(i),r=!1},d(s){s&&ie(t),i&&i.d(s)}}}function s_(o,t,r){let i,s,u,c,h,f,v=Ve,m=()=>(v(),v=ar(x,fe=>r(22,c=fe)),x),b=Ve;o.$$.on_destroy.push(()=>v()),o.$$.on_destroy.push(()=>b());let{diffViewModel:x}=t;m();let{initialConversation:E}=t,{initialFlags:F}=t;const z=rg.getContext().monaco,R={isWholeLine:!0,marginClassName:"instruction-edit-area-margin"},S=new Wh(Is);let T=new Xh;S.registerConsumer(T);let ne=new Yh(S,Is,T,{initialConversation:E,initialFlags:F});const de=ne.currentConversationModel;let oe,Ne;S.registerConsumer(ne),function(fe){yp("chatModel",fe)}(ne);let pe,Ie="";const me=x.mode;si(o,me,fe=>r(3,h=fe));const Te=x.selectionLines;function Ue(){const fe=x.getModifiedEditor(),mn=pt(z);if(!fe||!mn||(pe==null||pe.clear(),!u))return;const Fn=u.start,Vn=u.end,fr={range:new mn.Range(Fn+1,1,Vn+1,1),options:R};pe||(pe=fe.createDecorationsCollection()),pe.set([fr])}function st(){return!!(Ie!=null&&Ie.trim())&&(x.handleInstructionSubmit(Ie),!0)}si(o,Te,fe=>r(2,u=fe)),qh(async()=>{await Mu(),Ct(),r(5,at=x.editorOffset)}),Ku(()=>{oe==null||oe.destroy(),pe==null||pe.clear()});let Se,we,at=0,gn=57;const Ct=()=>Se==null?void 0:Se.forceFocus();return o.$$set=fe=>{"diffViewModel"in fe&&m(r(0,x=fe.diffViewModel)),"initialConversation"in fe&&r(20,E=fe.initialConversation),"initialFlags"in fe&&r(21,F=fe.initialFlags)},o.$$.update=()=>{if(8&o.$$.dirty[0]&&r(10,i=(h===Dr.instruction?"Instruct":"Edit with")+" Augment... @ to focus on files or docs"),4194304&o.$$.dirty[0]&&(r(9,s=c.isLoading),b(),b=ar(s,fe=>r(11,f=fe))),6&o.$$.dirty[0]&&Ne){if(u==null)r(6,gn=0);else{const fe=Ne.scrollHeight;r(6,gn=Math.min(40+fe,108))}oe==null||oe.update({heightInPx:gn}),Ue()}},[x,Ne,u,h,Ie,at,gn,Se,we,s,i,f,me,Te,st,function(fe){if(fe){const mn=u?u.start:1;oe=x.renderInstructionsDrawerViewZone(fe,{line:mn,heightInPx:gn,onDomNodeTop:Fn=>{r(5,at=x.editorOffset+Fn)},autoFocus:!0}),Ue()}},()=>Se==null?void 0:Se.requestFocus(),Ct,fe=>{de.saveDraftMentions(fe.current)},function(fe){r(4,Ie=fe.rawText)},E,F,c,()=>st(),function(fe){Oi[fe?"unshift":"push"](()=>{we=fe,r(8,we)})},function(fe){Oi[fe?"unshift":"push"](()=>{Se=fe,r(7,Se)})},function(fe){Oi[fe?"unshift":"push"](()=>{Ne=fe,r(1,Ne)})},fe=>{fe.key==="Enter"&&(Ct(),fe.stopPropagation(),fe.preventDefault())}]}class o_ extends oi{constructor(t){super(),ai(this,t,s_,i_,ui,{diffViewModel:0,initialConversation:20,initialFlags:21},null,[-1,-1])}}const{window:Eu}=wp;function Eh(o,t,r){const i=o.slice();return i[17]=t[r],i[19]=r,i}function Mh(o){let t,r,i,s,u;return r=new Gg({props:{diffViewModel:o[3]}}),s=new o_({props:{diffViewModel:o[3]}}),{c(){t=We("div"),G(r.$$.fragment),i=qe(),G(s.$$.fragment),De(t,"class","sticky-top svelte-dpt00g")},m(c,h){re(c,t,h),Y(r,t,null),re(c,i,h),Y(s,c,h),u=!0},p(c,h){const f={};8&h&&(f.diffViewModel=c[3]),r.$set(f);const v={};8&h&&(v.diffViewModel=c[3]),s.$set(v)},i(c){u||(O(r.$$.fragment,c),O(s.$$.fragment,c),u=!0)},o(c){q(r.$$.fragment,c),q(s.$$.fragment,c),u=!1},d(c){c&&(ie(t),ie(i)),K(r),K(s,c)}}}function kh(o){let t,r,i=Ko(o[4]),s=[];for(let c=0;c<i.length;c+=1)s[c]=Fh(Eh(o,i,c));const u=c=>q(s[c],1,1,()=>{s[c]=null});return{c(){for(let c=0;c<s.length;c+=1)s[c].c();t=hr()},m(c,h){for(let f=0;f<s.length;f+=1)s[f]&&s[f].m(c,h);re(c,t,h),r=!0},p(c,h){if(280&h){let f;for(i=Ko(c[4]),f=0;f<i.length;f+=1){const v=Eh(c,i,f);s[f]?(s[f].p(v,h),O(s[f],1)):(s[f]=Fh(v),s[f].c(),O(s[f],1),s[f].m(t.parentNode,t))}for($t(),f=i.length;f<s.length;f+=1)u(f);wt()}},i(c){if(!r){for(let h=0;h<i.length;h+=1)O(s[h]);r=!0}},o(c){s=s.filter(Boolean);for(let h=0;h<s.length;h+=1)q(s[h]);r=!1},d(c){c&&ie(t),Dh(s,c)}}}function Ah(o){var u;let t,r;function i(){return o[14](o[17])}function s(){return o[15](o[17])}return t=new Lg({props:{isFocused:((u=o[3])==null?void 0:u.currFocusedChunkIdx)===o[19],onAccept:i,onReject:s,diffViewModel:o[3],leaf:o[17],align:"right",disableApply:o[8]}}),{c(){G(t.$$.fragment)},m(c,h){Y(t,c,h),r=!0},p(c,h){var v;o=c;const f={};8&h&&(f.isFocused=((v=o[3])==null?void 0:v.currFocusedChunkIdx)===o[19]),24&h&&(f.onAccept=i),24&h&&(f.onReject=s),8&h&&(f.diffViewModel=o[3]),16&h&&(f.leaf=o[17]),256&h&&(f.disableApply=o[8]),t.$set(f)},i(c){r||(O(t.$$.fragment,c),r=!0)},o(c){q(t.$$.fragment,c),r=!1},d(c){K(t,c)}}}function Fh(o){let t,r,i=o[17].unitOfCodeWork.modifiedCode!==o[17].unitOfCodeWork.originalCode&&Ah(o);return{c(){i&&i.c(),t=hr()},m(s,u){i&&i.m(s,u),re(s,t,u),r=!0},p(s,u){s[17].unitOfCodeWork.modifiedCode!==s[17].unitOfCodeWork.originalCode?i?(i.p(s,u),16&u&&O(i,1)):(i=Ah(s),i.c(),O(i,1),i.m(t.parentNode,t)):i&&($t(),q(i,1,1,()=>{i=null}),wt())},i(s){r||(O(i),r=!0)},o(s){q(i),r=!1},d(s){s&&ie(t),i&&i.d(s)}}}function a_(o){var v;let t,r,i,s,u,c,h=o[3]&&Mh(o),f=o[3]&&((v=o[4])==null?void 0:v.length)&&!o[7]&&kh(o);return{c(){t=We("div"),h&&h.c(),r=qe(),i=We("div"),s=We("div"),u=qe(),f&&f.c(),De(s,"class","editor svelte-dpt00g"),De(i,"class","editor-container svelte-dpt00g"),De(t,"class","diff-view-container svelte-dpt00g")},m(m,b){re(m,t,b),h&&h.m(t,null),He(t,r),He(t,i),He(i,s),o[13](s),He(i,u),f&&f.m(i,null),c=!0},p(m,b){var x;m[3]?h?(h.p(m,b),8&b&&O(h,1)):(h=Mh(m),h.c(),O(h,1),h.m(t,r)):h&&($t(),q(h,1,1,()=>{h=null}),wt()),m[3]&&((x=m[4])!=null&&x.length)&&!m[7]?f?(f.p(m,b),152&b&&O(f,1)):(f=kh(m),f.c(),O(f,1),f.m(i,null)):f&&($t(),q(f,1,1,()=>{f=null}),wt())},i(m){c||(O(h),O(f),c=!0)},o(m){q(h),q(f),c=!1},d(m){m&&ie(t),h&&h.d(),o[13](null),f&&f.d()}}}function u_(o){let t,r,i,s;return t=new ig.Root({props:{$$slots:{default:[a_]},$$scope:{ctx:o}}}),{c(){G(t.$$.fragment)},m(u,c){Y(t,u,c),r=!0,i||(s=[Nr(Eu,"message",function(){var h,f;ur((h=o[0])==null?void 0:h.handleMessageFromExtension)&&((f=o[0])==null||f.handleMessageFromExtension.apply(this,arguments))}),Nr(Eu,"focus",o[11]),Nr(Eu,"blur",o[12])],i=!0)},p(u,[c]){o=u;const h={};1048986&c&&(h.$$scope={dirty:c,ctx:o}),t.$set(h)},i(u){r||(O(t.$$.fragment,u),r=!0)},o(u){q(t.$$.fragment,u),r=!1},d(u){K(t,u),i=!1,Yu(s)}}}function c_(o,t,r){let i,s,u,c,h,f,v,m,b,x,E=Ve,F=Ve,z=Ve;function R(T){const ne=$p.dark;return sg((T==null?void 0:T.category)||ne,T==null?void 0:T.intensity)??og.get(ne)}si(o,bp,T=>r(10,h=T)),o.$$.on_destroy.push(()=>E()),o.$$.on_destroy.push(()=>F()),o.$$.on_destroy.push(()=>z()),qh(async()=>{r(9,x=await window.augmentDeps.monaco),x||console.error("Monaco not loaded. Diff view cannot be initialized.")}),Ku(()=>{m==null||m.dispose()});let S=!1;return o.$$.update=()=>{if(1539&o.$$.dirty&&x&&b&&!m&&(r(0,m=new kg(b,R(h),x)),E(),E=ar(m,T=>r(3,c=T))),1&o.$$.dirty&&(r(6,i=m==null?void 0:m.disableApply),z(),z=ar(i,T=>r(8,v=T))),1&o.$$.dirty&&(r(5,s=m==null?void 0:m.disableResolution),F(),F=ar(s,T=>r(7,f=T))),1025&o.$$.dirty){const T=h;m&&(m==null||m.updateTheme(R(T)))}8&o.$$.dirty&&r(4,u=c==null?void 0:c.leaves),5&o.$$.dirty&&(m==null||m.updateIsWebviewFocused(S))},[m,b,S,c,u,s,i,f,v,x,h,()=>r(2,S=!0),()=>r(2,S=!1),function(T){Oi[T?"unshift":"push"](()=>{b=T,r(1,b)})},T=>c==null?void 0:c.acceptChunk(T),T=>c==null?void 0:c.rejectChunk(T)]}new class extends oi{constructor(o){super(),ai(this,o,c_,u_,ui,{})}}({target:document.getElementById("app")});
