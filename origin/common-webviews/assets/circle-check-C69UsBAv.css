.c-callout.svelte-8as1i4{--callout-text-color:var(--ds-color-a11);--callout-high-contrast-text-color:var(--ds-color-12);--callout-border-color:var(--ds-color-a6);--callout-soft-bg-color:var(--ds-color-a3);--callout-surface-bg-color:var(--ds-color-a2);--callout-border-radius:var(--ds-radius-3);--callout-padding:var(--ds-spacing-3);--callout-gap:var(--ds-spacing-2);--callout-icon-size:16px;display:flex;flex-direction:row;gap:var(--callout-gap);align-items:flex-start;border-radius:var(--callout-border-radius);padding:var(--callout-padding);color:var(--callout-text-color)}.c-callout--soft.svelte-8as1i4{background:var(--callout-soft-bg-color)}.c-callout--surface.svelte-8as1i4{background:var(--callout-surface-bg-color)}.c-callout--outline.svelte-8as1i4,.c-callout--surface.svelte-8as1i4{border:1px solid var(--callout-border-color)}.c-callout--size-2.svelte-8as1i4{--callout-border-radius:var(--ds-radius-4);--callout-padding:var(--ds-spacing-4);--callout-gap:var(--ds-spacing-3)}.c-callout--size-3.svelte-8as1i4{--callout-border-radius:var(--ds-radius-5);--callout-padding:var(--ds-spacing-5);--callout-gap:var(--ds-spacing-4);--callout-icon-size:20px}.c-callout.c-callout--highContrast.svelte-8as1i4,[data-augment-theme-intensity=high-contrast] .c-callout.svelte-8as1i4{color:var(--callout-high-contrast-text-color)}.c-callout--accent.svelte-8as1i4{--callout-text-color:var(--ds-text);--callout-high-contrast-text-color:var(--ds-text)}.c-callout--neutral.svelte-8as1i4{--callout-high-contrast-text-color:var(--ds-text)}.c-callout-icon.svelte-8as1i4{display:inline-flex;align-items:center;justify-content:center;height:1lh}.c-callout-icon.svelte-8as1i4 svg{display:block;width:var(--callout-icon-size);height:var(--callout-icon-size)}.c-badge.svelte-s5ltdx{--badge-text-color:var(--ds-color-a11);--badge-high-contrast-text-color:var(--ds-color-12);--badge-border-radius:var(--ds-radius-1);--badge-padding-vertical:calc(var(--ds-spacing-1) * .5);--badge-padding-horizontal:calc(var(--ds-spacing-1) * 1.5);--badge-gap:calc(var(--ds-spacing-1) * 1.5);--badge-icon-size:12px;display:inline-flex;flex-direction:row;align-items:center;gap:var(--badge-gap);border-radius:var(--badge-border-radius);padding:var(--badge-padding-vertical) var(--badge-padding-horizontal);color:var(--badge-text-color);background:var(--badge-bg-color)}.c-badge.svelte-s5ltdx:has(.c-chaser){position:relative;overflow:hidden;z-index:0}.c-badge--soft.svelte-s5ltdx{--badge-bg-color:var(--ds-color-a3)}.c-badge--solid.svelte-s5ltdx{--badge-text-color:var(--ds-white-contrast);--badge-high-contrast-text-color:var(--ds-color-1);--badge-high-contrast-bg-color:var(--ds-color-12);--badge-bg-color:var(--ds-color-9)}.c-badge--surface.svelte-s5ltdx{--badge-bg-color:var(--ds-color-a2);--badge-border-color:var(--ds-color-a7)}.c-badge--outline.svelte-s5ltdx{--badge-border-color:var(--ds-color-a8);--badge-high-contrast-border-color:var(--ds-color-12)}.c-badge--outline.svelte-s5ltdx,.c-badge--surface.svelte-s5ltdx{border:1px solid var(--badge-border-color)}.c-badge--size-2.svelte-s5ltdx{--badge-padding-vertical:var(--ds-spacing-1);--badge-padding-horizontal:var(--ds-spacing-2)}.c-badge--size-3.svelte-s5ltdx{--badge-padding-vertical:var(--ds-spacing-1);--badge-padding-horizontal:calc(var(--ds-spacing-1) * 2.5);--badge-gap:var(--ds-spacing-2)}.c-badge--size-2.svelte-s5ltdx,.c-badge--size-3.svelte-s5ltdx{--badge-icon-size:16px;--badge-border-radius:var(--ds-radius-2)}.c-badge.c-badge--highContrast.svelte-s5ltdx,[data-augment-theme-intensity=high-contrast] .c-badge.svelte-s5ltdx{color:var(--badge-high-contrast-text-color);background:var(--badge-high-contrast-bg-color, var(--badge-bg-color));border-color:var(--badge-high-contrast-border-color, var(--badge-border-color))}.c-badge--solid.c-badge--info.svelte-s5ltdx,.c-badge--solid.c-badge--warning.svelte-s5ltdx{--badge-text-color:var(--ds-black-contrast)}.c-badge-body.svelte-s5ltdx{display:inline-flex;flex-direction:row;align-items:center;gap:var(--ds-spacing-1)}.c-badge.svelte-s5ltdx svg{display:block;width:var(--badge-icon-size);height:var(--badge-icon-size);fill:var(--icon-color, currentColor)}.c-badge__left-buttons.svelte-s5ltdx{margin-left:calc(var(--badge-padding-horizontal) * -1)}.c-badge__right-buttons.svelte-s5ltdx{margin-right:calc(var(--badge-padding-horizontal) * -1)}
