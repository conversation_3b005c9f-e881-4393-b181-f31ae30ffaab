import{A as x,t as q,u as z}from"./SpinnerAugment-BUJasFTo.js";function D(n){return(n==null?void 0:n.length)!==void 0?n:Array.from(n)}function E(n,f){n.d(1),f.delete(n.key)}function F(n,f){q(n,1,1,()=>{f.delete(n.key)})}function G(n,f,A,B,S,g,h,b,p,j,c,v){let o=n.length,r=g.length,a=o;const k={};for(;a--;)k[n[a].key]=a;const i=[],l=new Map,y=new Map,m=[];for(a=r;a--;){const t=v(S,g,a),e=A(t);let s=h.get(e);s?m.push(()=>s.p(t,f)):(s=j(e,t),s.c()),l.set(e,i[a]=s),e in k&&y.set(e,Math.abs(a-k[e]))}const w=new Set,M=new Set;function d(t){z(t,1),t.m(b,c),h.set(t.key,t),c=t.first,r--}for(;o&&r;){const t=i[r-1],e=n[o-1],s=t.key,u=e.key;t===e?(c=t.first,o--,r--):l.has(u)?!h.has(s)||w.has(s)?d(t):M.has(u)?o--:y.get(s)>y.get(u)?(M.add(s),d(t)):(w.add(u),o--):(p(e,h),o--)}for(;o--;){const t=n[o];l.has(t.key)||p(t,h)}for(;r;)d(i[r-1]);return x(m),i}export{E as d,D as e,F as o,G as u};
