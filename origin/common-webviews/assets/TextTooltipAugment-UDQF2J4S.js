var I=Object.defineProperty;var J=(o,e,n)=>e in o?I(o,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):o[e]=n;var _=(o,e,n)=>J(o,typeof e!="symbol"?e+"":e,n);import{A as Q}from"./IconButtonAugment-DFy7vWkh.js";import{S,i as j,s as B,V as f,W as x,u,t as p,Z as C,a6 as V,T as Z,D as k,e as g,q as F,r as N,h,Q as G,I as b,M as v,N as z,O as A,J as H,a8 as m,$ as K,a0 as L,a1 as P}from"./SpinnerAugment-BUJasFTo.js";import"./BaseButton-ci_067e0.js";import{R as U,a as X,C as Y}from"./Content-CSmc2GUv.js";class me extends Q{constructor(n){super(s=>{this._host.postMessage(s)});_(this,"_consumers",[]);this._host=n,this.onMessageFromExtension=this.onMessageFromExtension.bind(this)}dispose(){this._consumers=[]}postMessage(n){this._host.postMessage(n)}registerConsumer(n){this._consumers.push(n)}onMessageFromExtension(n){this._consumers.forEach(s=>{s.handleMessageFromExtension(n)})}}const M={Root:U,Trigger:X,Content:Y},ee=o=>({}),E=o=>({});function se(o){let e;const n=o[15].default,s=b(n,o,o[17],null);return{c(){s&&s.c()},m(t,i){s&&s.m(t,i),e=!0},p(t,i){s&&s.p&&(!e||131072&i)&&v(s,n,t,t[17],e?A(n,t[17],i,null):z(t[17]),null)},i(t){e||(u(s,t),e=!0)},o(t){p(s,t),e=!1},d(t){s&&s.d(t)}}}function T(o){let e,n;return e=new M.Content({props:{side:o[6],align:o[7],$$slots:{default:[oe]},$$scope:{ctx:o}}}),{c(){f(e.$$.fragment)},m(s,t){x(e,s,t),n=!0},p(s,t){const i={};64&t&&(i.side=s[6]),128&t&&(i.align=s[7]),135183&t&&(i.$$scope={dirty:t,ctx:s}),e.$set(i)},i(s){n||(u(e.$$.fragment,s),n=!0)},o(s){p(e.$$.fragment,s),n=!1},d(s){C(e,s)}}}function ne(o){let e,n;return e=new K({props:{size:1,class:"tooltip-text",$$slots:{default:[ie]},$$scope:{ctx:o}}}),{c(){f(e.$$.fragment)},m(s,t){x(e,s,t),n=!0},p(s,t){const i={};131073&t&&(i.$$scope={dirty:t,ctx:s}),e.$set(i)},i(s){n||(u(e.$$.fragment,s),n=!0)},o(s){p(e.$$.fragment,s),n=!1},d(s){C(e,s)}}}function te(o){let e;const n=o[15].content,s=b(n,o,o[17],E);return{c(){s&&s.c()},m(t,i){s&&s.m(t,i),e=!0},p(t,i){s&&s.p&&(!e||131072&i)&&v(s,n,t,t[17],e?A(n,t[17],i,ee):z(t[17]),E)},i(t){e||(u(s,t),e=!0)},o(t){p(s,t),e=!1},d(t){s&&s.d(t)}}}function ie(o){let e;return{c(){e=L(o[0])},m(n,s){g(n,e,s)},p(n,s){1&s&&P(e,n[0])},d(n){n&&h(e)}}}function oe(o){let e,n,s,t;const i=[te,ne],r=[];function l(c,$){return c[12].content?0:1}return n=l(o),s=r[n]=i[n](o),{c(){e=H("div"),s.c(),m(e,"width",o[1]),m(e,"min-width",o[2]),m(e,"max-width",o[3])},m(c,$){g(c,e,$),r[n].m(e,null),t=!0},p(c,$){let d=n;n=l(c),n===d?r[n].p(c,$):(F(),p(r[d],1,1,()=>{r[d]=null}),N(),s=r[n],s?s.p(c,$):(s=r[n]=i[n](c),s.c()),u(s,1),s.m(e,null)),2&$&&m(e,"width",c[1]),4&$&&m(e,"min-width",c[2]),8&$&&m(e,"max-width",c[3])},i(c){t||(u(s),t=!0)},o(c){p(s),t=!1},d(c){c&&h(e),r[n].d()}}}function re(o){let e,n,s,t;e=new M.Trigger({props:{referenceClientRect:o[10],class:o[8],$$slots:{default:[se]},$$scope:{ctx:o}}});let i=(o[0]||o[12].content)&&T(o);return{c(){f(e.$$.fragment),n=Z(),i&&i.c(),s=k()},m(r,l){x(e,r,l),g(r,n,l),i&&i.m(r,l),g(r,s,l),t=!0},p(r,l){const c={};1024&l&&(c.referenceClientRect=r[10]),256&l&&(c.class=r[8]),131072&l&&(c.$$scope={dirty:l,ctx:r}),e.$set(c),r[0]||r[12].content?i?(i.p(r,l),4097&l&&u(i,1)):(i=T(r),i.c(),u(i,1),i.m(s.parentNode,s)):i&&(F(),p(i,1,1,()=>{i=null}),N())},i(r){t||(u(e.$$.fragment,r),u(i),t=!0)},o(r){p(e.$$.fragment,r),p(i),t=!1},d(r){r&&(h(n),h(s)),C(e,r),i&&i.d(r)}}}function ae(o){let e,n,s={delayDurationMs:o[4],onOpenChange:o[9],triggerOn:o[5],tippyTheme:"default text-tooltip-augment",$$slots:{default:[re]},$$scope:{ctx:o}};return e=new M.Root({props:s}),o[16](e),{c(){f(e.$$.fragment)},m(t,i){x(e,t,i),n=!0},p(t,[i]){const r={};16&i&&(r.delayDurationMs=t[4]),512&i&&(r.onOpenChange=t[9]),32&i&&(r.triggerOn=t[5]),136655&i&&(r.$$scope={dirty:i,ctx:t}),e.$set(r)},i(t){n||(u(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){o[16](null),C(e,t)}}}function ce(o,e,n){let{$$slots:s={},$$scope:t}=e;const i=V(s);let r,{content:l}=e,{width:c}=e,{minWidth:$}=e,{maxWidth:d="360px"}=e,{delayDurationMs:O}=e,{triggerOn:w}=e,{side:y="top"}=e,{align:R="center"}=e,{class:W=""}=e,{onOpenChange:q}=e,{referenceClientRect:D}=e;return o.$$set=a=>{"content"in a&&n(0,l=a.content),"width"in a&&n(1,c=a.width),"minWidth"in a&&n(2,$=a.minWidth),"maxWidth"in a&&n(3,d=a.maxWidth),"delayDurationMs"in a&&n(4,O=a.delayDurationMs),"triggerOn"in a&&n(5,w=a.triggerOn),"side"in a&&n(6,y=a.side),"align"in a&&n(7,R=a.align),"class"in a&&n(8,W=a.class),"onOpenChange"in a&&n(9,q=a.onOpenChange),"referenceClientRect"in a&&n(10,D=a.referenceClientRect),"$$scope"in a&&n(17,t=a.$$scope)},[l,c,$,d,O,w,y,R,W,q,D,r,i,()=>r==null?void 0:r.requestOpen(),()=>r==null?void 0:r.requestClose(),s,function(a){G[a?"unshift":"push"](()=>{r=a,n(11,r)})},t]}class ge extends S{constructor(e){super(),j(this,e,ce,ae,B,{content:0,width:1,minWidth:2,maxWidth:3,delayDurationMs:4,triggerOn:5,side:6,align:7,class:8,onOpenChange:9,referenceClientRect:10,requestOpen:13,requestClose:14})}get requestOpen(){return this.$$.ctx[13]}get requestClose(){return this.$$.ctx[14]}}export{me as M,ge as T};
