function u(n){const t=new Set;return n&&n.length>0&&n.flatMap(e=>e.sections||[]).flatMap(e=>e.changes).forEach(e=>{t.add(e.path)}),t}function g(n,t,e={},i=[],p=[]){const c=u(n),s=Array.from(c);s.length===0&&s.push(...t.map(a=>a.new_path||a.old_path).filter(Boolean));const h=s.every(a=>i.includes(a)),f=s.filter(a=>!i.includes(a)&&!p.includes(a)),l=[];return f.forEach(a=>{const d=n.flatMap(o=>o.sections||[]).flatMap(o=>o.changes).find(o=>o.path===a);if(d)l.push({path:a,originalCode:d.originalCode,newCode:e[a]||d.modifiedCode});else{const o=t.find(r=>(r.new_path||r.old_path)===a);o&&l.push({path:a,originalCode:o.old_contents||"",newCode:e[a]||o.new_contents||""})}}),{filesToApply:l,allPaths:s,areAllPathsApplied:h}}function _(n,t){n.length&&t&&n.forEach(e=>{t(e.path,e.originalCode,e.newCode)})}function w(n){return n.sort((t,e)=>{const i=new Date(t.updated_at||t.started_at);return new Date(e.updated_at||e.started_at).getTime()-i.getTime()})}export{_ as a,u as g,g as p,w as s};
