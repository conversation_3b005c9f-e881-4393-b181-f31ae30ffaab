import{S as Z,i as G,s as H,I as L,a as j,Q as K,R as M,J as T,T as F,V as q,c as _,e as I,f as V,W as D,M as P,N as U,O as tt,g as et,X as st,Y as N,u as v,q as O,t as g,r as A,h as S,Z as J,E as Q,_ as nt,j as ot,$ as Y,a0 as B,a1 as at}from"./SpinnerAugment-BUJasFTo.js";import"./BaseButton-ci_067e0.js";import"./BaseTextInput-BYcZ2XaJ.js";import{T as ct}from"./TextAreaAugment-FdvYFnJr.js";import{l as it}from"./lodash-BHrlUNHT.js";const rt=o=>({}),R=o=>({});function W(o){let t,e;return t=new Y({props:{size:1,weight:"light",color:"error",$$slots:{default:[lt]},$$scope:{ctx:o}}}),{c(){q(t.$$.fragment)},m(a,d){D(t,a,d),e=!0},p(a,d){const l={};4194432&d&&(l.$$scope={dirty:d,ctx:a}),t.$set(l)},i(a){e||(v(t.$$.fragment,a),e=!0)},o(a){g(t.$$.fragment,a),e=!1},d(a){J(t,a)}}}function lt(o){let t;return{c(){t=B(o[7])},m(e,a){I(e,t,a)},p(e,a){128&a&&at(t,e[7])},d(e){e&&S(t)}}}function X(o){let t,e;return t=new Y({props:{size:1,weight:"light",color:"success",$$slots:{default:[ut]},$$scope:{ctx:o}}}),{c(){q(t.$$.fragment)},m(a,d){D(t,a,d),e=!0},i(a){e||(v(t.$$.fragment,a),e=!0)},o(a){g(t.$$.fragment,a),e=!1},d(a){J(t,a)}}}function ut(o){let t;return{c(){t=B("Saved")},m(e,a){I(e,t,a)},d(e){e&&S(t)}}}function dt(o){let t,e,a,d,l,z,w,h,$,k,f;const y=o[17].header,u=L(y,o,o[22],R),m=[{variant:o[2]},{size:o[3]},{color:o[4]},{resize:o[5]},{placeholder:"Enter markdown content..."},o[11]];function E(s){o[18](s)}function x(s){o[19](s)}let p={};for(let s=0;s<m.length;s+=1)p=j(p,m[s]);o[0]!==void 0&&(p.textInput=o[0]),o[1]!==void 0&&(p.value=o[1]),l=new ct({props:p}),K.push(()=>M(l,"textInput",E)),K.push(()=>M(l,"value",x)),l.$on("select",o[9]),l.$on("mouseup",o[9]),l.$on("keyup",o[20]),l.$on("input",o[10]),l.$on("keydown",o[21]);let c=!!o[7]&&W(o),r=o[6]&&X(o);return{c(){t=T("div"),e=T("div"),u&&u.c(),a=F(),d=T("div"),q(l.$$.fragment),h=F(),$=T("div"),c&&c.c(),k=F(),r&&r.c(),_(e,"class","c-markdown-editor__header svelte-ybk6ut"),_(d,"class","c-markdown-editor__content svelte-ybk6ut"),_(t,"class","l-markdown-editor svelte-ybk6ut"),_($,"class","c-markdown-editor__status svelte-ybk6ut")},m(s,i){I(s,t,i),V(t,e),u&&u.m(e,null),V(t,a),V(t,d),D(l,d,null),I(s,h,i),I(s,$,i),c&&c.m($,null),V($,k),r&&r.m($,null),f=!0},p(s,[i]){u&&u.p&&(!f||4194304&i)&&P(u,y,s,s[22],f?tt(y,s[22],i,rt):U(s[22]),R);const b=2108&i?et(m,[4&i&&{variant:s[2]},8&i&&{size:s[3]},16&i&&{color:s[4]},32&i&&{resize:s[5]},m[4],2048&i&&st(s[11])]):{};!z&&1&i&&(z=!0,b.textInput=s[0],N(()=>z=!1)),!w&&2&i&&(w=!0,b.value=s[1],N(()=>w=!1)),l.$set(b),s[7]?c?(c.p(s,i),128&i&&v(c,1)):(c=W(s),c.c(),v(c,1),c.m($,k)):c&&(O(),g(c,1,1,()=>{c=null}),A()),s[6]?r?64&i&&v(r,1):(r=X(s),r.c(),v(r,1),r.m($,null)):r&&(O(),g(r,1,1,()=>{r=null}),A())},i(s){f||(v(u,s),v(l.$$.fragment,s),v(c),v(r),f=!0)},o(s){g(u,s),g(l.$$.fragment,s),g(c),g(r),f=!1},d(s){s&&(S(t),S(h),S($)),u&&u.d(s),J(l),c&&c.d(),r&&r.d()}}}function $t(o,t,e){const a=["variant","size","color","resize","textInput","value","selectedText","selectionStart","selectionEnd","saveFunction","debounceValue"];let d,l,z=Q(t,a),{$$slots:w={},$$scope:h}=t,{variant:$="surface"}=t,{size:k=2}=t,{color:f}=t,{resize:y="none"}=t,{textInput:u}=t,{value:m=""}=t,{selectedText:E=""}=t,{selectionStart:x=0}=t,{selectionEnd:p=0}=t,{saveFunction:c}=t,{debounceValue:r=1e3}=t,s=!1;const i=async()=>{try{c(),e(6,s=!0),clearTimeout(d),d=setTimeout(()=>{e(6,s=!1)},1500)}catch(n){e(7,l=n instanceof Error?n.message:String(n))}};function b(){u&&(e(13,x=u.selectionStart),e(14,p=u.selectionEnd),e(12,E=x!==p?m.substring(x,p):""))}const C=it.debounce(i,r);return nt(()=>{i()}),o.$$set=n=>{t=j(j({},t),ot(n)),e(11,z=Q(t,a)),"variant"in n&&e(2,$=n.variant),"size"in n&&e(3,k=n.size),"color"in n&&e(4,f=n.color),"resize"in n&&e(5,y=n.resize),"textInput"in n&&e(0,u=n.textInput),"value"in n&&e(1,m=n.value),"selectedText"in n&&e(12,E=n.selectedText),"selectionStart"in n&&e(13,x=n.selectionStart),"selectionEnd"in n&&e(14,p=n.selectionEnd),"saveFunction"in n&&e(15,c=n.saveFunction),"debounceValue"in n&&e(16,r=n.debounceValue),"$$scope"in n&&e(22,h=n.$$scope)},[u,m,$,k,f,y,s,l,i,b,C,z,E,x,p,c,r,w,function(n){u=n,e(0,u)},function(n){m=n,e(1,m)},()=>{b()},n=>{(n.key==="Escape"||(n.metaKey||n.ctrlKey)&&n.key==="s")&&(n.preventDefault(),i())},h]}class xt extends Z{constructor(t){super(),G(this,t,$t,dt,H,{variant:2,size:3,color:4,resize:5,textInput:0,value:1,selectedText:12,selectionStart:13,selectionEnd:14,saveFunction:15,debounceValue:16})}}export{xt as M};
