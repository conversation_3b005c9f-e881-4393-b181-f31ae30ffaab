import{S as ge,i as fe,s as de,J as D,V as M,T as A,c as R,L as W,e as _,W as S,f as ue,u as $,q as b,t as f,r as F,h as k,Z as C,ac as ee,ad as P,a2 as he,a4 as V,a9 as ce,ae as we,A as ye,n as I,af as Q,Q as Me,D as B}from"./SpinnerAugment-BUJasFTo.js";import{e as z,u as Se,o as Ce}from"./each-DUdYBCJG.js";import{G as xe,g as Le,t as _e,a as ke,M as Ie,A as ve,b as qe,c as Ee,S as be,d as Fe,e as He,R as Ne,C as je,f as Ae,U as Re,h as $e,E as Te,i as We,j as Be}from"./MessageListBottomButton-DxojfPD0.js";import"./Content-CSmc2GUv.js";import{R as ze,S as Z,i as te,a as De,b as Ge,c as Ue,d as Je,e as Pe,f as Qe,g as Ve,h as Ze,j as Ke,k as Oe,E as Xe}from"./open-in-new-window-eiueNVFd.js";import"./diff-operations-DcwKj7d6.js";import"./isObjectLike-DZvo29T1.js";import"./BaseButton-ci_067e0.js";import{S as Ye}from"./main-panel-C8Qm-2QZ.js";import{aq as et,ar as tt}from"./AugmentMessage-DqNyWKeW.js";import"./MaterialIcon-d9y4vLnQ.js";import"./keypress-DD1aQVr0.js";import"./autofix-state-d-ymFdyn.js";import"./Keybindings-CJ37aOb-.js";import"./CardAugment-DvO45c5p.js";import"./TextTooltipAugment-UDQF2J4S.js";import"./IconButtonAugment-DFy7vWkh.js";import"./index-yg8vr2DA.js";import"./BaseTextInput-BYcZ2XaJ.js";import"./exclamation-triangle-uzqmF3G7.js";import"./pen-to-square-DxHNIIBu.js";import"./augment-logo-CSOE_v2f.js";import"./ButtonAugment-DbAwCSeR.js";import"./expand-CE2AcHxk.js";import"./diff-utils-BYhHYFY1.js";import"./toggleHighContrast-CwIv4U26.js";import"./preload-helper-Dv6uf1Os.js";import"./index-B-fP3g4F.js";import"./layer-group-DiHphAz9.js";import"./circle-check-D3m08yO6.js";import"./types-CF53Ux0u.js";import"./globals-D0QH3NT1.js";import"./file-base64-RhZyEMB8.js";import"./file-paths-BcSg4gks.js";import"./types-e72Yl75f.js";import"./folder-opened-C1X7jSw2.js";import"./design-system-init-BKdwvVur.js";import"./trash-mbophkQL.js";import"./lodash-BHrlUNHT.js";import"./index-DlpZFSR-.js";import"./ellipsis-CRdQranZ.js";import"./terminal-CwJUqtXN.js";import"./await_block-MKx3qG42.js";import"./chevron-down-BPcCn3Z6.js";import"./VSCodeCodicon-CzBgPB9u.js";import"./chat-flags-model-pSBfdnEi.js";import"./magnifying-glass--PD1Uw4z.js";import"./IconFilePath-XiFTI0mW.js";import"./LanguageIcon-DPvfnfyG.js";import"./next-edit-types-904A5ehg.js";import"./play-Dd7ujDDf.js";function ne(s,e,n){const t=s.slice();t[35]=e[n],t[38]=n;const r=t[38]+1===t[10].length;return t[36]=r,t}function re(s,e,n){const t=s.slice();t[39]=e[n].turn,t[40]=e[n].idx;const r=t[40]+1===t[11].length;return t[41]=r,t}function se(s){let e,n;return e=new ve({}),{c(){M(e.$$.fragment)},m(t,r){S(e,t,r),n=!0},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function nt(s){let e,n,t,r;const o=[ct,ut],i=[];function u(l,a){return l[15].enableRichCheckpointInfo?0:1}return e=u(s),n=i[e]=o[e](s),{c(){n.c(),t=B()},m(l,a){i[e].m(l,a),_(l,t,a),r=!0},p(l,a){let c=e;e=u(l),e===c?i[e].p(l,a):(b(),f(i[c],1,1,()=>{i[c]=null}),F(),n=i[e],n?n.p(l,a):(n=i[e]=o[e](l),n.c()),$(n,1),n.m(t.parentNode,t))},i(l){r||($(n),r=!0)},o(l){f(n),r=!1},d(l){l&&k(t),i[e].d(l)}}}function rt(s){let e,n;return e=new je({props:{chatModel:s[1],turn:s[39],turnIndex:s[40],isLastTurn:s[41],messageListContainer:s[0]}}),{c(){M(e.$$.fragment)},m(t,r){S(e,t,r),n=!0},p(t,r){const o={};2&r[0]&&(o.chatModel=t[1]),1024&r[0]&&(o.turn=t[39]),1024&r[0]&&(o.turnIndex=t[40]),3072&r[0]&&(o.isLastTurn=t[41]),1&r[0]&&(o.messageListContainer=t[0]),e.$set(o)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function st(s){let e,n;return e=new Ae({props:{stage:s[39].stage,iterationId:s[39].iterationId,stageCount:s[39].stageCount}}),{c(){M(e.$$.fragment)},m(t,r){S(e,t,r),n=!0},p(t,r){const o={};1024&r[0]&&(o.stage=t[39].stage),1024&r[0]&&(o.iterationId=t[39].iterationId),1024&r[0]&&(o.stageCount=t[39].stageCount),e.$set(o)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function ot(s){let e,n;return e=new Re({props:{chatModel:s[1],msg:s[39].response_text??""}}),{c(){M(e.$$.fragment)},m(t,r){S(e,t,r),n=!0},p(t,r){const o={};2&r[0]&&(o.chatModel=t[1]),1024&r[0]&&(o.msg=t[39].response_text??""),e.$set(o)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function at(s){let e,n;return e=new et({props:{markdown:s[39].response_text??"",messageListContainer:s[0]}}),{c(){M(e.$$.fragment)},m(t,r){S(e,t,r),n=!0},p(t,r){const o={};1024&r[0]&&(o.markdown=t[39].response_text??""),1&r[0]&&(o.messageListContainer=t[0]),e.$set(o)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function lt(s){let e,n;function t(){return s[28](s[39])}return e=new $e({props:{turn:s[39],preamble:Ye,resendTurn:t,$$slots:{default:[$t]},$$scope:{ctx:s}}}),{c(){M(e.$$.fragment)},m(r,o){S(e,r,o),n=!0},p(r,o){s=r;const i={};1024&o[0]&&(i.turn=s[39]),1028&o[0]&&(i.resendTurn=t),17408&o[0]|8192&o[1]&&(i.$$scope={dirty:o,ctx:s}),e.$set(i)},i(r){n||($(e.$$.fragment,r),n=!0)},o(r){f(e.$$.fragment,r),n=!1},d(r){C(e,r)}}}function it(s){let e,n;return e=new Te({props:{flagsModel:s[12],turn:s[39]}}),{c(){M(e.$$.fragment)},m(t,r){S(e,t,r),n=!0},p(t,r){const o={};4096&r[0]&&(o.flagsModel=t[12]),1024&r[0]&&(o.turn=t[39]),e.$set(o)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function mt(s){let e,n;return e=new $e({props:{turn:s[39]}}),{c(){M(e.$$.fragment)},m(t,r){S(e,t,r),n=!0},p(t,r){const o={};1024&r[0]&&(o.turn=t[39]),e.$set(o)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function ut(s){let e,n;return e=new We({props:{turn:s[39]}}),{c(){M(e.$$.fragment)},m(t,r){S(e,t,r),n=!0},p(t,r){const o={};1024&r[0]&&(o.turn=t[39]),e.$set(o)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function ct(s){let e,n;return e=new Be({props:{turn:s[39]}}),{c(){M(e.$$.fragment)},m(t,r){S(e,t,r),n=!0},p(t,r){const o={};1024&r[0]&&(o.turn=t[39]),e.$set(o)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function $t(s){let e,n;return e=new tt({props:{conversationModel:s[14],turn:s[39]}}),{c(){M(e.$$.fragment)},m(t,r){S(e,t,r),n=!0},p(t,r){const o={};16384&r[0]&&(o.conversationModel=t[14]),1024&r[0]&&(o.turn=t[39]),e.$set(o)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function oe(s){let e,n,t,r;function o(){return s[29](s[39])}return{c(){e=D("div"),R(e,"class","c-msg-list__turn-seen")},m(i,u){_(i,e,u),t||(r=V(n=Ee.call(null,e,{onSeen:o,track:s[39].seen_state!==Z.seen})),t=!0)},p(i,u){s=i,n&&ce(n.update)&&1024&u[0]&&n.update.call(null,{onSeen:o,track:s[39].seen_state!==Z.seen})},d(i){i&&k(e),t=!1,r()}}}function ae(s,e){let n,t,r,o,i,u,l,a,c,m,g,d,x,v,H=te(e[39]);const q=[mt,it,lt,at,ot,st,rt,nt],L=[];function N(h,w){return 1024&w[0]&&(t=null),1024&w[0]&&(r=null),1024&w[0]&&(o=null),1024&w[0]&&(i=null),1024&w[0]&&(u=null),1024&w[0]&&(l=null),1024&w[0]&&(a=null),1024&w[0]&&(c=null),t==null&&(t=!!De(h[39])),t?0:(r==null&&(r=!!Ge(h[39])),r?1:(o==null&&(o=!!Ue(h[39])),o?2:(i==null&&(i=!!Je(h[39])),i?3:(u==null&&(u=!!Pe(h[39])),u?4:(l==null&&(l=!!Qe(h[39])),l?5:(a==null&&(a=!!(Ve(h[39])||Ze(h[39])||Ke(h[39]))),a?6:(c==null&&(c=!(!Oe(h[39])||h[39].status!==Xe.success)),c?7:-1)))))))}~(m=N(e,[-1,-1]))&&(g=L[m]=q[m](e));let y=H&&oe(e);return{key:s,first:null,c(){n=B(),g&&g.c(),d=A(),y&&y.c(),x=B(),this.first=n},m(h,w){_(h,n,w),~m&&L[m].m(h,w),_(h,d,w),y&&y.m(h,w),_(h,x,w),v=!0},p(h,w){let E=m;m=N(e=h,w),m===E?~m&&L[m].p(e,w):(g&&(b(),f(L[E],1,1,()=>{L[E]=null}),F()),~m?(g=L[m],g?g.p(e,w):(g=L[m]=q[m](e),g.c()),$(g,1),g.m(d.parentNode,d)):g=null),1024&w[0]&&(H=te(e[39])),H?y?y.p(e,w):(y=oe(e),y.c(),y.m(x.parentNode,x)):y&&(y.d(1),y=null)},i(h){v||($(g),v=!0)},o(h){f(g),v=!1},d(h){h&&(k(n),k(d),k(x)),~m&&L[m].d(h),y&&y.d(h)}}}function le(s){let e,n,t,r;const o=[ht,dt,ft,gt,pt],i=[];function u(l,a){return l[9].retryMessage?0:l[9].showGeneratingResponse?1:l[9].showAwaitingUserInput?2:l[9].showRunningSpacer?3:l[9].showStopped?4:-1}return~(e=u(s))&&(n=i[e]=o[e](s)),{c(){n&&n.c(),t=B()},m(l,a){~e&&i[e].m(l,a),_(l,t,a),r=!0},p(l,a){let c=e;e=u(l),e===c?~e&&i[e].p(l,a):(n&&(b(),f(i[c],1,1,()=>{i[c]=null}),F()),~e?(n=i[e],n?n.p(l,a):(n=i[e]=o[e](l),n.c()),$(n,1),n.m(t.parentNode,t)):n=null)},i(l){r||($(n),r=!0)},o(l){f(n),r=!1},d(l){l&&k(t),~e&&i[e].d(l)}}}function pt(s){let e,n;return e=new be({}),{c(){M(e.$$.fragment)},m(t,r){S(e,t,r),n=!0},p:I,i(t){n||($(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function gt(s){let e;return{c(){e=D("div"),R(e,"class","c-agent-running-spacer svelte-t9khzq")},m(n,t){_(n,e,t)},p:I,i:I,o:I,d(n){n&&k(e)}}}function ft(s){let e,n;return e=new Fe({}),{c(){M(e.$$.fragment)},m(t,r){S(e,t,r),n=!0},p:I,i(t){n||($(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function dt(s){let e,n;return e=new He({}),{c(){M(e.$$.fragment)},m(t,r){S(e,t,r),n=!0},p:I,i(t){n||($(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function ht(s){let e,n;return e=new Ne({props:{message:s[9].retryMessage}}),{c(){M(e.$$.fragment)},m(t,r){S(e,t,r),n=!0},p(t,r){const o={};512&r[0]&&(o.message=t[9].retryMessage),e.$set(o)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function wt(s){let e,n,t,r=[],o=new Map,i=z(s[35]);const u=a=>a[39].request_id??`no-request-id-${a[40]}`;for(let a=0;a<i.length;a+=1){let c=re(s,i,a),m=u(c);o.set(m,r[a]=ae(m,c))}let l=s[36]&&le(s);return{c(){for(let a=0;a<r.length;a+=1)r[a].c();e=A(),l&&l.c(),n=A()},m(a,c){for(let m=0;m<r.length;m+=1)r[m]&&r[m].m(a,c);_(a,e,c),l&&l.m(a,c),_(a,n,c),t=!0},p(a,c){4250631&c[0]&&(i=z(a[35]),b(),r=Se(r,c,u,1,a,i,o,e.parentNode,Ce,ae,e,re),F()),a[36]?l?(l.p(a,c),1024&c[0]&&$(l,1)):(l=le(a),l.c(),$(l,1),l.m(n.parentNode,n)):l&&(b(),f(l,1,1,()=>{l=null}),F())},i(a){if(!t){for(let c=0;c<i.length;c+=1)$(r[c]);$(l),t=!0}},o(a){for(let c=0;c<r.length;c+=1)f(r[c]);f(l),t=!1},d(a){a&&(k(e),k(n));for(let c=0;c<r.length;c+=1)r[c].d(a);l&&l.d(a)}}}function ie(s){let e,n;return e=new qe({props:{class:"c-msg-list__item--grouped",chatModel:s[1],isLastItem:s[36],userControlsScroll:s[3],requestId:s[35][0].turn.request_id,releaseScroll:s[30],messageListContainer:s[0],minHeight:s[36]?s[6]:0,$$slots:{default:[wt]},$$scope:{ctx:s}}}),{c(){M(e.$$.fragment)},m(t,r){S(e,t,r),n=!0},p(t,r){const o={};2&r[0]&&(o.chatModel=t[1]),1024&r[0]&&(o.isLastItem=t[36]),8&r[0]&&(o.userControlsScroll=t[3]),1024&r[0]&&(o.requestId=t[35][0].turn.request_id),8&r[0]&&(o.releaseScroll=t[30]),1&r[0]&&(o.messageListContainer=t[0]),1088&r[0]&&(o.minHeight=t[36]?t[6]:0),56839&r[0]|8192&r[1]&&(o.$$scope={dirty:r,ctx:t}),e.$set(o)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function yt(s){let e,n,t,r,o,i,u=s[7]&&se(),l=z(s[10]),a=[];for(let m=0;m<l.length;m+=1)a[m]=ie(ne(s,l,m));const c=m=>f(a[m],1,1,()=>{a[m]=null});return{c(){e=D("div"),u&&u.c(),n=A();for(let m=0;m<a.length;m+=1)a[m].c();R(e,"class","c-msg-list svelte-t9khzq"),W(e,"c-msg-list--minimal",!s[15].fullFeatured)},m(m,g){_(m,e,g),u&&u.m(e,null),ue(e,n);for(let d=0;d<a.length;d+=1)a[d]&&a[d].m(e,null);s[31](e),r=!0,o||(i=[V(_e.call(null,e,{onScrollIntoBottom:s[19],onScrollAwayFromBottom:s[20],onScroll:s[32]})),V(t=ke.call(null,e,{onHeightChange:s[33]}))],o=!0)},p(m,g){if(m[7]?u?128&g[0]&&$(u,1):(u=se(),u.c(),$(u,1),u.m(e,n)):u&&(b(),f(u,1,1,()=>{u=null}),F()),4251215&g[0]){let d;for(l=z(m[10]),d=0;d<l.length;d+=1){const x=ne(m,l,d);a[d]?(a[d].p(x,g),$(a[d],1)):(a[d]=ie(x),a[d].c(),$(a[d],1),a[d].m(e,null))}for(b(),d=l.length;d<a.length;d+=1)c(d);F()}t&&ce(t.update)&&16&g[0]&&t.update.call(null,{onHeightChange:m[33]}),(!r||32768&g[0])&&W(e,"c-msg-list--minimal",!m[15].fullFeatured)},i(m){if(!r){$(u);for(let g=0;g<l.length;g+=1)$(a[g]);r=!0}},o(m){f(u),a=a.filter(Boolean);for(let g=0;g<a.length;g+=1)f(a[g]);r=!1},d(m){m&&k(e),u&&u.d(),we(a,m),s[31](null),o=!1,ye(i)}}}function me(s){let e,n;return e=new Ie({props:{messageListElement:s[0],showScrollDown:s[5]}}),{c(){M(e.$$.fragment)},m(t,r){S(e,t,r),n=!0},p(t,r){const o={};1&r[0]&&(o.messageListElement=t[0]),32&r[0]&&(o.showScrollDown=t[5]),e.$set(o)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){f(e.$$.fragment,t),n=!1},d(t){C(e,t)}}}function Mt(s){let e,n,t,r;n=new xe({props:{$$slots:{default:[yt]},$$scope:{ctx:s}}});let o=s[8]&&me(s);return{c(){e=D("div"),M(n.$$.fragment),t=A(),o&&o.c(),R(e,"class","c-msg-list-container svelte-t9khzq"),R(e,"data-testid","chat-message-list"),W(e,"c-msg-list--minimal",!s[15].fullFeatured)},m(i,u){_(i,e,u),S(n,e,null),ue(e,t),o&&o.m(e,null),r=!0},p(i,u){const l={};57055&u[0]|8192&u[1]&&(l.$$scope={dirty:u,ctx:i}),n.$set(l),i[8]?o?(o.p(i,u),256&u[0]&&$(o,1)):(o=me(i),o.c(),$(o,1),o.m(e,null)):o&&(b(),f(o,1,1,()=>{o=null}),F()),(!r||32768&u[0])&&W(e,"c-msg-list--minimal",!i[15].fullFeatured)},i(i){r||($(n.$$.fragment,i),$(o),r=!0)},o(i){f(n.$$.fragment,i),f(o),r=!1},d(i){i&&k(e),C(n),o&&o.d()}}}function St(s,e,n){let t,r,o,i,u,l,a,c,m,g,d,x,v,H,q,L,N=I,y=I,h=()=>(y(),y=Q(E,p=>n(27,q=p)),E),w=I;s.$$.on_destroy.push(()=>N()),s.$$.on_destroy.push(()=>y()),s.$$.on_destroy.push(()=>w());let{chatModel:E}=e;h();let{onboardingWorkspaceModel:G}=e,{msgListElement:T}=e;const pe=ee("agentConversationModel"),{agentExchangeStatus:K,isCurrConversationAgentic:O}=pe;P(s,K,p=>n(26,H=p)),P(s,O,p=>n(25,v=p));const X=ee(ze.key);P(s,X,p=>n(24,x=p));let j=!1;function U(){n(3,j=!0)}he(()=>{var p;((p=d.lastExchange)==null?void 0:p.seen_state)===Z.unseen&&U()});let J=0;const Y=p=>d.markSeen(p);return s.$$set=p=>{"chatModel"in p&&h(n(1,E=p.chatModel)),"onboardingWorkspaceModel"in p&&n(2,G=p.onboardingWorkspaceModel),"msgListElement"in p&&n(0,T=p.msgListElement)},s.$$.update=()=>{134217728&s.$$.dirty[0]&&(n(13,t=q.currentConversationModel),N(),N=Q(t,p=>n(14,d=p))),134217728&s.$$.dirty[0]&&(n(12,r=q.flags),w(),w=Q(r,p=>n(15,L=p))),251658240&s.$$.dirty[0]&&n(23,o=Le(q,H,v,x)),8388608&s.$$.dirty[0]&&n(11,i=o.chatHistory),8388608&s.$$.dirty[0]&&n(10,u=o.groupedChatHistory),8388608&s.$$.dirty[0]&&n(9,l=o.lastGroupConfig),8388608&s.$$.dirty[0]&&n(8,a=o.doShowFloatingButtons),8388608&s.$$.dirty[0]&&n(7,c=o.doShowAgentSetupLogs),16&s.$$.dirty[0]&&n(6,m=J),8&s.$$.dirty[0]&&n(5,g=j)},[T,E,G,j,J,g,m,c,a,l,u,i,r,t,d,L,K,O,X,function(){n(3,j=!1)},function(){n(3,j=!0)},U,Y,o,x,v,H,q,p=>G.retryProjectSummary(p),p=>Y(p),()=>n(3,j=!0),function(p){Me[p?"unshift":"push"](()=>{T=p,n(0,T)})},p=>{p<=1&&U()},p=>n(4,J=p)]}class Mn extends ge{constructor(e){super(),fe(this,e,St,Mt,de,{chatModel:1,onboardingWorkspaceModel:2,msgListElement:0},null,[-1,-1])}}export{Mn as default};
