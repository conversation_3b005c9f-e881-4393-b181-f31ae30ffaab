var Wa=Object.defineProperty;var Ya=(s,e,t)=>e in s?Wa(s,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):s[e]=t;var ve=(s,e,t)=>Ya(s,typeof e!="symbol"?e+"":e,t);import{ah as Le,ai as pa,aj as rn,S as ue,i as pe,s as de,J as Z,T as E,V as S,a0 as U,c as $,e as _,f as M,W as k,P as et,a9 as wt,q as W,r as Y,u as p,t as m,h as y,Z as C,A as Is,L as _e,a1 as he,$ as ce,aD as Ds,D as Se,n as X,af as ma,b as Me,I as Ee,a as ye,K as Kn,M as Ie,N as Pe,O as je,g as $t,E as Gn,a6 as ha,j as Je,aA as bt,ad as rt,ae as In,a4 as Xa,X as Vs,H as Pn,w as jn,x as Rn,y as Ln,d as ot,z as Fn,Q as tt,R as nt,Y as st,al as fa,aE as Qa,as as ei,a2 as ti,au as Us,_ as ga}from"./SpinnerAugment-BUJasFTo.js";import"./design-system-init-BKdwvVur.js";import{W as le,a as Ke,h as Ve,O as Fe}from"./BaseButton-ci_067e0.js";import{T as sn,M as ni}from"./TextTooltipAugment-UDQF2J4S.js";import{G as si,S as ri,a as ai,C as ii,N as oi,J as ci,L as li,F as qs,b as Lt,c as di,M as Bn,d as ui,e as pi}from"./magnifying-glass--PD1Uw4z.js";import{T as gs,b as pt,G as mi,L as be,c as hi,R as fi,C as va,d as gi}from"./circle-check-D3m08yO6.js";import{e as xe,u as Gt,o as Wt}from"./each-DUdYBCJG.js";import{V as $a}from"./VSCodeCodicon-CzBgPB9u.js";import{S as xs}from"./types-e72Yl75f.js";import{P as Ps,B as vi,L as $i}from"./layer-group-DiHphAz9.js";import{I as zn,A as yi}from"./IconButtonAugment-DFy7vWkh.js";import{o as _i}from"./keypress-DD1aQVr0.js";import{D as xi}from"./Drawer-CXWO0nVQ.js";import{B as Ot}from"./ButtonAugment-DbAwCSeR.js";import{D as Ne,T as Wn}from"./index-yg8vr2DA.js";import{T as wi}from"./Content-CSmc2GUv.js";import{E as bi}from"./ellipsis-CRdQranZ.js";import{P as Si}from"./pen-to-square-DxHNIIBu.js";import{T as ki}from"./TextAreaAugment-FdvYFnJr.js";import{C as Ci}from"./chevron-down-BPcCn3Z6.js";import{P as Ti}from"./play-Dd7ujDDf.js";import{M as Mi}from"./index-B-fP3g4F.js";import{M as Zi}from"./MarkdownEditor-CL85cpd_.js";import"./index-DlpZFSR-.js";import"./resize-observer-DdAtcrRr.js";import"./CardAugment-DvO45c5p.js";import"./BaseTextInput-BYcZ2XaJ.js";import"./globals-D0QH3NT1.js";import"./lodash-BHrlUNHT.js";const Yt={maxMS:9e5,initialMS:6e4,mult:2,maxSteps:4};class Ni{constructor(e,t=Yt){ve(this,"timerId",null);ve(this,"currentMS");ve(this,"step",0);ve(this,"params");this.callback=e;const n={...t};n.maxMS<0&&(console.warn("PollingManager: Negative maxMS detected, using default value of 15 minutes"),n.maxMS=Yt.maxMS),n.initialMS<=0&&(console.warn("PollingManager: Negative or zero initialMS detected, using default value of 1 minute"),n.initialMS=Yt.initialMS),n.mult<=0&&(console.warn("PollingManager: Negative or zero multiplier detected, using default value of 2"),n.mult=Yt.mult),n.maxSteps!==void 0&&n.maxSteps<0&&(console.warn("PollingManager: Negative maxSteps detected, using default value of 4"),n.maxSteps=Yt.maxSteps),this.params=n,this.currentMS=this.params.maxMS}startPolling(){this.stopPolling(),this.currentMS=this.params.initialMS,this.step=0,this.safeExecute(),this.scheduleNext()}stopPolling(){this.timerId!==null&&(window.clearTimeout(this.timerId),this.timerId=null)}dispose(){this.stopPolling()}scheduleNext(){this.timerId=window.setTimeout(()=>{if(this.safeExecute(),this.params.maxMS===0){if(this.step++,this.params.maxSteps!==void 0&&this.step>=this.params.maxSteps)return void this.stopPolling()}else this.currentMS<this.params.maxMS&&(this.step++,this.params.maxSteps!==void 0&&this.step>=this.params.maxSteps?(this.currentMS=this.params.maxMS,this.step=0):this.currentMS=Math.min(this.currentMS*this.params.mult,this.params.maxMS));this.scheduleNext()},this.currentMS)}safeExecute(){try{const e=this.callback();e instanceof Promise&&e.catch(t=>console.error("Error in polling callback:",t))}catch(e){console.error("Error in polling callback:",e)}}}class Ai{constructor(e){ve(this,"configs",Le([]));ve(this,"pollingManager");ve(this,"_enableDebugFeatures",Le(!1));ve(this,"_settingsComponentSupported",Le({workspaceContext:!1,mcpServerList:!1,mcpServerImport:!1,orientation:!1,remoteTools:!1,userGuidelines:!1,terminal:!1}));ve(this,"_enableAgentMode",Le(!1));ve(this,"_enableInitialOrientation",Le(!1));ve(this,"_userTier",Le("unknown"));ve(this,"_guidelines",Le({}));this._host=e,this.pollingManager=new Ni(()=>this.requestToolStatus(!1),{maxMS:0,initialMS:2e3,mult:1,maxSteps:150}),this.requestToolStatus(!1)}transformToolDisplay(e){const t=!e.isConfigured,n=e.oauthUrl;if(e.identifier.hostName===gs.remoteToolHost){let r=e.identifier.toolId;switch(typeof r=="string"&&/^\d+$/.test(r)&&(r=Number(r)),r){case pt.GitHubApi:return{displayName:"GitHub",description:"Configure GitHub API access for repository operations",icon:mi,requiresAuthentication:t,authUrl:n};case pt.Linear:return{displayName:"Linear",description:"Configure Linear API access for issue tracking",icon:li,requiresAuthentication:t,authUrl:n};case pt.Jira:return{displayName:"Jira",description:"Configure Jira API access for issue tracking",icon:ci,requiresAuthentication:t,authUrl:n};case pt.Notion:return{displayName:"Notion",description:"Configure Notion API access",icon:oi,requiresAuthentication:t,authUrl:n};case pt.Confluence:return{displayName:"Confluence",description:"Configure Confluence API access",icon:ii,requiresAuthentication:t,authUrl:n};case pt.WebSearch:return{displayName:"Web Search",description:"Configure web search capabilities",icon:ai,requiresAuthentication:t,authUrl:n};case pt.Supabase:return{displayName:"Supabase",description:"Configure Supabase API access",icon:ri,requiresAuthentication:t,authUrl:n};case pt.Glean:return{displayName:"Glean",description:"Configure Glean API access",icon:si,requiresAuthentication:t,authUrl:n};case pt.Unknown:return{displayName:"Unknown",description:"Unknown tool",requiresAuthentication:t,authUrl:n};default:throw new Error(`Unhandled RemoteToolId: ${r}`)}}else if(e.identifier.hostName===gs.localToolHost){const r=e.identifier.toolId;switch(r){case be.readFile:case be.editFile:case be.saveFile:case be.launchProcess:case be.killProcess:case be.readProcess:case be.writeProcess:case be.listProcesses:case be.waitProcess:case be.openBrowser:case be.clarify:case be.onboardingSubAgent:case be.strReplaceEditor:case be.remember:case be.diagnostics:case be.setupScript:case be.readTerminal:return{displayName:e.definition.name.toString(),description:"Local tool",icon:qs,requiresAuthentication:t,authUrl:n};default:throw new Error(`Unhandled LocalToolType: ${r}`)}}else if(e.identifier.hostName===gs.sidecarToolHost){const r=e.identifier.toolId;switch(r){case Lt.codebaseRetrieval:return{displayName:"Code Search",description:"Configure codebase search capabilities",icon:Bn,requiresAuthentication:t,authUrl:n};case Lt.shell:return{displayName:"Shell",description:"Shell",icon:Bn,requiresAuthentication:t,authUrl:n};case Lt.strReplaceEditor:return{displayName:"File Edit",description:"File Editor",icon:Bn,requiresAuthentication:t,authUrl:n};case Lt.webFetch:return{displayName:"Web Fetch",description:"Retrieve information from the web",icon:Bn,requiresAuthentication:t,authUrl:n};case Lt.removeFiles:return{displayName:"Remove Files",description:"Remove files from the codebase",icon:di,requiresAuthentication:t,authUrl:n};case Lt.remember:return{displayName:e.definition.name.toString(),description:"Remember",icon:qs,requiresAuthentication:t,authUrl:n};default:throw new Error(`Unhandled SidecarToolType: ${r}`)}}return{displayName:e.definition.name.toString(),description:e.definition.description||"",requiresAuthentication:t,authUrl:n}}handleMessageFromExtension(e){const t=e.data;switch(t.type){case le.toolConfigInitialize:return this.createConfigsFromHostTools(t.data.hostTools,t.data.toolConfigs),t.data&&t.data.enableDebugFeatures!==void 0&&this._enableDebugFeatures.set(t.data.enableDebugFeatures),t.data&&t.data.settingsComponentSupported!==void 0&&this._settingsComponentSupported.set(t.data.settingsComponentSupported),t.data.enableAgentMode!==void 0&&this._enableAgentMode.set(t.data.enableAgentMode),t.data.enableInitialOrientation!==void 0&&this._enableInitialOrientation.set(t.data.enableInitialOrientation),t.data.userTier!==void 0&&this._userTier.set(t.data.userTier),t.data.guidelines!==void 0&&this._guidelines.set(t.data.guidelines),!0;case le.toolConfigDefinitionsResponse:return this.configs.update(n=>this.createConfigsFromHostTools(t.data.hostTools,[]).map(r=>{const a=n.find(i=>i.name===r.name);return a?{...a,displayName:r.displayName,description:r.description,icon:r.icon,requiresAuthentication:r.requiresAuthentication,authUrl:r.authUrl,isConfigured:r.isConfigured}:r})),!0}return!1}createConfigsFromHostTools(e,t){return e.map(n=>{const r=this.transformToolDisplay(n),a=t.find(o=>o.name===n.definition.name),i=(a==null?void 0:a.isConfigured)??!r.requiresAuthentication;return{config:(a==null?void 0:a.config)??{},configString:JSON.stringify((a==null?void 0:a.config)??{},null,2),isConfigured:i,name:n.definition.name.toString(),displayName:r.displayName,description:r.description,identifier:n.identifier,icon:r.icon,requiresAuthentication:r.requiresAuthentication,authUrl:r.authUrl,showStatus:!1,statusMessage:"",statusType:"info"}})}getConfigs(){return this.configs}isDisplayableTool(e){return["github","linear","notion","jira","confluence","supabase"].includes(e.displayName.toLowerCase())}getDisplayableTools(){return pa(this.configs,e=>{const t=e.filter(r=>this.isDisplayableTool(r)),n=new Map;for(const r of t)n.set(r.displayName,r);return Array.from(n.values()).sort((r,a)=>{const i={GitHub:1,Linear:2,Notion:3},o=Number.MAX_SAFE_INTEGER,c=i[r.displayName]||o,l=i[a.displayName]||o;return c<o&&l<o||c===o&&l===o?c!==l?c-l:r.displayName.localeCompare(a.displayName):c-l})})}saveConfig(e){this.startPolling()}notifyLoaded(){this._host.postMessage({type:le.toolConfigLoaded})}startPolling(){this.pollingManager.startPolling()}requestToolStatus(e=!0){this._host.postMessage({type:le.toolConfigGetDefinitions,data:{useCache:e}})}dispose(){this.pollingManager.dispose()}getEnableDebugFeatures(){return this._enableDebugFeatures}getEnableAgentMode(){return this._enableAgentMode}getEnableInitialOrientation(){return this._enableInitialOrientation}getUserTier(){return this._userTier}getGuidelines(){return this._guidelines}updateLocalUserGuidelines(e){this._guidelines.update(t=>t.userGuidelines?{...t,userGuidelines:{...t.userGuidelines,contents:e,enabled:e.length>0}}:t)}getSettingsComponentSupported(){return this._settingsComponentSupported}}var ie,ws;(function(s){s.assertEqual=e=>e,s.assertIs=function(e){},s.assertNever=function(e){throw new Error},s.arrayToEnum=e=>{const t={};for(const n of e)t[n]=n;return t},s.getValidEnumValues=e=>{const t=s.objectKeys(e).filter(r=>typeof e[e[r]]!="number"),n={};for(const r of t)n[r]=e[r];return s.objectValues(n)},s.objectValues=e=>s.objectKeys(e).map(function(t){return e[t]}),s.objectKeys=typeof Object.keys=="function"?e=>Object.keys(e):e=>{const t=[];for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.push(n);return t},s.find=(e,t)=>{for(const n of e)if(t(n))return n},s.isInteger=typeof Number.isInteger=="function"?e=>Number.isInteger(e):e=>typeof e=="number"&&isFinite(e)&&Math.floor(e)===e,s.joinValues=function(e,t=" | "){return e.map(n=>typeof n=="string"?`'${n}'`:n).join(t)},s.jsonStringifyReplacer=(e,t)=>typeof t=="bigint"?t.toString():t})(ie||(ie={})),(ws||(ws={})).mergeShapes=(s,e)=>({...s,...e});const j=ie.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),mt=s=>{switch(typeof s){case"undefined":return j.undefined;case"string":return j.string;case"number":return isNaN(s)?j.nan:j.number;case"boolean":return j.boolean;case"function":return j.function;case"bigint":return j.bigint;case"symbol":return j.symbol;case"object":return Array.isArray(s)?j.array:s===null?j.null:s.then&&typeof s.then=="function"&&s.catch&&typeof s.catch=="function"?j.promise:typeof Map<"u"&&s instanceof Map?j.map:typeof Set<"u"&&s instanceof Set?j.set:typeof Date<"u"&&s instanceof Date?j.date:j.object;default:return j.unknown}},A=ie.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);let Xe=class ya extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=n=>{this.issues=[...this.issues,n]},this.addIssues=(n=[])=>{this.issues=[...this.issues,...n]};const t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){const t=e||function(a){return a.message},n={_errors:[]},r=a=>{for(const i of a.issues)if(i.code==="invalid_union")i.unionErrors.map(r);else if(i.code==="invalid_return_type")r(i.returnTypeError);else if(i.code==="invalid_arguments")r(i.argumentsError);else if(i.path.length===0)n._errors.push(t(i));else{let o=n,c=0;for(;c<i.path.length;){const l=i.path[c];c===i.path.length-1?(o[l]=o[l]||{_errors:[]},o[l]._errors.push(t(i))):o[l]=o[l]||{_errors:[]},o=o[l],c++}}};return r(this),n}static assert(e){if(!(e instanceof ya))throw new Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,ie.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(e=t=>t.message){const t={},n=[];for(const r of this.issues)r.path.length>0?(t[r.path[0]]=t[r.path[0]]||[],t[r.path[0]].push(e(r))):n.push(e(r));return{formErrors:n,fieldErrors:t}}get formErrors(){return this.flatten()}};Xe.create=s=>new Xe(s);const Vt=(s,e)=>{let t;switch(s.code){case A.invalid_type:t=s.received===j.undefined?"Required":`Expected ${s.expected}, received ${s.received}`;break;case A.invalid_literal:t=`Invalid literal value, expected ${JSON.stringify(s.expected,ie.jsonStringifyReplacer)}`;break;case A.unrecognized_keys:t=`Unrecognized key(s) in object: ${ie.joinValues(s.keys,", ")}`;break;case A.invalid_union:t="Invalid input";break;case A.invalid_union_discriminator:t=`Invalid discriminator value. Expected ${ie.joinValues(s.options)}`;break;case A.invalid_enum_value:t=`Invalid enum value. Expected ${ie.joinValues(s.options)}, received '${s.received}'`;break;case A.invalid_arguments:t="Invalid function arguments";break;case A.invalid_return_type:t="Invalid function return type";break;case A.invalid_date:t="Invalid date";break;case A.invalid_string:typeof s.validation=="object"?"includes"in s.validation?(t=`Invalid input: must include "${s.validation.includes}"`,typeof s.validation.position=="number"&&(t=`${t} at one or more positions greater than or equal to ${s.validation.position}`)):"startsWith"in s.validation?t=`Invalid input: must start with "${s.validation.startsWith}"`:"endsWith"in s.validation?t=`Invalid input: must end with "${s.validation.endsWith}"`:ie.assertNever(s.validation):t=s.validation!=="regex"?`Invalid ${s.validation}`:"Invalid";break;case A.too_small:t=s.type==="array"?`Array must contain ${s.exact?"exactly":s.inclusive?"at least":"more than"} ${s.minimum} element(s)`:s.type==="string"?`String must contain ${s.exact?"exactly":s.inclusive?"at least":"over"} ${s.minimum} character(s)`:s.type==="number"?`Number must be ${s.exact?"exactly equal to ":s.inclusive?"greater than or equal to ":"greater than "}${s.minimum}`:s.type==="date"?`Date must be ${s.exact?"exactly equal to ":s.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(s.minimum))}`:"Invalid input";break;case A.too_big:t=s.type==="array"?`Array must contain ${s.exact?"exactly":s.inclusive?"at most":"less than"} ${s.maximum} element(s)`:s.type==="string"?`String must contain ${s.exact?"exactly":s.inclusive?"at most":"under"} ${s.maximum} character(s)`:s.type==="number"?`Number must be ${s.exact?"exactly":s.inclusive?"less than or equal to":"less than"} ${s.maximum}`:s.type==="bigint"?`BigInt must be ${s.exact?"exactly":s.inclusive?"less than or equal to":"less than"} ${s.maximum}`:s.type==="date"?`Date must be ${s.exact?"exactly":s.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(s.maximum))}`:"Invalid input";break;case A.custom:t="Invalid input";break;case A.invalid_intersection_types:t="Intersection results could not be merged";break;case A.not_multiple_of:t=`Number must be a multiple of ${s.multipleOf}`;break;case A.not_finite:t="Number must be finite";break;default:t=e.defaultError,ie.assertNever(s)}return{message:t}};let _a=Vt;function Yn(){return _a}const Xn=s=>{const{data:e,path:t,errorMaps:n,issueData:r}=s,a=[...t,...r.path||[]],i={...r,path:a};if(r.message!==void 0)return{...r,path:a,message:r.message};let o="";const c=n.filter(l=>!!l).slice().reverse();for(const l of c)o=l(i,{data:e,defaultError:o}).message;return{...r,path:a,message:o}};function I(s,e){const t=Yn(),n=Xn({issueData:e,data:s.data,path:s.path,errorMaps:[s.common.contextualErrorMap,s.schemaErrorMap,t,t===Vt?void 0:Vt].filter(r=>!!r)});s.common.issues.push(n)}let Re=class xa{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(e,t){const n=[];for(const r of t){if(r.status==="aborted")return K;r.status==="dirty"&&e.dirty(),n.push(r.value)}return{status:e.value,value:n}}static async mergeObjectAsync(e,t){const n=[];for(const r of t){const a=await r.key,i=await r.value;n.push({key:a,value:i})}return xa.mergeObjectSync(e,n)}static mergeObjectSync(e,t){const n={};for(const r of t){const{key:a,value:i}=r;if(a.status==="aborted"||i.status==="aborted")return K;a.status==="dirty"&&e.dirty(),i.status==="dirty"&&e.dirty(),a.value==="__proto__"||i.value===void 0&&!r.alwaysSet||(n[a.value]=i.value)}return{status:e.value,value:n}}};const K=Object.freeze({status:"aborted"}),Qn=s=>({status:"dirty",value:s}),Ae=s=>({status:"valid",value:s}),bs=s=>s.status==="aborted",Ss=s=>s.status==="dirty",Et=s=>s.status==="valid",an=s=>typeof Promise<"u"&&s instanceof Promise;function es(s,e,t,n){if(typeof e=="function"?s!==e||!n:!e.has(s))throw new TypeError("Cannot read private member from an object whose class did not declare it");return e.get(s)}function wa(s,e,t,n,r){if(typeof e=="function"?s!==e||!r:!e.has(s))throw new TypeError("Cannot write private member to an object whose class did not declare it");return e.set(s,t),t}var F,Xt,Qt;typeof SuppressedError=="function"&&SuppressedError,function(s){s.errToObj=e=>typeof e=="string"?{message:e}:e||{},s.toString=e=>typeof e=="string"?e:e==null?void 0:e.message}(F||(F={}));let ct=class{constructor(s,e,t,n){this._cachedPath=[],this.parent=s,this.data=e,this._path=t,this._key=n}get path(){return this._cachedPath.length||(this._key instanceof Array?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}};const Bs=(s,e)=>{if(Et(e))return{success:!0,data:e.value};if(!s.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const t=new Xe(s.common.issues);return this._error=t,this._error}}};function Q(s){if(!s)return{};const{errorMap:e,invalid_type_error:t,required_error:n,description:r}=s;if(e&&(t||n))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return e?{errorMap:e,description:r}:{errorMap:(a,i)=>{var o,c;const{message:l}=s;return a.code==="invalid_enum_value"?{message:l??i.defaultError}:i.data===void 0?{message:(o=l??n)!==null&&o!==void 0?o:i.defaultError}:a.code!=="invalid_type"?{message:i.defaultError}:{message:(c=l??t)!==null&&c!==void 0?c:i.defaultError}},description:r}}let te=class{get description(){return this._def.description}_getType(s){return mt(s.data)}_getOrReturnCtx(s,e){return e||{common:s.parent.common,data:s.data,parsedType:mt(s.data),schemaErrorMap:this._def.errorMap,path:s.path,parent:s.parent}}_processInputParams(s){return{status:new Re,ctx:{common:s.parent.common,data:s.data,parsedType:mt(s.data),schemaErrorMap:this._def.errorMap,path:s.path,parent:s.parent}}}_parseSync(s){const e=this._parse(s);if(an(e))throw new Error("Synchronous parse encountered promise.");return e}_parseAsync(s){const e=this._parse(s);return Promise.resolve(e)}parse(s,e){const t=this.safeParse(s,e);if(t.success)return t.data;throw t.error}safeParse(s,e){var t;const n={common:{issues:[],async:(t=e==null?void 0:e.async)!==null&&t!==void 0&&t,contextualErrorMap:e==null?void 0:e.errorMap},path:(e==null?void 0:e.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:s,parsedType:mt(s)},r=this._parseSync({data:s,path:n.path,parent:n});return Bs(n,r)}"~validate"(s){var e,t;const n={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:s,parsedType:mt(s)};if(!this["~standard"].async)try{const r=this._parseSync({data:s,path:[],parent:n});return Et(r)?{value:r.value}:{issues:n.common.issues}}catch(r){!((t=(e=r==null?void 0:r.message)===null||e===void 0?void 0:e.toLowerCase())===null||t===void 0)&&t.includes("encountered")&&(this["~standard"].async=!0),n.common={issues:[],async:!0}}return this._parseAsync({data:s,path:[],parent:n}).then(r=>Et(r)?{value:r.value}:{issues:n.common.issues})}async parseAsync(s,e){const t=await this.safeParseAsync(s,e);if(t.success)return t.data;throw t.error}async safeParseAsync(s,e){const t={common:{issues:[],contextualErrorMap:e==null?void 0:e.errorMap,async:!0},path:(e==null?void 0:e.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:s,parsedType:mt(s)},n=this._parse({data:s,path:t.path,parent:t}),r=await(an(n)?n:Promise.resolve(n));return Bs(t,r)}refine(s,e){const t=n=>typeof e=="string"||e===void 0?{message:e}:typeof e=="function"?e(n):e;return this._refinement((n,r)=>{const a=s(n),i=()=>r.addIssue({code:A.custom,...t(n)});return typeof Promise<"u"&&a instanceof Promise?a.then(o=>!!o||(i(),!1)):!!a||(i(),!1)})}refinement(s,e){return this._refinement((t,n)=>!!s(t)||(n.addIssue(typeof e=="function"?e(t,n):e),!1))}_refinement(s){return new Ge({schema:this,typeName:J.ZodEffects,effect:{type:"refinement",refinement:s}})}superRefine(s){return this._refinement(s)}constructor(s){this.spa=this.safeParseAsync,this._def=s,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:e=>this["~validate"](e)}}optional(){return at.create(this,this._def)}nullable(){return kt.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return It.create(this)}promise(){return Bt.create(this,this._def)}or(s){return mn.create([this,s],this._def)}and(s){return hn.create(this,s,this._def)}transform(s){return new Ge({...Q(this._def),schema:this,typeName:J.ZodEffects,effect:{type:"transform",transform:s}})}default(s){const e=typeof s=="function"?s:()=>s;return new $n({...Q(this._def),innerType:this,defaultValue:e,typeName:J.ZodDefault})}brand(){return new js({typeName:J.ZodBranded,type:this,...Q(this._def)})}catch(s){const e=typeof s=="function"?s:()=>s;return new yn({...Q(this._def),innerType:this,catchValue:e,typeName:J.ZodCatch})}describe(s){return new this.constructor({...this._def,description:s})}pipe(s){return Rs.create(this,s)}readonly(){return _n.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}};const Oi=/^c[^\s-]{8,}$/i,Ei=/^[0-9a-z]+$/,Ii=/^[0-9A-HJKMNP-TV-Z]{26}$/i,Pi=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,ji=/^[a-z0-9_-]{21}$/i,Ri=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,Li=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,Fi=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i;let vs;const zi=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,Di=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,Vi=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,Ui=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,qi=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,Bi=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,ba="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",Ji=new RegExp(`^${ba}$`);function Sa(s){let e="([01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d";return s.precision?e=`${e}\\.\\d{${s.precision}}`:s.precision==null&&(e=`${e}(\\.\\d+)?`),e}function ka(s){let e=`${ba}T${Sa(s)}`;const t=[];return t.push(s.local?"Z?":"Z"),s.offset&&t.push("([+-]\\d{2}:?\\d{2})"),e=`${e}(${t.join("|")})`,new RegExp(`^${e}$`)}function Hi(s,e){if(!Ri.test(s))return!1;try{const[t]=s.split("."),n=t.replace(/-/g,"+").replace(/_/g,"/").padEnd(t.length+(4-t.length%4)%4,"="),r=JSON.parse(atob(n));return typeof r=="object"&&r!==null&&!(!r.typ||!r.alg)&&(!e||r.alg===e)}catch{return!1}}function Ki(s,e){return!(e!=="v4"&&e||!Di.test(s))||!(e!=="v6"&&e||!Ui.test(s))}let Ut=class en extends te{_parse(e){if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==j.string){const i=this._getOrReturnCtx(e);return I(i,{code:A.invalid_type,expected:j.string,received:i.parsedType}),K}const t=new Re;let n;for(const i of this._def.checks)if(i.kind==="min")e.data.length<i.value&&(n=this._getOrReturnCtx(e,n),I(n,{code:A.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),t.dirty());else if(i.kind==="max")e.data.length>i.value&&(n=this._getOrReturnCtx(e,n),I(n,{code:A.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),t.dirty());else if(i.kind==="length"){const o=e.data.length>i.value,c=e.data.length<i.value;(o||c)&&(n=this._getOrReturnCtx(e,n),o?I(n,{code:A.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}):c&&I(n,{code:A.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}),t.dirty())}else if(i.kind==="email")Fi.test(e.data)||(n=this._getOrReturnCtx(e,n),I(n,{validation:"email",code:A.invalid_string,message:i.message}),t.dirty());else if(i.kind==="emoji")vs||(vs=new RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),vs.test(e.data)||(n=this._getOrReturnCtx(e,n),I(n,{validation:"emoji",code:A.invalid_string,message:i.message}),t.dirty());else if(i.kind==="uuid")Pi.test(e.data)||(n=this._getOrReturnCtx(e,n),I(n,{validation:"uuid",code:A.invalid_string,message:i.message}),t.dirty());else if(i.kind==="nanoid")ji.test(e.data)||(n=this._getOrReturnCtx(e,n),I(n,{validation:"nanoid",code:A.invalid_string,message:i.message}),t.dirty());else if(i.kind==="cuid")Oi.test(e.data)||(n=this._getOrReturnCtx(e,n),I(n,{validation:"cuid",code:A.invalid_string,message:i.message}),t.dirty());else if(i.kind==="cuid2")Ei.test(e.data)||(n=this._getOrReturnCtx(e,n),I(n,{validation:"cuid2",code:A.invalid_string,message:i.message}),t.dirty());else if(i.kind==="ulid")Ii.test(e.data)||(n=this._getOrReturnCtx(e,n),I(n,{validation:"ulid",code:A.invalid_string,message:i.message}),t.dirty());else if(i.kind==="url")try{new URL(e.data)}catch{n=this._getOrReturnCtx(e,n),I(n,{validation:"url",code:A.invalid_string,message:i.message}),t.dirty()}else i.kind==="regex"?(i.regex.lastIndex=0,i.regex.test(e.data)||(n=this._getOrReturnCtx(e,n),I(n,{validation:"regex",code:A.invalid_string,message:i.message}),t.dirty())):i.kind==="trim"?e.data=e.data.trim():i.kind==="includes"?e.data.includes(i.value,i.position)||(n=this._getOrReturnCtx(e,n),I(n,{code:A.invalid_string,validation:{includes:i.value,position:i.position},message:i.message}),t.dirty()):i.kind==="toLowerCase"?e.data=e.data.toLowerCase():i.kind==="toUpperCase"?e.data=e.data.toUpperCase():i.kind==="startsWith"?e.data.startsWith(i.value)||(n=this._getOrReturnCtx(e,n),I(n,{code:A.invalid_string,validation:{startsWith:i.value},message:i.message}),t.dirty()):i.kind==="endsWith"?e.data.endsWith(i.value)||(n=this._getOrReturnCtx(e,n),I(n,{code:A.invalid_string,validation:{endsWith:i.value},message:i.message}),t.dirty()):i.kind==="datetime"?ka(i).test(e.data)||(n=this._getOrReturnCtx(e,n),I(n,{code:A.invalid_string,validation:"datetime",message:i.message}),t.dirty()):i.kind==="date"?Ji.test(e.data)||(n=this._getOrReturnCtx(e,n),I(n,{code:A.invalid_string,validation:"date",message:i.message}),t.dirty()):i.kind==="time"?new RegExp(`^${Sa(i)}$`).test(e.data)||(n=this._getOrReturnCtx(e,n),I(n,{code:A.invalid_string,validation:"time",message:i.message}),t.dirty()):i.kind==="duration"?Li.test(e.data)||(n=this._getOrReturnCtx(e,n),I(n,{validation:"duration",code:A.invalid_string,message:i.message}),t.dirty()):i.kind==="ip"?(r=e.data,((a=i.version)!=="v4"&&a||!zi.test(r))&&(a!=="v6"&&a||!Vi.test(r))&&(n=this._getOrReturnCtx(e,n),I(n,{validation:"ip",code:A.invalid_string,message:i.message}),t.dirty())):i.kind==="jwt"?Hi(e.data,i.alg)||(n=this._getOrReturnCtx(e,n),I(n,{validation:"jwt",code:A.invalid_string,message:i.message}),t.dirty()):i.kind==="cidr"?Ki(e.data,i.version)||(n=this._getOrReturnCtx(e,n),I(n,{validation:"cidr",code:A.invalid_string,message:i.message}),t.dirty()):i.kind==="base64"?qi.test(e.data)||(n=this._getOrReturnCtx(e,n),I(n,{validation:"base64",code:A.invalid_string,message:i.message}),t.dirty()):i.kind==="base64url"?Bi.test(e.data)||(n=this._getOrReturnCtx(e,n),I(n,{validation:"base64url",code:A.invalid_string,message:i.message}),t.dirty()):ie.assertNever(i);var r,a;return{status:t.value,value:e.data}}_regex(e,t,n){return this.refinement(r=>e.test(r),{validation:t,code:A.invalid_string,...F.errToObj(n)})}_addCheck(e){return new en({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...F.errToObj(e)})}url(e){return this._addCheck({kind:"url",...F.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...F.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...F.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...F.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...F.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...F.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...F.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...F.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...F.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...F.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...F.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...F.errToObj(e)})}datetime(e){var t,n;return typeof e=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:(e==null?void 0:e.precision)===void 0?null:e==null?void 0:e.precision,offset:(t=e==null?void 0:e.offset)!==null&&t!==void 0&&t,local:(n=e==null?void 0:e.local)!==null&&n!==void 0&&n,...F.errToObj(e==null?void 0:e.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return typeof e=="string"?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:(e==null?void 0:e.precision)===void 0?null:e==null?void 0:e.precision,...F.errToObj(e==null?void 0:e.message)})}duration(e){return this._addCheck({kind:"duration",...F.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...F.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:t==null?void 0:t.position,...F.errToObj(t==null?void 0:t.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...F.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...F.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...F.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...F.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...F.errToObj(t)})}nonempty(e){return this.min(1,F.errToObj(e))}trim(){return new en({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new en({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new en({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>e.kind==="datetime")}get isDate(){return!!this._def.checks.find(e=>e.kind==="date")}get isTime(){return!!this._def.checks.find(e=>e.kind==="time")}get isDuration(){return!!this._def.checks.find(e=>e.kind==="duration")}get isEmail(){return!!this._def.checks.find(e=>e.kind==="email")}get isURL(){return!!this._def.checks.find(e=>e.kind==="url")}get isEmoji(){return!!this._def.checks.find(e=>e.kind==="emoji")}get isUUID(){return!!this._def.checks.find(e=>e.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(e=>e.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(e=>e.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(e=>e.kind==="cuid2")}get isULID(){return!!this._def.checks.find(e=>e.kind==="ulid")}get isIP(){return!!this._def.checks.find(e=>e.kind==="ip")}get isCIDR(){return!!this._def.checks.find(e=>e.kind==="cidr")}get isBase64(){return!!this._def.checks.find(e=>e.kind==="base64")}get isBase64url(){return!!this._def.checks.find(e=>e.kind==="base64url")}get minLength(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}};function Gi(s,e){const t=(s.toString().split(".")[1]||"").length,n=(e.toString().split(".")[1]||"").length,r=t>n?t:n;return parseInt(s.toFixed(r).replace(".",""))%parseInt(e.toFixed(r).replace(".",""))/Math.pow(10,r)}Ut.create=s=>{var e;return new Ut({checks:[],typeName:J.ZodString,coerce:(e=s==null?void 0:s.coerce)!==null&&e!==void 0&&e,...Q(s)})};let on=class ks extends te{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==j.number){const r=this._getOrReturnCtx(e);return I(r,{code:A.invalid_type,expected:j.number,received:r.parsedType}),K}let t;const n=new Re;for(const r of this._def.checks)r.kind==="int"?ie.isInteger(e.data)||(t=this._getOrReturnCtx(e,t),I(t,{code:A.invalid_type,expected:"integer",received:"float",message:r.message}),n.dirty()):r.kind==="min"?(r.inclusive?e.data<r.value:e.data<=r.value)&&(t=this._getOrReturnCtx(e,t),I(t,{code:A.too_small,minimum:r.value,type:"number",inclusive:r.inclusive,exact:!1,message:r.message}),n.dirty()):r.kind==="max"?(r.inclusive?e.data>r.value:e.data>=r.value)&&(t=this._getOrReturnCtx(e,t),I(t,{code:A.too_big,maximum:r.value,type:"number",inclusive:r.inclusive,exact:!1,message:r.message}),n.dirty()):r.kind==="multipleOf"?Gi(e.data,r.value)!==0&&(t=this._getOrReturnCtx(e,t),I(t,{code:A.not_multiple_of,multipleOf:r.value,message:r.message}),n.dirty()):r.kind==="finite"?Number.isFinite(e.data)||(t=this._getOrReturnCtx(e,t),I(t,{code:A.not_finite,message:r.message}),n.dirty()):ie.assertNever(r);return{status:n.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,F.toString(t))}gt(e,t){return this.setLimit("min",e,!1,F.toString(t))}lte(e,t){return this.setLimit("max",e,!0,F.toString(t))}lt(e,t){return this.setLimit("max",e,!1,F.toString(t))}setLimit(e,t,n,r){return new ks({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:n,message:F.toString(r)}]})}_addCheck(e){return new ks({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:F.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:F.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:F.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:F.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:F.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:F.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:F.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:F.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:F.toString(e)})}get minValue(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>e.kind==="int"||e.kind==="multipleOf"&&ie.isInteger(e.value))}get isFinite(){let e=null,t=null;for(const n of this._def.checks){if(n.kind==="finite"||n.kind==="int"||n.kind==="multipleOf")return!0;n.kind==="min"?(t===null||n.value>t)&&(t=n.value):n.kind==="max"&&(e===null||n.value<e)&&(e=n.value)}return Number.isFinite(t)&&Number.isFinite(e)}};on.create=s=>new on({checks:[],typeName:J.ZodNumber,coerce:(s==null?void 0:s.coerce)||!1,...Q(s)});let cn=class Cs extends te{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==j.bigint)return this._getInvalidInput(e);let t;const n=new Re;for(const r of this._def.checks)r.kind==="min"?(r.inclusive?e.data<r.value:e.data<=r.value)&&(t=this._getOrReturnCtx(e,t),I(t,{code:A.too_small,type:"bigint",minimum:r.value,inclusive:r.inclusive,message:r.message}),n.dirty()):r.kind==="max"?(r.inclusive?e.data>r.value:e.data>=r.value)&&(t=this._getOrReturnCtx(e,t),I(t,{code:A.too_big,type:"bigint",maximum:r.value,inclusive:r.inclusive,message:r.message}),n.dirty()):r.kind==="multipleOf"?e.data%r.value!==BigInt(0)&&(t=this._getOrReturnCtx(e,t),I(t,{code:A.not_multiple_of,multipleOf:r.value,message:r.message}),n.dirty()):ie.assertNever(r);return{status:n.value,value:e.data}}_getInvalidInput(e){const t=this._getOrReturnCtx(e);return I(t,{code:A.invalid_type,expected:j.bigint,received:t.parsedType}),K}gte(e,t){return this.setLimit("min",e,!0,F.toString(t))}gt(e,t){return this.setLimit("min",e,!1,F.toString(t))}lte(e,t){return this.setLimit("max",e,!0,F.toString(t))}lt(e,t){return this.setLimit("max",e,!1,F.toString(t))}setLimit(e,t,n,r){return new Cs({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:n,message:F.toString(r)}]})}_addCheck(e){return new Cs({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:F.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:F.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:F.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:F.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:F.toString(t)})}get minValue(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}};cn.create=s=>{var e;return new cn({checks:[],typeName:J.ZodBigInt,coerce:(e=s==null?void 0:s.coerce)!==null&&e!==void 0&&e,...Q(s)})};let ln=class extends te{_parse(s){if(this._def.coerce&&(s.data=!!s.data),this._getType(s)!==j.boolean){const e=this._getOrReturnCtx(s);return I(e,{code:A.invalid_type,expected:j.boolean,received:e.parsedType}),K}return Ae(s.data)}};ln.create=s=>new ln({typeName:J.ZodBoolean,coerce:(s==null?void 0:s.coerce)||!1,...Q(s)});let dn=class Ca extends te{_parse(e){if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==j.date){const r=this._getOrReturnCtx(e);return I(r,{code:A.invalid_type,expected:j.date,received:r.parsedType}),K}if(isNaN(e.data.getTime()))return I(this._getOrReturnCtx(e),{code:A.invalid_date}),K;const t=new Re;let n;for(const r of this._def.checks)r.kind==="min"?e.data.getTime()<r.value&&(n=this._getOrReturnCtx(e,n),I(n,{code:A.too_small,message:r.message,inclusive:!0,exact:!1,minimum:r.value,type:"date"}),t.dirty()):r.kind==="max"?e.data.getTime()>r.value&&(n=this._getOrReturnCtx(e,n),I(n,{code:A.too_big,message:r.message,inclusive:!0,exact:!1,maximum:r.value,type:"date"}),t.dirty()):ie.assertNever(r);return{status:t.value,value:new Date(e.data.getTime())}}_addCheck(e){return new Ca({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:F.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:F.toString(t)})}get minDate(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e!=null?new Date(e):null}get maxDate(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e!=null?new Date(e):null}};dn.create=s=>new dn({checks:[],coerce:(s==null?void 0:s.coerce)||!1,typeName:J.ZodDate,...Q(s)});let ts=class extends te{_parse(s){if(this._getType(s)!==j.symbol){const e=this._getOrReturnCtx(s);return I(e,{code:A.invalid_type,expected:j.symbol,received:e.parsedType}),K}return Ae(s.data)}};ts.create=s=>new ts({typeName:J.ZodSymbol,...Q(s)});let un=class extends te{_parse(s){if(this._getType(s)!==j.undefined){const e=this._getOrReturnCtx(s);return I(e,{code:A.invalid_type,expected:j.undefined,received:e.parsedType}),K}return Ae(s.data)}};un.create=s=>new un({typeName:J.ZodUndefined,...Q(s)});let pn=class extends te{_parse(s){if(this._getType(s)!==j.null){const e=this._getOrReturnCtx(s);return I(e,{code:A.invalid_type,expected:j.null,received:e.parsedType}),K}return Ae(s.data)}};pn.create=s=>new pn({typeName:J.ZodNull,...Q(s)});let qt=class extends te{constructor(){super(...arguments),this._any=!0}_parse(s){return Ae(s.data)}};qt.create=s=>new qt({typeName:J.ZodAny,...Q(s)});let Nt=class extends te{constructor(){super(...arguments),this._unknown=!0}_parse(s){return Ae(s.data)}};Nt.create=s=>new Nt({typeName:J.ZodUnknown,...Q(s)});let gt=class extends te{_parse(s){const e=this._getOrReturnCtx(s);return I(e,{code:A.invalid_type,expected:j.never,received:e.parsedType}),K}};gt.create=s=>new gt({typeName:J.ZodNever,...Q(s)});let ns=class extends te{_parse(s){if(this._getType(s)!==j.undefined){const e=this._getOrReturnCtx(s);return I(e,{code:A.invalid_type,expected:j.void,received:e.parsedType}),K}return Ae(s.data)}};ns.create=s=>new ns({typeName:J.ZodVoid,...Q(s)});let It=class Jn extends te{_parse(e){const{ctx:t,status:n}=this._processInputParams(e),r=this._def;if(t.parsedType!==j.array)return I(t,{code:A.invalid_type,expected:j.array,received:t.parsedType}),K;if(r.exactLength!==null){const i=t.data.length>r.exactLength.value,o=t.data.length<r.exactLength.value;(i||o)&&(I(t,{code:i?A.too_big:A.too_small,minimum:o?r.exactLength.value:void 0,maximum:i?r.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:r.exactLength.message}),n.dirty())}if(r.minLength!==null&&t.data.length<r.minLength.value&&(I(t,{code:A.too_small,minimum:r.minLength.value,type:"array",inclusive:!0,exact:!1,message:r.minLength.message}),n.dirty()),r.maxLength!==null&&t.data.length>r.maxLength.value&&(I(t,{code:A.too_big,maximum:r.maxLength.value,type:"array",inclusive:!0,exact:!1,message:r.maxLength.message}),n.dirty()),t.common.async)return Promise.all([...t.data].map((i,o)=>r.type._parseAsync(new ct(t,i,t.path,o)))).then(i=>Re.mergeArray(n,i));const a=[...t.data].map((i,o)=>r.type._parseSync(new ct(t,i,t.path,o)));return Re.mergeArray(n,a)}get element(){return this._def.type}min(e,t){return new Jn({...this._def,minLength:{value:e,message:F.toString(t)}})}max(e,t){return new Jn({...this._def,maxLength:{value:e,message:F.toString(t)}})}length(e,t){return new Jn({...this._def,exactLength:{value:e,message:F.toString(t)}})}nonempty(e){return this.min(1,e)}};function Ft(s){if(s instanceof Ue){const e={};for(const t in s.shape){const n=s.shape[t];e[t]=at.create(Ft(n))}return new Ue({...s._def,shape:()=>e})}return s instanceof It?new It({...s._def,type:Ft(s.element)}):s instanceof at?at.create(Ft(s.unwrap())):s instanceof kt?kt.create(Ft(s.unwrap())):s instanceof St?St.create(s.items.map(e=>Ft(e))):s}It.create=(s,e)=>new It({type:s,minLength:null,maxLength:null,exactLength:null,typeName:J.ZodArray,...Q(e)});let Ue=class He extends te{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;const e=this._def.shape(),t=ie.objectKeys(e);return this._cached={shape:e,keys:t}}_parse(e){if(this._getType(e)!==j.object){const c=this._getOrReturnCtx(e);return I(c,{code:A.invalid_type,expected:j.object,received:c.parsedType}),K}const{status:t,ctx:n}=this._processInputParams(e),{shape:r,keys:a}=this._getCached(),i=[];if(!(this._def.catchall instanceof gt&&this._def.unknownKeys==="strip"))for(const c in n.data)a.includes(c)||i.push(c);const o=[];for(const c of a){const l=r[c],d=n.data[c];o.push({key:{status:"valid",value:c},value:l._parse(new ct(n,d,n.path,c)),alwaysSet:c in n.data})}if(this._def.catchall instanceof gt){const c=this._def.unknownKeys;if(c==="passthrough")for(const l of i)o.push({key:{status:"valid",value:l},value:{status:"valid",value:n.data[l]}});else if(c==="strict")i.length>0&&(I(n,{code:A.unrecognized_keys,keys:i}),t.dirty());else if(c!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const c=this._def.catchall;for(const l of i){const d=n.data[l];o.push({key:{status:"valid",value:l},value:c._parse(new ct(n,d,n.path,l)),alwaysSet:l in n.data})}}return n.common.async?Promise.resolve().then(async()=>{const c=[];for(const l of o){const d=await l.key,u=await l.value;c.push({key:d,value:u,alwaysSet:l.alwaysSet})}return c}).then(c=>Re.mergeObjectSync(t,c)):Re.mergeObjectSync(t,o)}get shape(){return this._def.shape()}strict(e){return F.errToObj,new He({...this._def,unknownKeys:"strict",...e!==void 0?{errorMap:(t,n)=>{var r,a,i,o;const c=(i=(a=(r=this._def).errorMap)===null||a===void 0?void 0:a.call(r,t,n).message)!==null&&i!==void 0?i:n.defaultError;return t.code==="unrecognized_keys"?{message:(o=F.errToObj(e).message)!==null&&o!==void 0?o:c}:{message:c}}}:{}})}strip(){return new He({...this._def,unknownKeys:"strip"})}passthrough(){return new He({...this._def,unknownKeys:"passthrough"})}extend(e){return new He({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new He({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:J.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new He({...this._def,catchall:e})}pick(e){const t={};return ie.objectKeys(e).forEach(n=>{e[n]&&this.shape[n]&&(t[n]=this.shape[n])}),new He({...this._def,shape:()=>t})}omit(e){const t={};return ie.objectKeys(this.shape).forEach(n=>{e[n]||(t[n]=this.shape[n])}),new He({...this._def,shape:()=>t})}deepPartial(){return Ft(this)}partial(e){const t={};return ie.objectKeys(this.shape).forEach(n=>{const r=this.shape[n];e&&!e[n]?t[n]=r:t[n]=r.optional()}),new He({...this._def,shape:()=>t})}required(e){const t={};return ie.objectKeys(this.shape).forEach(n=>{if(e&&!e[n])t[n]=this.shape[n];else{let r=this.shape[n];for(;r instanceof at;)r=r._def.innerType;t[n]=r}}),new He({...this._def,shape:()=>t})}keyof(){return Ea(ie.objectKeys(this.shape))}};Ue.create=(s,e)=>new Ue({shape:()=>s,unknownKeys:"strip",catchall:gt.create(),typeName:J.ZodObject,...Q(e)}),Ue.strictCreate=(s,e)=>new Ue({shape:()=>s,unknownKeys:"strict",catchall:gt.create(),typeName:J.ZodObject,...Q(e)}),Ue.lazycreate=(s,e)=>new Ue({shape:s,unknownKeys:"strip",catchall:gt.create(),typeName:J.ZodObject,...Q(e)});let mn=class extends te{_parse(s){const{ctx:e}=this._processInputParams(s),t=this._def.options;if(e.common.async)return Promise.all(t.map(async n=>{const r={...e,common:{...e.common,issues:[]},parent:null};return{result:await n._parseAsync({data:e.data,path:e.path,parent:r}),ctx:r}})).then(function(n){for(const a of n)if(a.result.status==="valid")return a.result;for(const a of n)if(a.result.status==="dirty")return e.common.issues.push(...a.ctx.common.issues),a.result;const r=n.map(a=>new Xe(a.ctx.common.issues));return I(e,{code:A.invalid_union,unionErrors:r}),K});{let n;const r=[];for(const i of t){const o={...e,common:{...e.common,issues:[]},parent:null},c=i._parseSync({data:e.data,path:e.path,parent:o});if(c.status==="valid")return c;c.status!=="dirty"||n||(n={result:c,ctx:o}),o.common.issues.length&&r.push(o.common.issues)}if(n)return e.common.issues.push(...n.ctx.common.issues),n.result;const a=r.map(i=>new Xe(i));return I(e,{code:A.invalid_union,unionErrors:a}),K}}get options(){return this._def.options}};mn.create=(s,e)=>new mn({options:s,typeName:J.ZodUnion,...Q(e)});const yt=s=>s instanceof fn?yt(s.schema):s instanceof Ge?yt(s.innerType()):s instanceof gn?[s.value]:s instanceof Dn?s.options:s instanceof vn?ie.objectValues(s.enum):s instanceof $n?yt(s._def.innerType):s instanceof un?[void 0]:s instanceof pn?[null]:s instanceof at?[void 0,...yt(s.unwrap())]:s instanceof kt?[null,...yt(s.unwrap())]:s instanceof js||s instanceof _n?yt(s.unwrap()):s instanceof yn?yt(s._def.innerType):[];let Ta=class Ma extends te{_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==j.object)return I(t,{code:A.invalid_type,expected:j.object,received:t.parsedType}),K;const n=this.discriminator,r=t.data[n],a=this.optionsMap.get(r);return a?t.common.async?a._parseAsync({data:t.data,path:t.path,parent:t}):a._parseSync({data:t.data,path:t.path,parent:t}):(I(t,{code:A.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[n]}),K)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,n){const r=new Map;for(const a of t){const i=yt(a.shape[e]);if(!i.length)throw new Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(const o of i){if(r.has(o))throw new Error(`Discriminator property ${String(e)} has duplicate value ${String(o)}`);r.set(o,a)}}return new Ma({typeName:J.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:r,...Q(n)})}};function Ts(s,e){const t=mt(s),n=mt(e);if(s===e)return{valid:!0,data:s};if(t===j.object&&n===j.object){const r=ie.objectKeys(e),a=ie.objectKeys(s).filter(o=>r.indexOf(o)!==-1),i={...s,...e};for(const o of a){const c=Ts(s[o],e[o]);if(!c.valid)return{valid:!1};i[o]=c.data}return{valid:!0,data:i}}if(t===j.array&&n===j.array){if(s.length!==e.length)return{valid:!1};const r=[];for(let a=0;a<s.length;a++){const i=Ts(s[a],e[a]);if(!i.valid)return{valid:!1};r.push(i.data)}return{valid:!0,data:r}}return t===j.date&&n===j.date&&+s==+e?{valid:!0,data:s}:{valid:!1}}let hn=class extends te{_parse(s){const{status:e,ctx:t}=this._processInputParams(s),n=(r,a)=>{if(bs(r)||bs(a))return K;const i=Ts(r.value,a.value);return i.valid?((Ss(r)||Ss(a))&&e.dirty(),{status:e.value,value:i.data}):(I(t,{code:A.invalid_intersection_types}),K)};return t.common.async?Promise.all([this._def.left._parseAsync({data:t.data,path:t.path,parent:t}),this._def.right._parseAsync({data:t.data,path:t.path,parent:t})]).then(([r,a])=>n(r,a)):n(this._def.left._parseSync({data:t.data,path:t.path,parent:t}),this._def.right._parseSync({data:t.data,path:t.path,parent:t}))}};hn.create=(s,e,t)=>new hn({left:s,right:e,typeName:J.ZodIntersection,...Q(t)});let St=class Za extends te{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==j.array)return I(n,{code:A.invalid_type,expected:j.array,received:n.parsedType}),K;if(n.data.length<this._def.items.length)return I(n,{code:A.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),K;!this._def.rest&&n.data.length>this._def.items.length&&(I(n,{code:A.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());const r=[...n.data].map((a,i)=>{const o=this._def.items[i]||this._def.rest;return o?o._parse(new ct(n,a,n.path,i)):null}).filter(a=>!!a);return n.common.async?Promise.all(r).then(a=>Re.mergeArray(t,a)):Re.mergeArray(t,r)}get items(){return this._def.items}rest(e){return new Za({...this._def,rest:e})}};St.create=(s,e)=>{if(!Array.isArray(s))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new St({items:s,typeName:J.ZodTuple,rest:null,...Q(e)})};let Na=class Aa extends te{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==j.object)return I(n,{code:A.invalid_type,expected:j.object,received:n.parsedType}),K;const r=[],a=this._def.keyType,i=this._def.valueType;for(const o in n.data)r.push({key:a._parse(new ct(n,o,n.path,o)),value:i._parse(new ct(n,n.data[o],n.path,o)),alwaysSet:o in n.data});return n.common.async?Re.mergeObjectAsync(t,r):Re.mergeObjectSync(t,r)}get element(){return this._def.valueType}static create(e,t,n){return new Aa(t instanceof te?{keyType:e,valueType:t,typeName:J.ZodRecord,...Q(n)}:{keyType:Ut.create(),valueType:e,typeName:J.ZodRecord,...Q(t)})}},ss=class extends te{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(s){const{status:e,ctx:t}=this._processInputParams(s);if(t.parsedType!==j.map)return I(t,{code:A.invalid_type,expected:j.map,received:t.parsedType}),K;const n=this._def.keyType,r=this._def.valueType,a=[...t.data.entries()].map(([i,o],c)=>({key:n._parse(new ct(t,i,t.path,[c,"key"])),value:r._parse(new ct(t,o,t.path,[c,"value"]))}));if(t.common.async){const i=new Map;return Promise.resolve().then(async()=>{for(const o of a){const c=await o.key,l=await o.value;if(c.status==="aborted"||l.status==="aborted")return K;c.status!=="dirty"&&l.status!=="dirty"||e.dirty(),i.set(c.value,l.value)}return{status:e.value,value:i}})}{const i=new Map;for(const o of a){const c=o.key,l=o.value;if(c.status==="aborted"||l.status==="aborted")return K;c.status!=="dirty"&&l.status!=="dirty"||e.dirty(),i.set(c.value,l.value)}return{status:e.value,value:i}}}};ss.create=(s,e,t)=>new ss({valueType:e,keyType:s,typeName:J.ZodMap,...Q(t)});let rs=class Ms extends te{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==j.set)return I(n,{code:A.invalid_type,expected:j.set,received:n.parsedType}),K;const r=this._def;r.minSize!==null&&n.data.size<r.minSize.value&&(I(n,{code:A.too_small,minimum:r.minSize.value,type:"set",inclusive:!0,exact:!1,message:r.minSize.message}),t.dirty()),r.maxSize!==null&&n.data.size>r.maxSize.value&&(I(n,{code:A.too_big,maximum:r.maxSize.value,type:"set",inclusive:!0,exact:!1,message:r.maxSize.message}),t.dirty());const a=this._def.valueType;function i(c){const l=new Set;for(const d of c){if(d.status==="aborted")return K;d.status==="dirty"&&t.dirty(),l.add(d.value)}return{status:t.value,value:l}}const o=[...n.data.values()].map((c,l)=>a._parse(new ct(n,c,n.path,l)));return n.common.async?Promise.all(o).then(c=>i(c)):i(o)}min(e,t){return new Ms({...this._def,minSize:{value:e,message:F.toString(t)}})}max(e,t){return new Ms({...this._def,maxSize:{value:e,message:F.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}};rs.create=(s,e)=>new rs({valueType:s,minSize:null,maxSize:null,typeName:J.ZodSet,...Q(e)});let Oa=class Hn extends te{constructor(){super(...arguments),this.validate=this.implement}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==j.function)return I(t,{code:A.invalid_type,expected:j.function,received:t.parsedType}),K;function n(o,c){return Xn({data:o,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,Yn(),Vt].filter(l=>!!l),issueData:{code:A.invalid_arguments,argumentsError:c}})}function r(o,c){return Xn({data:o,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,Yn(),Vt].filter(l=>!!l),issueData:{code:A.invalid_return_type,returnTypeError:c}})}const a={errorMap:t.common.contextualErrorMap},i=t.data;if(this._def.returns instanceof Bt){const o=this;return Ae(async function(...c){const l=new Xe([]),d=await o._def.args.parseAsync(c,a).catch(f=>{throw l.addIssue(n(c,f)),l}),u=await Reflect.apply(i,this,d);return await o._def.returns._def.type.parseAsync(u,a).catch(f=>{throw l.addIssue(r(u,f)),l})})}{const o=this;return Ae(function(...c){const l=o._def.args.safeParse(c,a);if(!l.success)throw new Xe([n(c,l.error)]);const d=Reflect.apply(i,this,l.data),u=o._def.returns.safeParse(d,a);if(!u.success)throw new Xe([r(d,u.error)]);return u.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new Hn({...this._def,args:St.create(e).rest(Nt.create())})}returns(e){return new Hn({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,t,n){return new Hn({args:e||St.create([]).rest(Nt.create()),returns:t||Nt.create(),typeName:J.ZodFunction,...Q(n)})}},fn=class extends te{get schema(){return this._def.getter()}_parse(s){const{ctx:e}=this._processInputParams(s);return this._def.getter()._parse({data:e.data,path:e.path,parent:e})}};fn.create=(s,e)=>new fn({getter:s,typeName:J.ZodLazy,...Q(e)});let gn=class extends te{_parse(s){if(s.data!==this._def.value){const e=this._getOrReturnCtx(s);return I(e,{received:e.data,code:A.invalid_literal,expected:this._def.value}),K}return{status:"valid",value:s.data}}get value(){return this._def.value}};function Ea(s,e){return new Dn({values:s,typeName:J.ZodEnum,...Q(e)})}gn.create=(s,e)=>new gn({value:s,typeName:J.ZodLiteral,...Q(e)});let Dn=class Zs extends te{constructor(){super(...arguments),Xt.set(this,void 0)}_parse(e){if(typeof e.data!="string"){const t=this._getOrReturnCtx(e),n=this._def.values;return I(t,{expected:ie.joinValues(n),received:t.parsedType,code:A.invalid_type}),K}if(es(this,Xt)||wa(this,Xt,new Set(this._def.values)),!es(this,Xt).has(e.data)){const t=this._getOrReturnCtx(e),n=this._def.values;return I(t,{received:t.data,code:A.invalid_enum_value,options:n}),K}return Ae(e.data)}get options(){return this._def.values}get enum(){const e={};for(const t of this._def.values)e[t]=t;return e}get Values(){const e={};for(const t of this._def.values)e[t]=t;return e}get Enum(){const e={};for(const t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return Zs.create(e,{...this._def,...t})}exclude(e,t=this._def){return Zs.create(this.options.filter(n=>!e.includes(n)),{...this._def,...t})}};Xt=new WeakMap,Dn.create=Ea;let vn=class extends te{constructor(){super(...arguments),Qt.set(this,void 0)}_parse(s){const e=ie.getValidEnumValues(this._def.values),t=this._getOrReturnCtx(s);if(t.parsedType!==j.string&&t.parsedType!==j.number){const n=ie.objectValues(e);return I(t,{expected:ie.joinValues(n),received:t.parsedType,code:A.invalid_type}),K}if(es(this,Qt)||wa(this,Qt,new Set(ie.getValidEnumValues(this._def.values))),!es(this,Qt).has(s.data)){const n=ie.objectValues(e);return I(t,{received:t.data,code:A.invalid_enum_value,options:n}),K}return Ae(s.data)}get enum(){return this._def.values}};Qt=new WeakMap,vn.create=(s,e)=>new vn({values:s,typeName:J.ZodNativeEnum,...Q(e)});let Bt=class extends te{unwrap(){return this._def.type}_parse(s){const{ctx:e}=this._processInputParams(s);if(e.parsedType!==j.promise&&e.common.async===!1)return I(e,{code:A.invalid_type,expected:j.promise,received:e.parsedType}),K;const t=e.parsedType===j.promise?e.data:Promise.resolve(e.data);return Ae(t.then(n=>this._def.type.parseAsync(n,{path:e.path,errorMap:e.common.contextualErrorMap})))}};Bt.create=(s,e)=>new Bt({type:s,typeName:J.ZodPromise,...Q(e)});let Ge=class extends te{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===J.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(s){const{status:e,ctx:t}=this._processInputParams(s),n=this._def.effect||null,r={addIssue:a=>{I(t,a),a.fatal?e.abort():e.dirty()},get path(){return t.path}};if(r.addIssue=r.addIssue.bind(r),n.type==="preprocess"){const a=n.transform(t.data,r);if(t.common.async)return Promise.resolve(a).then(async i=>{if(e.value==="aborted")return K;const o=await this._def.schema._parseAsync({data:i,path:t.path,parent:t});return o.status==="aborted"?K:o.status==="dirty"||e.value==="dirty"?Qn(o.value):o});{if(e.value==="aborted")return K;const i=this._def.schema._parseSync({data:a,path:t.path,parent:t});return i.status==="aborted"?K:i.status==="dirty"||e.value==="dirty"?Qn(i.value):i}}if(n.type==="refinement"){const a=i=>{const o=n.refinement(i,r);if(t.common.async)return Promise.resolve(o);if(o instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return i};if(t.common.async===!1){const i=this._def.schema._parseSync({data:t.data,path:t.path,parent:t});return i.status==="aborted"?K:(i.status==="dirty"&&e.dirty(),a(i.value),{status:e.value,value:i.value})}return this._def.schema._parseAsync({data:t.data,path:t.path,parent:t}).then(i=>i.status==="aborted"?K:(i.status==="dirty"&&e.dirty(),a(i.value).then(()=>({status:e.value,value:i.value}))))}if(n.type==="transform"){if(t.common.async===!1){const a=this._def.schema._parseSync({data:t.data,path:t.path,parent:t});if(!Et(a))return a;const i=n.transform(a.value,r);if(i instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:e.value,value:i}}return this._def.schema._parseAsync({data:t.data,path:t.path,parent:t}).then(a=>Et(a)?Promise.resolve(n.transform(a.value,r)).then(i=>({status:e.value,value:i})):a)}ie.assertNever(n)}};Ge.create=(s,e,t)=>new Ge({schema:s,typeName:J.ZodEffects,effect:e,...Q(t)}),Ge.createWithPreprocess=(s,e,t)=>new Ge({schema:e,effect:{type:"preprocess",transform:s},typeName:J.ZodEffects,...Q(t)});let at=class extends te{_parse(s){return this._getType(s)===j.undefined?Ae(void 0):this._def.innerType._parse(s)}unwrap(){return this._def.innerType}};at.create=(s,e)=>new at({innerType:s,typeName:J.ZodOptional,...Q(e)});let kt=class extends te{_parse(s){return this._getType(s)===j.null?Ae(null):this._def.innerType._parse(s)}unwrap(){return this._def.innerType}};kt.create=(s,e)=>new kt({innerType:s,typeName:J.ZodNullable,...Q(e)});let $n=class extends te{_parse(s){const{ctx:e}=this._processInputParams(s);let t=e.data;return e.parsedType===j.undefined&&(t=this._def.defaultValue()),this._def.innerType._parse({data:t,path:e.path,parent:e})}removeDefault(){return this._def.innerType}};$n.create=(s,e)=>new $n({innerType:s,typeName:J.ZodDefault,defaultValue:typeof e.default=="function"?e.default:()=>e.default,...Q(e)});let yn=class extends te{_parse(s){const{ctx:e}=this._processInputParams(s),t={...e,common:{...e.common,issues:[]}},n=this._def.innerType._parse({data:t.data,path:t.path,parent:{...t}});return an(n)?n.then(r=>({status:"valid",value:r.status==="valid"?r.value:this._def.catchValue({get error(){return new Xe(t.common.issues)},input:t.data})})):{status:"valid",value:n.status==="valid"?n.value:this._def.catchValue({get error(){return new Xe(t.common.issues)},input:t.data})}}removeCatch(){return this._def.innerType}};yn.create=(s,e)=>new yn({innerType:s,typeName:J.ZodCatch,catchValue:typeof e.catch=="function"?e.catch:()=>e.catch,...Q(e)});let as=class extends te{_parse(s){if(this._getType(s)!==j.nan){const e=this._getOrReturnCtx(s);return I(e,{code:A.invalid_type,expected:j.nan,received:e.parsedType}),K}return{status:"valid",value:s.data}}};as.create=s=>new as({typeName:J.ZodNaN,...Q(s)});const Wi=Symbol("zod_brand");let js=class extends te{_parse(s){const{ctx:e}=this._processInputParams(s),t=e.data;return this._def.type._parse({data:t,path:e.path,parent:e})}unwrap(){return this._def.type}},Rs=class Ia extends te{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.common.async)return(async()=>{const r=await this._def.in._parseAsync({data:n.data,path:n.path,parent:n});return r.status==="aborted"?K:r.status==="dirty"?(t.dirty(),Qn(r.value)):this._def.out._parseAsync({data:r.value,path:n.path,parent:n})})();{const r=this._def.in._parseSync({data:n.data,path:n.path,parent:n});return r.status==="aborted"?K:r.status==="dirty"?(t.dirty(),{status:"dirty",value:r.value}):this._def.out._parseSync({data:r.value,path:n.path,parent:n})}}static create(e,t){return new Ia({in:e,out:t,typeName:J.ZodPipeline})}},_n=class extends te{_parse(s){const e=this._def.innerType._parse(s),t=n=>(Et(n)&&(n.value=Object.freeze(n.value)),n);return an(e)?e.then(n=>t(n)):t(e)}unwrap(){return this._def.innerType}};function Js(s,e={},t){return s?qt.create().superRefine((n,r)=>{var a,i;if(!s(n)){const o=typeof e=="function"?e(n):typeof e=="string"?{message:e}:e,c=(i=(a=o.fatal)!==null&&a!==void 0?a:t)===null||i===void 0||i,l=typeof o=="string"?{message:o}:o;r.addIssue({code:"custom",...l,fatal:c})}}):qt.create()}_n.create=(s,e)=>new _n({innerType:s,typeName:J.ZodReadonly,...Q(e)});const Yi={object:Ue.lazycreate};var J;(function(s){s.ZodString="ZodString",s.ZodNumber="ZodNumber",s.ZodNaN="ZodNaN",s.ZodBigInt="ZodBigInt",s.ZodBoolean="ZodBoolean",s.ZodDate="ZodDate",s.ZodSymbol="ZodSymbol",s.ZodUndefined="ZodUndefined",s.ZodNull="ZodNull",s.ZodAny="ZodAny",s.ZodUnknown="ZodUnknown",s.ZodNever="ZodNever",s.ZodVoid="ZodVoid",s.ZodArray="ZodArray",s.ZodObject="ZodObject",s.ZodUnion="ZodUnion",s.ZodDiscriminatedUnion="ZodDiscriminatedUnion",s.ZodIntersection="ZodIntersection",s.ZodTuple="ZodTuple",s.ZodRecord="ZodRecord",s.ZodMap="ZodMap",s.ZodSet="ZodSet",s.ZodFunction="ZodFunction",s.ZodLazy="ZodLazy",s.ZodLiteral="ZodLiteral",s.ZodEnum="ZodEnum",s.ZodEffects="ZodEffects",s.ZodNativeEnum="ZodNativeEnum",s.ZodOptional="ZodOptional",s.ZodNullable="ZodNullable",s.ZodDefault="ZodDefault",s.ZodCatch="ZodCatch",s.ZodPromise="ZodPromise",s.ZodBranded="ZodBranded",s.ZodPipeline="ZodPipeline",s.ZodReadonly="ZodReadonly"})(J||(J={}));const Hs=Ut.create,Ks=on.create,Xi=as.create,Qi=cn.create,Gs=ln.create,eo=dn.create,to=ts.create,no=un.create,so=pn.create,ro=qt.create,ao=Nt.create,io=gt.create,oo=ns.create,co=It.create,lo=Ue.create,uo=Ue.strictCreate,po=mn.create,mo=Ta.create,ho=hn.create,fo=St.create,go=Na.create,vo=ss.create,$o=rs.create,yo=Oa.create,_o=fn.create,xo=gn.create,wo=Dn.create,bo=vn.create,So=Bt.create,Ws=Ge.create,ko=at.create,Co=kt.create,To=Ge.createWithPreprocess,Mo=Rs.create,Zo={string:s=>Ut.create({...s,coerce:!0}),number:s=>on.create({...s,coerce:!0}),boolean:s=>ln.create({...s,coerce:!0}),bigint:s=>cn.create({...s,coerce:!0}),date:s=>dn.create({...s,coerce:!0})},No=K;var fe=Object.freeze({__proto__:null,defaultErrorMap:Vt,setErrorMap:function(s){_a=s},getErrorMap:Yn,makeIssue:Xn,EMPTY_PATH:[],addIssueToContext:I,ParseStatus:Re,INVALID:K,DIRTY:Qn,OK:Ae,isAborted:bs,isDirty:Ss,isValid:Et,isAsync:an,get util(){return ie},get objectUtil(){return ws},ZodParsedType:j,getParsedType:mt,ZodType:te,datetimeRegex:ka,ZodString:Ut,ZodNumber:on,ZodBigInt:cn,ZodBoolean:ln,ZodDate:dn,ZodSymbol:ts,ZodUndefined:un,ZodNull:pn,ZodAny:qt,ZodUnknown:Nt,ZodNever:gt,ZodVoid:ns,ZodArray:It,ZodObject:Ue,ZodUnion:mn,ZodDiscriminatedUnion:Ta,ZodIntersection:hn,ZodTuple:St,ZodRecord:Na,ZodMap:ss,ZodSet:rs,ZodFunction:Oa,ZodLazy:fn,ZodLiteral:gn,ZodEnum:Dn,ZodNativeEnum:vn,ZodPromise:Bt,ZodEffects:Ge,ZodTransformer:Ge,ZodOptional:at,ZodNullable:kt,ZodDefault:$n,ZodCatch:yn,ZodNaN:as,BRAND:Wi,ZodBranded:js,ZodPipeline:Rs,ZodReadonly:_n,custom:Js,Schema:te,ZodSchema:te,late:Yi,get ZodFirstPartyTypeKind(){return J},coerce:Zo,any:ro,array:co,bigint:Qi,boolean:Gs,date:eo,discriminatedUnion:mo,effect:Ws,enum:wo,function:yo,instanceof:(s,e={message:`Input not instance of ${s.name}`})=>Js(t=>t instanceof s,e),intersection:ho,lazy:_o,literal:xo,map:vo,nan:Xi,nativeEnum:bo,never:io,null:so,nullable:Co,number:Ks,object:lo,oboolean:()=>Gs().optional(),onumber:()=>Ks().optional(),optional:ko,ostring:()=>Hs().optional(),pipeline:Mo,preprocess:To,promise:So,record:go,set:$o,strictObject:uo,string:Hs,symbol:to,transformer:Ws,tuple:fo,undefined:no,union:po,unknown:ao,void:oo,NEVER:No,ZodIssueCode:A,quotelessJson:s=>JSON.stringify(s,null,2).replace(/"([^"]+)":/g,"$1:"),ZodError:Xe});const qe=fe.object({name:fe.string().optional(),title:fe.string().optional(),command:fe.string().optional(),args:fe.array(fe.union([fe.string(),fe.number(),fe.boolean()])).optional(),env:fe.record(fe.union([fe.string(),fe.number(),fe.boolean(),fe.null(),fe.undefined()])).optional()}).passthrough(),Ao=fe.array(qe),Oo=fe.object({servers:fe.array(qe)}).passthrough(),Eo=fe.object({mcpServers:fe.array(qe)}).passthrough(),Io=fe.object({servers:fe.record(qe)}).passthrough(),Po=fe.object({mcpServers:fe.record(qe)}).passthrough(),jo=fe.record(qe),Ro=qe.refine(s=>s.command!==void 0,{message:"Server must have a 'command' property"}),Lo=Symbol("MCPServerError");let De=class Pa extends Error{constructor(e){super(e),this.name="MCPServerError",Object.setPrototypeOf(this,Pa.prototype)}};var da,_s;let Fo=(da=Lo,_s=class{constructor(s){ve(this,"servers",Le([]));this.host=s,this.loadServersFromStorage()}handleMessageFromExtension(s){const e=s.data;if(e.type===le.getStoredMCPServersResponse){const t=e.data;return Array.isArray(t)&&this.servers.set(t),!0}return!1}async importServersFromJSON(s){return this.importFromJSON(s)}loadServersFromStorage(){try{this.host.postMessage({type:le.getStoredMCPServers})}catch(s){console.error("Failed to load MCP servers:",s),this.servers.set([])}}saveServers(s){try{this.host.postMessage({type:le.setStoredMCPServers,data:s})}catch(e){throw console.error("Failed to save MCP servers:",e),new De("Failed to save MCP servers")}}getServers(){return this.servers}addServer(s){this.checkExistingServerName(s.name),this.servers.update(e=>{const t=[...e,{...s,id:crypto.randomUUID()}];return this.saveServers(t),t})}checkExistingServerName(s,e){const t=rn(this.servers).find(n=>n.name===s);if(t&&(t==null?void 0:t.id)!==e)throw new De(`Server name '${s}' already exists`)}updateServer(s){this.checkExistingServerName(s.name,s.id),this.servers.update(e=>{const t=e.map(n=>n.id===s.id?s:n);return this.saveServers(t),t})}deleteServer(s){this.servers.update(e=>{const t=e.filter(n=>n.id!==s);return this.saveServers(t),t})}static convertServerToJSON(s){return JSON.stringify({mcpServers:{[s.name]:{command:s.command.split(" ")[0],args:s.command.split(" ").slice(1),env:s.env}}},null,2)}static parseServerValidationMessages(s){const e=new Map,t=new Map;s.forEach(r=>{var a;r.tools&&r.tools.length===0?e.set(r.id,"No tools are available for this MCP server"):r.disabledTools&&r.disabledTools.length===((a=r.tools)==null?void 0:a.length)?e.set(r.id,"All tools for this MCP server have validation errors: "+r.disabledTools.join(", ")):r.disabledTools&&r.disabledTools.length>0&&t.set(r.id,"MCP server has validation errors in the following tools which have been disabled: "+r.disabledTools.join(", "))});const n=this.parseDuplicateServerIds(s);return{errors:new Map([...e,...n]),warnings:t}}static parseDuplicateServerIds(s){const e=new Map;for(const n of s)e.has(n.name)||e.set(n.name,[]),e.get(n.name).push(n.id);const t=new Map;for(const[,n]of e)if(n.length>1)for(let r=1;r<n.length;r++)t.set(n[r],"MCP server is disabled due to duplicate server names");return t}parseServerConfigFromJSON(s){try{const e=JSON.parse(s),t=fe.union([Ao.transform(n=>n.map(r=>this.normalizeServerConfig(r))),Oo.transform(n=>n.servers.map(r=>this.normalizeServerConfig(r))),Eo.transform(n=>n.mcpServers.map(r=>this.normalizeServerConfig(r))),Io.transform(n=>Object.entries(n.servers).map(([r,a])=>{const i=qe.parse(a);return this.normalizeServerConfig({...i,name:i.name||r})})),Po.transform(n=>Object.entries(n.mcpServers).map(([r,a])=>{const i=qe.parse(a);return this.normalizeServerConfig({...i,name:i.name||r})})),jo.transform(n=>{if(!Object.values(n).some(r=>{const a=qe.safeParse(r);return a.success&&a.data.command!==void 0}))throw new Error("No command property found in any server config");return Object.entries(n).map(([r,a])=>{const i=qe.parse(a);return this.normalizeServerConfig({...i,name:i.name||r})})}),Ro.transform(n=>[this.normalizeServerConfig(n)])]).safeParse(e);if(t.success)return t.data;throw new De("Invalid JSON format. Expected an array of servers or an object with a 'servers' property.")}catch(e){throw e instanceof De?e:new De("Failed to parse MCP servers from JSON. Please check the format.")}}importFromJSON(s){try{const e=this.parseServerConfigFromJSON(s),t=rn(this.servers),n=new Set(t.map(r=>r.name));for(const r of e){if(!r.name)throw new De("All servers must have a name.");if(n.has(r.name))throw new De(`A server with the name '${r.name}' already exists.`);n.add(r.name)}return this.servers.update(r=>{const a=[...r,...e.map(i=>({...i,id:crypto.randomUUID()}))];return this.saveServers(a),a}),e.length}catch(e){throw e instanceof De?e:new De("Failed to import MCP servers from JSON. Please check the format.")}}normalizeServerConfig(s){try{const e=qe.transform(t=>{const n=t.command||"",r=t.args?t.args.map(c=>String(c)):[];if(!n)throw new Error("Server must have a 'command' property");const a=r.length>0?`${n} ${r.join(" ")}`:n,i=t.name||t.title||(n?n.split(" ")[0]:""),o=t.env?Object.fromEntries(Object.entries(t.env).filter(([c,l])=>l!=null).map(([c,l])=>[c,String(l)])):void 0;return{name:i,command:a,arguments:"",useShellInterpolation:!0,env:Object.keys(o||{}).length>0?o:void 0}}).refine(t=>!!t.name,{message:"Server must have a name",path:["name"]}).refine(t=>!!t.command,{message:"Server must have a command",path:["command"]}).safeParse(s);if(!e.success)throw new De(e.error.message);return e.data}catch(e){throw e instanceof Error?new De(`Invalid server configuration: ${e.message}`):new De("Invalid server configuration")}}},ve(_s,da,"MCPServerError"),_s);class zo{constructor(e){ve(this,"_terminalSettings",Le({supportedShells:[],selectedShell:void 0,startupScript:void 0}));this._host=e,this.requestTerminalSettings()}handleMessageFromExtension(e){const t=e.data;return t.type===le.terminalSettingsResponse&&(this._terminalSettings.set(t.data),!0)}getTerminalSettings(){return this._terminalSettings}requestTerminalSettings(){this._host.postMessage({type:le.getTerminalSettings})}updateSelectedShell(e){this._terminalSettings.update(t=>({...t,selectedShell:e})),this._host.postMessage({type:le.updateTerminalSettings,data:{selectedShell:e}})}updateStartupScript(e){this._terminalSettings.update(t=>({...t,startupScript:e})),this._host.postMessage({type:le.updateTerminalSettings,data:{startupScript:e}})}}function xn(s,e){return t=>!t.shiftKey&&t.key===s&&(e(t),!0)}var ft=(s=>(s.file="file",s.folder="folder",s))(ft||{});class xt{constructor(e,t){ve(this,"subscribe");ve(this,"set");ve(this,"update");ve(this,"handleMessageFromExtension",async e=>{const t=e.data;switch(t.type){case le.wsContextSourceFoldersChanged:case le.wsContextFolderContentsChanged:this.updateSourceFolders(await this.getSourceFolders());break;case le.sourceFoldersSyncStatus:this.update(n=>({...n,syncStatus:t.data.status}))}});ve(this,"getSourceFolders",async()=>(await this.asyncMsgSender.send({type:le.wsContextGetSourceFoldersRequest},1e4)).data.workspaceFolders);ve(this,"getChildren",async e=>(await this.asyncMsgSender.send({type:le.wsContextGetChildrenRequest,data:{fileId:e}},1e4)).data.children.map(t=>t.type==="folder"?{...t,children:[],expanded:!1}:{...t}).sort((t,n)=>t.type===n.type?t.name.localeCompare(n.name):t.type==="folder"?-1:1));this.host=e,this.asyncMsgSender=t;const{subscribe:n,set:r,update:a}=Le({sourceFolders:[],sourceTree:[],syncStatus:xs.done});this.subscribe=n,this.set=r,this.update=a,this.getSourceFolders().then(i=>{this.update(o=>({...o,sourceFolders:i,sourceTree:xt.sourceFoldersToSourceNodes(i)}))})}async expandNode(e){e.children=await this.getChildren(e.fileId),e.expanded=!0,this.update(t=>t)}collapseNode(e){this.update(t=>(e.children=[],e.expanded=!1,t))}toggleNode(e){e.type==="folder"&&e.inclusionState!==Ke.excluded&&(e.expanded?this.collapseNode(e):this.expandNode(e))}addMoreSourceFolders(){this.host.postMessage({type:le.wsContextAddMoreSourceFolders})}removeSourceFolder(e){this.host.postMessage({type:le.wsContextRemoveSourceFolder,data:e})}requestRefresh(){this.host.postMessage({type:le.wsContextUserRequestedRefresh})}async updateSourceFolders(e){let t=rn(this);const n=await this.getRefreshedSourceTree(t.sourceTree,e);this.update(r=>({...r,sourceFolders:e,sourceTree:n}))}async getRefreshedSourceTree(e,t){const n=xt.sourceFoldersToSourceNodes(t);return this.getRefreshedSourceTreeRecurse(e,n)}async getRefreshedSourceTreeRecurse(e,t){const n=new Map(e.map(r=>[JSON.stringify([r.fileId.folderRoot,r.fileId.relPath]),r]));for(let r of t){const a=xt.fileIdToString(r.fileId);if(r.type==="folder"){const i=n.get(a);i&&(r.expanded=i.type==="folder"&&i.expanded,r.expanded&&(r.children=await this.getChildren(r.fileId),r.children=await this.getRefreshedSourceTreeRecurse(i.children,r.children)))}}return t}static fileIdToString(e){return JSON.stringify([e.folderRoot,e.relPath])}static sourceFoldersToSourceNodes(e){return e.filter(t=>!t.isNestedFolder&&!t.isPending).sort((t,n)=>t.name.localeCompare(n.name)).map(t=>({name:t.name,fileId:t.fileId,children:[],expanded:!1,type:"folder",inclusionState:t.inclusionState,reason:"",trackedFileCount:t.trackedFileCount}))}}function Ys(s,e,t){const n=s.slice();return n[6]=e[t],n}function Xs(s){let e,t;function n(){return s[5](s[6])}return e=new zn({props:{title:"Remove source folder from Augment context",variant:"ghost",color:"neutral",size:1,class:"source-folder-v-adjust",$$slots:{default:[Do]},$$scope:{ctx:s}}}),e.$on("click",function(){return s[4](s[6])}),e.$on("keyup",function(){wt(xn("Enter",n))&&xn("Enter",n).apply(this,arguments)}),{c(){S(e.$$.fragment)},m(r,a){k(e,r,a),t=!0},p(r,a){s=r;const i={};512&a&&(i.$$scope={dirty:a,ctx:s}),e.$set(i)},i(r){t||(p(e.$$.fragment,r),t=!0)},o(r){m(e.$$.fragment,r),t=!1},d(r){C(e,r)}}}function Do(s){let e,t;return e=new hi({}),{c(){S(e.$$.fragment)},m(n,r){k(e,n,r),t=!0},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Qs(s){let e,t;return e=new ce({props:{size:1,class:"file-count",$$slots:{default:[Vo]},$$scope:{ctx:s}}}),{c(){S(e.$$.fragment)},m(n,r){k(e,n,r),t=!0},p(n,r){const a={};513&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Vo(s){let e,t=s[6].trackedFileCount.toLocaleString()+"";return{c(){e=U(t)},m(n,r){_(n,e,r)},p(n,r){1&r&&t!==(t=n[6].trackedFileCount.toLocaleString()+"")&&he(e,t)},d(n){n&&y(e)}}}function er(s,e){let t,n,r,a,i,o,c,l,d,u,f,v=e[6].name+"",g=(e[6].isPending?"(pending)":e[6].fileId.folderRoot)+"",h=!e[6].isWorkspaceFolder&&Xs(e);r=new $a({props:{class:"source-folder-v-adjust",icon:e[3](e[6])}});let w=e[6].trackedFileCount&&Qs(e);return{key:s,first:null,c(){t=Z("div"),h&&h.c(),n=E(),S(r.$$.fragment),a=E(),i=Z("span"),o=U(v),c=E(),l=Z("span"),d=U(g),u=E(),w&&w.c(),$(l,"class","folderRoot svelte-1skknri"),$(i,"class","name svelte-1skknri"),$(t,"class","item svelte-1skknri"),_e(t,"workspace-folder",e[6].isWorkspaceFolder),this.first=t},m(N,x){_(N,t,x),h&&h.m(t,null),M(t,n),k(r,t,null),M(t,a),M(t,i),M(i,o),M(i,c),M(i,l),M(l,d),M(t,u),w&&w.m(t,null),f=!0},p(N,x){(e=N)[6].isWorkspaceFolder?h&&(W(),m(h,1,1,()=>{h=null}),Y()):h?(h.p(e,x),1&x&&p(h,1)):(h=Xs(e),h.c(),p(h,1),h.m(t,n));const b={};1&x&&(b.icon=e[3](e[6])),r.$set(b),(!f||1&x)&&v!==(v=e[6].name+"")&&he(o,v),(!f||1&x)&&g!==(g=(e[6].isPending?"(pending)":e[6].fileId.folderRoot)+"")&&he(d,g),e[6].trackedFileCount?w?(w.p(e,x),1&x&&p(w,1)):(w=Qs(e),w.c(),p(w,1),w.m(t,null)):w&&(W(),m(w,1,1,()=>{w=null}),Y()),(!f||1&x)&&_e(t,"workspace-folder",e[6].isWorkspaceFolder)},i(N){f||(p(h),p(r.$$.fragment,N),p(w),f=!0)},o(N){m(h),m(r.$$.fragment,N),m(w),f=!1},d(N){N&&y(t),h&&h.d(),C(r),w&&w.d()}}}function Uo(s){let e,t,n,r,a,i,o,c,l=[],d=new Map,u=xe(s[0]);const f=v=>xt.fileIdToString(v[6].fileId);for(let v=0;v<u.length;v+=1){let g=Ys(s,u,v),h=f(g);d.set(h,l[v]=er(h,g))}return r=new Ps({}),{c(){e=Z("div");for(let v=0;v<l.length;v+=1)l[v].c();t=E(),n=Z("div"),S(r.$$.fragment),a=U(" Add more..."),$(n,"role","button"),$(n,"tabindex","0"),$(n,"class","add-more svelte-1skknri"),$(e,"class","source-folder svelte-1skknri")},m(v,g){_(v,e,g);for(let h=0;h<l.length;h+=1)l[h]&&l[h].m(e,null);M(e,t),M(e,n),k(r,n,null),M(n,a),i=!0,o||(c=[et(n,"keyup",function(){wt(xn("Enter",s[1]))&&xn("Enter",s[1]).apply(this,arguments)}),et(n,"click",function(){wt(s[1])&&s[1].apply(this,arguments)})],o=!0)},p(v,[g]){s=v,13&g&&(u=xe(s[0]),W(),l=Gt(l,g,f,1,s,u,d,e,Wt,er,t,Ys),Y())},i(v){if(!i){for(let g=0;g<u.length;g+=1)p(l[g]);p(r.$$.fragment,v),i=!0}},o(v){for(let g=0;g<l.length;g+=1)m(l[g]);m(r.$$.fragment,v),i=!1},d(v){v&&y(e);for(let g=0;g<l.length;g+=1)l[g].d();C(r),o=!1,Is(c)}}}function qo(s,e,t){let{folders:n=[]}=e,{onAddMore:r}=e,{onRemove:a}=e;return s.$$set=i=>{"folders"in i&&t(0,n=i.folders),"onAddMore"in i&&t(1,r=i.onAddMore),"onRemove"in i&&t(2,a=i.onRemove)},[n,r,a,i=>i.isWorkspaceFolder?"root-folder":"folder",i=>a(i.fileId.folderRoot),i=>a(i.fileId.folderRoot)]}class Bo extends ue{constructor(e){super(),pe(this,e,qo,Uo,de,{folders:0,onAddMore:1,onRemove:2})}}function tr(s,e,t){const n=s.slice();return n[10]=e[t],n}function nr(s){let e,t;return e=new ce({props:{size:1,class:"file-count",$$slots:{default:[Jo]},$$scope:{ctx:s}}}),{c(){S(e.$$.fragment)},m(n,r){k(e,n,r),t=!0},p(n,r){const a={};8193&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Jo(s){let e,t=s[0].trackedFileCount.toLocaleString()+"";return{c(){e=U(t)},m(n,r){_(n,e,r)},p(n,r){1&r&&t!==(t=n[0].trackedFileCount.toLocaleString()+"")&&he(e,t)},d(n){n&&y(e)}}}function sr(s){let e,t,n=[],r=new Map,a=xe(s[5].children);const i=o=>xt.fileIdToString(o[10].fileId);for(let o=0;o<a.length;o+=1){let c=tr(s,a,o),l=i(c);r.set(l,n[o]=rr(l,c))}return{c(){e=Z("div");for(let o=0;o<n.length;o+=1)n[o].c();$(e,"class","children-container")},m(o,c){_(o,e,c);for(let l=0;l<n.length;l+=1)n[l]&&n[l].m(e,null);t=!0},p(o,c){38&c&&(a=xe(o[5].children),W(),n=Gt(n,c,i,1,o,a,r,e,Wt,rr,null,tr),Y())},i(o){if(!t){for(let c=0;c<a.length;c+=1)p(n[c]);t=!0}},o(o){for(let c=0;c<n.length;c+=1)m(n[c]);t=!1},d(o){o&&y(e);for(let c=0;c<n.length;c+=1)n[c].d()}}}function rr(s,e){let t,n,r;return n=new ja({props:{data:e[10],wsContextModel:e[1],indentLevel:e[2]+1}}),{key:s,first:null,c(){t=Se(),S(n.$$.fragment),this.first=t},m(a,i){_(a,t,i),k(n,a,i),r=!0},p(a,i){e=a;const o={};32&i&&(o.data=e[10]),2&i&&(o.wsContextModel=e[1]),4&i&&(o.indentLevel=e[2]+1),n.$set(o)},i(a){r||(p(n.$$.fragment,a),r=!0)},o(a){m(n.$$.fragment,a),r=!1},d(a){a&&y(t),C(n,a)}}}function Ho(s){let e,t,n,r,a,i,o,c,l,d,u,f,v,g,h,w,N,x,b=s[0].name+"";n=new $a({props:{icon:s[4]}});let T=s[0].type===ft.folder&&s[0].inclusionState!==Ke.excluded&&typeof s[0].trackedFileCount=="number"&&nr(s),D=s[5]&&sr(s);return{c(){e=Z("div"),t=Z("div"),S(n.$$.fragment),r=E(),a=Z("span"),i=U(b),o=E(),T&&T.c(),c=E(),l=Z("img"),h=E(),D&&D.c(),$(a,"class","name svelte-sympus"),Ds(l.src,d=s[7][s[0].inclusionState])||$(l,"src",d),$(l,"alt",u=s[8][s[0].inclusionState]),$(t,"class","tree-item svelte-sympus"),$(t,"role","treeitem"),$(t,"aria-selected","false"),$(t,"tabindex","0"),$(t,"title",f=s[0].reason),$(t,"aria-expanded",v=s[0].type===ft.folder&&s[0].expanded),$(t,"aria-level",s[2]),$(t,"style",g=`padding-left: ${10*s[2]+20}px;`),_e(t,"included-folder",s[3])},m(q,se){_(q,e,se),M(e,t),k(n,t,null),M(t,r),M(t,a),M(a,i),M(t,o),T&&T.m(t,null),M(t,c),M(t,l),M(e,h),D&&D.m(e,null),w=!0,N||(x=[et(t,"click",s[6]),et(t,"keyup",xn("Enter",s[6]))],N=!0)},p(q,[se]){const we={};16&se&&(we.icon=q[4]),n.$set(we),(!w||1&se)&&b!==(b=q[0].name+"")&&he(i,b),q[0].type===ft.folder&&q[0].inclusionState!==Ke.excluded&&typeof q[0].trackedFileCount=="number"?T?(T.p(q,se),1&se&&p(T,1)):(T=nr(q),T.c(),p(T,1),T.m(t,c)):T&&(W(),m(T,1,1,()=>{T=null}),Y()),(!w||1&se&&!Ds(l.src,d=q[7][q[0].inclusionState]))&&$(l,"src",d),(!w||1&se&&u!==(u=q[8][q[0].inclusionState]))&&$(l,"alt",u),(!w||1&se&&f!==(f=q[0].reason))&&$(t,"title",f),(!w||1&se&&v!==(v=q[0].type===ft.folder&&q[0].expanded))&&$(t,"aria-expanded",v),(!w||4&se)&&$(t,"aria-level",q[2]),(!w||4&se&&g!==(g=`padding-left: ${10*q[2]+20}px;`))&&$(t,"style",g),(!w||8&se)&&_e(t,"included-folder",q[3]),q[5]?D?(D.p(q,se),32&se&&p(D,1)):(D=sr(q),D.c(),p(D,1),D.m(e,null)):D&&(W(),m(D,1,1,()=>{D=null}),Y())},i(q){w||(p(n.$$.fragment,q),p(T),p(D),w=!0)},o(q){m(n.$$.fragment,q),m(T),m(D),w=!1},d(q){q&&y(e),C(n),T&&T.d(),D&&D.d(),N=!1,Is(x)}}}function Ko(s,e,t){let{data:n}=e,{wsContextModel:r}=e,{indentLevel:a}=e;const i={[Ke.included]:"data:image/svg+xml,%3csvg%20width='12'%20height='12'%20viewBox='0%200%2012%2012'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M2.66658%201.01118C3.65328%200.351894%204.81331%200%206%200C7.5907%200.00195418%209.11569%200.634723%2010.2405%201.75952C11.3653%202.88431%2011.998%204.4093%2012%206C12%207.18669%2011.6481%208.34672%2010.9888%209.33342C10.3295%2010.3201%209.39246%2011.0891%208.2961%2011.5433C7.19975%2011.9974%205.99335%2012.1162%204.82946%2011.8847C3.66557%2011.6532%202.59648%2011.0818%201.75736%2010.2426C0.918247%209.40352%200.346802%208.33443%200.115291%207.17054C-0.11622%206.00666%200.00259969%204.80025%200.456725%203.7039C0.910851%202.60754%201.67989%201.67047%202.66658%201.01118ZM5.86301%208.67273L9.44256%203.9L8.24256%203L5.12729%207.15368L3.17471%205.59162L2.23767%206.76292L4.79449%208.80838L5.86301%208.67273Z'%20fill='%23388A34'/%3e%3c/svg%3e",[Ke.excluded]:"data:image/svg+xml,%3csvg%20width='12'%20height='12'%20viewBox='0%200%2012%2012'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M2.66658%201.01119C3.65328%200.351896%204.81332%200%206%200C7.5907%200.00195419%209.11569%200.634726%2010.2405%201.75953C11.3653%202.88433%2011.998%204.40933%2012%206.00003C12%207.18673%2011.6481%208.34677%2010.9888%209.33347C10.3295%2010.3202%209.39246%2011.0892%208.2961%2011.5433C7.19975%2011.9975%205.99335%2012.1163%204.82946%2011.8848C3.66558%2011.6533%202.59648%2011.0818%201.75736%2010.2427C0.918247%209.40358%200.346802%208.33447%200.115291%207.17058C-0.11622%206.00669%200.00259969%204.80028%200.456726%203.70392C0.910851%202.60756%201.67989%201.67048%202.66658%201.01119ZM6.00007%207.07359L8.1213%209.19482L9.18196%208.13416L7.06073%206.01292L9.18198%203.89166L8.12132%202.83099L6.00007%204.95225L3.87866%202.83083L2.818%203.89149L4.93941%206.01292L2.81802%208.13432L3.87868%209.19499L6.00007%207.07359Z'%20fill='%23E51400'/%3e%3c/svg%3e",[Ke.partial]:"data:image/svg+xml,%3csvg%20width='12'%20height='12'%20viewBox='0%200%2012%2012'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M2.66658%201.01118C3.65328%200.351894%204.81331%200%206%200C7.5907%200.00195418%209.11569%200.634723%2010.2405%201.75952C11.3653%202.88431%2011.998%204.4093%2012%206C12%207.18669%2011.6481%208.34672%2010.9888%209.33342C10.3295%2010.3201%209.39246%2011.0891%208.2961%2011.5433C7.19975%2011.9974%205.99335%2012.1162%204.82946%2011.8847C3.66557%2011.6532%202.59648%2011.0818%201.75736%2010.2426C0.918247%209.40352%200.346802%208.33443%200.115291%207.17054C-0.11622%206.00666%200.00259969%204.80025%200.456725%203.7039C0.910851%202.60754%201.67989%201.67047%202.66658%201.01118ZM3.66667%205.83333C3.66667%205.99815%203.61779%206.15927%203.52623%206.29631C3.43466%206.43335%203.30451%206.54016%203.15224%206.60323C2.99997%206.66631%202.83241%206.68281%202.67076%206.65065C2.50911%206.6185%202.36062%206.53913%202.24408%206.42259C2.12753%206.30605%202.04817%206.15756%202.01601%205.99591C1.98386%205.83426%202.00036%205.6667%202.06343%205.51443C2.12651%205.36216%202.23332%205.23201%202.37036%205.14044C2.5074%205.04887%202.66852%205%202.83333%205C3.05435%205%203.26631%205.0878%203.42259%205.24408C3.57887%205.40036%203.66667%205.61232%203.66667%205.83333ZM6.83333%205.83333C6.83333%205.99815%206.78446%206.15927%206.69289%206.29631C6.60132%206.43335%206.47117%206.54016%206.3189%206.60323C6.16663%206.66631%205.99908%206.68281%205.83742%206.65065C5.67577%206.6185%205.52729%206.53913%205.41074%206.42259C5.2942%206.30605%205.21483%206.15756%205.18268%205.99591C5.15052%205.83426%205.16703%205.6667%205.2301%205.51443C5.29317%205.36216%205.39998%205.23201%205.53702%205.14044C5.67407%205.04887%205.83518%205%206%205C6.22101%205%206.43297%205.0878%206.58926%205.24408C6.74554%205.40036%206.83333%205.61232%206.83333%205.83333ZM9.85956%206.29631C9.95113%206.15927%2010%205.99815%2010%205.83333C10%205.61232%209.9122%205.40036%209.75592%205.24408C9.59964%205.0878%209.38768%205%209.16667%205C9.00185%205%208.84073%205.04887%208.70369%205.14044C8.56665%205.23201%208.45984%205.36216%208.39677%205.51443C8.33369%205.6667%208.31719%205.83426%208.34935%205.99591C8.3815%206.15756%208.46087%206.30605%208.57741%206.42259C8.69395%206.53913%208.84244%206.6185%209.00409%206.65065C9.16574%206.68281%209.3333%206.66631%209.48557%206.60323C9.63784%206.54016%209.76799%206.43335%209.85956%206.29631Z'%20fill='%23388A34'/%3e%3c/svg%3e"},o={[Ke.included]:"included",[Ke.excluded]:"excluded",[Ke.partial]:"partially included"};let c,l,d;return s.$$set=u=>{"data"in u&&t(0,n=u.data),"wsContextModel"in u&&t(1,r=u.wsContextModel),"indentLevel"in u&&t(2,a=u.indentLevel)},s.$$.update=()=>{var u;1&s.$$.dirty&&t(4,l=(u=n).type===ft.folder&&u.inclusionState!==Ke.excluded?u.expanded?"chevron-down":"chevron-right":u.type===ft.folder?"folder":"file"),1&s.$$.dirty&&t(3,c=n.type===ft.folder&&n.inclusionState!==Ke.excluded),1&s.$$.dirty&&t(5,d=n.type===ft.folder&&n.expanded&&n.children&&n.children.length>0?n:null)},[n,r,a,c,l,d,()=>{r.toggleNode(n)},i,o]}class ja extends ue{constructor(e){super(),pe(this,e,Ko,Ho,de,{data:0,wsContextModel:1,indentLevel:2})}}function ar(s,e,t){const n=s.slice();return n[3]=e[t],n}function ir(s,e){let t,n,r;return n=new ja({props:{wsContextModel:e[0],data:e[3],indentLevel:0}}),{key:s,first:null,c(){t=Se(),S(n.$$.fragment),this.first=t},m(a,i){_(a,t,i),k(n,a,i),r=!0},p(a,i){e=a;const o={};1&i&&(o.wsContextModel=e[0]),2&i&&(o.data=e[3]),n.$set(o)},i(a){r||(p(n.$$.fragment,a),r=!0)},o(a){m(n.$$.fragment,a),r=!1},d(a){a&&y(t),C(n,a)}}}function Go(s){let e,t,n=[],r=new Map,a=xe(s[1]);const i=o=>xt.fileIdToString(o[3].fileId);for(let o=0;o<a.length;o+=1){let c=ar(s,a,o),l=i(c);r.set(l,n[o]=ir(l,c))}return{c(){e=Z("div");for(let o=0;o<n.length;o+=1)n[o].c();$(e,"class","files-container svelte-8hfqhl")},m(o,c){_(o,e,c);for(let l=0;l<n.length;l+=1)n[l]&&n[l].m(e,null);t=!0},p(o,[c]){3&c&&(a=xe(o[1]),W(),n=Gt(n,c,i,1,o,a,r,e,Wt,ir,null,ar),Y())},i(o){if(!t){for(let c=0;c<a.length;c+=1)p(n[c]);t=!0}},o(o){for(let c=0;c<n.length;c+=1)m(n[c]);t=!1},d(o){o&&y(e);for(let c=0;c<n.length;c+=1)n[c].d()}}}function Wo(s,e,t){let n,r=X,a=()=>(r(),r=ma(o,c=>t(2,n=c)),o);s.$$.on_destroy.push(()=>r());let i,{wsContextModel:o}=e;return a(),s.$$set=c=>{"wsContextModel"in c&&a(t(0,o=c.wsContextModel))},s.$$.update=()=>{4&s.$$.dirty&&t(1,i=n.sourceTree)},[o,i,n]}class Yo extends ue{constructor(e){super(),pe(this,e,Wo,Go,de,{wsContextModel:0})}}function Xo(s){let e,t,n;return{c(){e=Me("svg"),t=Me("rect"),n=Me("path"),$(t,"width","16"),$(t,"height","16"),$(t,"transform","matrix(-1 0 0 -1 16 16)"),$(t,"fill","currentColor"),$(t,"fill-opacity","0.01"),$(n,"fill-rule","evenodd"),$(n,"clip-rule","evenodd"),$(n,"d","M13.7075 11.7333C13.7075 12.8236 12.8236 13.7075 11.7333 13.7075C10.643 13.7075 9.75909 12.8236 9.75909 11.7333C9.75909 10.643 10.643 9.75909 11.7333 9.75909C12.8236 9.75909 13.7075 10.643 13.7075 11.7333ZM11.7333 14.6675C13.3538 14.6675 14.6675 13.3538 14.6675 11.7333C14.6675 10.1128 13.3538 8.79909 11.7333 8.79909C10.1128 8.79909 8.79909 10.1128 8.79909 11.7333C8.79909 13.3538 10.1128 14.6675 11.7333 14.6675ZM9.79161 4.26647L13.3333 2.30721V6.22571L9.79161 4.26647ZM13.1852 7.24088C13.6829 7.51617 14.2933 7.15625 14.2933 6.58752V1.9454C14.2933 1.37665 13.6829 1.01676 13.1852 1.29207L8.98946 3.61313C8.47582 3.89729 8.47582 4.63564 8.98946 4.9198L13.1852 7.24088ZM7.14663 6.39988C7.14663 6.81225 6.81233 7.14654 6.39996 7.14654H2.1333C1.72093 7.14654 1.38664 6.81225 1.38664 6.39988V2.13324C1.38664 1.72087 1.72093 1.38657 2.1333 1.38657H6.39996C6.81233 1.38657 7.14663 1.72087 7.14663 2.13324V6.39988ZM6.18663 6.18654V2.34657H2.34664V6.18654H6.18663ZM1.66056 13.6606C1.47314 13.848 1.47314 14.152 1.66056 14.3394C1.84797 14.5269 2.15186 14.5269 2.33938 14.3394L4.26664 12.4121L6.19388 14.3394C6.38133 14.5268 6.68525 14.5268 6.8727 14.3394C7.06015 14.1519 7.06015 13.848 6.8727 13.6606L4.94546 11.7333L6.8727 9.80608C7.06015 9.61863 7.06015 9.31471 6.8727 9.12726C6.68525 8.9398 6.38133 8.9398 6.19388 9.12726L4.26664 11.0545L2.33938 9.12722C2.15186 8.93978 1.84797 8.93978 1.66056 9.12722C1.47314 9.31468 1.47314 9.61861 1.66056 9.80605L3.58781 11.7333L1.66056 13.6606Z"),$(n,"fill","currentColor"),$(e,"width","15"),$(e,"height","15"),$(e,"viewBox","0 0 16 16"),$(e,"fill","none"),$(e,"xmlns","http://www.w3.org/2000/svg")},m(r,a){_(r,e,a),M(e,t),M(e,n)},p:X,i:X,o:X,d(r){r&&y(e)}}}class Qo extends ue{constructor(e){super(),pe(this,e,null,Xo,de,{})}}const ec=s=>({}),or=s=>({}),tc=s=>({}),cr=s=>({});function nc(s){let e;const t=s[7]["header-left"],n=Ee(t,s,s[8],cr);return{c(){n&&n.c()},m(r,a){n&&n.m(r,a),e=!0},p(r,a){n&&n.p&&(!e||256&a)&&Ie(n,t,r,r[8],e?je(t,r[8],a,tc):Pe(r[8]),cr)},i(r){e||(p(n,r),e=!0)},o(r){m(n,r),e=!1},d(r){n&&n.d(r)}}}function sc(s){let e,t,n,r=s[0]&&lr(s),a=s[1]&&dr(s);return{c(){r&&r.c(),e=E(),a&&a.c(),t=Se()},m(i,o){r&&r.m(i,o),_(i,e,o),a&&a.m(i,o),_(i,t,o),n=!0},p(i,o){i[0]?r?(r.p(i,o),1&o&&p(r,1)):(r=lr(i),r.c(),p(r,1),r.m(e.parentNode,e)):r&&(W(),m(r,1,1,()=>{r=null}),Y()),i[1]?a?(a.p(i,o),2&o&&p(a,1)):(a=dr(i),a.c(),p(a,1),a.m(t.parentNode,t)):a&&(W(),m(a,1,1,()=>{a=null}),Y())},i(i){n||(p(r),p(a),n=!0)},o(i){m(r),m(a),n=!1},d(i){i&&(y(e),y(t)),r&&r.d(i),a&&a.d(i)}}}function lr(s){let e,t,n;var r=s[0];return r&&(t=bt(r,{})),{c(){e=Z("div"),t&&S(t.$$.fragment),$(e,"class","icon-wrapper svelte-7mispq")},m(a,i){_(a,e,i),t&&k(t,e,null),n=!0},p(a,i){if(1&i&&r!==(r=a[0])){if(t){W();const o=t;m(o.$$.fragment,1,0,()=>{C(o,1)}),Y()}r?(t=bt(r,{}),S(t.$$.fragment),p(t.$$.fragment,1),k(t,e,null)):t=null}},i(a){n||(t&&p(t.$$.fragment,a),n=!0)},o(a){t&&m(t.$$.fragment,a),n=!1},d(a){a&&y(e),t&&C(t)}}}function dr(s){let e,t;return e=new ce({props:{color:"neutral",size:1,weight:"light",class:"card-title",$$slots:{default:[rc]},$$scope:{ctx:s}}}),{c(){S(e.$$.fragment)},m(n,r){k(e,n,r),t=!0},p(n,r){const a={};258&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function rc(s){let e;return{c(){e=U(s[1])},m(t,n){_(t,e,n)},p(t,n){2&n&&he(e,t[1])},d(t){t&&y(e)}}}function ur(s){let e,t;const n=s[7].default,r=Ee(n,s,s[8],null);return{c(){e=Z("div"),r&&r.c(),$(e,"class","settings-card-body")},m(a,i){_(a,e,i),r&&r.m(e,null),t=!0},p(a,i){r&&r.p&&(!t||256&i)&&Ie(r,n,a,a[8],t?je(n,a[8],i,null):Pe(a[8]),null)},i(a){t||(p(r,a),t=!0)},o(a){m(r,a),t=!1},d(a){a&&y(e),r&&r.d(a)}}}function ac(s){let e,t,n,r,a,i,o,c,l;const d=[sc,nc],u=[];function f(x,b){return x[0]||x[1]?0:1}r=f(s),a=u[r]=d[r](s);const v=s[7]["header-right"],g=Ee(v,s,s[8],or);let h=s[4].default&&ur(s),w=[{class:s[2]},s[3]],N={};for(let x=0;x<w.length;x+=1)N=ye(N,w[x]);return{c(){e=Z("div"),t=Z("div"),n=Z("div"),a.c(),i=E(),o=Z("div"),g&&g.c(),c=E(),h&&h.c(),$(n,"class","settings-card-left svelte-7mispq"),$(o,"class","settings-card-right svelte-7mispq"),$(t,"class","settings-card-content svelte-7mispq"),Kn(e,N),_e(e,"svelte-7mispq",!0)},m(x,b){_(x,e,b),M(e,t),M(t,n),u[r].m(n,null),M(t,i),M(t,o),g&&g.m(o,null),M(e,c),h&&h.m(e,null),l=!0},p(x,[b]){let T=r;r=f(x),r===T?u[r].p(x,b):(W(),m(u[T],1,1,()=>{u[T]=null}),Y(),a=u[r],a?a.p(x,b):(a=u[r]=d[r](x),a.c()),p(a,1),a.m(n,null)),g&&g.p&&(!l||256&b)&&Ie(g,v,x,x[8],l?je(v,x[8],b,ec):Pe(x[8]),or),x[4].default?h?(h.p(x,b),16&b&&p(h,1)):(h=ur(x),h.c(),p(h,1),h.m(e,null)):h&&(W(),m(h,1,1,()=>{h=null}),Y()),Kn(e,N=$t(w,[(!l||4&b)&&{class:x[2]},8&b&&x[3]])),_e(e,"svelte-7mispq",!0)},i(x){l||(p(a),p(g,x),p(h),l=!0)},o(x){m(a),m(g,x),m(h),l=!1},d(x){x&&y(e),u[r].d(),g&&g.d(x),h&&h.d()}}}function ic(s,e,t){let n,r,a;const i=["class","icon","title"];let o=Gn(e,i),{$$slots:c={},$$scope:l}=e;const d=ha(c);let{class:u=""}=e,{icon:f}=e,{title:v}=e;return s.$$set=g=>{e=ye(ye({},e),Je(g)),t(9,o=Gn(e,i)),"class"in g&&t(5,u=g.class),"icon"in g&&t(0,f=g.icon),"title"in g&&t(1,v=g.title),"$$scope"in g&&t(8,l=g.$$scope)},s.$$.update=()=>{t(6,{class:n,...r}=o,n,(t(3,r),t(9,o))),96&s.$$.dirty&&t(2,a=`settings-card ${u} ${n||""}`)},[f,v,a,r,d,u,n,c,l]}let oc=class extends ue{constructor(s){super(),pe(this,s,ic,ac,de,{class:5,icon:0,title:1})}};function cc(s){let e;return{c(){e=U("SOURCE FOLDERS")},m(t,n){_(t,e,n)},d(t){t&&y(e)}}}function lc(s){let e;return{c(){e=U("FILES")},m(t,n){_(t,e,n)},d(t){t&&y(e)}}}function dc(s){let e,t=s[2].toLocaleString()+"";return{c(){e=U(t)},m(n,r){_(n,e,r)},p(n,r){4&r&&t!==(t=n[2].toLocaleString()+"")&&he(e,t)},d(n){n&&y(e)}}}function uc(s){let e,t,n,r,a,i,o,c,l,d,u,f,v,g;return n=new ce({props:{size:1,weight:"medium",class:"context-section-header",$$slots:{default:[cc]},$$scope:{ctx:s}}}),a=new Bo({props:{folders:s[0],onRemove:s[7],onAddMore:s[8]}}),l=new ce({props:{size:1,weight:"medium",class:"context-section-header",$$slots:{default:[lc]},$$scope:{ctx:s}}}),u=new ce({props:{size:1,class:"file-count",$$slots:{default:[dc]},$$scope:{ctx:s}}}),v=new Yo({props:{wsContextModel:s[3]}}),{c(){e=Z("div"),t=Z("div"),S(n.$$.fragment),r=E(),S(a.$$.fragment),i=E(),o=Z("div"),c=Z("div"),S(l.$$.fragment),d=E(),S(u.$$.fragment),f=E(),S(v.$$.fragment),$(c,"class","files-header svelte-qsnirf"),$(e,"class","context-list svelte-qsnirf")},m(h,w){_(h,e,w),M(e,t),k(n,t,null),M(t,r),k(a,t,null),M(e,i),M(e,o),M(o,c),k(l,c,null),M(c,d),k(u,c,null),M(o,f),k(v,o,null),g=!0},p(h,w){const N={};512&w&&(N.$$scope={dirty:w,ctx:h}),n.$set(N);const x={};1&w&&(x.folders=h[0]),a.$set(x);const b={};512&w&&(b.$$scope={dirty:w,ctx:h}),l.$set(b);const T={};516&w&&(T.$$scope={dirty:w,ctx:h}),u.$set(T)},i(h){g||(p(n.$$.fragment,h),p(a.$$.fragment,h),p(l.$$.fragment,h),p(u.$$.fragment,h),p(v.$$.fragment,h),g=!0)},o(h){m(n.$$.fragment,h),m(a.$$.fragment,h),m(l.$$.fragment,h),m(u.$$.fragment,h),m(v.$$.fragment,h),g=!1},d(h){h&&y(e),C(n),C(a),C(l),C(u),C(v)}}}function pr(s){let e,t;return e=new zn({props:{title:"Refresh",variant:"ghost-block",color:"neutral",size:1,$$slots:{default:[pc]},$$scope:{ctx:s}}}),e.$on("click",s[5]),e.$on("keyup",_i("Enter",s[6])),{c(){S(e.$$.fragment)},m(n,r){k(e,n,r),t=!0},p(n,r){const a={};512&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function pc(s){let e,t;return e=new fi({}),{c(){S(e.$$.fragment)},m(n,r){k(e,n,r),t=!0},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function mc(s){let e,t,n=s[1]===xs.done&&pr(s);return{c(){e=Z("div"),n&&n.c(),$(e,"slot","header-right")},m(r,a){_(r,e,a),n&&n.m(e,null),t=!0},p(r,a){r[1]===xs.done?n?(n.p(r,a),2&a&&p(n,1)):(n=pr(r),n.c(),p(n,1),n.m(e,null)):n&&(W(),m(n,1,1,()=>{n=null}),Y())},i(r){t||(p(n),t=!0)},o(r){m(n),t=!1},d(r){r&&y(e),n&&n.d()}}}function hc(s){let e,t,n,r;return e=new oc({props:{icon:Qo,title:"Context",$$slots:{"header-right":[mc],default:[uc]},$$scope:{ctx:s}}}),e.$on("contextmenu",fc),{c(){S(e.$$.fragment)},m(a,i){k(e,a,i),t=!0,n||(r=et(window,"message",s[3].handleMessageFromExtension),n=!0)},p(a,[i]){const o={};519&i&&(o.$$scope={dirty:i,ctx:a}),e.$set(o)},i(a){t||(p(e.$$.fragment,a),t=!0)},o(a){m(e.$$.fragment,a),t=!1},d(a){C(e,a),n=!1,r()}}}const fc=s=>s.preventDefault();function gc(s,e,t){let n,r,a,i,o=new xt(Ve,new yi(Ve.postMessage));return rt(s,o,c=>t(4,r=c)),s.$$.update=()=>{16&s.$$.dirty&&t(0,a=r.sourceFolders.sort((c,l)=>c.isWorkspaceFolder!==l.isWorkspaceFolder?c.isWorkspaceFolder?-1:1:c.fileId.folderRoot.localeCompare(l.fileId.folderRoot))),16&s.$$.dirty&&t(1,i=r.syncStatus),1&s.$$.dirty&&t(2,n=a.reduce((c,l)=>c+(l.trackedFileCount??0),0))},[a,i,n,o,r,()=>o.requestRefresh(),()=>o.requestRefresh(),c=>o.removeSourceFolder(c),()=>o.addMoreSourceFolders()]}class vc extends ue{constructor(e){super(),pe(this,e,gc,hc,de,{})}}function Ra(s){return function(e){switch(typeof e){case"object":return e!=null;case"function":return!0;default:return!1}}(s)&&"name"in s}function mr(s){return Ra(s)&&"component"in s}function $c(s){let e,t;return{c(){e=Me("svg"),t=Me("path"),$(t,"d","M5.5 1.75V3H10.5V1.75C10.5 1.625 10.375 1.5 10.25 1.5H5.75C5.59375 1.5 5.5 1.625 5.5 1.75ZM4 3V1.75C4 0.8125 4.78125 0 5.75 0H10.25C11.1875 0 12 0.8125 12 1.75V3H14C15.0938 3 16 3.90625 16 5V8.75V13C16 14.125 15.0938 15 14 15H2C0.875 15 0 14.125 0 13V8.75V5C0 3.90625 0.875 3 2 3H4ZM1.5 9.5V13C1.5 13.2812 1.71875 13.5 2 13.5H14C14.25 13.5 14.5 13.2812 14.5 13V9.5H10V10C10 10.5625 9.53125 11 9 11H7C6.4375 11 6 10.5625 6 10V9.5H1.5ZM6 8H10H14.5V5C14.5 4.75 14.25 4.5 14 4.5H11.25H4.75H2C1.71875 4.5 1.5 4.75 1.5 5V8H6Z"),$(t,"fill","currentColor"),$(e,"width","16"),$(e,"height","15"),$(e,"viewBox","0 0 16 15"),$(e,"xmlns","http://www.w3.org/2000/svg")},m(n,r){_(n,e,r),M(e,t)},p:X,i:X,o:X,d(n){n&&y(e)}}}class La extends ue{constructor(e){super(),pe(this,e,null,$c,de,{})}}const yc=s=>({item:1&s}),hr=s=>({item:s[0]}),_c=s=>({}),fr=s=>({});function gr(s){var l;let e,t,n,r,a;e=new ce({props:{size:4,weight:"medium",color:"neutral",$$slots:{default:[xc]},$$scope:{ctx:s}}});let i=((l=s[0])==null?void 0:l.description)&&vr(s);const o=s[1].content,c=Ee(o,s,s[2],hr);return{c(){S(e.$$.fragment),t=E(),i&&i.c(),n=E(),r=Z("div"),c&&c.c(),$(r,"class","c-navigation__content-container svelte-z0ijuz")},m(d,u){k(e,d,u),_(d,t,u),i&&i.m(d,u),_(d,n,u),_(d,r,u),c&&c.m(r,null),a=!0},p(d,u){var v;const f={};5&u&&(f.$$scope={dirty:u,ctx:d}),e.$set(f),(v=d[0])!=null&&v.description?i?(i.p(d,u),1&u&&p(i,1)):(i=vr(d),i.c(),p(i,1),i.m(n.parentNode,n)):i&&(W(),m(i,1,1,()=>{i=null}),Y()),c&&c.p&&(!a||5&u)&&Ie(c,o,d,d[2],a?je(o,d[2],u,yc):Pe(d[2]),hr)},i(d){a||(p(e.$$.fragment,d),p(i),p(c,d),a=!0)},o(d){m(e.$$.fragment,d),m(i),m(c,d),a=!1},d(d){d&&(y(t),y(n),y(r)),C(e,d),i&&i.d(d),c&&c.d(d)}}}function xc(s){var r;let e,t,n=((r=s[0])==null?void 0:r.name)+"";return{c(){e=Z("div"),t=U(n),$(e,"class","c-navigation__content-header svelte-z0ijuz")},m(a,i){_(a,e,i),M(e,t)},p(a,i){var o;1&i&&n!==(n=((o=a[0])==null?void 0:o.name)+"")&&he(t,n)},d(a){a&&y(e)}}}function vr(s){let e,t;return e=new ce({props:{color:"secondary",size:1,weight:"light",$$slots:{default:[wc]},$$scope:{ctx:s}}}),{c(){S(e.$$.fragment)},m(n,r){k(e,n,r),t=!0},p(n,r){const a={};5&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function wc(s){var r;let e,t,n=((r=s[0])==null?void 0:r.description)+"";return{c(){e=Z("div"),t=U(n),$(e,"class","c-navigation__content-description svelte-z0ijuz")},m(a,i){_(a,e,i),M(e,t)},p(a,i){var o;1&i&&n!==(n=((o=a[0])==null?void 0:o.description)+"")&&he(t,n)},d(a){a&&y(e)}}}function bc(s){let e,t,n,r,a;const i=s[1].header,o=Ee(i,s,s[2],fr);let c=s[0]!=null&&gr(s);return{c(){var l;e=Z("div"),o&&o.c(),t=E(),n=Z("div"),c&&c.c(),$(e,"class","c-navigation__content svelte-z0ijuz"),$(e,"id",r=(l=s[0])==null?void 0:l.id)},m(l,d){_(l,e,d),o&&o.m(e,null),M(e,t),M(e,n),c&&c.m(n,null),a=!0},p(l,[d]){var u;o&&o.p&&(!a||4&d)&&Ie(o,i,l,l[2],a?je(i,l[2],d,_c):Pe(l[2]),fr),l[0]!=null?c?(c.p(l,d),1&d&&p(c,1)):(c=gr(l),c.c(),p(c,1),c.m(n,null)):c&&(W(),m(c,1,1,()=>{c=null}),Y()),(!a||1&d&&r!==(r=(u=l[0])==null?void 0:u.id))&&$(e,"id",r)},i(l){a||(p(o,l),p(c),a=!0)},o(l){m(o,l),m(c),a=!1},d(l){l&&y(e),o&&o.d(l),c&&c.d()}}}function Sc(s,e,t){let{$$slots:n={},$$scope:r}=e,{item:a}=e;return s.$$set=i=>{"item"in i&&t(0,a=i.item),"$$scope"in i&&t(2,r=i.$$scope)},[a,n,r]}class Fa extends ue{constructor(e){super(),pe(this,e,Sc,bc,de,{item:0})}}function kc(s,e){let t;function n({scrollTo:r,delay:a,options:i}){clearTimeout(t),r&&(t=setTimeout(()=>{s.scrollIntoView(i)},a))}return n(e),{update:n,destroy(){clearTimeout(t)}}}function $r(s,e,t){const n=s.slice();return n[13]=e[t][0],n[14]=e[t][1],n}function yr(s,e,t){const n=s.slice();return n[22]=e[t],n}const Cc=s=>({item:32&s}),_r=s=>({slot:"content",item:s[22]}),Tc=s=>({label:32&s,mode:4&s}),xr=s=>({label:s[13],mode:s[2]}),Mc=s=>({item:1&s}),wr=s=>({item:s[0]});function br(s,e,t){const n=s.slice();return n[13]=e[t][0],n[14]=e[t][1],n}function Sr(s,e,t){const n=s.slice();return n[17]=e[t],n}const Zc=s=>({label:32&s,mode:4&s}),kr=s=>({label:s[13],mode:s[2]}),Nc=s=>({item:1&s,selectedId:2&s}),Cr=s=>({slot:"header",item:s[0],selectedId:s[1]}),Ac=s=>({item:1&s,isSelected:3&s}),Tr=s=>{var e;return{slot:"content",item:s[0],isSelected:((e=s[0])==null?void 0:e.id)===s[1]}};function Oc(s){let e,t,n;const r=s[10].header,a=Ee(r,s,s[12],wr);let i=xe(s[5]),o=[];for(let l=0;l<i.length;l+=1)o[l]=Zr($r(s,i,l));const c=l=>m(o[l],1,1,()=>{o[l]=null});return{c(){e=Z("div"),a&&a.c(),t=E();for(let l=0;l<o.length;l+=1)o[l].c();$(e,"class","c-navigation__flat svelte-1u6mxxy")},m(l,d){_(l,e,d),a&&a.m(e,null),M(e,t);for(let u=0;u<o.length;u+=1)o[u]&&o[u].m(e,null);n=!0},p(l,d){if(a&&a.p&&(!n||4097&d)&&Ie(a,r,l,l[12],n?je(r,l[12],d,Mc):Pe(l[12]),wr),4134&d){let u;for(i=xe(l[5]),u=0;u<i.length;u+=1){const f=$r(l,i,u);o[u]?(o[u].p(f,d),p(o[u],1)):(o[u]=Zr(f),o[u].c(),p(o[u],1),o[u].m(e,null))}for(W(),u=i.length;u<o.length;u+=1)c(u);Y()}},i(l){if(!n){p(a,l);for(let d=0;d<i.length;d+=1)p(o[d]);n=!0}},o(l){m(a,l),o=o.filter(Boolean);for(let d=0;d<o.length;d+=1)m(o[d]);n=!1},d(l){l&&y(e),a&&a.d(l),In(o,l)}}}function Ec(s){let e,t;return e=new xi({props:{initialWidth:200,expandedMinWidth:150,columnLayoutThreshold:0,showButton:s[3],minimized:!1,$$slots:{right:[Dc],left:[Lc]},$$scope:{ctx:s}}}),{c(){S(e.$$.fragment)},m(n,r){k(e,n,r),t=!0},p(n,r){const a={};8&r&&(a.showButton=n[3]),4135&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Ic(s){let e,t,n,r,a,i,o=s[13]+"";return t=new La({}),{c(){e=Z("span"),S(t.$$.fragment),n=E(),r=Z("span"),a=U(o),$(e,"class","c-navigation__head-icon")},m(c,l){_(c,e,l),k(t,e,null),_(c,n,l),_(c,r,l),M(r,a),i=!0},p(c,l){(!i||32&l)&&o!==(o=c[13]+"")&&he(a,o)},i(c){i||(p(t.$$.fragment,c),i=!0)},o(c){m(t.$$.fragment,c),i=!1},d(c){c&&(y(e),y(n),y(r)),C(t)}}}function Pc(s){let e;const t=s[10].content,n=Ee(t,s,s[12],_r);return{c(){n&&n.c()},m(r,a){n&&n.m(r,a),e=!0},p(r,a){n&&n.p&&(!e||4128&a)&&Ie(n,t,r,r[12],e?je(t,r[12],a,Cc):Pe(r[12]),_r)},i(r){e||(p(n,r),e=!0)},o(r){m(n,r),e=!1},d(r){n&&n.d(r)}}}function Mr(s){let e,t,n,r,a,i,o;return t=new Fa({props:{item:s[22],$$slots:{content:[Pc]},$$scope:{ctx:s}}}),{c(){e=Z("span"),S(t.$$.fragment),n=E()},m(c,l){_(c,e,l),k(t,e,null),M(e,n),a=!0,i||(o=Xa(r=kc.call(null,e,{scrollTo:s[2]==="flat"&&s[22].id===s[1],delay:300,options:{behavior:"smooth"}})),i=!0)},p(c,l){s=c;const d={};32&l&&(d.item=s[22]),4128&l&&(d.$$scope={dirty:l,ctx:s}),t.$set(d),r&&wt(r.update)&&38&l&&r.update.call(null,{scrollTo:s[2]==="flat"&&s[22].id===s[1],delay:300,options:{behavior:"smooth"}})},i(c){a||(p(t.$$.fragment,c),a=!0)},o(c){m(t.$$.fragment,c),a=!1},d(c){c&&y(e),C(t),i=!1,o()}}}function Zr(s){let e,t,n,r;const a=s[10].group,i=Ee(a,s,s[12],xr),o=i||function(u){let f,v;return f=new ce({props:{color:"secondary",size:2,weight:"medium",$$slots:{default:[Ic]},$$scope:{ctx:u}}}),{c(){S(f.$$.fragment)},m(g,h){k(f,g,h),v=!0},p(g,h){const w={};4128&h&&(w.$$scope={dirty:h,ctx:g}),f.$set(w)},i(g){v||(p(f.$$.fragment,g),v=!0)},o(g){m(f.$$.fragment,g),v=!1},d(g){C(f,g)}}}(s);let c=xe(s[14]),l=[];for(let u=0;u<c.length;u+=1)l[u]=Mr(yr(s,c,u));const d=u=>m(l[u],1,1,()=>{l[u]=null});return{c(){e=Z("div"),o&&o.c(),t=E();for(let u=0;u<l.length;u+=1)l[u].c();n=Se(),$(e,"class","c-navigation__head svelte-1u6mxxy")},m(u,f){_(u,e,f),o&&o.m(e,null),_(u,t,f);for(let v=0;v<l.length;v+=1)l[v]&&l[v].m(u,f);_(u,n,f),r=!0},p(u,f){if(i?i.p&&(!r||4132&f)&&Ie(i,a,u,u[12],r?je(a,u[12],f,Tc):Pe(u[12]),xr):o&&o.p&&(!r||32&f)&&o.p(u,r?f:-1),4134&f){let v;for(c=xe(u[14]),v=0;v<c.length;v+=1){const g=yr(u,c,v);l[v]?(l[v].p(g,f),p(l[v],1)):(l[v]=Mr(g),l[v].c(),p(l[v],1),l[v].m(n.parentNode,n))}for(W(),v=c.length;v<l.length;v+=1)d(v);Y()}},i(u){if(!r){p(o,u);for(let f=0;f<c.length;f+=1)p(l[f]);r=!0}},o(u){m(o,u),l=l.filter(Boolean);for(let f=0;f<l.length;f+=1)m(l[f]);r=!1},d(u){u&&(y(e),y(t),y(n)),o&&o.d(u),In(l,u)}}}function jc(s){let e,t=s[13]+"";return{c(){e=U(t)},m(n,r){_(n,e,r)},p(n,r){32&r&&t!==(t=n[13]+"")&&he(e,t)},d(n){n&&y(e)}}}function Rc(s){let e,t,n,r,a,i=s[17].name+"";var o=s[17].icon;return o&&(t=bt(o,{})),{c(){e=Z("span"),t&&S(t.$$.fragment),n=E(),r=U(i),$(e,"class","c-navigation__head-icon")},m(c,l){_(c,e,l),t&&k(t,e,null),_(c,n,l),_(c,r,l),a=!0},p(c,l){if(32&l&&o!==(o=c[17].icon)){if(t){W();const d=t;m(d.$$.fragment,1,0,()=>{C(d,1)}),Y()}o?(t=bt(o,{}),S(t.$$.fragment),p(t.$$.fragment,1),k(t,e,null)):t=null}(!a||32&l)&&i!==(i=c[17].name+"")&&he(r,i)},i(c){a||(t&&p(t.$$.fragment,c),a=!0)},o(c){t&&m(t.$$.fragment,c),a=!1},d(c){c&&(y(e),y(n),y(r)),t&&C(t)}}}function Nr(s){let e,t,n,r,a,i;function o(){return s[11](s[17])}return t=new ce({props:{size:2,weight:"regular",color:"primary",$$slots:{default:[Rc]},$$scope:{ctx:s}}}),{c(){e=Z("button"),S(t.$$.fragment),n=E(),$(e,"class","c-navigation__item svelte-1u6mxxy"),_e(e,"is-active",s[17].id===s[1])},m(c,l){_(c,e,l),k(t,e,null),M(e,n),r=!0,a||(i=et(e,"click",o),a=!0)},p(c,l){s=c;const d={};4128&l&&(d.$$scope={dirty:l,ctx:s}),t.$set(d),(!r||34&l)&&_e(e,"is-active",s[17].id===s[1])},i(c){r||(p(t.$$.fragment,c),r=!0)},o(c){m(t.$$.fragment,c),r=!1},d(c){c&&y(e),C(t),a=!1,i()}}}function Ar(s){let e,t,n,r,a;const i=s[10].group,o=Ee(i,s,s[12],kr),c=o||function(f){let v,g,h,w,N;return g=new La({}),w=new ce({props:{size:2,color:"primary",$$slots:{default:[jc]},$$scope:{ctx:f}}}),{c(){v=Z("div"),S(g.$$.fragment),h=E(),S(w.$$.fragment),$(v,"class","c-navigation__head svelte-1u6mxxy")},m(x,b){_(x,v,b),k(g,v,null),M(v,h),k(w,v,null),N=!0},p(x,b){const T={};4128&b&&(T.$$scope={dirty:b,ctx:x}),w.$set(T)},i(x){N||(p(g.$$.fragment,x),p(w.$$.fragment,x),N=!0)},o(x){m(g.$$.fragment,x),m(w.$$.fragment,x),N=!1},d(x){x&&y(v),C(g),C(w)}}}(s);let l=xe(s[14]),d=[];for(let f=0;f<l.length;f+=1)d[f]=Nr(Sr(s,l,f));const u=f=>m(d[f],1,1,()=>{d[f]=null});return{c(){e=Z("div"),c&&c.c(),t=E(),n=Z("div");for(let f=0;f<d.length;f+=1)d[f].c();r=E(),$(n,"class","c-navigation__items svelte-1u6mxxy"),$(e,"class","c-navigation__group")},m(f,v){_(f,e,v),c&&c.m(e,null),M(e,t),M(e,n);for(let g=0;g<d.length;g+=1)d[g]&&d[g].m(n,null);M(e,r),a=!0},p(f,v){if(o?o.p&&(!a||4132&v)&&Ie(o,i,f,f[12],a?je(i,f[12],v,Zc):Pe(f[12]),kr):c&&c.p&&(!a||32&v)&&c.p(f,a?v:-1),98&v){let g;for(l=xe(f[14]),g=0;g<l.length;g+=1){const h=Sr(f,l,g);d[g]?(d[g].p(h,v),p(d[g],1)):(d[g]=Nr(h),d[g].c(),p(d[g],1),d[g].m(n,null))}for(W(),g=l.length;g<d.length;g+=1)u(g);Y()}},i(f){if(!a){p(c,f);for(let v=0;v<l.length;v+=1)p(d[v]);a=!0}},o(f){m(c,f),d=d.filter(Boolean);for(let v=0;v<d.length;v+=1)m(d[v]);a=!1},d(f){f&&y(e),c&&c.d(f),In(d,f)}}}function Or(s){let e,t,n=xe(s[5]),r=[];for(let i=0;i<n.length;i+=1)r[i]=Ar(br(s,n,i));const a=i=>m(r[i],1,1,()=>{r[i]=null});return{c(){for(let i=0;i<r.length;i+=1)r[i].c();e=Se()},m(i,o){for(let c=0;c<r.length;c+=1)r[c]&&r[c].m(i,o);_(i,e,o),t=!0},p(i,o){if(4198&o){let c;for(n=xe(i[5]),c=0;c<n.length;c+=1){const l=br(i,n,c);r[c]?(r[c].p(l,o),p(r[c],1)):(r[c]=Ar(l),r[c].c(),p(r[c],1),r[c].m(e.parentNode,e))}for(W(),c=n.length;c<r.length;c+=1)a(c);Y()}},i(i){if(!t){for(let o=0;o<n.length;o+=1)p(r[o]);t=!0}},o(i){r=r.filter(Boolean);for(let o=0;o<r.length;o+=1)m(r[o]);t=!1},d(i){i&&y(e),In(r,i)}}}function Lc(s){let e,t,n=s[1],r=Or(s);return{c(){e=Z("nav"),r.c(),$(e,"class","c-navigation__nav svelte-1u6mxxy"),$(e,"slot","left")},m(a,i){_(a,e,i),r.m(e,null),t=!0},p(a,i){2&i&&de(n,n=a[1])?(W(),m(r,1,1,X),Y(),r=Or(a),r.c(),p(r,1),r.m(e,null)):r.p(a,i)},i(a){t||(p(r),t=!0)},o(a){m(r),t=!1},d(a){a&&y(e),r.d(a)}}}function Fc(s){let e;const t=s[10].header,n=Ee(t,s,s[12],Cr);return{c(){n&&n.c()},m(r,a){n&&n.m(r,a),e=!0},p(r,a){n&&n.p&&(!e||4099&a)&&Ie(n,t,r,r[12],e?je(t,r[12],a,Nc):Pe(r[12]),Cr)},i(r){e||(p(n,r),e=!0)},o(r){m(n,r),e=!1},d(r){n&&n.d(r)}}}function Er(s){let e,t,n;const r=[s[0].props];var a=s[0].component;function i(o,c){let l={};for(let d=0;d<r.length;d+=1)l=ye(l,r[d]);return c!==void 0&&1&c&&(l=ye(l,$t(r,[Vs(o[0].props)]))),{props:l}}return a&&(e=bt(a,i(s))),{c(){e&&S(e.$$.fragment),t=Se()},m(o,c){e&&k(e,o,c),_(o,t,c),n=!0},p(o,c){if(1&c&&a!==(a=o[0].component)){if(e){W();const l=e;m(l.$$.fragment,1,0,()=>{C(l,1)}),Y()}a?(e=bt(a,i(o,c)),S(e.$$.fragment),p(e.$$.fragment,1),k(e,t.parentNode,t)):e=null}else if(a){const l=1&c?$t(r,[Vs(o[0].props)]):{};e.$set(l)}},i(o){n||(e&&p(e.$$.fragment,o),n=!0)},o(o){e&&m(e.$$.fragment,o),n=!1},d(o){o&&y(t),e&&C(e,o)}}}function zc(s){let e;const t=s[10].content,n=Ee(t,s,s[12],Tr),r=n||function(a){let i,o,c=mr(a[0])&&Ir(a[0],a[2],a[1]),l=c&&Er(a);return{c(){l&&l.c(),i=Se()},m(d,u){l&&l.m(d,u),_(d,i,u),o=!0},p(d,u){7&u&&(c=mr(d[0])&&Ir(d[0],d[2],d[1])),c?l?(l.p(d,u),7&u&&p(l,1)):(l=Er(d),l.c(),p(l,1),l.m(i.parentNode,i)):l&&(W(),m(l,1,1,()=>{l=null}),Y())},i(d){o||(p(l),o=!0)},o(d){m(l),o=!1},d(d){d&&y(i),l&&l.d(d)}}}(s);return{c(){r&&r.c()},m(a,i){r&&r.m(a,i),e=!0},p(a,i){n?n.p&&(!e||4099&i)&&Ie(n,t,a,a[12],e?je(t,a[12],i,Ac):Pe(a[12]),Tr):r&&r.p&&(!e||7&i)&&r.p(a,e?i:-1)},i(a){e||(p(r,a),e=!0)},o(a){m(r,a),e=!1},d(a){r&&r.d(a)}}}function Dc(s){let e,t;return e=new Fa({props:{item:s[0],slot:"right",$$slots:{content:[zc],header:[Fc]},$$scope:{ctx:s}}}),{c(){S(e.$$.fragment)},m(n,r){k(e,n,r),t=!0},p(n,r){const a={};1&r&&(a.item=n[0]),4103&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Vc(s){let e,t,n,r,a;const i=[Ec,Oc],o=[];function c(l,d){return l[2]==="tree"?0:1}return t=c(s),n=o[t]=i[t](s),{c(){e=Z("div"),n.c(),$(e,"class",r="c-navigation c-navigation--mode__"+s[2]+" "+s[4]+" svelte-1u6mxxy")},m(l,d){_(l,e,d),o[t].m(e,null),a=!0},p(l,[d]){let u=t;t=c(l),t===u?o[t].p(l,d):(W(),m(o[u],1,1,()=>{o[u]=null}),Y(),n=o[t],n?n.p(l,d):(n=o[t]=i[t](l),n.c()),p(n,1),n.m(e,null)),(!a||20&d&&r!==(r="c-navigation c-navigation--mode__"+l[2]+" "+l[4]+" svelte-1u6mxxy"))&&$(e,"class",r)},i(l){a||(p(n),a=!0)},o(l){m(n),a=!1},d(l){l&&y(e),o[t].d()}}}function $s(s,e,t,n,r,a){return{name:s,description:e,icon:t,id:n}}function Ir(s,e,t){return e!=="tree"||(s==null?void 0:s.id)===t}function Uc(s,e,t){let{$$slots:n={},$$scope:r}=e,{group:a="Workspace Settings"}=e,{items:i=[]}=e,{item:o}=e,{mode:c="tree"}=e,{selectedId:l}=e,{onNavigationChangeItem:d=h=>{}}=e,{showButton:u=!0}=e,{class:f=""}=e,v=new Map;function g(h){t(0,o=h),t(1,l=h==null?void 0:h.id)}return s.$$set=h=>{"group"in h&&t(7,a=h.group),"items"in h&&t(8,i=h.items),"item"in h&&t(0,o=h.item),"mode"in h&&t(2,c=h.mode),"selectedId"in h&&t(1,l=h.selectedId),"onNavigationChangeItem"in h&&t(9,d=h.onNavigationChangeItem),"showButton"in h&&t(3,u=h.showButton),"class"in h&&t(4,f=h.class),"$$scope"in h&&t(12,r=h.$$scope)},s.$$.update=()=>{259&s.$$.dirty&&(l?t(0,o=i.find(h=>(h==null?void 0:h.id)===l)):t(1,l=o==null?void 0:o.id)),384&s.$$.dirty&&t(5,v=i.reduce((h,w)=>{if(!w)return h;const N=w.group??a,x=h.get(N)??[];return x.push(w),h.set(N,x),h},new Map)),257&s.$$.dirty&&(o||t(0,o=i[0])),514&s.$$.dirty&&d(l)},[o,l,c,u,f,v,g,a,i,d,n,h=>g(h),r]}class qc extends ue{constructor(e){super(),pe(this,e,Uc,Vc,de,{group:7,items:8,item:0,mode:2,selectedId:1,onNavigationChangeItem:9,showButton:3,class:4})}}function Bc(s){let e,t;return{c(){e=Me("svg"),t=Me("path"),$(t,"d","M3.13281 0.886719L5.97656 3.07422C6.14062 3.21094 6.25 3.40234 6.25 3.59375V5.07031L9.23047 8.05078C10.0234 7.66797 11.0078 7.80469 11.6641 8.46094L14.7266 11.5234C15.082 11.8516 15.082 12.4258 14.7266 12.7539L12.9766 14.5039C12.6484 14.8594 12.0742 14.8594 11.7461 14.5039L8.68359 11.4414C8.02734 10.7852 7.89062 9.77344 8.30078 8.98047L5.32031 6H3.81641C3.625 6 3.43359 5.91797 3.29688 5.75391L1.10938 2.91016C0.917969 2.63672 0.945312 2.28125 1.19141 2.03516L2.28516 0.941406C2.50391 0.722656 2.88672 0.695312 3.13281 0.886719ZM1.62891 11.0586L5.375 7.3125L6.30469 8.24219L2.55859 11.9883C2.39453 12.1523 2.3125 12.3711 2.3125 12.5898C2.3125 13.0547 2.69531 13.4375 3.16016 13.4375C3.37891 13.4375 3.59766 13.3555 3.76172 13.1914L7.17969 9.77344C7.15234 10.293 7.26172 10.8125 7.50781 11.3047L4.69141 14.1211C4.28125 14.5312 3.73438 14.75 3.16016 14.75C1.95703 14.75 1 13.793 1 12.5898C1 12.0156 1.21875 11.4688 1.62891 11.0586ZM13.6602 5.23438L12.9766 5.94531C12.6484 6.27344 12.2109 6.46484 11.7461 6.46484H11.0625C10.0781 6.46484 9.3125 5.67188 9.3125 4.71484V4.00391C9.3125 3.53906 9.47656 3.10156 9.80469 2.77344L10.5156 2.08984C8.875 2.14453 7.5625 3.48438 7.5625 5.125V5.15234L7.125 4.71484V3.59375C7.125 3.32031 7.04297 3.04688 6.90625 2.82812C7.67188 1.59766 9.03906 0.75 10.625 0.75C11.2812 0.75 11.9375 0.914062 12.5117 1.1875C12.7578 1.32422 12.7852 1.65234 12.5938 1.84375L10.7344 3.70312C10.6523 3.78516 10.625 3.89453 10.625 4.00391V4.6875C10.625 4.93359 10.8164 5.125 11.0625 5.125L11.7461 5.15234C11.8555 5.15234 11.9648 5.09766 12.0469 5.01562L13.9062 3.15625C14.0977 2.96484 14.4258 2.99219 14.5625 3.23828C14.8359 3.8125 15 4.46875 15 5.15234C15 6.60156 14.2617 7.91406 13.1406 8.70703L12.293 7.83203C12.2656 7.80469 12.2383 7.77734 12.2109 7.75C13.0586 7.23047 13.6328 6.30078 13.6602 5.23438Z"),$(t,"fill","currentColor"),$(e,"width","16"),$(e,"height","16"),$(e,"viewBox","0 0 16 16"),$(e,"xmlns","http://www.w3.org/2000/svg")},m(n,r){_(n,e,r),M(e,t)},p:X,i:X,o:X,d(n){n&&y(e)}}}class Jc extends ue{constructor(e){super(),pe(this,e,null,Bc,de,{})}}function Hc(s){let e,t;return{c(){e=Me("svg"),t=Me("path"),$(t,"d","M13.6875 7.75C13.6875 5.72656 12.5938 3.86719 10.8438 2.82812C9.06641 1.81641 6.90625 1.81641 5.15625 2.82812C3.37891 3.86719 2.3125 5.72656 2.3125 7.75C2.3125 9.80078 3.37891 11.6602 5.15625 12.6992C6.90625 13.7109 9.06641 13.7109 10.8438 12.6992C12.5938 11.6602 13.6875 9.80078 13.6875 7.75ZM1 7.75C1 5.26172 2.3125 2.96484 4.5 1.70703C6.66016 0.449219 9.3125 0.449219 11.5 1.70703C13.6602 2.96484 15 5.26172 15 7.75C15 10.2656 13.6602 12.5625 11.5 13.8203C9.3125 15.0781 6.66016 15.0781 4.5 13.8203C2.3125 12.5625 1 10.2656 1 7.75ZM9.36719 9.66406L5.42969 11.168C4.91016 11.3867 4.36328 10.8398 4.58203 10.3203L6.08594 6.38281C6.19531 6.13672 6.35938 5.97266 6.60547 5.86328L10.543 4.35938C11.0898 4.14062 11.6094 4.66016 11.3906 5.20703L9.88672 9.14453C9.77734 9.39062 9.61328 9.55469 9.36719 9.66406ZM8.875 7.75C8.875 7.28516 8.46484 6.875 8 6.875C7.50781 6.875 7.125 7.28516 7.125 7.75C7.125 8.24219 7.50781 8.625 8 8.625C8.46484 8.625 8.875 8.24219 8.875 7.75Z"),$(t,"fill","currentColor"),$(e,"width","16"),$(e,"height","16"),$(e,"viewBox","0 0 16 16"),$(e,"xmlns","http://www.w3.org/2000/svg")},m(n,r){_(n,e,r),M(e,t)},p:X,i:X,o:X,d(n){n&&y(e)}}}class Kc extends ue{constructor(e){super(),pe(this,e,null,Hc,de,{})}}function Gc(s){let e,t;return{c(){e=Me("svg"),t=Me("path"),$(t,"d","M6.25 10.8125H12.375C12.5938 10.8125 12.8125 10.6211 12.8125 10.375V4.25H11.5C11.0078 4.25 10.625 3.86719 10.625 3.375V2.0625H6.25C6.00391 2.0625 5.8125 2.28125 5.8125 2.5V10.375C5.8125 10.6211 6.00391 10.8125 6.25 10.8125ZM12.375 12.125H6.25C5.26562 12.125 4.5 11.3594 4.5 10.375V2.5C4.5 1.54297 5.26562 0.75 6.25 0.75H10.7617C11.2266 0.75 11.6641 0.941406 11.9922 1.26953L13.6055 2.88281C13.9336 3.21094 14.125 3.64844 14.125 4.11328V10.375C14.125 11.3594 13.332 12.125 12.375 12.125ZM2.53125 3.375C2.88672 3.375 3.1875 3.67578 3.1875 4.03125V11.0312C3.1875 12.3711 4.25391 13.4375 5.59375 13.4375H10.8438C11.1992 13.4375 11.5 13.7383 11.5 14.0938C11.5 14.4766 11.1992 14.75 10.8438 14.75H5.59375C3.51562 14.75 1.875 13.1094 1.875 11.0312V4.03125C1.875 3.67578 2.14844 3.375 2.53125 3.375Z"),$(t,"fill","currentColor"),$(e,"width","16"),$(e,"height","16"),$(e,"viewBox","0 0 16 16"),$(e,"xmlns","http://www.w3.org/2000/svg")},m(n,r){_(n,e,r),M(e,t)},p:X,i:X,o:X,d(n){n&&y(e)}}}class Wc extends ue{constructor(e){super(),pe(this,e,null,Gc,de,{})}}const Yc=s=>({}),Pr=s=>({}),Xc=s=>({}),jr=s=>({});function Qc(s){let e;const t=s[7]["header-left"],n=Ee(t,s,s[8],jr);return{c(){n&&n.c()},m(r,a){n&&n.m(r,a),e=!0},p(r,a){n&&n.p&&(!e||256&a)&&Ie(n,t,r,r[8],e?je(t,r[8],a,Xc):Pe(r[8]),jr)},i(r){e||(p(n,r),e=!0)},o(r){m(n,r),e=!1},d(r){n&&n.d(r)}}}function el(s){let e,t,n,r=s[0]&&Rr(s),a=s[1]&&Lr(s);return{c(){r&&r.c(),e=E(),a&&a.c(),t=Se()},m(i,o){r&&r.m(i,o),_(i,e,o),a&&a.m(i,o),_(i,t,o),n=!0},p(i,o){i[0]?r?(r.p(i,o),1&o&&p(r,1)):(r=Rr(i),r.c(),p(r,1),r.m(e.parentNode,e)):r&&(W(),m(r,1,1,()=>{r=null}),Y()),i[1]?a?(a.p(i,o),2&o&&p(a,1)):(a=Lr(i),a.c(),p(a,1),a.m(t.parentNode,t)):a&&(W(),m(a,1,1,()=>{a=null}),Y())},i(i){n||(p(r),p(a),n=!0)},o(i){m(r),m(a),n=!1},d(i){i&&(y(e),y(t)),r&&r.d(i),a&&a.d(i)}}}function Rr(s){let e,t,n;var r=s[0];return r&&(t=bt(r,{})),{c(){e=Z("div"),t&&S(t.$$.fragment),$(e,"class","icon-wrapper svelte-7mispq")},m(a,i){_(a,e,i),t&&k(t,e,null),n=!0},p(a,i){if(1&i&&r!==(r=a[0])){if(t){W();const o=t;m(o.$$.fragment,1,0,()=>{C(o,1)}),Y()}r?(t=bt(r,{}),S(t.$$.fragment),p(t.$$.fragment,1),k(t,e,null)):t=null}},i(a){n||(t&&p(t.$$.fragment,a),n=!0)},o(a){t&&m(t.$$.fragment,a),n=!1},d(a){a&&y(e),t&&C(t)}}}function Lr(s){let e,t;return e=new ce({props:{color:"neutral",size:1,weight:"light",class:"card-title",$$slots:{default:[tl]},$$scope:{ctx:s}}}),{c(){S(e.$$.fragment)},m(n,r){k(e,n,r),t=!0},p(n,r){const a={};258&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function tl(s){let e;return{c(){e=U(s[1])},m(t,n){_(t,e,n)},p(t,n){2&n&&he(e,t[1])},d(t){t&&y(e)}}}function Fr(s){let e,t;const n=s[7].default,r=Ee(n,s,s[8],null);return{c(){e=Z("div"),r&&r.c(),$(e,"class","settings-card-body")},m(a,i){_(a,e,i),r&&r.m(e,null),t=!0},p(a,i){r&&r.p&&(!t||256&i)&&Ie(r,n,a,a[8],t?je(n,a[8],i,null):Pe(a[8]),null)},i(a){t||(p(r,a),t=!0)},o(a){m(r,a),t=!1},d(a){a&&y(e),r&&r.d(a)}}}function nl(s){let e,t,n,r,a,i,o,c,l;const d=[el,Qc],u=[];function f(x,b){return x[0]||x[1]?0:1}r=f(s),a=u[r]=d[r](s);const v=s[7]["header-right"],g=Ee(v,s,s[8],Pr);let h=s[4].default&&Fr(s),w=[{class:s[2]},s[3]],N={};for(let x=0;x<w.length;x+=1)N=ye(N,w[x]);return{c(){e=Z("div"),t=Z("div"),n=Z("div"),a.c(),i=E(),o=Z("div"),g&&g.c(),c=E(),h&&h.c(),$(n,"class","settings-card-left svelte-7mispq"),$(o,"class","settings-card-right svelte-7mispq"),$(t,"class","settings-card-content svelte-7mispq"),Kn(e,N),_e(e,"svelte-7mispq",!0)},m(x,b){_(x,e,b),M(e,t),M(t,n),u[r].m(n,null),M(t,i),M(t,o),g&&g.m(o,null),M(e,c),h&&h.m(e,null),l=!0},p(x,[b]){let T=r;r=f(x),r===T?u[r].p(x,b):(W(),m(u[T],1,1,()=>{u[T]=null}),Y(),a=u[r],a?a.p(x,b):(a=u[r]=d[r](x),a.c()),p(a,1),a.m(n,null)),g&&g.p&&(!l||256&b)&&Ie(g,v,x,x[8],l?je(v,x[8],b,Yc):Pe(x[8]),Pr),x[4].default?h?(h.p(x,b),16&b&&p(h,1)):(h=Fr(x),h.c(),p(h,1),h.m(e,null)):h&&(W(),m(h,1,1,()=>{h=null}),Y()),Kn(e,N=$t(w,[(!l||4&b)&&{class:x[2]},8&b&&x[3]])),_e(e,"svelte-7mispq",!0)},i(x){l||(p(a),p(g,x),p(h),l=!0)},o(x){m(a),m(g,x),m(h),l=!1},d(x){x&&y(e),u[r].d(),g&&g.d(x),h&&h.d()}}}function sl(s,e,t){let n,r,a;const i=["class","icon","title"];let o=Gn(e,i),{$$slots:c={},$$scope:l}=e;const d=ha(c);let{class:u=""}=e,{icon:f}=e,{title:v}=e;return s.$$set=g=>{e=ye(ye({},e),Je(g)),t(9,o=Gn(e,i)),"class"in g&&t(5,u=g.class),"icon"in g&&t(0,f=g.icon),"title"in g&&t(1,v=g.title),"$$scope"in g&&t(8,l=g.$$scope)},s.$$.update=()=>{t(6,{class:n,...r}=o,n,(t(3,r),t(9,o))),96&s.$$.dirty&&t(2,a=`settings-card ${u} ${n||""}`)},[f,v,a,r,d,u,n,c,l]}class za extends ue{constructor(e){super(),pe(this,e,sl,nl,de,{class:5,icon:0,title:1})}}function rl(s){let e,t,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 17 16"},s[0]],r={};for(let a=0;a<n.length;a+=1)r=ye(r,n[a]);return{c(){e=Me("svg"),t=new Pn(!0),this.h()},l(a){e=jn(a,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var i=Rn(e);t=Ln(i,!0),i.forEach(y),this.h()},h(){t.a=null,ot(e,r)},m(a,i){Fn(a,e,i),t.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M3.552 7.158a.568.568 0 0 1 .804 0l.702.702L6.23 6.688c.2-.2.511-.22.734-.06l.07.06a.568.568 0 0 1 0 .804L5.862 8.664l1.626 1.626 1.173-1.172c.2-.2.511-.22.733-.06l.071.06a.568.568 0 0 1 0 .804l-1.173 1.172.703.703c.2.2.22.51.06.733l-.06.07a.568.568 0 0 1-.804 0l-.041-.039-.812.813a3.226 3.226 0 0 1-4.043.421l-.08-.054-.959.96c-.2.2-.511.22-.733.06l-.071-.06a.568.568 0 0 1 0-.804l.96-.96-.054-.079a3.226 3.226 0 0 1 .294-3.91l.127-.133.811-.813-.038-.04a.567.567 0 0 1-.06-.734zm3.759-3.759a.568.568 0 0 1 .804 0l.038.04.815-.813a3.226 3.226 0 0 1 4.043-.421l.078.054.96-.96c.2-.2.511-.22.734-.06l.07.06a.568.568 0 0 1 0 .804l-.96.96.055.079a3.226 3.226 0 0 1-.295 3.91l-.126.133-.814.813.04.04c.201.2.221.511.06.734l-.06.07a.568.568 0 0 1-.804 0L7.31 4.204a.568.568 0 0 1 0-.805m2.39-.04-.884.884 3.093 3.093.884-.884A2.186 2.186 0 1 0 9.7 3.359M4.396 8.664l-.884.884a2.186 2.186 0 1 0 3.092 3.093l.884-.884z"/>',e)},p(a,[i]){ot(e,r=$t(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 17 16"},1&i&&a[0]]))},i:X,o:X,d(a){a&&y(e)}}}function al(s,e,t){return s.$$set=n=>{t(0,e=ye(ye({},e),Je(n)))},[e=Je(e)]}class il extends ue{constructor(e){super(),pe(this,e,al,rl,de,{})}}function ol(s){let e,t,n,r,a,i,o;function c(d){s[11](d)}let l={onOpenChange:s[10],$$slots:{default:[fl]},$$scope:{ctx:s}};return s[3]!==void 0&&(l.requestClose=s[3]),n=new Ne.Root({props:l}),tt.push(()=>nt(n,"requestClose",c)),i=new vi.Root({props:{color:"success",size:1,variant:"soft",$$slots:{default:[gl]},$$scope:{ctx:s}}}),{c(){e=Z("div"),t=Z("div"),S(n.$$.fragment),a=E(),S(i.$$.fragment),$(t,"class","icon-container svelte-js5lik"),$(e,"class","status-controls svelte-js5lik")},m(d,u){_(d,e,u),M(e,t),k(n,t,null),M(t,a),k(i,t,null),o=!0},p(d,u){const f={};16&u&&(f.onOpenChange=d[10]),16411&u&&(f.$$scope={dirty:u,ctx:d}),!r&&8&u&&(r=!0,f.requestClose=d[3],st(()=>r=!1)),n.$set(f);const v={};16384&u&&(v.$$scope={dirty:u,ctx:d}),i.$set(v)},i(d){o||(p(n.$$.fragment,d),p(i.$$.fragment,d),o=!0)},o(d){m(n.$$.fragment,d),m(i.$$.fragment,d),o=!1},d(d){d&&y(e),C(n),C(i)}}}function cl(s){let e,t;return e=new Ot({props:{variant:"ghost-block",color:s[2]?"neutral":"accent",size:1,$$slots:{default:[yl]},$$scope:{ctx:s}}}),e.$on("click",s[5]),{c(){S(e.$$.fragment)},m(n,r){k(e,n,r),t=!0},p(n,r){const a={};4&r&&(a.color=n[2]?"neutral":"accent"),16388&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function ll(s){let e,t;return e=new il({}),{c(){S(e.$$.fragment)},m(n,r){k(e,n,r),t=!0},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function dl(s){let e,t;return e=new zn({props:{color:"neutral",variant:"ghost",size:1,$$slots:{default:[ll]},$$scope:{ctx:s}}}),e.$on("click",s[8]),{c(){S(e.$$.fragment)},m(n,r){k(e,n,r),t=!0},p(n,r){const a={};16384&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function ul(s){let e,t,n,r;return n=new sn({props:{triggerOn:[wi.Hover],content:"Revoke Access",$$slots:{default:[dl]},$$scope:{ctx:s}}}),{c(){e=Z("div"),t=Z("div"),S(n.$$.fragment),$(t,"class","icon-button-wrapper svelte-js5lik"),_e(t,"active",s[4]),$(e,"class","connection-status svelte-js5lik")},m(a,i){_(a,e,i),M(e,t),k(n,t,null),r=!0},p(a,i){const o={};16400&i&&(o.$$scope={dirty:i,ctx:a}),n.$set(o),(!r||16&i)&&_e(t,"active",a[4])},i(a){r||(p(n.$$.fragment,a),r=!0)},o(a){m(n.$$.fragment,a),r=!1},d(a){a&&y(e),C(n)}}}function pl(s){let e;return{c(){e=U("Revoke Access")},m(t,n){_(t,e,n)},d(t){t&&y(e)}}}function ml(s){let e,t;return e=new ce({props:{size:1,weight:"medium",$$slots:{default:[pl]},$$scope:{ctx:s}}}),{c(){S(e.$$.fragment)},m(n,r){k(e,n,r),t=!0},p(n,r){const a={};16384&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function hl(s){let e,t;return e=new Ne.Item({props:{onSelect:s[9],$$slots:{default:[ml]},$$scope:{ctx:s}}}),{c(){S(e.$$.fragment)},m(n,r){k(e,n,r),t=!0},p(n,r){const a={};27&r&&(a.onSelect=n[9]),16384&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function fl(s){let e,t,n,r;return e=new Ne.Trigger({props:{$$slots:{default:[ul]},$$scope:{ctx:s}}}),n=new Ne.Content({props:{size:1,side:"bottom",align:"end",$$slots:{default:[hl]},$$scope:{ctx:s}}}),{c(){S(e.$$.fragment),t=E(),S(n.$$.fragment)},m(a,i){k(e,a,i),_(a,t,i),k(n,a,i),r=!0},p(a,i){const o={};16400&i&&(o.$$scope={dirty:i,ctx:a}),e.$set(o);const c={};16411&i&&(c.$$scope={dirty:i,ctx:a}),n.$set(c)},i(a){r||(p(e.$$.fragment,a),p(n.$$.fragment,a),r=!0)},o(a){m(e.$$.fragment,a),m(n.$$.fragment,a),r=!1},d(a){a&&y(t),C(e,a),C(n,a)}}}function gl(s){let e;return{c(){e=U("Connected")},m(t,n){_(t,e,n)},d(t){t&&y(e)}}}function vl(s){let e;return{c(){e=Z("span"),e.textContent="Connect"},m(t,n){_(t,e,n)},i:X,o:X,d(t){t&&y(e)}}}function $l(s){let e,t,n,r,a;return t=new fa({props:{size:1,useCurrentColor:!0}}),{c(){e=Z("div"),S(t.$$.fragment),n=E(),r=Z("span"),r.textContent="Cancel",$(e,"class","connect-button-spinner svelte-js5lik")},m(i,o){_(i,e,o),k(t,e,null),_(i,n,o),_(i,r,o),a=!0},i(i){a||(p(t.$$.fragment,i),a=!0)},o(i){m(t.$$.fragment,i),a=!1},d(i){i&&(y(e),y(n),y(r)),C(t)}}}function yl(s){let e,t,n,r;const a=[$l,vl],i=[];function o(c,l){return c[2]?0:1}return t=o(s),n=i[t]=a[t](s),{c(){e=Z("div"),n.c(),$(e,"class","connect-button-content svelte-js5lik")},m(c,l){_(c,e,l),i[t].m(e,null),r=!0},p(c,l){let d=t;t=o(c),t!==d&&(W(),m(i[d],1,1,()=>{i[d]=null}),Y(),n=i[t],n||(n=i[t]=a[t](c),n.c()),p(n,1),n.m(e,null))},i(c){r||(p(n),r=!0)},o(c){m(n),r=!1},d(c){c&&y(e),i[t].d()}}}function _l(s){let e,t,n,r;const a=[cl,ol],i=[];function o(c,l){return!c[0].isConfigured&&c[0].authUrl?0:c[0].isConfigured?1:-1}return~(t=o(s))&&(n=i[t]=a[t](s)),{c(){e=Z("div"),n&&n.c(),$(e,"slot","header-right")},m(c,l){_(c,e,l),~t&&i[t].m(e,null),r=!0},p(c,l){let d=t;t=o(c),t===d?~t&&i[t].p(c,l):(n&&(W(),m(i[d],1,1,()=>{i[d]=null}),Y()),~t?(n=i[t],n?n.p(c,l):(n=i[t]=a[t](c),n.c()),p(n,1),n.m(e,null)):n=null)},i(c){r||(p(n),r=!0)},o(c){m(n),r=!1},d(c){c&&y(e),~t&&i[t].d()}}}function zr(s){let e,t,n,r=s[0].statusMessage+"";return{c(){e=Z("div"),t=U(r),$(e,"class",n="status-message "+s[0].statusType+" svelte-js5lik")},m(a,i){_(a,e,i),M(e,t)},p(a,i){1&i&&r!==(r=a[0].statusMessage+"")&&he(t,r),1&i&&n!==(n="status-message "+a[0].statusType+" svelte-js5lik")&&$(e,"class",n)},d(a){a&&y(e)}}}function xl(s){let e,t,n,r,a,i;t=new za({props:{icon:s[0].icon,title:s[0].displayName,$$slots:{"header-right":[_l]},$$scope:{ctx:s}}});let o=s[0].showStatus&&zr(s);return{c(){e=Z("div"),S(t.$$.fragment),n=E(),o&&o.c(),$(e,"class","config-wrapper"),$(e,"role","group"),$(e,"aria-label","Connection status controls")},m(c,l){_(c,e,l),k(t,e,null),M(e,n),o&&o.m(e,null),r=!0,a||(i=[et(e,"mouseenter",s[12]),et(e,"mouseleave",s[13])],a=!0)},p(c,[l]){const d={};1&l&&(d.icon=c[0].icon),1&l&&(d.title=c[0].displayName),16415&l&&(d.$$scope={dirty:l,ctx:c}),t.$set(d),c[0].showStatus?o?o.p(c,l):(o=zr(c),o.c(),o.m(e,null)):o&&(o.d(1),o=null)},i(c){r||(p(t.$$.fragment,c),r=!0)},o(c){m(t.$$.fragment,c),r=!1},d(c){c&&y(e),C(t),o&&o.d(),a=!1,Is(i)}}}function wl(s,e,t){let{config:n}=e,{onAuthenticate:r}=e,{onRevokeAccess:a}=e,i=()=>{t(4,l=!1)},o=!1,c=null,l=!1;return s.$$set=d=>{"config"in d&&t(0,n=d.config),"onAuthenticate"in d&&t(6,r=d.onAuthenticate),"onRevokeAccess"in d&&t(1,a=d.onRevokeAccess)},s.$$.update=()=>{133&s.$$.dirty&&n.isConfigured&&o&&(t(2,o=!1),c&&(clearTimeout(c),t(7,c=null)))},[n,a,o,i,l,function(){if(o)t(2,o=!1),c&&(clearTimeout(c),t(7,c=null));else{t(2,o=!0);const d=n.authUrl||"";r(d),t(7,c=setTimeout(()=>{t(2,o=!1),t(7,c=null)},6e4))}},r,c,()=>t(4,l=!l),()=>{a(n),t(4,l=!1),i()},d=>{d||t(4,l=!1)},function(d){i=d,t(3,i)},()=>t(4,l=!0),()=>t(4,l=!1)]}class bl extends ue{constructor(e){super(),pe(this,e,wl,xl,de,{config:0,onAuthenticate:6,onRevokeAccess:1})}}function Sl(s){let e;return{c(){e=U(s[0])},m(t,n){_(t,e,n)},p(t,n){1&n&&he(e,t[0])},d(t){t&&y(e)}}}function kl(s){let e,t;const n=s[2].default,r=Ee(n,s,s[3],null);return{c(){e=Z("div"),r&&r.c(),$(e,"class","category-content")},m(a,i){_(a,e,i),r&&r.m(e,null),t=!0},p(a,i){r&&r.p&&(!t||8&i)&&Ie(r,n,a,a[3],t?je(n,a[3],i,null):Pe(a[3]),null)},i(a){t||(p(r,a),t=!0)},o(a){m(r,a),t=!1},d(a){a&&y(e),r&&r.d(a)}}}function Cl(s){let e,t,n,r,a;return t=new fa({props:{size:1}}),r=new ce({props:{size:1,color:"secondary",$$slots:{default:[Tl]},$$scope:{ctx:s}}}),{c(){e=Z("div"),S(t.$$.fragment),n=E(),S(r.$$.fragment),$(e,"class","loading-container svelte-2bsejd")},m(i,o){_(i,e,o),k(t,e,null),M(e,n),k(r,e,null),a=!0},p(i,o){const c={};8&o&&(c.$$scope={dirty:o,ctx:i}),r.$set(c)},i(i){a||(p(t.$$.fragment,i),p(r.$$.fragment,i),a=!0)},o(i){m(t.$$.fragment,i),m(r.$$.fragment,i),a=!1},d(i){i&&y(e),C(t),C(r)}}}function Tl(s){let e;return{c(){e=U("Loading...")},m(t,n){_(t,e,n)},d(t){t&&y(e)}}}function Ml(s){let e,t,n,r,a,i,o;n=new ce({props:{size:1,color:"secondary",weight:"regular",$$slots:{default:[Sl]},$$scope:{ctx:s}}});const c=[Cl,kl],l=[];function d(u,f){return u[1]?0:1}return a=d(s),i=l[a]=c[a](s),{c(){e=Z("div"),t=Z("div"),S(n.$$.fragment),r=E(),i.c(),$(t,"class","category-heading"),$(e,"class","category")},m(u,f){_(u,e,f),M(e,t),k(n,t,null),M(e,r),l[a].m(e,null),o=!0},p(u,[f]){const v={};9&f&&(v.$$scope={dirty:f,ctx:u}),n.$set(v);let g=a;a=d(u),a===g?l[a].p(u,f):(W(),m(l[g],1,1,()=>{l[g]=null}),Y(),i=l[a],i?i.p(u,f):(i=l[a]=c[a](u),i.c()),p(i,1),i.m(e,null))},i(u){o||(p(n.$$.fragment,u),p(i),o=!0)},o(u){m(n.$$.fragment,u),m(i),o=!1},d(u){u&&y(e),C(n),l[a].d()}}}function Zl(s,e,t){let{$$slots:n={},$$scope:r}=e,{title:a}=e,{loading:i=!1}=e;return s.$$set=o=>{"title"in o&&t(0,a=o.title),"loading"in o&&t(1,i=o.loading),"$$scope"in o&&t(3,r=o.$$scope)},[a,i,n,r]}class Nl extends ue{constructor(e){super(),pe(this,e,Zl,Ml,de,{title:0,loading:1})}}function Dr(s,e,t){const n=s.slice();return n[4]=e[t],n}function Vr(s){let e,t;return e=new bl({props:{config:s[4],onAuthenticate:s[2],onRevokeAccess:s[3]}}),{c(){S(e.$$.fragment)},m(n,r){k(e,n,r),t=!0},p(n,r){const a={};2&r&&(a.config=n[4]),4&r&&(a.onAuthenticate=n[2]),8&r&&(a.onRevokeAccess=n[3]),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Al(s){let e,t,n=xe(s[1]),r=[];for(let i=0;i<n.length;i+=1)r[i]=Vr(Dr(s,n,i));const a=i=>m(r[i],1,1,()=>{r[i]=null});return{c(){e=Z("div");for(let i=0;i<r.length;i+=1)r[i].c();$(e,"class","tool-category-list svelte-on3wl5")},m(i,o){_(i,e,o);for(let c=0;c<r.length;c+=1)r[c]&&r[c].m(e,null);t=!0},p(i,o){if(14&o){let c;for(n=xe(i[1]),c=0;c<n.length;c+=1){const l=Dr(i,n,c);r[c]?(r[c].p(l,o),p(r[c],1)):(r[c]=Vr(l),r[c].c(),p(r[c],1),r[c].m(e,null))}for(W(),c=n.length;c<r.length;c+=1)a(c);Y()}},i(i){if(!t){for(let o=0;o<n.length;o+=1)p(r[o]);t=!0}},o(i){r=r.filter(Boolean);for(let o=0;o<r.length;o+=1)m(r[o]);t=!1},d(i){i&&y(e),In(r,i)}}}function Ol(s){let e,t;return e=new Nl({props:{title:s[0],loading:s[1].length===0,$$slots:{default:[Al]},$$scope:{ctx:s}}}),{c(){S(e.$$.fragment)},m(n,r){k(e,n,r),t=!0},p(n,[r]){const a={};1&r&&(a.title=n[0]),2&r&&(a.loading=n[1].length===0),142&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function El(s,e,t){let{title:n}=e,{tools:r=[]}=e,{onAuthenticate:a}=e,{onRevokeAccess:i}=e;return s.$$set=o=>{"title"in o&&t(0,n=o.title),"tools"in o&&t(1,r=o.tools),"onAuthenticate"in o&&t(2,a=o.onAuthenticate),"onRevokeAccess"in o&&t(3,i=o.onRevokeAccess)},[n,r,a,i]}class Il extends ue{constructor(e){super(),pe(this,e,El,Ol,de,{title:0,tools:1,onAuthenticate:2,onRevokeAccess:3})}}var oe,Ns;(function(s){s.assertEqual=e=>e,s.assertIs=function(e){},s.assertNever=function(e){throw new Error},s.arrayToEnum=e=>{const t={};for(const n of e)t[n]=n;return t},s.getValidEnumValues=e=>{const t=s.objectKeys(e).filter(r=>typeof e[e[r]]!="number"),n={};for(const r of t)n[r]=e[r];return s.objectValues(n)},s.objectValues=e=>s.objectKeys(e).map(function(t){return e[t]}),s.objectKeys=typeof Object.keys=="function"?e=>Object.keys(e):e=>{const t=[];for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.push(n);return t},s.find=(e,t)=>{for(const n of e)if(t(n))return n},s.isInteger=typeof Number.isInteger=="function"?e=>Number.isInteger(e):e=>typeof e=="number"&&isFinite(e)&&Math.floor(e)===e,s.joinValues=function(e,t=" | "){return e.map(n=>typeof n=="string"?`'${n}'`:n).join(t)},s.jsonStringifyReplacer=(e,t)=>typeof t=="bigint"?t.toString():t})(oe||(oe={})),function(s){s.mergeShapes=(e,t)=>({...e,...t})}(Ns||(Ns={}));const R=oe.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),ht=s=>{switch(typeof s){case"undefined":return R.undefined;case"string":return R.string;case"number":return isNaN(s)?R.nan:R.number;case"boolean":return R.boolean;case"function":return R.function;case"bigint":return R.bigint;case"symbol":return R.symbol;case"object":return Array.isArray(s)?R.array:s===null?R.null:s.then&&typeof s.then=="function"&&s.catch&&typeof s.catch=="function"?R.promise:typeof Map<"u"&&s instanceof Map?R.map:typeof Set<"u"&&s instanceof Set?R.set:typeof Date<"u"&&s instanceof Date?R.date:R.object;default:return R.unknown}},O=oe.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class ze extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=n=>{this.issues=[...this.issues,n]},this.addIssues=(n=[])=>{this.issues=[...this.issues,...n]};const t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){const t=e||function(a){return a.message},n={_errors:[]},r=a=>{for(const i of a.issues)if(i.code==="invalid_union")i.unionErrors.map(r);else if(i.code==="invalid_return_type")r(i.returnTypeError);else if(i.code==="invalid_arguments")r(i.argumentsError);else if(i.path.length===0)n._errors.push(t(i));else{let o=n,c=0;for(;c<i.path.length;){const l=i.path[c];c===i.path.length-1?(o[l]=o[l]||{_errors:[]},o[l]._errors.push(t(i))):o[l]=o[l]||{_errors:[]},o=o[l],c++}}};return r(this),n}static assert(e){if(!(e instanceof ze))throw new Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,oe.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(e=t=>t.message){const t={},n=[];for(const r of this.issues)r.path.length>0?(t[r.path[0]]=t[r.path[0]]||[],t[r.path[0]].push(e(r))):n.push(e(r));return{formErrors:n,fieldErrors:t}}get formErrors(){return this.flatten()}}ze.create=s=>new ze(s);const Jt=(s,e)=>{let t;switch(s.code){case O.invalid_type:t=s.received===R.undefined?"Required":`Expected ${s.expected}, received ${s.received}`;break;case O.invalid_literal:t=`Invalid literal value, expected ${JSON.stringify(s.expected,oe.jsonStringifyReplacer)}`;break;case O.unrecognized_keys:t=`Unrecognized key(s) in object: ${oe.joinValues(s.keys,", ")}`;break;case O.invalid_union:t="Invalid input";break;case O.invalid_union_discriminator:t=`Invalid discriminator value. Expected ${oe.joinValues(s.options)}`;break;case O.invalid_enum_value:t=`Invalid enum value. Expected ${oe.joinValues(s.options)}, received '${s.received}'`;break;case O.invalid_arguments:t="Invalid function arguments";break;case O.invalid_return_type:t="Invalid function return type";break;case O.invalid_date:t="Invalid date";break;case O.invalid_string:typeof s.validation=="object"?"includes"in s.validation?(t=`Invalid input: must include "${s.validation.includes}"`,typeof s.validation.position=="number"&&(t=`${t} at one or more positions greater than or equal to ${s.validation.position}`)):"startsWith"in s.validation?t=`Invalid input: must start with "${s.validation.startsWith}"`:"endsWith"in s.validation?t=`Invalid input: must end with "${s.validation.endsWith}"`:oe.assertNever(s.validation):t=s.validation!=="regex"?`Invalid ${s.validation}`:"Invalid";break;case O.too_small:t=s.type==="array"?`Array must contain ${s.exact?"exactly":s.inclusive?"at least":"more than"} ${s.minimum} element(s)`:s.type==="string"?`String must contain ${s.exact?"exactly":s.inclusive?"at least":"over"} ${s.minimum} character(s)`:s.type==="number"?`Number must be ${s.exact?"exactly equal to ":s.inclusive?"greater than or equal to ":"greater than "}${s.minimum}`:s.type==="date"?`Date must be ${s.exact?"exactly equal to ":s.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(s.minimum))}`:"Invalid input";break;case O.too_big:t=s.type==="array"?`Array must contain ${s.exact?"exactly":s.inclusive?"at most":"less than"} ${s.maximum} element(s)`:s.type==="string"?`String must contain ${s.exact?"exactly":s.inclusive?"at most":"under"} ${s.maximum} character(s)`:s.type==="number"?`Number must be ${s.exact?"exactly":s.inclusive?"less than or equal to":"less than"} ${s.maximum}`:s.type==="bigint"?`BigInt must be ${s.exact?"exactly":s.inclusive?"less than or equal to":"less than"} ${s.maximum}`:s.type==="date"?`Date must be ${s.exact?"exactly":s.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(s.maximum))}`:"Invalid input";break;case O.custom:t="Invalid input";break;case O.invalid_intersection_types:t="Intersection results could not be merged";break;case O.not_multiple_of:t=`Number must be a multiple of ${s.multipleOf}`;break;case O.not_finite:t="Number must be finite";break;default:t=e.defaultError,oe.assertNever(s)}return{message:t}};let Da=Jt;function is(){return Da}const os=s=>{const{data:e,path:t,errorMaps:n,issueData:r}=s,a=[...t,...r.path||[]],i={...r,path:a};if(r.message!==void 0)return{...r,path:a,message:r.message};let o="";const c=n.filter(l=>!!l).slice().reverse();for(const l of c)o=l(i,{data:e,defaultError:o}).message;return{...r,path:a,message:o}};function P(s,e){const t=is(),n=os({issueData:e,data:s.data,path:s.path,errorMaps:[s.common.contextualErrorMap,s.schemaErrorMap,t,t===Jt?void 0:Jt].filter(r=>!!r)});s.common.issues.push(n)}class Ze{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(e,t){const n=[];for(const r of t){if(r.status==="aborted")return G;r.status==="dirty"&&e.dirty(),n.push(r.value)}return{status:e.value,value:n}}static async mergeObjectAsync(e,t){const n=[];for(const r of t){const a=await r.key,i=await r.value;n.push({key:a,value:i})}return Ze.mergeObjectSync(e,n)}static mergeObjectSync(e,t){const n={};for(const r of t){const{key:a,value:i}=r;if(a.status==="aborted"||i.status==="aborted")return G;a.status==="dirty"&&e.dirty(),i.status==="dirty"&&e.dirty(),a.value==="__proto__"||i.value===void 0&&!r.alwaysSet||(n[a.value]=i.value)}return{status:e.value,value:n}}}const G=Object.freeze({status:"aborted"}),cs=s=>({status:"dirty",value:s}),Oe=s=>({status:"valid",value:s}),As=s=>s.status==="aborted",Os=s=>s.status==="dirty",Pt=s=>s.status==="valid",wn=s=>typeof Promise<"u"&&s instanceof Promise;function ls(s,e,t,n){if(typeof e=="function"?s!==e||!n:!e.has(s))throw new TypeError("Cannot read private member from an object whose class did not declare it");return e.get(s)}function Va(s,e,t,n,r){if(typeof e=="function"?s!==e||!r:!e.has(s))throw new TypeError("Cannot write private member to an object whose class did not declare it");return e.set(s,t),t}var z,tn,nn;typeof SuppressedError=="function"&&SuppressedError,function(s){s.errToObj=e=>typeof e=="string"?{message:e}:e||{},s.toString=e=>typeof e=="string"?e:e==null?void 0:e.message}(z||(z={}));class lt{constructor(e,t,n,r){this._cachedPath=[],this.parent=e,this.data=t,this._path=n,this._key=r}get path(){return this._cachedPath.length||(this._key instanceof Array?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const Ur=(s,e)=>{if(Pt(e))return{success:!0,data:e.value};if(!s.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const t=new ze(s.common.issues);return this._error=t,this._error}}};function ee(s){if(!s)return{};const{errorMap:e,invalid_type_error:t,required_error:n,description:r}=s;if(e&&(t||n))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return e?{errorMap:e,description:r}:{errorMap:(a,i)=>{var o,c;const{message:l}=s;return a.code==="invalid_enum_value"?{message:l??i.defaultError}:i.data===void 0?{message:(o=l??n)!==null&&o!==void 0?o:i.defaultError}:a.code!=="invalid_type"?{message:i.defaultError}:{message:(c=l??t)!==null&&c!==void 0?c:i.defaultError}},description:r}}class ne{get description(){return this._def.description}_getType(e){return ht(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:ht(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new Ze,ctx:{common:e.parent.common,data:e.data,parsedType:ht(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){const t=this._parse(e);if(wn(t))throw new Error("Synchronous parse encountered promise.");return t}_parseAsync(e){const t=this._parse(e);return Promise.resolve(t)}parse(e,t){const n=this.safeParse(e,t);if(n.success)return n.data;throw n.error}safeParse(e,t){var n;const r={common:{issues:[],async:(n=t==null?void 0:t.async)!==null&&n!==void 0&&n,contextualErrorMap:t==null?void 0:t.errorMap},path:(t==null?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:ht(e)},a=this._parseSync({data:e,path:r.path,parent:r});return Ur(r,a)}"~validate"(e){var t,n;const r={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:ht(e)};if(!this["~standard"].async)try{const a=this._parseSync({data:e,path:[],parent:r});return Pt(a)?{value:a.value}:{issues:r.common.issues}}catch(a){!((n=(t=a==null?void 0:a.message)===null||t===void 0?void 0:t.toLowerCase())===null||n===void 0)&&n.includes("encountered")&&(this["~standard"].async=!0),r.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:r}).then(a=>Pt(a)?{value:a.value}:{issues:r.common.issues})}async parseAsync(e,t){const n=await this.safeParseAsync(e,t);if(n.success)return n.data;throw n.error}async safeParseAsync(e,t){const n={common:{issues:[],contextualErrorMap:t==null?void 0:t.errorMap,async:!0},path:(t==null?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:ht(e)},r=this._parse({data:e,path:n.path,parent:n}),a=await(wn(r)?r:Promise.resolve(r));return Ur(n,a)}refine(e,t){const n=r=>typeof t=="string"||t===void 0?{message:t}:typeof t=="function"?t(r):t;return this._refinement((r,a)=>{const i=e(r),o=()=>a.addIssue({code:O.custom,...n(r)});return typeof Promise<"u"&&i instanceof Promise?i.then(c=>!!c||(o(),!1)):!!i||(o(),!1)})}refinement(e,t){return this._refinement((n,r)=>!!e(n)||(r.addIssue(typeof t=="function"?t(n,r):t),!1))}_refinement(e){return new Ye({schema:this,typeName:H.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:t=>this["~validate"](t)}}optional(){return it.create(this,this._def)}nullable(){return Zt.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return Qe.create(this)}promise(){return Kt.create(this,this._def)}or(e){return Cn.create([this,e],this._def)}and(e){return Tn.create(this,e,this._def)}transform(e){return new Ye({...ee(this._def),schema:this,typeName:H.ZodEffects,effect:{type:"transform",transform:e}})}default(e){const t=typeof e=="function"?e:()=>e;return new An({...ee(this._def),innerType:this,defaultValue:t,typeName:H.ZodDefault})}brand(){return new Ls({typeName:H.ZodBranded,type:this,...ee(this._def)})}catch(e){const t=typeof e=="function"?e:()=>e;return new On({...ee(this._def),innerType:this,catchValue:t,typeName:H.ZodCatch})}describe(e){return new this.constructor({...this._def,description:e})}pipe(e){return Vn.create(this,e)}readonly(){return En.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const Pl=/^c[^\s-]{8,}$/i,jl=/^[0-9a-z]+$/,Rl=/^[0-9A-HJKMNP-TV-Z]{26}$/i,Ll=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,Fl=/^[a-z0-9_-]{21}$/i,zl=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,Dl=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,Vl=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i;let ys;const Ul=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,ql=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,Bl=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,Jl=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,Hl=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,Kl=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,Ua="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",Gl=new RegExp(`^${Ua}$`);function qa(s){let e="([01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d";return s.precision?e=`${e}\\.\\d{${s.precision}}`:s.precision==null&&(e=`${e}(\\.\\d+)?`),e}function Ba(s){let e=`${Ua}T${qa(s)}`;const t=[];return t.push(s.local?"Z?":"Z"),s.offset&&t.push("([+-]\\d{2}:?\\d{2})"),e=`${e}(${t.join("|")})`,new RegExp(`^${e}$`)}function Wl(s,e){if(!zl.test(s))return!1;try{const[t]=s.split("."),n=t.replace(/-/g,"+").replace(/_/g,"/").padEnd(t.length+(4-t.length%4)%4,"="),r=JSON.parse(atob(n));return typeof r=="object"&&r!==null&&!(!r.typ||!r.alg)&&(!e||r.alg===e)}catch{return!1}}function Yl(s,e){return!(e!=="v4"&&e||!ql.test(s))||!(e!=="v6"&&e||!Jl.test(s))}class We extends ne{_parse(e){if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==R.string){const i=this._getOrReturnCtx(e);return P(i,{code:O.invalid_type,expected:R.string,received:i.parsedType}),G}const t=new Ze;let n;for(const i of this._def.checks)if(i.kind==="min")e.data.length<i.value&&(n=this._getOrReturnCtx(e,n),P(n,{code:O.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),t.dirty());else if(i.kind==="max")e.data.length>i.value&&(n=this._getOrReturnCtx(e,n),P(n,{code:O.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),t.dirty());else if(i.kind==="length"){const o=e.data.length>i.value,c=e.data.length<i.value;(o||c)&&(n=this._getOrReturnCtx(e,n),o?P(n,{code:O.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}):c&&P(n,{code:O.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}),t.dirty())}else if(i.kind==="email")Vl.test(e.data)||(n=this._getOrReturnCtx(e,n),P(n,{validation:"email",code:O.invalid_string,message:i.message}),t.dirty());else if(i.kind==="emoji")ys||(ys=new RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),ys.test(e.data)||(n=this._getOrReturnCtx(e,n),P(n,{validation:"emoji",code:O.invalid_string,message:i.message}),t.dirty());else if(i.kind==="uuid")Ll.test(e.data)||(n=this._getOrReturnCtx(e,n),P(n,{validation:"uuid",code:O.invalid_string,message:i.message}),t.dirty());else if(i.kind==="nanoid")Fl.test(e.data)||(n=this._getOrReturnCtx(e,n),P(n,{validation:"nanoid",code:O.invalid_string,message:i.message}),t.dirty());else if(i.kind==="cuid")Pl.test(e.data)||(n=this._getOrReturnCtx(e,n),P(n,{validation:"cuid",code:O.invalid_string,message:i.message}),t.dirty());else if(i.kind==="cuid2")jl.test(e.data)||(n=this._getOrReturnCtx(e,n),P(n,{validation:"cuid2",code:O.invalid_string,message:i.message}),t.dirty());else if(i.kind==="ulid")Rl.test(e.data)||(n=this._getOrReturnCtx(e,n),P(n,{validation:"ulid",code:O.invalid_string,message:i.message}),t.dirty());else if(i.kind==="url")try{new URL(e.data)}catch{n=this._getOrReturnCtx(e,n),P(n,{validation:"url",code:O.invalid_string,message:i.message}),t.dirty()}else i.kind==="regex"?(i.regex.lastIndex=0,i.regex.test(e.data)||(n=this._getOrReturnCtx(e,n),P(n,{validation:"regex",code:O.invalid_string,message:i.message}),t.dirty())):i.kind==="trim"?e.data=e.data.trim():i.kind==="includes"?e.data.includes(i.value,i.position)||(n=this._getOrReturnCtx(e,n),P(n,{code:O.invalid_string,validation:{includes:i.value,position:i.position},message:i.message}),t.dirty()):i.kind==="toLowerCase"?e.data=e.data.toLowerCase():i.kind==="toUpperCase"?e.data=e.data.toUpperCase():i.kind==="startsWith"?e.data.startsWith(i.value)||(n=this._getOrReturnCtx(e,n),P(n,{code:O.invalid_string,validation:{startsWith:i.value},message:i.message}),t.dirty()):i.kind==="endsWith"?e.data.endsWith(i.value)||(n=this._getOrReturnCtx(e,n),P(n,{code:O.invalid_string,validation:{endsWith:i.value},message:i.message}),t.dirty()):i.kind==="datetime"?Ba(i).test(e.data)||(n=this._getOrReturnCtx(e,n),P(n,{code:O.invalid_string,validation:"datetime",message:i.message}),t.dirty()):i.kind==="date"?Gl.test(e.data)||(n=this._getOrReturnCtx(e,n),P(n,{code:O.invalid_string,validation:"date",message:i.message}),t.dirty()):i.kind==="time"?new RegExp(`^${qa(i)}$`).test(e.data)||(n=this._getOrReturnCtx(e,n),P(n,{code:O.invalid_string,validation:"time",message:i.message}),t.dirty()):i.kind==="duration"?Dl.test(e.data)||(n=this._getOrReturnCtx(e,n),P(n,{validation:"duration",code:O.invalid_string,message:i.message}),t.dirty()):i.kind==="ip"?(r=e.data,((a=i.version)!=="v4"&&a||!Ul.test(r))&&(a!=="v6"&&a||!Bl.test(r))&&(n=this._getOrReturnCtx(e,n),P(n,{validation:"ip",code:O.invalid_string,message:i.message}),t.dirty())):i.kind==="jwt"?Wl(e.data,i.alg)||(n=this._getOrReturnCtx(e,n),P(n,{validation:"jwt",code:O.invalid_string,message:i.message}),t.dirty()):i.kind==="cidr"?Yl(e.data,i.version)||(n=this._getOrReturnCtx(e,n),P(n,{validation:"cidr",code:O.invalid_string,message:i.message}),t.dirty()):i.kind==="base64"?Hl.test(e.data)||(n=this._getOrReturnCtx(e,n),P(n,{validation:"base64",code:O.invalid_string,message:i.message}),t.dirty()):i.kind==="base64url"?Kl.test(e.data)||(n=this._getOrReturnCtx(e,n),P(n,{validation:"base64url",code:O.invalid_string,message:i.message}),t.dirty()):oe.assertNever(i);var r,a;return{status:t.value,value:e.data}}_regex(e,t,n){return this.refinement(r=>e.test(r),{validation:t,code:O.invalid_string,...z.errToObj(n)})}_addCheck(e){return new We({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...z.errToObj(e)})}url(e){return this._addCheck({kind:"url",...z.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...z.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...z.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...z.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...z.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...z.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...z.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...z.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...z.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...z.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...z.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...z.errToObj(e)})}datetime(e){var t,n;return typeof e=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:(e==null?void 0:e.precision)===void 0?null:e==null?void 0:e.precision,offset:(t=e==null?void 0:e.offset)!==null&&t!==void 0&&t,local:(n=e==null?void 0:e.local)!==null&&n!==void 0&&n,...z.errToObj(e==null?void 0:e.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return typeof e=="string"?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:(e==null?void 0:e.precision)===void 0?null:e==null?void 0:e.precision,...z.errToObj(e==null?void 0:e.message)})}duration(e){return this._addCheck({kind:"duration",...z.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...z.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:t==null?void 0:t.position,...z.errToObj(t==null?void 0:t.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...z.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...z.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...z.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...z.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...z.errToObj(t)})}nonempty(e){return this.min(1,z.errToObj(e))}trim(){return new We({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new We({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new We({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>e.kind==="datetime")}get isDate(){return!!this._def.checks.find(e=>e.kind==="date")}get isTime(){return!!this._def.checks.find(e=>e.kind==="time")}get isDuration(){return!!this._def.checks.find(e=>e.kind==="duration")}get isEmail(){return!!this._def.checks.find(e=>e.kind==="email")}get isURL(){return!!this._def.checks.find(e=>e.kind==="url")}get isEmoji(){return!!this._def.checks.find(e=>e.kind==="emoji")}get isUUID(){return!!this._def.checks.find(e=>e.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(e=>e.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(e=>e.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(e=>e.kind==="cuid2")}get isULID(){return!!this._def.checks.find(e=>e.kind==="ulid")}get isIP(){return!!this._def.checks.find(e=>e.kind==="ip")}get isCIDR(){return!!this._def.checks.find(e=>e.kind==="cidr")}get isBase64(){return!!this._def.checks.find(e=>e.kind==="base64")}get isBase64url(){return!!this._def.checks.find(e=>e.kind==="base64url")}get minLength(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}}function Xl(s,e){const t=(s.toString().split(".")[1]||"").length,n=(e.toString().split(".")[1]||"").length,r=t>n?t:n;return parseInt(s.toFixed(r).replace(".",""))%parseInt(e.toFixed(r).replace(".",""))/Math.pow(10,r)}We.create=s=>{var e;return new We({checks:[],typeName:H.ZodString,coerce:(e=s==null?void 0:s.coerce)!==null&&e!==void 0&&e,...ee(s)})};class Ct extends ne{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==R.number){const r=this._getOrReturnCtx(e);return P(r,{code:O.invalid_type,expected:R.number,received:r.parsedType}),G}let t;const n=new Ze;for(const r of this._def.checks)r.kind==="int"?oe.isInteger(e.data)||(t=this._getOrReturnCtx(e,t),P(t,{code:O.invalid_type,expected:"integer",received:"float",message:r.message}),n.dirty()):r.kind==="min"?(r.inclusive?e.data<r.value:e.data<=r.value)&&(t=this._getOrReturnCtx(e,t),P(t,{code:O.too_small,minimum:r.value,type:"number",inclusive:r.inclusive,exact:!1,message:r.message}),n.dirty()):r.kind==="max"?(r.inclusive?e.data>r.value:e.data>=r.value)&&(t=this._getOrReturnCtx(e,t),P(t,{code:O.too_big,maximum:r.value,type:"number",inclusive:r.inclusive,exact:!1,message:r.message}),n.dirty()):r.kind==="multipleOf"?Xl(e.data,r.value)!==0&&(t=this._getOrReturnCtx(e,t),P(t,{code:O.not_multiple_of,multipleOf:r.value,message:r.message}),n.dirty()):r.kind==="finite"?Number.isFinite(e.data)||(t=this._getOrReturnCtx(e,t),P(t,{code:O.not_finite,message:r.message}),n.dirty()):oe.assertNever(r);return{status:n.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,z.toString(t))}gt(e,t){return this.setLimit("min",e,!1,z.toString(t))}lte(e,t){return this.setLimit("max",e,!0,z.toString(t))}lt(e,t){return this.setLimit("max",e,!1,z.toString(t))}setLimit(e,t,n,r){return new Ct({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:n,message:z.toString(r)}]})}_addCheck(e){return new Ct({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:z.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:z.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:z.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:z.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:z.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:z.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:z.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:z.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:z.toString(e)})}get minValue(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>e.kind==="int"||e.kind==="multipleOf"&&oe.isInteger(e.value))}get isFinite(){let e=null,t=null;for(const n of this._def.checks){if(n.kind==="finite"||n.kind==="int"||n.kind==="multipleOf")return!0;n.kind==="min"?(t===null||n.value>t)&&(t=n.value):n.kind==="max"&&(e===null||n.value<e)&&(e=n.value)}return Number.isFinite(t)&&Number.isFinite(e)}}Ct.create=s=>new Ct({checks:[],typeName:H.ZodNumber,coerce:(s==null?void 0:s.coerce)||!1,...ee(s)});class Tt extends ne{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==R.bigint)return this._getInvalidInput(e);let t;const n=new Ze;for(const r of this._def.checks)r.kind==="min"?(r.inclusive?e.data<r.value:e.data<=r.value)&&(t=this._getOrReturnCtx(e,t),P(t,{code:O.too_small,type:"bigint",minimum:r.value,inclusive:r.inclusive,message:r.message}),n.dirty()):r.kind==="max"?(r.inclusive?e.data>r.value:e.data>=r.value)&&(t=this._getOrReturnCtx(e,t),P(t,{code:O.too_big,type:"bigint",maximum:r.value,inclusive:r.inclusive,message:r.message}),n.dirty()):r.kind==="multipleOf"?e.data%r.value!==BigInt(0)&&(t=this._getOrReturnCtx(e,t),P(t,{code:O.not_multiple_of,multipleOf:r.value,message:r.message}),n.dirty()):oe.assertNever(r);return{status:n.value,value:e.data}}_getInvalidInput(e){const t=this._getOrReturnCtx(e);return P(t,{code:O.invalid_type,expected:R.bigint,received:t.parsedType}),G}gte(e,t){return this.setLimit("min",e,!0,z.toString(t))}gt(e,t){return this.setLimit("min",e,!1,z.toString(t))}lte(e,t){return this.setLimit("max",e,!0,z.toString(t))}lt(e,t){return this.setLimit("max",e,!1,z.toString(t))}setLimit(e,t,n,r){return new Tt({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:n,message:z.toString(r)}]})}_addCheck(e){return new Tt({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:z.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:z.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:z.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:z.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:z.toString(t)})}get minValue(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}}Tt.create=s=>{var e;return new Tt({checks:[],typeName:H.ZodBigInt,coerce:(e=s==null?void 0:s.coerce)!==null&&e!==void 0&&e,...ee(s)})};class bn extends ne{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==R.boolean){const t=this._getOrReturnCtx(e);return P(t,{code:O.invalid_type,expected:R.boolean,received:t.parsedType}),G}return Oe(e.data)}}bn.create=s=>new bn({typeName:H.ZodBoolean,coerce:(s==null?void 0:s.coerce)||!1,...ee(s)});class jt extends ne{_parse(e){if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==R.date){const r=this._getOrReturnCtx(e);return P(r,{code:O.invalid_type,expected:R.date,received:r.parsedType}),G}if(isNaN(e.data.getTime()))return P(this._getOrReturnCtx(e),{code:O.invalid_date}),G;const t=new Ze;let n;for(const r of this._def.checks)r.kind==="min"?e.data.getTime()<r.value&&(n=this._getOrReturnCtx(e,n),P(n,{code:O.too_small,message:r.message,inclusive:!0,exact:!1,minimum:r.value,type:"date"}),t.dirty()):r.kind==="max"?e.data.getTime()>r.value&&(n=this._getOrReturnCtx(e,n),P(n,{code:O.too_big,message:r.message,inclusive:!0,exact:!1,maximum:r.value,type:"date"}),t.dirty()):oe.assertNever(r);return{status:t.value,value:new Date(e.data.getTime())}}_addCheck(e){return new jt({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:z.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:z.toString(t)})}get minDate(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e!=null?new Date(e):null}get maxDate(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e!=null?new Date(e):null}}jt.create=s=>new jt({checks:[],coerce:(s==null?void 0:s.coerce)||!1,typeName:H.ZodDate,...ee(s)});class ds extends ne{_parse(e){if(this._getType(e)!==R.symbol){const t=this._getOrReturnCtx(e);return P(t,{code:O.invalid_type,expected:R.symbol,received:t.parsedType}),G}return Oe(e.data)}}ds.create=s=>new ds({typeName:H.ZodSymbol,...ee(s)});class Sn extends ne{_parse(e){if(this._getType(e)!==R.undefined){const t=this._getOrReturnCtx(e);return P(t,{code:O.invalid_type,expected:R.undefined,received:t.parsedType}),G}return Oe(e.data)}}Sn.create=s=>new Sn({typeName:H.ZodUndefined,...ee(s)});class kn extends ne{_parse(e){if(this._getType(e)!==R.null){const t=this._getOrReturnCtx(e);return P(t,{code:O.invalid_type,expected:R.null,received:t.parsedType}),G}return Oe(e.data)}}kn.create=s=>new kn({typeName:H.ZodNull,...ee(s)});class Ht extends ne{constructor(){super(...arguments),this._any=!0}_parse(e){return Oe(e.data)}}Ht.create=s=>new Ht({typeName:H.ZodAny,...ee(s)});class At extends ne{constructor(){super(...arguments),this._unknown=!0}_parse(e){return Oe(e.data)}}At.create=s=>new At({typeName:H.ZodUnknown,...ee(s)});class vt extends ne{_parse(e){const t=this._getOrReturnCtx(e);return P(t,{code:O.invalid_type,expected:R.never,received:t.parsedType}),G}}vt.create=s=>new vt({typeName:H.ZodNever,...ee(s)});class us extends ne{_parse(e){if(this._getType(e)!==R.undefined){const t=this._getOrReturnCtx(e);return P(t,{code:O.invalid_type,expected:R.void,received:t.parsedType}),G}return Oe(e.data)}}us.create=s=>new us({typeName:H.ZodVoid,...ee(s)});class Qe extends ne{_parse(e){const{ctx:t,status:n}=this._processInputParams(e),r=this._def;if(t.parsedType!==R.array)return P(t,{code:O.invalid_type,expected:R.array,received:t.parsedType}),G;if(r.exactLength!==null){const i=t.data.length>r.exactLength.value,o=t.data.length<r.exactLength.value;(i||o)&&(P(t,{code:i?O.too_big:O.too_small,minimum:o?r.exactLength.value:void 0,maximum:i?r.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:r.exactLength.message}),n.dirty())}if(r.minLength!==null&&t.data.length<r.minLength.value&&(P(t,{code:O.too_small,minimum:r.minLength.value,type:"array",inclusive:!0,exact:!1,message:r.minLength.message}),n.dirty()),r.maxLength!==null&&t.data.length>r.maxLength.value&&(P(t,{code:O.too_big,maximum:r.maxLength.value,type:"array",inclusive:!0,exact:!1,message:r.maxLength.message}),n.dirty()),t.common.async)return Promise.all([...t.data].map((i,o)=>r.type._parseAsync(new lt(t,i,t.path,o)))).then(i=>Ze.mergeArray(n,i));const a=[...t.data].map((i,o)=>r.type._parseSync(new lt(t,i,t.path,o)));return Ze.mergeArray(n,a)}get element(){return this._def.type}min(e,t){return new Qe({...this._def,minLength:{value:e,message:z.toString(t)}})}max(e,t){return new Qe({...this._def,maxLength:{value:e,message:z.toString(t)}})}length(e,t){return new Qe({...this._def,exactLength:{value:e,message:z.toString(t)}})}nonempty(e){return this.min(1,e)}}function zt(s){if(s instanceof $e){const e={};for(const t in s.shape){const n=s.shape[t];e[t]=it.create(zt(n))}return new $e({...s._def,shape:()=>e})}return s instanceof Qe?new Qe({...s._def,type:zt(s.element)}):s instanceof it?it.create(zt(s.unwrap())):s instanceof Zt?Zt.create(zt(s.unwrap())):s instanceof dt?dt.create(s.items.map(e=>zt(e))):s}Qe.create=(s,e)=>new Qe({type:s,minLength:null,maxLength:null,exactLength:null,typeName:H.ZodArray,...ee(e)});class $e extends ne{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;const e=this._def.shape(),t=oe.objectKeys(e);return this._cached={shape:e,keys:t}}_parse(e){if(this._getType(e)!==R.object){const c=this._getOrReturnCtx(e);return P(c,{code:O.invalid_type,expected:R.object,received:c.parsedType}),G}const{status:t,ctx:n}=this._processInputParams(e),{shape:r,keys:a}=this._getCached(),i=[];if(!(this._def.catchall instanceof vt&&this._def.unknownKeys==="strip"))for(const c in n.data)a.includes(c)||i.push(c);const o=[];for(const c of a){const l=r[c],d=n.data[c];o.push({key:{status:"valid",value:c},value:l._parse(new lt(n,d,n.path,c)),alwaysSet:c in n.data})}if(this._def.catchall instanceof vt){const c=this._def.unknownKeys;if(c==="passthrough")for(const l of i)o.push({key:{status:"valid",value:l},value:{status:"valid",value:n.data[l]}});else if(c==="strict")i.length>0&&(P(n,{code:O.unrecognized_keys,keys:i}),t.dirty());else if(c!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const c=this._def.catchall;for(const l of i){const d=n.data[l];o.push({key:{status:"valid",value:l},value:c._parse(new lt(n,d,n.path,l)),alwaysSet:l in n.data})}}return n.common.async?Promise.resolve().then(async()=>{const c=[];for(const l of o){const d=await l.key,u=await l.value;c.push({key:d,value:u,alwaysSet:l.alwaysSet})}return c}).then(c=>Ze.mergeObjectSync(t,c)):Ze.mergeObjectSync(t,o)}get shape(){return this._def.shape()}strict(e){return z.errToObj,new $e({...this._def,unknownKeys:"strict",...e!==void 0?{errorMap:(t,n)=>{var r,a,i,o;const c=(i=(a=(r=this._def).errorMap)===null||a===void 0?void 0:a.call(r,t,n).message)!==null&&i!==void 0?i:n.defaultError;return t.code==="unrecognized_keys"?{message:(o=z.errToObj(e).message)!==null&&o!==void 0?o:c}:{message:c}}}:{}})}strip(){return new $e({...this._def,unknownKeys:"strip"})}passthrough(){return new $e({...this._def,unknownKeys:"passthrough"})}extend(e){return new $e({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new $e({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:H.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new $e({...this._def,catchall:e})}pick(e){const t={};return oe.objectKeys(e).forEach(n=>{e[n]&&this.shape[n]&&(t[n]=this.shape[n])}),new $e({...this._def,shape:()=>t})}omit(e){const t={};return oe.objectKeys(this.shape).forEach(n=>{e[n]||(t[n]=this.shape[n])}),new $e({...this._def,shape:()=>t})}deepPartial(){return zt(this)}partial(e){const t={};return oe.objectKeys(this.shape).forEach(n=>{const r=this.shape[n];e&&!e[n]?t[n]=r:t[n]=r.optional()}),new $e({...this._def,shape:()=>t})}required(e){const t={};return oe.objectKeys(this.shape).forEach(n=>{if(e&&!e[n])t[n]=this.shape[n];else{let r=this.shape[n];for(;r instanceof it;)r=r._def.innerType;t[n]=r}}),new $e({...this._def,shape:()=>t})}keyof(){return Ja(oe.objectKeys(this.shape))}}$e.create=(s,e)=>new $e({shape:()=>s,unknownKeys:"strip",catchall:vt.create(),typeName:H.ZodObject,...ee(e)}),$e.strictCreate=(s,e)=>new $e({shape:()=>s,unknownKeys:"strict",catchall:vt.create(),typeName:H.ZodObject,...ee(e)}),$e.lazycreate=(s,e)=>new $e({shape:s,unknownKeys:"strip",catchall:vt.create(),typeName:H.ZodObject,...ee(e)});class Cn extends ne{_parse(e){const{ctx:t}=this._processInputParams(e),n=this._def.options;if(t.common.async)return Promise.all(n.map(async r=>{const a={...t,common:{...t.common,issues:[]},parent:null};return{result:await r._parseAsync({data:t.data,path:t.path,parent:a}),ctx:a}})).then(function(r){for(const i of r)if(i.result.status==="valid")return i.result;for(const i of r)if(i.result.status==="dirty")return t.common.issues.push(...i.ctx.common.issues),i.result;const a=r.map(i=>new ze(i.ctx.common.issues));return P(t,{code:O.invalid_union,unionErrors:a}),G});{let r;const a=[];for(const o of n){const c={...t,common:{...t.common,issues:[]},parent:null},l=o._parseSync({data:t.data,path:t.path,parent:c});if(l.status==="valid")return l;l.status!=="dirty"||r||(r={result:l,ctx:c}),c.common.issues.length&&a.push(c.common.issues)}if(r)return t.common.issues.push(...r.ctx.common.issues),r.result;const i=a.map(o=>new ze(o));return P(t,{code:O.invalid_union,unionErrors:i}),G}}get options(){return this._def.options}}Cn.create=(s,e)=>new Cn({options:s,typeName:H.ZodUnion,...ee(e)});const _t=s=>s instanceof Mn?_t(s.schema):s instanceof Ye?_t(s.innerType()):s instanceof Zn?[s.value]:s instanceof Mt?s.options:s instanceof Nn?oe.objectValues(s.enum):s instanceof An?_t(s._def.innerType):s instanceof Sn?[void 0]:s instanceof kn?[null]:s instanceof it?[void 0,..._t(s.unwrap())]:s instanceof Zt?[null,..._t(s.unwrap())]:s instanceof Ls||s instanceof En?_t(s.unwrap()):s instanceof On?_t(s._def.innerType):[];class hs extends ne{_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==R.object)return P(t,{code:O.invalid_type,expected:R.object,received:t.parsedType}),G;const n=this.discriminator,r=t.data[n],a=this.optionsMap.get(r);return a?t.common.async?a._parseAsync({data:t.data,path:t.path,parent:t}):a._parseSync({data:t.data,path:t.path,parent:t}):(P(t,{code:O.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[n]}),G)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,n){const r=new Map;for(const a of t){const i=_t(a.shape[e]);if(!i.length)throw new Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(const o of i){if(r.has(o))throw new Error(`Discriminator property ${String(e)} has duplicate value ${String(o)}`);r.set(o,a)}}return new hs({typeName:H.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:r,...ee(n)})}}function Es(s,e){const t=ht(s),n=ht(e);if(s===e)return{valid:!0,data:s};if(t===R.object&&n===R.object){const r=oe.objectKeys(e),a=oe.objectKeys(s).filter(o=>r.indexOf(o)!==-1),i={...s,...e};for(const o of a){const c=Es(s[o],e[o]);if(!c.valid)return{valid:!1};i[o]=c.data}return{valid:!0,data:i}}if(t===R.array&&n===R.array){if(s.length!==e.length)return{valid:!1};const r=[];for(let a=0;a<s.length;a++){const i=Es(s[a],e[a]);if(!i.valid)return{valid:!1};r.push(i.data)}return{valid:!0,data:r}}return t===R.date&&n===R.date&&+s==+e?{valid:!0,data:s}:{valid:!1}}class Tn extends ne{_parse(e){const{status:t,ctx:n}=this._processInputParams(e),r=(a,i)=>{if(As(a)||As(i))return G;const o=Es(a.value,i.value);return o.valid?((Os(a)||Os(i))&&t.dirty(),{status:t.value,value:o.data}):(P(n,{code:O.invalid_intersection_types}),G)};return n.common.async?Promise.all([this._def.left._parseAsync({data:n.data,path:n.path,parent:n}),this._def.right._parseAsync({data:n.data,path:n.path,parent:n})]).then(([a,i])=>r(a,i)):r(this._def.left._parseSync({data:n.data,path:n.path,parent:n}),this._def.right._parseSync({data:n.data,path:n.path,parent:n}))}}Tn.create=(s,e,t)=>new Tn({left:s,right:e,typeName:H.ZodIntersection,...ee(t)});class dt extends ne{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==R.array)return P(n,{code:O.invalid_type,expected:R.array,received:n.parsedType}),G;if(n.data.length<this._def.items.length)return P(n,{code:O.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),G;!this._def.rest&&n.data.length>this._def.items.length&&(P(n,{code:O.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());const r=[...n.data].map((a,i)=>{const o=this._def.items[i]||this._def.rest;return o?o._parse(new lt(n,a,n.path,i)):null}).filter(a=>!!a);return n.common.async?Promise.all(r).then(a=>Ze.mergeArray(t,a)):Ze.mergeArray(t,r)}get items(){return this._def.items}rest(e){return new dt({...this._def,rest:e})}}dt.create=(s,e)=>{if(!Array.isArray(s))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new dt({items:s,typeName:H.ZodTuple,rest:null,...ee(e)})};class fs extends ne{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==R.object)return P(n,{code:O.invalid_type,expected:R.object,received:n.parsedType}),G;const r=[],a=this._def.keyType,i=this._def.valueType;for(const o in n.data)r.push({key:a._parse(new lt(n,o,n.path,o)),value:i._parse(new lt(n,n.data[o],n.path,o)),alwaysSet:o in n.data});return n.common.async?Ze.mergeObjectAsync(t,r):Ze.mergeObjectSync(t,r)}get element(){return this._def.valueType}static create(e,t,n){return new fs(t instanceof ne?{keyType:e,valueType:t,typeName:H.ZodRecord,...ee(n)}:{keyType:We.create(),valueType:e,typeName:H.ZodRecord,...ee(t)})}}class ps extends ne{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==R.map)return P(n,{code:O.invalid_type,expected:R.map,received:n.parsedType}),G;const r=this._def.keyType,a=this._def.valueType,i=[...n.data.entries()].map(([o,c],l)=>({key:r._parse(new lt(n,o,n.path,[l,"key"])),value:a._parse(new lt(n,c,n.path,[l,"value"]))}));if(n.common.async){const o=new Map;return Promise.resolve().then(async()=>{for(const c of i){const l=await c.key,d=await c.value;if(l.status==="aborted"||d.status==="aborted")return G;l.status!=="dirty"&&d.status!=="dirty"||t.dirty(),o.set(l.value,d.value)}return{status:t.value,value:o}})}{const o=new Map;for(const c of i){const l=c.key,d=c.value;if(l.status==="aborted"||d.status==="aborted")return G;l.status!=="dirty"&&d.status!=="dirty"||t.dirty(),o.set(l.value,d.value)}return{status:t.value,value:o}}}}ps.create=(s,e,t)=>new ps({valueType:e,keyType:s,typeName:H.ZodMap,...ee(t)});class Rt extends ne{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==R.set)return P(n,{code:O.invalid_type,expected:R.set,received:n.parsedType}),G;const r=this._def;r.minSize!==null&&n.data.size<r.minSize.value&&(P(n,{code:O.too_small,minimum:r.minSize.value,type:"set",inclusive:!0,exact:!1,message:r.minSize.message}),t.dirty()),r.maxSize!==null&&n.data.size>r.maxSize.value&&(P(n,{code:O.too_big,maximum:r.maxSize.value,type:"set",inclusive:!0,exact:!1,message:r.maxSize.message}),t.dirty());const a=this._def.valueType;function i(c){const l=new Set;for(const d of c){if(d.status==="aborted")return G;d.status==="dirty"&&t.dirty(),l.add(d.value)}return{status:t.value,value:l}}const o=[...n.data.values()].map((c,l)=>a._parse(new lt(n,c,n.path,l)));return n.common.async?Promise.all(o).then(c=>i(c)):i(o)}min(e,t){return new Rt({...this._def,minSize:{value:e,message:z.toString(t)}})}max(e,t){return new Rt({...this._def,maxSize:{value:e,message:z.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}Rt.create=(s,e)=>new Rt({valueType:s,minSize:null,maxSize:null,typeName:H.ZodSet,...ee(e)});class Dt extends ne{constructor(){super(...arguments),this.validate=this.implement}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==R.function)return P(t,{code:O.invalid_type,expected:R.function,received:t.parsedType}),G;function n(o,c){return os({data:o,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,is(),Jt].filter(l=>!!l),issueData:{code:O.invalid_arguments,argumentsError:c}})}function r(o,c){return os({data:o,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,is(),Jt].filter(l=>!!l),issueData:{code:O.invalid_return_type,returnTypeError:c}})}const a={errorMap:t.common.contextualErrorMap},i=t.data;if(this._def.returns instanceof Kt){const o=this;return Oe(async function(...c){const l=new ze([]),d=await o._def.args.parseAsync(c,a).catch(f=>{throw l.addIssue(n(c,f)),l}),u=await Reflect.apply(i,this,d);return await o._def.returns._def.type.parseAsync(u,a).catch(f=>{throw l.addIssue(r(u,f)),l})})}{const o=this;return Oe(function(...c){const l=o._def.args.safeParse(c,a);if(!l.success)throw new ze([n(c,l.error)]);const d=Reflect.apply(i,this,l.data),u=o._def.returns.safeParse(d,a);if(!u.success)throw new ze([r(d,u.error)]);return u.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new Dt({...this._def,args:dt.create(e).rest(At.create())})}returns(e){return new Dt({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,t,n){return new Dt({args:e||dt.create([]).rest(At.create()),returns:t||At.create(),typeName:H.ZodFunction,...ee(n)})}}class Mn extends ne{get schema(){return this._def.getter()}_parse(e){const{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}Mn.create=(s,e)=>new Mn({getter:s,typeName:H.ZodLazy,...ee(e)});class Zn extends ne{_parse(e){if(e.data!==this._def.value){const t=this._getOrReturnCtx(e);return P(t,{received:t.data,code:O.invalid_literal,expected:this._def.value}),G}return{status:"valid",value:e.data}}get value(){return this._def.value}}function Ja(s,e){return new Mt({values:s,typeName:H.ZodEnum,...ee(e)})}Zn.create=(s,e)=>new Zn({value:s,typeName:H.ZodLiteral,...ee(e)});class Mt extends ne{constructor(){super(...arguments),tn.set(this,void 0)}_parse(e){if(typeof e.data!="string"){const t=this._getOrReturnCtx(e),n=this._def.values;return P(t,{expected:oe.joinValues(n),received:t.parsedType,code:O.invalid_type}),G}if(ls(this,tn)||Va(this,tn,new Set(this._def.values)),!ls(this,tn).has(e.data)){const t=this._getOrReturnCtx(e),n=this._def.values;return P(t,{received:t.data,code:O.invalid_enum_value,options:n}),G}return Oe(e.data)}get options(){return this._def.values}get enum(){const e={};for(const t of this._def.values)e[t]=t;return e}get Values(){const e={};for(const t of this._def.values)e[t]=t;return e}get Enum(){const e={};for(const t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return Mt.create(e,{...this._def,...t})}exclude(e,t=this._def){return Mt.create(this.options.filter(n=>!e.includes(n)),{...this._def,...t})}}tn=new WeakMap,Mt.create=Ja;class Nn extends ne{constructor(){super(...arguments),nn.set(this,void 0)}_parse(e){const t=oe.getValidEnumValues(this._def.values),n=this._getOrReturnCtx(e);if(n.parsedType!==R.string&&n.parsedType!==R.number){const r=oe.objectValues(t);return P(n,{expected:oe.joinValues(r),received:n.parsedType,code:O.invalid_type}),G}if(ls(this,nn)||Va(this,nn,new Set(oe.getValidEnumValues(this._def.values))),!ls(this,nn).has(e.data)){const r=oe.objectValues(t);return P(n,{received:n.data,code:O.invalid_enum_value,options:r}),G}return Oe(e.data)}get enum(){return this._def.values}}nn=new WeakMap,Nn.create=(s,e)=>new Nn({values:s,typeName:H.ZodNativeEnum,...ee(e)});class Kt extends ne{unwrap(){return this._def.type}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==R.promise&&t.common.async===!1)return P(t,{code:O.invalid_type,expected:R.promise,received:t.parsedType}),G;const n=t.parsedType===R.promise?t.data:Promise.resolve(t.data);return Oe(n.then(r=>this._def.type.parseAsync(r,{path:t.path,errorMap:t.common.contextualErrorMap})))}}Kt.create=(s,e)=>new Kt({type:s,typeName:H.ZodPromise,...ee(e)});class Ye extends ne{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===H.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){const{status:t,ctx:n}=this._processInputParams(e),r=this._def.effect||null,a={addIssue:i=>{P(n,i),i.fatal?t.abort():t.dirty()},get path(){return n.path}};if(a.addIssue=a.addIssue.bind(a),r.type==="preprocess"){const i=r.transform(n.data,a);if(n.common.async)return Promise.resolve(i).then(async o=>{if(t.value==="aborted")return G;const c=await this._def.schema._parseAsync({data:o,path:n.path,parent:n});return c.status==="aborted"?G:c.status==="dirty"||t.value==="dirty"?cs(c.value):c});{if(t.value==="aborted")return G;const o=this._def.schema._parseSync({data:i,path:n.path,parent:n});return o.status==="aborted"?G:o.status==="dirty"||t.value==="dirty"?cs(o.value):o}}if(r.type==="refinement"){const i=o=>{const c=r.refinement(o,a);if(n.common.async)return Promise.resolve(c);if(c instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return o};if(n.common.async===!1){const o=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});return o.status==="aborted"?G:(o.status==="dirty"&&t.dirty(),i(o.value),{status:t.value,value:o.value})}return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(o=>o.status==="aborted"?G:(o.status==="dirty"&&t.dirty(),i(o.value).then(()=>({status:t.value,value:o.value}))))}if(r.type==="transform"){if(n.common.async===!1){const i=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});if(!Pt(i))return i;const o=r.transform(i.value,a);if(o instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:o}}return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(i=>Pt(i)?Promise.resolve(r.transform(i.value,a)).then(o=>({status:t.value,value:o})):i)}oe.assertNever(r)}}Ye.create=(s,e,t)=>new Ye({schema:s,typeName:H.ZodEffects,effect:e,...ee(t)}),Ye.createWithPreprocess=(s,e,t)=>new Ye({schema:e,effect:{type:"preprocess",transform:s},typeName:H.ZodEffects,...ee(t)});class it extends ne{_parse(e){return this._getType(e)===R.undefined?Oe(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}it.create=(s,e)=>new it({innerType:s,typeName:H.ZodOptional,...ee(e)});class Zt extends ne{_parse(e){return this._getType(e)===R.null?Oe(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}Zt.create=(s,e)=>new Zt({innerType:s,typeName:H.ZodNullable,...ee(e)});class An extends ne{_parse(e){const{ctx:t}=this._processInputParams(e);let n=t.data;return t.parsedType===R.undefined&&(n=this._def.defaultValue()),this._def.innerType._parse({data:n,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}An.create=(s,e)=>new An({innerType:s,typeName:H.ZodDefault,defaultValue:typeof e.default=="function"?e.default:()=>e.default,...ee(e)});class On extends ne{_parse(e){const{ctx:t}=this._processInputParams(e),n={...t,common:{...t.common,issues:[]}},r=this._def.innerType._parse({data:n.data,path:n.path,parent:{...n}});return wn(r)?r.then(a=>({status:"valid",value:a.status==="valid"?a.value:this._def.catchValue({get error(){return new ze(n.common.issues)},input:n.data})})):{status:"valid",value:r.status==="valid"?r.value:this._def.catchValue({get error(){return new ze(n.common.issues)},input:n.data})}}removeCatch(){return this._def.innerType}}On.create=(s,e)=>new On({innerType:s,typeName:H.ZodCatch,catchValue:typeof e.catch=="function"?e.catch:()=>e.catch,...ee(e)});class ms extends ne{_parse(e){if(this._getType(e)!==R.nan){const t=this._getOrReturnCtx(e);return P(t,{code:O.invalid_type,expected:R.nan,received:t.parsedType}),G}return{status:"valid",value:e.data}}}ms.create=s=>new ms({typeName:H.ZodNaN,...ee(s)});const Ql=Symbol("zod_brand");class Ls extends ne{_parse(e){const{ctx:t}=this._processInputParams(e),n=t.data;return this._def.type._parse({data:n,path:t.path,parent:t})}unwrap(){return this._def.type}}class Vn extends ne{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.common.async)return(async()=>{const r=await this._def.in._parseAsync({data:n.data,path:n.path,parent:n});return r.status==="aborted"?G:r.status==="dirty"?(t.dirty(),cs(r.value)):this._def.out._parseAsync({data:r.value,path:n.path,parent:n})})();{const r=this._def.in._parseSync({data:n.data,path:n.path,parent:n});return r.status==="aborted"?G:r.status==="dirty"?(t.dirty(),{status:"dirty",value:r.value}):this._def.out._parseSync({data:r.value,path:n.path,parent:n})}}static create(e,t){return new Vn({in:e,out:t,typeName:H.ZodPipeline})}}class En extends ne{_parse(e){const t=this._def.innerType._parse(e),n=r=>(Pt(r)&&(r.value=Object.freeze(r.value)),r);return wn(t)?t.then(r=>n(r)):n(t)}unwrap(){return this._def.innerType}}function qr(s,e={},t){return s?Ht.create().superRefine((n,r)=>{var a,i;if(!s(n)){const o=typeof e=="function"?e(n):typeof e=="string"?{message:e}:e,c=(i=(a=o.fatal)!==null&&a!==void 0?a:t)===null||i===void 0||i,l=typeof o=="string"?{message:o}:o;r.addIssue({code:"custom",...l,fatal:c})}}):Ht.create()}En.create=(s,e)=>new En({innerType:s,typeName:H.ZodReadonly,...ee(e)});const ed={object:$e.lazycreate};var H;(function(s){s.ZodString="ZodString",s.ZodNumber="ZodNumber",s.ZodNaN="ZodNaN",s.ZodBigInt="ZodBigInt",s.ZodBoolean="ZodBoolean",s.ZodDate="ZodDate",s.ZodSymbol="ZodSymbol",s.ZodUndefined="ZodUndefined",s.ZodNull="ZodNull",s.ZodAny="ZodAny",s.ZodUnknown="ZodUnknown",s.ZodNever="ZodNever",s.ZodVoid="ZodVoid",s.ZodArray="ZodArray",s.ZodObject="ZodObject",s.ZodUnion="ZodUnion",s.ZodDiscriminatedUnion="ZodDiscriminatedUnion",s.ZodIntersection="ZodIntersection",s.ZodTuple="ZodTuple",s.ZodRecord="ZodRecord",s.ZodMap="ZodMap",s.ZodSet="ZodSet",s.ZodFunction="ZodFunction",s.ZodLazy="ZodLazy",s.ZodLiteral="ZodLiteral",s.ZodEnum="ZodEnum",s.ZodEffects="ZodEffects",s.ZodNativeEnum="ZodNativeEnum",s.ZodOptional="ZodOptional",s.ZodNullable="ZodNullable",s.ZodDefault="ZodDefault",s.ZodCatch="ZodCatch",s.ZodPromise="ZodPromise",s.ZodBranded="ZodBranded",s.ZodPipeline="ZodPipeline",s.ZodReadonly="ZodReadonly"})(H||(H={}));const Br=We.create,Jr=Ct.create,td=ms.create,nd=Tt.create,Hr=bn.create,sd=jt.create,rd=ds.create,ad=Sn.create,id=kn.create,od=Ht.create,cd=At.create,ld=vt.create,dd=us.create,ud=Qe.create,pd=$e.create,md=$e.strictCreate,hd=Cn.create,fd=hs.create,gd=Tn.create,vd=dt.create,$d=fs.create,yd=ps.create,_d=Rt.create,xd=Dt.create,wd=Mn.create,bd=Zn.create,Sd=Mt.create,kd=Nn.create,Cd=Kt.create,Kr=Ye.create,Td=it.create,Md=Zt.create,Zd=Ye.createWithPreprocess,Nd=Vn.create,Ad={string:s=>We.create({...s,coerce:!0}),number:s=>Ct.create({...s,coerce:!0}),boolean:s=>bn.create({...s,coerce:!0}),bigint:s=>Tt.create({...s,coerce:!0}),date:s=>jt.create({...s,coerce:!0})},Od=G;var ge=Object.freeze({__proto__:null,defaultErrorMap:Jt,setErrorMap:function(s){Da=s},getErrorMap:is,makeIssue:os,EMPTY_PATH:[],addIssueToContext:P,ParseStatus:Ze,INVALID:G,DIRTY:cs,OK:Oe,isAborted:As,isDirty:Os,isValid:Pt,isAsync:wn,get util(){return oe},get objectUtil(){return Ns},ZodParsedType:R,getParsedType:ht,ZodType:ne,datetimeRegex:Ba,ZodString:We,ZodNumber:Ct,ZodBigInt:Tt,ZodBoolean:bn,ZodDate:jt,ZodSymbol:ds,ZodUndefined:Sn,ZodNull:kn,ZodAny:Ht,ZodUnknown:At,ZodNever:vt,ZodVoid:us,ZodArray:Qe,ZodObject:$e,ZodUnion:Cn,ZodDiscriminatedUnion:hs,ZodIntersection:Tn,ZodTuple:dt,ZodRecord:fs,ZodMap:ps,ZodSet:Rt,ZodFunction:Dt,ZodLazy:Mn,ZodLiteral:Zn,ZodEnum:Mt,ZodNativeEnum:Nn,ZodPromise:Kt,ZodEffects:Ye,ZodTransformer:Ye,ZodOptional:it,ZodNullable:Zt,ZodDefault:An,ZodCatch:On,ZodNaN:ms,BRAND:Ql,ZodBranded:Ls,ZodPipeline:Vn,ZodReadonly:En,custom:qr,Schema:ne,ZodSchema:ne,late:ed,get ZodFirstPartyTypeKind(){return H},coerce:Ad,any:od,array:ud,bigint:nd,boolean:Hr,date:sd,discriminatedUnion:fd,effect:Kr,enum:Sd,function:xd,instanceof:(s,e={message:`Input not instance of ${s.name}`})=>qr(t=>t instanceof s,e),intersection:gd,lazy:wd,literal:bd,map:yd,nan:td,nativeEnum:kd,never:ld,null:id,nullable:Md,number:Jr,object:pd,oboolean:()=>Hr().optional(),onumber:()=>Jr().optional(),optional:Td,ostring:()=>Br().optional(),pipeline:Nd,preprocess:Zd,promise:Cd,record:$d,set:_d,strictObject:md,string:Br,symbol:rd,transformer:Kr,tuple:vd,undefined:ad,union:hd,unknown:cd,void:dd,NEVER:Od,ZodIssueCode:O,quotelessJson:s=>JSON.stringify(s,null,2).replace(/"([^"]+)":/g,"$1:"),ZodError:ze});const Be=ge.object({name:ge.string().optional(),title:ge.string().optional(),command:ge.string().optional(),args:ge.array(ge.union([ge.string(),ge.number(),ge.boolean()])).optional(),env:ge.record(ge.union([ge.string(),ge.number(),ge.boolean(),ge.null(),ge.undefined()])).optional()}).passthrough(),Ed=ge.array(Be),Id=ge.object({servers:ge.array(Be)}).passthrough(),Pd=ge.object({mcpServers:ge.array(Be)}).passthrough(),jd=ge.object({servers:ge.record(Be)}).passthrough(),Rd=ge.object({mcpServers:ge.record(Be)}).passthrough(),Ld=ge.record(Be),Fd=Be.refine(s=>s.command!==void 0,{message:"Server must have a 'command' property"}),Ha=Symbol("MCPServerError");class Te extends Error{constructor(e){super(e),this.name="MCPServerError",Object.setPrototypeOf(this,Te.prototype)}}var ua;ua=Ha;class Fs{constructor(e){ve(this,"servers",Le([]));this.host=e,this.loadServersFromStorage()}handleMessageFromExtension(e){const t=e.data;if(t.type===le.getStoredMCPServersResponse){const n=t.data;return Array.isArray(n)&&this.servers.set(n),!0}return!1}async importServersFromJSON(e){return this.importFromJSON(e)}loadServersFromStorage(){try{this.host.postMessage({type:le.getStoredMCPServers})}catch(e){console.error("Failed to load MCP servers:",e),this.servers.set([])}}saveServers(e){try{this.host.postMessage({type:le.setStoredMCPServers,data:e})}catch(t){throw console.error("Failed to save MCP servers:",t),new Te("Failed to save MCP servers")}}getServers(){return this.servers}addServer(e){this.checkExistingServerName(e.name),this.servers.update(t=>{const n=[...t,{...e,id:crypto.randomUUID()}];return this.saveServers(n),n})}checkExistingServerName(e,t){const n=rn(this.servers).find(r=>r.name===e);if(n&&(n==null?void 0:n.id)!==t)throw new Te(`Server name '${e}' already exists`)}updateServer(e){this.checkExistingServerName(e.name,e.id),this.servers.update(t=>{const n=t.map(r=>r.id===e.id?e:r);return this.saveServers(n),n})}deleteServer(e){this.servers.update(t=>{const n=t.filter(r=>r.id!==e);return this.saveServers(n),n})}static convertServerToJSON(e){return JSON.stringify({mcpServers:{[e.name]:{command:e.command.split(" ")[0],args:e.command.split(" ").slice(1),env:e.env}}},null,2)}static parseServerValidationMessages(e){const t=new Map,n=new Map;e.forEach(a=>{var i;a.tools&&a.tools.length===0?t.set(a.id,"No tools are available for this MCP server"):a.disabledTools&&a.disabledTools.length===((i=a.tools)==null?void 0:i.length)?t.set(a.id,"All tools for this MCP server have validation errors: "+a.disabledTools.join(", ")):a.disabledTools&&a.disabledTools.length>0&&n.set(a.id,"MCP server has validation errors in the following tools which have been disabled: "+a.disabledTools.join(", "))});const r=this.parseDuplicateServerIds(e);return{errors:new Map([...t,...r]),warnings:n}}static parseDuplicateServerIds(e){const t=new Map;for(const r of e)t.has(r.name)||t.set(r.name,[]),t.get(r.name).push(r.id);const n=new Map;for(const[,r]of t)if(r.length>1)for(let a=1;a<r.length;a++)n.set(r[a],"MCP server is disabled due to duplicate server names");return n}parseServerConfigFromJSON(e){try{const t=JSON.parse(e),n=ge.union([Ed.transform(r=>r.map(a=>this.normalizeServerConfig(a))),Id.transform(r=>r.servers.map(a=>this.normalizeServerConfig(a))),Pd.transform(r=>r.mcpServers.map(a=>this.normalizeServerConfig(a))),jd.transform(r=>Object.entries(r.servers).map(([a,i])=>{const o=Be.parse(i);return this.normalizeServerConfig({...o,name:o.name||a})})),Rd.transform(r=>Object.entries(r.mcpServers).map(([a,i])=>{const o=Be.parse(i);return this.normalizeServerConfig({...o,name:o.name||a})})),Ld.transform(r=>{if(!Object.values(r).some(a=>{const i=Be.safeParse(a);return i.success&&i.data.command!==void 0}))throw new Error("No command property found in any server config");return Object.entries(r).map(([a,i])=>{const o=Be.parse(i);return this.normalizeServerConfig({...o,name:o.name||a})})}),Fd.transform(r=>[this.normalizeServerConfig(r)])]).safeParse(t);if(n.success)return n.data;throw new Te("Invalid JSON format. Expected an array of servers or an object with a 'servers' property.")}catch(t){throw t instanceof Te?t:new Te("Failed to parse MCP servers from JSON. Please check the format.")}}importFromJSON(e){try{const t=this.parseServerConfigFromJSON(e),n=rn(this.servers),r=new Set(n.map(a=>a.name));for(const a of t){if(!a.name)throw new Te("All servers must have a name.");if(r.has(a.name))throw new Te(`A server with the name '${a.name}' already exists.`);r.add(a.name)}return this.servers.update(a=>{const i=[...a,...t.map(o=>({...o,id:crypto.randomUUID()}))];return this.saveServers(i),i}),t.length}catch(t){throw t instanceof Te?t:new Te("Failed to import MCP servers from JSON. Please check the format.")}}normalizeServerConfig(e){try{const t=Be.transform(n=>{const r=n.command||"",a=n.args?n.args.map(l=>String(l)):[];if(!r)throw new Error("Server must have a 'command' property");const i=a.length>0?`${r} ${a.join(" ")}`:r,o=n.name||n.title||(r?r.split(" ")[0]:""),c=n.env?Object.fromEntries(Object.entries(n.env).filter(([l,d])=>d!=null).map(([l,d])=>[l,String(d)])):void 0;return{name:o,command:i,arguments:"",useShellInterpolation:!0,env:Object.keys(c||{}).length>0?c:void 0}}).refine(n=>!!n.name,{message:"Server must have a name",path:["name"]}).refine(n=>!!n.command,{message:"Server must have a command",path:["command"]}).safeParse(e);if(!t.success)throw new Te(t.error.message);return t.data}catch(t){throw t instanceof Error?new Te(`Invalid server configuration: ${t.message}`):new Te("Invalid server configuration")}}}ve(Fs,ua,"MCPServerError");function zd(s){let e,t,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},s[0]],r={};for(let a=0;a<n.length;a+=1)r=ye(r,n[a]);return{c(){e=Me("svg"),t=new Pn(!0),this.h()},l(a){e=jn(a,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var i=Rn(e);t=Ln(i,!0),i.forEach(y),this.h()},h(){t.a=null,ot(e,r)},m(a,i){Fn(a,e,i),t.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M256 48a208 208 0 1 1 0 416 208 208 0 1 1 0-416m0 464a256 256 0 1 0 0-512 256 256 0 1 0 0 512m-81-337c-9.4 9.4-9.4 24.6 0 33.9l47 47-47 47c-9.4 9.4-9.4 24.6 0 33.9s24.6 9.4 33.9 0l47-47 47 47c9.4 9.4 24.6 9.4 33.9 0s9.4-24.6 0-33.9l-47-47 47-47c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-47 47-47-47c-9.4-9.4-24.6-9.4-33.9 0"/>',e)},p(a,[i]){ot(e,r=$t(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&i&&a[0]]))},i:X,o:X,d(a){a&&y(e)}}}function Dd(s,e,t){return s.$$set=n=>{t(0,e=ye(ye({},e),Je(n)))},[e=Je(e)]}class Vd extends ue{constructor(e){super(),pe(this,e,Dd,zd,de,{})}}function Ud(s){let e,t,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},s[0]],r={};for(let a=0;a<n.length;a+=1)r=ye(r,n[a]);return{c(){e=Me("svg"),t=new Pn(!0),this.h()},l(a){e=jn(a,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var i=Rn(e);t=Ln(i,!0),i.forEach(y),this.h()},h(){t.a=null,ot(e,r)},m(a,i){Fn(a,e,i),t.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="m170.5 51.6-19 28.4h145l-19-28.4c-1.5-2.2-4-3.6-6.7-3.6h-93.7c-2.7 0-5.2 1.3-6.7 3.6zm147-26.6 36.7 55H424c13.3 0 24 10.7 24 24s-10.7 24-24 24h-8v304c0 44.2-35.8 80-80 80H112c-44.2 0-80-35.8-80-80V128h-8c-13.3 0-24-10.7-24-24s10.7-24 24-24h69.8l36.7-55.1C140.9 9.4 158.4 0 177.1 0h93.7c18.7 0 36.2 9.4 46.6 24.9zM80 128v304c0 17.7 14.3 32 32 32h224c17.7 0 32-14.3 32-32V128zm80 64v208c0 8.8-7.2 16-16 16s-16-7.2-16-16V192c0-8.8 7.2-16 16-16s16 7.2 16 16m80 0v208c0 8.8-7.2 16-16 16s-16-7.2-16-16V192c0-8.8 7.2-16 16-16s16 7.2 16 16m80 0v208c0 8.8-7.2 16-16 16s-16-7.2-16-16V192c0-8.8 7.2-16 16-16s16 7.2 16 16"/>',e)},p(a,[i]){ot(e,r=$t(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},1&i&&a[0]]))},i:X,o:X,d(a){a&&y(e)}}}function qd(s,e,t){return s.$$set=n=>{t(0,e=ye(ye({},e),Je(n)))},[e=Je(e)]}class Ka extends ue{constructor(e){super(),pe(this,e,qd,Ud,de,{})}}function Gr(s,e,t){const n=s.slice();return n[11]=e[t],n[12]=e,n[13]=t,n}function Bd(s){let e;return{c(){e=U("Environment Variables")},m(t,n){_(t,e,n)},d(t){t&&y(e)}}}function Wr(s){let e,t,n=[],r=new Map,a=xe(s[0]);const i=o=>o[11].id;for(let o=0;o<a.length;o+=1){let c=Gr(s,a,o),l=i(c);r.set(l,n[o]=Yr(l,c))}return{c(){for(let o=0;o<n.length;o+=1)n[o].c();e=Se()},m(o,c){for(let l=0;l<n.length;l+=1)n[l]&&n[l].m(o,c);_(o,e,c),t=!0},p(o,c){59&c&&(a=xe(o[0]),W(),n=Gt(n,c,i,1,o,a,r,e.parentNode,Wt,Yr,e,Gr),Y())},i(o){if(!t){for(let c=0;c<a.length;c+=1)p(n[c]);t=!0}},o(o){for(let c=0;c<n.length;c+=1)m(n[c]);t=!1},d(o){o&&y(e);for(let c=0;c<n.length;c+=1)n[c].d(o)}}}function Jd(s){let e,t;return e=new Ka({props:{slot:"iconLeft"}}),{c(){S(e.$$.fragment)},m(n,r){k(e,n,r),t=!0},p:X,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Hd(s){let e,t;return e=new Ot({props:{variant:"ghost",color:"neutral",type:"button",size:1,$$slots:{iconLeft:[Jd]},$$scope:{ctx:s}}}),e.$on("focus",function(){wt(s[1])&&s[1].apply(this,arguments)}),e.$on("click",function(){return s[10](s[11])}),{c(){S(e.$$.fragment)},m(n,r){k(e,n,r),t=!0},p(n,r){s=n;const a={};16384&r&&(a.$$scope={dirty:r,ctx:s}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Yr(s,e){let t,n,r,a,i,o,c,l,d,u,f,v,g;function h(b){e[6](b,e[11])}let w={size:1,placeholder:"Name",class:"full-width"};function N(b){e[8](b,e[11])}e[11].key!==void 0&&(w.value=e[11].key),r=new Wn({props:w}),tt.push(()=>nt(r,"value",h)),r.$on("focus",function(){wt(e[1])&&e[1].apply(this,arguments)}),r.$on("change",function(){return e[7](e[11])});let x={size:1,placeholder:"Value",class:"full-width"};return e[11].value!==void 0&&(x.value=e[11].value),c=new Wn({props:x}),tt.push(()=>nt(c,"value",N)),c.$on("focus",function(){wt(e[1])&&e[1].apply(this,arguments)}),c.$on("change",function(){return e[9](e[11])}),f=new sn({props:{content:"Remove",$$slots:{default:[Hd]},$$scope:{ctx:e}}}),{key:s,first:null,c(){t=Z("tr"),n=Z("td"),S(r.$$.fragment),i=E(),o=Z("td"),S(c.$$.fragment),d=E(),u=Z("td"),S(f.$$.fragment),v=E(),$(n,"class","name-cell svelte-1mazg1z"),$(o,"class","value-cell svelte-1mazg1z"),$(u,"class","action-cell svelte-1mazg1z"),$(t,"class","env-var-row svelte-1mazg1z"),this.first=t},m(b,T){_(b,t,T),M(t,n),k(r,n,null),M(t,i),M(t,o),k(c,o,null),M(t,d),M(t,u),k(f,u,null),M(t,v),g=!0},p(b,T){e=b;const D={};!a&&1&T&&(a=!0,D.value=e[11].key,st(()=>a=!1)),r.$set(D);const q={};!l&&1&T&&(l=!0,q.value=e[11].value,st(()=>l=!1)),c.$set(q);const se={};16387&T&&(se.$$scope={dirty:T,ctx:e}),f.$set(se)},i(b){g||(p(r.$$.fragment,b),p(c.$$.fragment,b),p(f.$$.fragment,b),g=!0)},o(b){m(r.$$.fragment,b),m(c.$$.fragment,b),m(f.$$.fragment,b),g=!1},d(b){b&&y(t),C(r),C(c),C(f)}}}function Kd(s){let e;return{c(){e=U("Variable")},m(t,n){_(t,e,n)},d(t){t&&y(e)}}}function Gd(s){let e,t;return e=new Ps({props:{slot:"iconLeft"}}),{c(){S(e.$$.fragment)},m(n,r){k(e,n,r),t=!0},p:X,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Wd(s){let e,t,n,r,a,i,o,c;e=new ce({props:{size:1,weight:"medium",$$slots:{default:[Bd]},$$scope:{ctx:s}}});let l=s[0].length>0&&Wr(s);return o=new Ot({props:{size:1,variant:"soft",color:"neutral",type:"button",$$slots:{iconLeft:[Gd],default:[Kd]},$$scope:{ctx:s}}}),o.$on("click",s[2]),{c(){S(e.$$.fragment),t=E(),n=Z("table"),r=Z("tbody"),l&&l.c(),a=E(),i=Z("div"),S(o.$$.fragment),$(n,"class","env-vars-table svelte-1mazg1z"),$(i,"class","new-var-button-container svelte-1mazg1z")},m(d,u){k(e,d,u),_(d,t,u),_(d,n,u),M(n,r),l&&l.m(r,null),_(d,a,u),_(d,i,u),k(o,i,null),c=!0},p(d,[u]){const f={};16384&u&&(f.$$scope={dirty:u,ctx:d}),e.$set(f),d[0].length>0?l?(l.p(d,u),1&u&&p(l,1)):(l=Wr(d),l.c(),p(l,1),l.m(r,null)):l&&(W(),m(l,1,1,()=>{l=null}),Y());const v={};16384&u&&(v.$$scope={dirty:u,ctx:d}),o.$set(v)},i(d){c||(p(e.$$.fragment,d),p(l),p(o.$$.fragment,d),c=!0)},o(d){m(e.$$.fragment,d),m(l),m(o.$$.fragment,d),c=!1},d(d){d&&(y(t),y(n),y(a),y(i)),C(e,d),l&&l.d(),C(o)}}}function Yd(s,e,t){let{handleEnterEditMode:n}=e,{envVarEntries:r=[]}=e;function a(c){n(),t(0,r=r.filter(l=>l.id!==c))}function i(c,l){const d=r.findIndex(u=>u.id===c);d!==-1&&(t(0,r[d].key=l,r),t(0,r))}function o(c,l){const d=r.findIndex(u=>u.id===c);d!==-1&&(t(0,r[d].value=l,r),t(0,r))}return s.$$set=c=>{"handleEnterEditMode"in c&&t(1,n=c.handleEnterEditMode),"envVarEntries"in c&&t(0,r=c.envVarEntries)},[r,n,function(){n(),t(0,r=[...r,{id:crypto.randomUUID(),key:"",value:""}])},a,i,o,function(c,l){s.$$.not_equal(l.key,c)&&(l.key=c,t(0,r))},c=>i(c.id,c.key),function(c,l){s.$$.not_equal(l.value,c)&&(l.value=c,t(0,r))},c=>o(c.id,c.value),c=>a(c.id)]}class Xd extends ue{constructor(e){super(),pe(this,e,Yd,Wd,de,{handleEnterEditMode:1,envVarEntries:0})}}function Qd(s){let e,t,n=[{xmlns:"http://www.w3.org/2000/svg"},{width:"16"},{height:"16"},{"data-ds-icon":"fa"},{viewBox:"0 0 16 16"},s[0]],r={};for(let a=0;a<n.length;a+=1)r=ye(r,n[a]);return{c(){e=Me("svg"),t=new Pn(!0),this.h()},l(a){e=jn(a,"svg",{xmlns:!0,width:!0,height:!0,"data-ds-icon":!0,viewBox:!0});var i=Rn(e);t=Ln(i,!0),i.forEach(y),this.h()},h(){t.a=null,ot(e,r)},m(a,i){Fn(a,e,i),t.m('<path fill-opacity=".01" d="M0 0h16v16H0z"/><circle cx="8" cy="8" r="3" fill-opacity=".831"/>',e)},p(a,[i]){ot(e,r=$t(n,[{xmlns:"http://www.w3.org/2000/svg"},{width:"16"},{height:"16"},{"data-ds-icon":"fa"},{viewBox:"0 0 16 16"},1&i&&a[0]]))},i:X,o:X,d(a){a&&y(e)}}}function eu(s,e,t){return s.$$set=n=>{t(0,e=ye(ye({},e),Je(n)))},[e=Je(e)]}class tu extends ue{constructor(e){super(),pe(this,e,eu,Qd,de,{})}}function nu(s){let e,t,n,r,a,i,o,c,l,d,u,f,v,g,h,w,N,x,b,T,D,q,se,we,ke;i=new ui({}),c=new ce({props:{color:"secondary",size:1,weight:"medium",$$slots:{default:[ru]},$$scope:{ctx:s}}});const re=[iu,au],L=[];function Ce(B,me){return B[0]==="addJson"?0:B[0]==="add"||B[0]==="edit"?1:-1}~(d=Ce(s))&&(u=L[d]=re[d](s));let ae=(s[0]==="add"||s[0]==="edit")&&Xr(s);return w=new va({props:{variant:"soft",color:"error",size:1,$$slots:{icon:[mu],default:[pu]},$$scope:{ctx:s}}}),b=new Ot({props:{size:1,variant:"ghost",color:"neutral",type:"button",$$slots:{default:[hu]},$$scope:{ctx:s}}}),b.$on("click",s[17]),D=new Ot({props:{size:1,variant:"solid",color:"accent",loading:s[2],type:"submit",disabled:s[13],$$slots:{default:[$u]},$$scope:{ctx:s}}}),{c(){e=Z("form"),t=Z("div"),n=Z("div"),r=Z("div"),a=Z("div"),S(i.$$.fragment),o=E(),S(c.$$.fragment),l=E(),u&&u.c(),f=E(),ae&&ae.c(),v=E(),g=Z("div"),h=Z("div"),S(w.$$.fragment),N=E(),x=Z("div"),S(b.$$.fragment),T=E(),S(D.$$.fragment),$(a,"class","server-icon svelte-13gg70a"),$(r,"class","server-title svelte-13gg70a"),$(n,"class","server-header svelte-13gg70a"),$(h,"class","error-container svelte-13gg70a"),_e(h,"is-error",!!s[1]),$(x,"class","form-actions svelte-13gg70a"),$(g,"class","form-actions-row svelte-13gg70a"),$(t,"class","server-edit-form svelte-13gg70a"),$(e,"class",q="c-mcp-server-card "+(s[0]==="add"||s[0]==="addJson"?"add-server-section":"server-item")+" svelte-13gg70a")},m(B,me){_(B,e,me),M(e,t),M(t,n),M(n,r),M(r,a),k(i,a,null),M(r,o),k(c,r,null),M(t,l),~d&&L[d].m(t,null),M(t,f),ae&&ae.m(t,null),M(t,v),M(t,g),M(g,h),k(w,h,null),M(g,N),M(g,x),k(b,x,null),M(x,T),k(D,x,null),se=!0,we||(ke=et(e,"submit",Qa(s[16])),we=!0)},p(B,me){const V={};4096&me[0]|8&me[1]&&(V.$$scope={dirty:me,ctx:B}),c.$set(V);let ut=d;d=Ce(B),d===ut?~d&&L[d].p(B,me):(u&&(W(),m(L[ut],1,1,()=>{L[ut]=null}),Y()),~d?(u=L[d],u?u.p(B,me):(u=L[d]=re[d](B),u.c()),p(u,1),u.m(t,f)):u=null),B[0]==="add"||B[0]==="edit"?ae?(ae.p(B,me),1&me[0]&&p(ae,1)):(ae=Xr(B),ae.c(),p(ae,1),ae.m(t,v)):ae&&(W(),m(ae,1,1,()=>{ae=null}),Y());const Un={};2&me[0]|8&me[1]&&(Un.$$scope={dirty:me,ctx:B}),w.$set(Un),(!se||2&me[0])&&_e(h,"is-error",!!B[1]);const zs={};8&me[1]&&(zs.$$scope={dirty:me,ctx:B}),b.$set(zs);const qn={};4&me[0]&&(qn.loading=B[2]),8192&me[0]&&(qn.disabled=B[13]),1&me[0]|8&me[1]&&(qn.$$scope={dirty:me,ctx:B}),D.$set(qn),(!se||1&me[0]&&q!==(q="c-mcp-server-card "+(B[0]==="add"||B[0]==="addJson"?"add-server-section":"server-item")+" svelte-13gg70a"))&&$(e,"class",q)},i(B){se||(p(i.$$.fragment,B),p(c.$$.fragment,B),p(u),p(ae),p(w.$$.fragment,B),p(b.$$.fragment,B),p(D.$$.fragment,B),se=!0)},o(B){m(i.$$.fragment,B),m(c.$$.fragment,B),m(u),m(ae),m(w.$$.fragment,B),m(b.$$.fragment,B),m(D.$$.fragment,B),se=!1},d(B){B&&y(e),C(i),C(c),~d&&L[d].d(),ae&&ae.d(),C(w),C(b),C(D),we=!1,ke()}}}function su(s){let e,t;return e=new za({props:{$$slots:{"header-right":[Pu],"header-left":[Su]},$$scope:{ctx:s}}}),{c(){S(e.$$.fragment)},m(n,r){k(e,n,r),t=!0},p(n,r){const a={};2168&r[0]|8&r[1]&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function ru(s){let e;return{c(){e=U(s[12])},m(t,n){_(t,e,n)},p(t,n){4096&n[0]&&he(e,t[12])},d(t){t&&y(e)}}}function au(s){let e,t,n,r,a,i,o,c,l,d;function u(h){s[30](h)}let f={size:1,placeholder:"Enter a name for your MCP server (e.g., 'Server Memory')",$$slots:{label:[cu]},$$scope:{ctx:s}};function v(h){s[31](h)}s[7]!==void 0&&(f.value=s[7]),n=new Wn({props:f}),tt.push(()=>nt(n,"value",u)),n.$on("focus",s[14]);let g={size:1,placeholder:"Enter the MCP command (e.g., 'npx -y @modelcontextprotocol/server-memory')",$$slots:{label:[du]},$$scope:{ctx:s}};return s[8]!==void 0&&(g.value=s[8]),c=new Wn({props:g}),tt.push(()=>nt(c,"value",v)),c.$on("focus",s[14]),{c(){e=Z("div"),t=Z("div"),S(n.$$.fragment),a=E(),i=Z("div"),o=Z("div"),S(c.$$.fragment),$(t,"class","input-field svelte-13gg70a"),$(e,"class","form-row svelte-13gg70a"),$(o,"class","input-field svelte-13gg70a"),$(i,"class","form-row svelte-13gg70a")},m(h,w){_(h,e,w),M(e,t),k(n,t,null),_(h,a,w),_(h,i,w),M(i,o),k(c,o,null),d=!0},p(h,w){const N={};8&w[1]&&(N.$$scope={dirty:w,ctx:h}),!r&&128&w[0]&&(r=!0,N.value=h[7],st(()=>r=!1)),n.$set(N);const x={};8&w[1]&&(x.$$scope={dirty:w,ctx:h}),!l&&256&w[0]&&(l=!0,x.value=h[8],st(()=>l=!1)),c.$set(x)},i(h){d||(p(n.$$.fragment,h),p(c.$$.fragment,h),d=!0)},o(h){m(n.$$.fragment,h),m(c.$$.fragment,h),d=!1},d(h){h&&(y(e),y(a),y(i)),C(n),C(c)}}}function iu(s){let e,t,n,r,a,i,o,c,l;function d(f){s[29](f)}n=new ce({props:{size:1,weight:"medium",$$slots:{default:[uu]},$$scope:{ctx:s}}});let u={size:1,placeholder:"Paste JSON here..."};return s[9]!==void 0&&(u.value=s[9]),o=new ki({props:u}),tt.push(()=>nt(o,"value",d)),{c(){e=Z("div"),t=Z("div"),S(n.$$.fragment),r=E(),a=Z("div"),i=Z("div"),S(o.$$.fragment),$(t,"class","input-field svelte-13gg70a"),$(e,"class","form-row svelte-13gg70a"),$(i,"class","input-field svelte-13gg70a"),$(a,"class","form-row svelte-13gg70a")},m(f,v){_(f,e,v),M(e,t),k(n,t,null),_(f,r,v),_(f,a,v),M(a,i),k(o,i,null),l=!0},p(f,v){const g={};8&v[1]&&(g.$$scope={dirty:v,ctx:f}),n.$set(g);const h={};!c&&512&v[0]&&(c=!0,h.value=f[9],st(()=>c=!1)),o.$set(h)},i(f){l||(p(n.$$.fragment,f),p(o.$$.fragment,f),l=!0)},o(f){m(n.$$.fragment,f),m(o.$$.fragment,f),l=!1},d(f){f&&(y(e),y(r),y(a)),C(n),C(o)}}}function ou(s){let e;return{c(){e=U("Name")},m(t,n){_(t,e,n)},d(t){t&&y(e)}}}function cu(s){let e,t;return e=new ce({props:{slot:"label",size:1,weight:"medium",$$slots:{default:[ou]},$$scope:{ctx:s}}}),{c(){S(e.$$.fragment)},m(n,r){k(e,n,r),t=!0},p(n,r){const a={};8&r[1]&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function lu(s){let e;return{c(){e=U("Command")},m(t,n){_(t,e,n)},d(t){t&&y(e)}}}function du(s){let e,t;return e=new ce({props:{slot:"label",size:1,weight:"medium",$$slots:{default:[lu]},$$scope:{ctx:s}}}),{c(){S(e.$$.fragment)},m(n,r){k(e,n,r),t=!0},p(n,r){const a={};8&r[1]&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function uu(s){let e;return{c(){e=U("Code Snippet")},m(t,n){_(t,e,n)},d(t){t&&y(e)}}}function Xr(s){let e,t,n;function r(i){s[32](i)}let a={handleEnterEditMode:s[14]};return s[10]!==void 0&&(a.envVarEntries=s[10]),e=new Xd({props:a}),tt.push(()=>nt(e,"envVarEntries",r)),{c(){S(e.$$.fragment)},m(i,o){k(e,i,o),n=!0},p(i,o){const c={};!t&&1024&o[0]&&(t=!0,c.envVarEntries=i[10],st(()=>t=!1)),e.$set(c)},i(i){n||(p(e.$$.fragment,i),n=!0)},o(i){m(e.$$.fragment,i),n=!1},d(i){C(e,i)}}}function pu(s){let e;return{c(){e=U(s[1])},m(t,n){_(t,e,n)},p(t,n){2&n[0]&&he(e,t[1])},d(t){t&&y(e)}}}function mu(s){let e,t;return e=new Vd({props:{slot:"icon"}}),{c(){S(e.$$.fragment)},m(n,r){k(e,n,r),t=!0},p:X,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function hu(s){let e;return{c(){e=U("Cancel")},m(t,n){_(t,e,n)},d(t){t&&y(e)}}}function fu(s){let e;return{c(){e=U("Save")},m(t,n){_(t,e,n)},d(t){t&&y(e)}}}function gu(s){let e;return{c(){e=U("Add")},m(t,n){_(t,e,n)},d(t){t&&y(e)}}}function vu(s){let e;return{c(){e=U("Import")},m(t,n){_(t,e,n)},d(t){t&&y(e)}}}function $u(s){let e;function t(a,i){return a[0]==="addJson"?vu:a[0]==="add"?gu:a[0]==="edit"?fu:void 0}let n=t(s),r=n&&n(s);return{c(){r&&r.c(),e=Se()},m(a,i){r&&r.m(a,i),_(a,e,i)},p(a,i){n!==(n=t(a))&&(r&&r.d(1),r=n&&n(a),r&&(r.c(),r.m(e.parentNode,e)))},d(a){a&&y(e),r&&r.d(a)}}}function yu(s){let e,t;return e=new tu({}),{c(){S(e.$$.fragment)},m(n,r){k(e,n,r),t=!0},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function _u(s){let e,t=s[3].name+"";return{c(){e=U(t)},m(n,r){_(n,e,r)},p(n,r){8&r[0]&&t!==(t=n[3].name+"")&&he(e,t)},d(n){n&&y(e)}}}function xu(s){let e,t,n;return t=new ce({props:{size:1,weight:"medium",$$slots:{default:[_u]},$$scope:{ctx:s}}}),{c(){e=Z("div"),S(t.$$.fragment),$(e,"class","server-name svelte-13gg70a")},m(r,a){_(r,e,a),k(t,e,null),n=!0},p(r,a){const i={};8&a[0]|8&a[1]&&(i.$$scope={dirty:a,ctx:r}),t.$set(i)},i(r){n||(p(t.$$.fragment,r),n=!0)},o(r){m(t.$$.fragment,r),n=!1},d(r){r&&y(e),C(t)}}}function wu(s){let e,t=s[3].command+"";return{c(){e=U(t)},m(n,r){_(n,e,r)},p(n,r){8&r[0]&&t!==(t=n[3].command+"")&&he(e,t)},d(n){n&&y(e)}}}function bu(s){let e,t,n;return t=new ce({props:{color:"secondary",size:1,weight:"regular",$$slots:{default:[wu]},$$scope:{ctx:s}}}),{c(){e=Z("div"),S(t.$$.fragment),$(e,"class","command-text svelte-13gg70a")},m(r,a){_(r,e,a),k(t,e,null),n=!0},p(r,a){const i={};8&a[0]|8&a[1]&&(i.$$scope={dirty:a,ctx:r}),t.$set(i)},i(r){n||(p(t.$$.fragment,r),n=!0)},o(r){m(t.$$.fragment,r),n=!1},d(r){r&&y(e),C(t)}}}function Su(s){let e,t,n,r,a,i,o;return t=new sn({props:{content:s[5]||s[6],$$slots:{default:[yu]},$$scope:{ctx:s}}}),r=new sn({props:{content:s[3].name,side:"top",align:"start",$$slots:{default:[xu]},$$scope:{ctx:s}}}),i=new sn({props:{content:s[3].command,side:"top",align:"start",$$slots:{default:[bu]},$$scope:{ctx:s}}}),{c(){e=Z("div"),S(t.$$.fragment),n=E(),S(r.$$.fragment),a=E(),S(i.$$.fragment),$(e,"class","l-header svelte-13gg70a"),$(e,"slot","header-left"),_e(e,"c-green-circle",!s[5]),_e(e,"c-warning-circle",!s[5]&&!!s[6]),_e(e,"c-red-circle",!!s[5])},m(c,l){_(c,e,l),k(t,e,null),M(e,n),k(r,e,null),M(e,a),k(i,e,null),o=!0},p(c,l){const d={};96&l[0]&&(d.content=c[5]||c[6]),8&l[1]&&(d.$$scope={dirty:l,ctx:c}),t.$set(d);const u={};8&l[0]&&(u.content=c[3].name),8&l[0]|8&l[1]&&(u.$$scope={dirty:l,ctx:c}),r.$set(u);const f={};8&l[0]&&(f.content=c[3].command),8&l[0]|8&l[1]&&(f.$$scope={dirty:l,ctx:c}),i.$set(f),(!o||32&l[0])&&_e(e,"c-green-circle",!c[5]),(!o||96&l[0])&&_e(e,"c-warning-circle",!c[5]&&!!c[6]),(!o||32&l[0])&&_e(e,"c-red-circle",!!c[5])},i(c){o||(p(t.$$.fragment,c),p(r.$$.fragment,c),p(i.$$.fragment,c),o=!0)},o(c){m(t.$$.fragment,c),m(r.$$.fragment,c),m(i.$$.fragment,c),o=!1},d(c){c&&y(e),C(t),C(r),C(i)}}}function ku(s){let e,t;return e=new bi({}),{c(){S(e.$$.fragment)},m(n,r){k(e,n,r),t=!0},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Cu(s){let e,t;return e=new zn({props:{size:1,variant:"ghost-block",color:"neutral",$$slots:{default:[ku]},$$scope:{ctx:s}}}),{c(){S(e.$$.fragment)},m(n,r){k(e,n,r),t=!0},p(n,r){const a={};8&r[1]&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Tu(s){let e;return{c(){e=U("Edit")},m(t,n){_(t,e,n)},d(t){t&&y(e)}}}function Mu(s){let e,t,n,r,a;return t=new Si({}),r=new ce({props:{size:1,weight:"medium",$$slots:{default:[Tu]},$$scope:{ctx:s}}}),{c(){e=Z("div"),S(t.$$.fragment),n=E(),S(r.$$.fragment),$(e,"class","status-controls-button svelte-13gg70a")},m(i,o){_(i,e,o),k(t,e,null),M(e,n),k(r,e,null),a=!0},p(i,o){const c={};8&o[1]&&(c.$$scope={dirty:o,ctx:i}),r.$set(c)},i(i){a||(p(t.$$.fragment,i),p(r.$$.fragment,i),a=!0)},o(i){m(t.$$.fragment,i),m(r.$$.fragment,i),a=!1},d(i){i&&y(e),C(t),C(r)}}}function Zu(s){let e;return{c(){e=U("Copy JSON")},m(t,n){_(t,e,n)},d(t){t&&y(e)}}}function Nu(s){let e,t,n,r,a;return t=new pi({}),r=new ce({props:{size:1,weight:"medium",$$slots:{default:[Zu]},$$scope:{ctx:s}}}),{c(){e=Z("div"),S(t.$$.fragment),n=E(),S(r.$$.fragment),$(e,"class","status-controls-button svelte-13gg70a")},m(i,o){_(i,e,o),k(t,e,null),M(e,n),k(r,e,null),a=!0},p(i,o){const c={};8&o[1]&&(c.$$scope={dirty:o,ctx:i}),r.$set(c)},i(i){a||(p(t.$$.fragment,i),p(r.$$.fragment,i),a=!0)},o(i){m(t.$$.fragment,i),m(r.$$.fragment,i),a=!1},d(i){i&&y(e),C(t),C(r)}}}function Au(s){let e;return{c(){e=U("Delete")},m(t,n){_(t,e,n)},d(t){t&&y(e)}}}function Ou(s){let e,t,n,r,a;return t=new Ka({}),r=new ce({props:{size:1,weight:"medium",$$slots:{default:[Au]},$$scope:{ctx:s}}}),{c(){e=Z("div"),S(t.$$.fragment),n=E(),S(r.$$.fragment),$(e,"class","status-controls-button svelte-13gg70a")},m(i,o){_(i,e,o),k(t,e,null),M(e,n),k(r,e,null),a=!0},p(i,o){const c={};8&o[1]&&(c.$$scope={dirty:o,ctx:i}),r.$set(c)},i(i){a||(p(t.$$.fragment,i),p(r.$$.fragment,i),a=!0)},o(i){m(t.$$.fragment,i),m(r.$$.fragment,i),a=!1},d(i){i&&y(e),C(t),C(r)}}}function Eu(s){let e,t,n,r,a,i;return e=new Ne.Item({props:{onSelect:s[14],$$slots:{default:[Mu]},$$scope:{ctx:s}}}),n=new Ne.Item({props:{onSelect:s[26],$$slots:{default:[Nu]},$$scope:{ctx:s}}}),a=new Ne.Item({props:{color:"error",onSelect:s[27],$$slots:{default:[Ou]},$$scope:{ctx:s}}}),{c(){S(e.$$.fragment),t=E(),S(n.$$.fragment),r=E(),S(a.$$.fragment)},m(o,c){k(e,o,c),_(o,t,c),k(n,o,c),_(o,r,c),k(a,o,c),i=!0},p(o,c){const l={};8&c[1]&&(l.$$scope={dirty:c,ctx:o}),e.$set(l);const d={};2048&c[0]&&(d.onSelect=o[26]),8&c[1]&&(d.$$scope={dirty:c,ctx:o}),n.$set(d);const u={};2072&c[0]&&(u.onSelect=o[27]),8&c[1]&&(u.$$scope={dirty:c,ctx:o}),a.$set(u)},i(o){i||(p(e.$$.fragment,o),p(n.$$.fragment,o),p(a.$$.fragment,o),i=!0)},o(o){m(e.$$.fragment,o),m(n.$$.fragment,o),m(a.$$.fragment,o),i=!1},d(o){o&&(y(t),y(r)),C(e,o),C(n,o),C(a,o)}}}function Iu(s){let e,t,n,r;return e=new Ne.Trigger({props:{$$slots:{default:[Cu]},$$scope:{ctx:s}}}),n=new Ne.Content({props:{side:"bottom",align:"end",$$slots:{default:[Eu]},$$scope:{ctx:s}}}),{c(){S(e.$$.fragment),t=E(),S(n.$$.fragment)},m(a,i){k(e,a,i),_(a,t,i),k(n,a,i),r=!0},p(a,i){const o={};8&i[1]&&(o.$$scope={dirty:i,ctx:a}),e.$set(o);const c={};2072&i[0]|8&i[1]&&(c.$$scope={dirty:i,ctx:a}),n.$set(c)},i(a){r||(p(e.$$.fragment,a),p(n.$$.fragment,a),r=!0)},o(a){m(e.$$.fragment,a),m(n.$$.fragment,a),r=!1},d(a){a&&y(t),C(e,a),C(n,a)}}}function Pu(s){let e,t,n,r,a;function i(c){s[28](c)}let o={$$slots:{default:[Iu]},$$scope:{ctx:s}};return s[11]!==void 0&&(o.requestClose=s[11]),n=new Ne.Root({props:o}),tt.push(()=>nt(n,"requestClose",i)),{c(){e=Z("div"),t=Z("div"),S(n.$$.fragment),$(t,"class","status-controls svelte-13gg70a"),$(e,"class","server-actions svelte-13gg70a"),$(e,"slot","header-right")},m(c,l){_(c,e,l),M(e,t),k(n,t,null),a=!0},p(c,l){const d={};2072&l[0]|8&l[1]&&(d.$$scope={dirty:l,ctx:c}),!r&&2048&l[0]&&(r=!0,d.requestClose=c[11],st(()=>r=!1)),n.$set(d)},i(c){a||(p(n.$$.fragment,c),a=!0)},o(c){m(n.$$.fragment,c),a=!1},d(c){c&&y(e),C(n)}}}function ju(s){let e,t,n,r;const a=[su,nu],i=[];function o(c,l){return c[0]==="view"&&c[3]?0:1}return e=o(s),t=i[e]=a[e](s),{c(){t.c(),n=Se()},m(c,l){i[e].m(c,l),_(c,n,l),r=!0},p(c,l){let d=e;e=o(c),e===d?i[e].p(c,l):(W(),m(i[d],1,1,()=>{i[d]=null}),Y(),t=i[e],t?t.p(c,l):(t=i[e]=a[e](c),t.c()),p(t,1),t.m(n.parentNode,n))},i(c){r||(p(t),r=!0)},o(c){m(t),r=!1},d(c){c&&y(n),i[e].d(c)}}}function Ru({key:s,value:e}){return s.trim()&&e.trim()}function Lu(s,e,t){let n,r,a,i,{server:o=null}=e,{onDelete:c}=e,{onAdd:l}=e,{onSave:d}=e,{onEdit:u}=e,{onJSONImport:f}=e,{onCancel:v}=e,{disabledText:g}=e,{warningText:h}=e,{mode:w="view"}=e,{mcpServerError:N=""}=e,x=(o==null?void 0:o.name)??"",b=(o==null?void 0:o.command)??"",T=(o==null?void 0:o.env)??{},D="",q=[];function se(){t(10,q=Object.entries(T).map(([L,Ce])=>({id:crypto.randomUUID(),key:L,value:Ce})))}se();let we=()=>{},{busy:ke=!1}=e;function re(){if(o){const L=Fs.convertServerToJSON(o);navigator.clipboard.writeText(L)}}return s.$$set=L=>{"server"in L&&t(3,o=L.server),"onDelete"in L&&t(4,c=L.onDelete),"onAdd"in L&&t(18,l=L.onAdd),"onSave"in L&&t(19,d=L.onSave),"onEdit"in L&&t(20,u=L.onEdit),"onJSONImport"in L&&t(21,f=L.onJSONImport),"onCancel"in L&&t(22,v=L.onCancel),"disabledText"in L&&t(5,g=L.disabledText),"warningText"in L&&t(6,h=L.warningText),"mode"in L&&t(0,w=L.mode),"mcpServerError"in L&&t(1,N=L.mcpServerError),"busy"in L&&t(2,ke=L.busy)},s.$$.update=()=>{384&s.$$.dirty[0]&&x&&b&&t(1,N=""),385&s.$$.dirty[0]&&t(25,n=!(w!=="add"||x.trim()&&b.trim())),513&s.$$.dirty[0]&&t(24,r=w==="addJson"&&!D.trim()),50331649&s.$$.dirty[0]&&t(13,a=n||w==="view"||r),1&s.$$.dirty[0]&&t(12,i=w==="add"||w==="addJson"?"New MCP Server":"Edit MCP Server")},[w,N,ke,o,c,g,h,x,b,D,q,we,i,a,function(){o&&w==="view"&&(t(0,w="edit"),u(o),we())},re,async function(){t(1,N=""),t(2,ke=!0);const L=q.filter(Ru);T=Object.fromEntries(L.map(({key:ae,value:B})=>[ae.trim(),B.trim()])),se();try{if(w==="add")await l({name:x.trim(),command:b.trim(),arguments:"",useShellInterpolation:!0,env:Object.keys(T).length>0?T:void 0});else if(w==="addJson"){try{JSON.parse(D)}catch(ae){const B=ae instanceof Error?ae.message:String(ae);throw new Te(`Invalid JSON format: ${B}`)}await f(D)}else w==="edit"&&o&&await d({...o,name:x.trim(),command:b.trim(),arguments:"",env:Object.keys(T).length>0?T:void 0})}catch(ae){t(1,N=(Ce=ae)!=null&&typeof Ce=="object"&&(Ce instanceof Te||Ha in(Ce.constructor||Ce))?ae.message:"Failed to save server"),console.warn(ae)}finally{t(2,ke=!1)}var Ce},function(){t(2,ke=!1),t(1,N=""),v==null||v(),t(9,D=""),t(7,x=(o==null?void 0:o.name)??""),t(8,b=(o==null?void 0:o.command)??""),T=o!=null&&o.env?{...o.env}:{},se()},l,d,u,f,v,se,r,n,()=>{re(),we()},()=>{c(o.id),we()},function(L){we=L,t(11,we)},function(L){D=L,t(9,D)},function(L){x=L,t(7,x)},function(L){b=L,t(8,b)},function(L){q=L,t(10,q)}]}class Ga extends ue{constructor(e){super(),pe(this,e,Lu,ju,de,{server:3,onDelete:4,onAdd:18,onSave:19,onEdit:20,onJSONImport:21,onCancel:22,disabledText:5,warningText:6,mode:0,mcpServerError:1,setLocalEnvVarFormState:23,busy:2},null,[-1,-1])}get setLocalEnvVarFormState(){return this.$$.ctx[23]}}function Fu(s){let e,t,n=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},s[0]],r={};for(let a=0;a<n.length;a+=1)r=ye(r,n[a]);return{c(){e=Me("svg"),t=new Pn(!0),this.h()},l(a){e=jn(a,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var i=Rn(e);t=Ln(i,!0),i.forEach(y),this.h()},h(){t.a=null,ot(e,r)},m(a,i){Fn(a,e,i),t.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M280 24c0-13.3-10.7-24-24-24s-24 10.7-24 24v270.1l-95-95c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9L239 369c9.4 9.4 24.6 9.4 33.9 0L409 233c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-95 95zM128.8 304H64c-35.3 0-64 28.7-64 64v80c0 35.3 28.7 64 64 64h384c35.3 0 64-28.7 64-64v-80c0-35.3-28.7-64-64-64h-64.8l-48 48H448c8.8 0 16 7.2 16 16v80c0 8.8-7.2 16-16 16H64c-8.8 0-16-7.2-16-16v-80c0-8.8 7.2-16 16-16h112.8zM432 408a24 24 0 1 0-48 0 24 24 0 1 0 48 0"/>',e)},p(a,[i]){ot(e,r=$t(n,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&i&&a[0]]))},i:X,o:X,d(a){a&&y(e)}}}function zu(s,e,t){return s.$$set=n=>{t(0,e=ye(ye({},e),Je(n)))},[e=Je(e)]}class Du extends ue{constructor(e){super(),pe(this,e,zu,Fu,de,{})}}function Qr(s,e,t){const n=s.slice();return n[20]=e[t],n}function Vu(s){let e;return{c(){e=Z("div"),e.textContent="MCP",$(e,"class","section-heading-text")},m(t,n){_(t,e,n)},p:X,d(t){t&&y(e)}}}function ea(s,e){let t,n,r;return n=new Ga({props:{mode:e[2]===e[20].id?"edit":"view",server:e[20],onAdd:e[8],onSave:e[9],onDelete:e[11],onEdit:e[7],onCancel:e[6],onJSONImport:e[10],disabledText:e[4].errors.get(e[20].id),warningText:e[4].warnings.get(e[20].id)}}),{key:s,first:null,c(){t=Se(),S(n.$$.fragment),this.first=t},m(a,i){_(a,t,i),k(n,a,i),r=!0},p(a,i){e=a;const o={};5&i&&(o.mode=e[2]===e[20].id?"edit":"view"),1&i&&(o.server=e[20]),17&i&&(o.disabledText=e[4].errors.get(e[20].id)),17&i&&(o.warningText=e[4].warnings.get(e[20].id)),n.$set(o)},i(a){r||(p(n.$$.fragment,a),r=!0)},o(a){m(n.$$.fragment,a),r=!1},d(a){a&&y(t),C(n,a)}}}function ta(s){let e,t;return e=new Ga({props:{mode:s[3],onAdd:s[8],onSave:s[9],onDelete:s[11],onEdit:s[7],onCancel:s[6],onJSONImport:s[10]}}),{c(){S(e.$$.fragment)},m(n,r){k(e,n,r),t=!0},p(n,r){const a={};8&r&&(a.mode=n[3]),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Uu(s){let e;return{c(){e=U("Add MCP")},m(t,n){_(t,e,n)},d(t){t&&y(e)}}}function qu(s){let e,t;return e=new Ps({props:{slot:"iconLeft"}}),{c(){S(e.$$.fragment)},m(n,r){k(e,n,r),t=!0},p:X,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function na(s){let e,t;return e=new Ot({props:{disabled:s[5],color:"neutral",variant:"soft",size:1,title:"Add MCP from JSON",$$slots:{iconLeft:[Ju],default:[Bu]},$$scope:{ctx:s}}}),e.$on("click",s[18]),{c(){S(e.$$.fragment)},m(n,r){k(e,n,r),t=!0},p(n,r){const a={};32&r&&(a.disabled=n[5]),8388608&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Bu(s){let e;return{c(){e=U("Import from JSON")},m(t,n){_(t,e,n)},d(t){t&&y(e)}}}function Ju(s){let e,t;return e=new Du({props:{slot:"iconLeft"}}),{c(){S(e.$$.fragment)},m(n,r){k(e,n,r),t=!0},p:X,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Hu(s){let e,t,n,r,a,i,o,c,l,d,u,f,v=[],g=new Map;n=new ce({props:{size:1,weight:"regular",color:"secondary",$$slots:{default:[Vu]},$$scope:{ctx:s}}});let h=xe(s[0]);const w=b=>b[20].id;for(let b=0;b<h.length;b+=1){let T=Qr(s,h,b),D=w(T);g.set(D,v[b]=ea(D,T))}let N=(s[3]==="add"||s[3]==="addJson")&&ta(s);d=new Ot({props:{disabled:s[5],color:"neutral",variant:"soft",size:1,$$slots:{iconLeft:[qu],default:[Uu]},$$scope:{ctx:s}}}),d.$on("click",s[17]);let x=s[1]&&na(s);return{c(){e=Z("div"),t=Z("div"),S(n.$$.fragment),r=E(),a=Z("div"),a.innerHTML=`Configure a new Model Context Protocol server to connect Augment to custom tools. Find out more
    about MCP <a href="https://docs.augmentcode.com/setup-augment/mcp">in the docs</a>.`,i=E();for(let b=0;b<v.length;b+=1)v[b].c();o=E(),N&&N.c(),c=E(),l=Z("div"),S(d.$$.fragment),u=E(),x&&x.c(),$(t,"class","section-heading svelte-1vnq4q3"),$(a,"class","description-text svelte-1vnq4q3"),$(e,"class","mcp-servers svelte-1vnq4q3"),$(l,"class","add-mcp-button-container svelte-1vnq4q3")},m(b,T){_(b,e,T),M(e,t),k(n,t,null),M(e,r),M(e,a),M(e,i);for(let D=0;D<v.length;D+=1)v[D]&&v[D].m(e,null);_(b,o,T),N&&N.m(b,T),_(b,c,T),_(b,l,T),k(d,l,null),M(l,u),x&&x.m(l,null),f=!0},p(b,[T]){const D={};8388608&T&&(D.$$scope={dirty:T,ctx:b}),n.$set(D),4053&T&&(h=xe(b[0]),W(),v=Gt(v,T,w,1,b,h,g,e,Wt,ea,null,Qr),Y()),b[3]==="add"||b[3]==="addJson"?N?(N.p(b,T),8&T&&p(N,1)):(N=ta(b),N.c(),p(N,1),N.m(c.parentNode,c)):N&&(W(),m(N,1,1,()=>{N=null}),Y());const q={};32&T&&(q.disabled=b[5]),8388608&T&&(q.$$scope={dirty:T,ctx:b}),d.$set(q),b[1]?x?(x.p(b,T),2&T&&p(x,1)):(x=na(b),x.c(),p(x,1),x.m(l,null)):x&&(W(),m(x,1,1,()=>{x=null}),Y())},i(b){if(!f){p(n.$$.fragment,b);for(let T=0;T<h.length;T+=1)p(v[T]);p(N),p(d.$$.fragment,b),p(x),f=!0}},o(b){m(n.$$.fragment,b);for(let T=0;T<v.length;T+=1)m(v[T]);m(N),m(d.$$.fragment,b),m(x),f=!1},d(b){b&&(y(e),y(o),y(c),y(l)),C(n);for(let T=0;T<v.length;T+=1)v[T].d();N&&N.d(b),C(d),x&&x.d()}}}function Ku(s,e,t){let n,r,{servers:a}=e,{onMCPServerAdd:i}=e,{onMCPServerSave:o}=e,{onMCPServerDelete:c}=e,{onCancel:l}=e,{onMCPServerJSONImport:d}=e,{isMCPImportEnabled:u=!0}=e,f=null,v=null;function g(b){return async function(...T){const D=await b(...T);return t(3,v=null),t(2,f=null),D}}const h=g(i),w=g(o),N=g(d),x=g(c);return s.$$set=b=>{"servers"in b&&t(0,a=b.servers),"onMCPServerAdd"in b&&t(12,i=b.onMCPServerAdd),"onMCPServerSave"in b&&t(13,o=b.onMCPServerSave),"onMCPServerDelete"in b&&t(14,c=b.onMCPServerDelete),"onCancel"in b&&t(15,l=b.onCancel),"onMCPServerJSONImport"in b&&t(16,d=b.onMCPServerJSONImport),"isMCPImportEnabled"in b&&t(1,u=b.isMCPImportEnabled)},s.$$.update=()=>{12&s.$$.dirty&&t(5,n=v==="add"||v==="addJson"||f!==null),1&s.$$.dirty&&t(4,r=Fs.parseServerValidationMessages(a))},[a,u,f,v,r,n,function(){t(2,f=null),t(3,v=null),l==null||l()},function(b){t(2,f=b.id)},h,w,N,x,i,o,c,l,d,()=>{t(3,v="add")},()=>{t(3,v="addJson")}]}class Gu extends ue{constructor(e){super(),pe(this,e,Ku,Hu,de,{servers:0,onMCPServerAdd:12,onMCPServerSave:13,onMCPServerDelete:14,onCancel:15,onMCPServerJSONImport:16,isMCPImportEnabled:1})}}function sa(s,e,t){const n=s.slice();return n[10]=e[t],n}function Wu(s){let e;return{c(){e=Z("div"),e.textContent="Terminal",$(e,"class","section-heading-text")},m(t,n){_(t,e,n)},p:X,d(t){t&&y(e)}}}function Yu(s){let e;return{c(){e=U("Shell")},m(t,n){_(t,e,n)},d(t){t&&y(e)}}}function Xu(s){let e;return{c(){e=U("Select a shell")},m(t,n){_(t,e,n)},p:X,d(t){t&&y(e)}}}function Qu(s){let e;return{c(){e=U("No shells available")},m(t,n){_(t,e,n)},p:X,d(t){t&&y(e)}}}function ep(s){let e,t;function n(i,o){return 2&o&&(e=null),e==null&&(e=!!i[5](i[1])),e?np:tp}let r=n(s,-1),a=r(s);return{c(){a.c(),t=Se()},m(i,o){a.m(i,o),_(i,t,o)},p(i,o){r===(r=n(i,o))&&a?a.p(i,o):(a.d(1),a=r(i),a&&(a.c(),a.m(t.parentNode,t)))},d(i){i&&y(t),a.d(i)}}}function tp(s){let e;return{c(){e=U(s[1])},m(t,n){_(t,e,n)},p(t,n){2&n&&he(e,t[1])},d(t){t&&y(e)}}}function np(s){var n;let e,t=(((n=s[5](s[1]))==null?void 0:n.friendlyName)||s[1])+"";return{c(){e=U(t)},m(r,a){_(r,e,a)},p(r,a){var i;2&a&&t!==(t=(((i=r[5](r[1]))==null?void 0:i.friendlyName)||r[1])+"")&&he(e,t)},d(r){r&&y(e)}}}function ra(s){var r;let e,t,n=((r=s[5](s[1]))==null?void 0:r.supportString)+"";return{c(){e=Z("div"),t=U(n),$(e,"class","shell-description svelte-zio0bh")},m(a,i){_(a,e,i),M(e,t)},p(a,i){var o;2&i&&n!==(n=((o=a[5](a[1]))==null?void 0:o.supportString)+"")&&he(t,n)},d(a){a&&y(e)}}}function sp(s){var h;let e,t,n,r,a,i,o,c,l,d=s[1]&&s[0].length>0&&((h=s[5](s[1]))==null?void 0:h.supportString);function u(w,N){return w[1]&&w[0].length>0?ep:w[0].length===0?Qu:Xu}let f=u(s),v=f(s),g=d&&ra(s);return c=new Ci({}),{c(){e=Z("div"),t=Z("div"),n=Z("div"),r=Z("div"),v.c(),a=E(),g&&g.c(),i=E(),o=Z("div"),S(c.$$.fragment),$(r,"class","shell-name svelte-zio0bh"),$(n,"class","shell-display svelte-zio0bh"),$(t,"class","dropdown-content svelte-zio0bh"),$(o,"class","dropdown-icon svelte-zio0bh"),$(e,"class","shell-dropdown-trigger svelte-zio0bh"),_e(e,"disabled",s[0].length===0)},m(w,N){_(w,e,N),M(e,t),M(t,n),M(n,r),v.m(r,null),M(n,a),g&&g.m(n,null),M(e,i),M(e,o),k(c,o,null),l=!0},p(w,N){var x;f===(f=u(w))&&v?v.p(w,N):(v.d(1),v=f(w),v&&(v.c(),v.m(r,null))),3&N&&(d=w[1]&&w[0].length>0&&((x=w[5](w[1]))==null?void 0:x.supportString)),d?g?g.p(w,N):(g=ra(w),g.c(),g.m(n,null)):g&&(g.d(1),g=null),(!l||1&N)&&_e(e,"disabled",w[0].length===0)},i(w){l||(p(c.$$.fragment,w),l=!0)},o(w){m(c.$$.fragment,w),l=!1},d(w){w&&y(e),v.d(),g&&g.d(),C(c)}}}function rp(s){let e,t;return e=new Ne.Label({props:{$$slots:{default:[ip]},$$scope:{ctx:s}}}),{c(){S(e.$$.fragment)},m(n,r){k(e,n,r),t=!0},p(n,r){const a={};8192&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function ap(s){let e,t,n=[],r=new Map,a=xe(s[0]);const i=o=>o[10].friendlyName;for(let o=0;o<a.length;o+=1){let c=sa(s,a,o),l=i(c);r.set(l,n[o]=ia(l,c))}return{c(){for(let o=0;o<n.length;o+=1)n[o].c();e=Se()},m(o,c){for(let l=0;l<n.length;l+=1)n[l]&&n[l].m(o,c);_(o,e,c),t=!0},p(o,c){27&c&&(a=xe(o[0]),W(),n=Gt(n,c,i,1,o,a,r,e.parentNode,Wt,ia,e,sa),Y())},i(o){if(!t){for(let c=0;c<a.length;c+=1)p(n[c]);t=!0}},o(o){for(let c=0;c<n.length;c+=1)m(n[c]);t=!1},d(o){o&&y(e);for(let c=0;c<n.length;c+=1)n[c].d(o)}}}function ip(s){let e;return{c(){e=U("No shells available")},m(t,n){_(t,e,n)},d(t){t&&y(e)}}}function aa(s){let e,t,n=s[10].supportString+"";return{c(){e=Z("div"),t=U(n),$(e,"class","shell-description svelte-zio0bh")},m(r,a){_(r,e,a),M(e,t)},p(r,a){1&a&&n!==(n=r[10].supportString+"")&&he(t,n)},d(r){r&&y(e)}}}function op(s){let e,t,n,r,a,i=s[10].friendlyName+"",o=s[10].supportString&&aa(s);return{c(){e=Z("div"),t=Z("div"),n=U(i),r=E(),o&&o.c(),a=E(),$(t,"class","shell-name svelte-zio0bh"),$(e,"class","shell-display svelte-zio0bh")},m(c,l){_(c,e,l),M(e,t),M(t,n),M(e,r),o&&o.m(e,null),_(c,a,l)},p(c,l){1&l&&i!==(i=c[10].friendlyName+"")&&he(n,i),c[10].supportString?o?o.p(c,l):(o=aa(c),o.c(),o.m(e,null)):o&&(o.d(1),o=null)},d(c){c&&(y(e),y(a)),o&&o.d()}}}function ia(s,e){let t,n,r;function a(){return e[8](e[10])}return n=new Ne.Item({props:{onSelect:a,highlight:e[1]===e[10].friendlyName,$$slots:{default:[op]},$$scope:{ctx:e}}}),{key:s,first:null,c(){t=Se(),S(n.$$.fragment),this.first=t},m(i,o){_(i,t,o),k(n,i,o),r=!0},p(i,o){e=i;const c={};25&o&&(c.onSelect=a),3&o&&(c.highlight=e[1]===e[10].friendlyName),8193&o&&(c.$$scope={dirty:o,ctx:e}),n.$set(c)},i(i){r||(p(n.$$.fragment,i),r=!0)},o(i){m(n.$$.fragment,i),r=!1},d(i){i&&y(t),C(n,i)}}}function cp(s){let e,t,n,r;const a=[ap,rp],i=[];function o(c,l){return c[0].length>0?0:1}return e=o(s),t=i[e]=a[e](s),{c(){t.c(),n=Se()},m(c,l){i[e].m(c,l),_(c,n,l),r=!0},p(c,l){let d=e;e=o(c),e===d?i[e].p(c,l):(W(),m(i[d],1,1,()=>{i[d]=null}),Y(),t=i[e],t?t.p(c,l):(t=i[e]=a[e](c),t.c()),p(t,1),t.m(n.parentNode,n))},i(c){r||(p(t),r=!0)},o(c){m(t),r=!1},d(c){c&&y(n),i[e].d(c)}}}function lp(s){let e,t,n,r;return e=new Ne.Trigger({props:{$$slots:{default:[sp]},$$scope:{ctx:s}}}),n=new Ne.Content({props:{side:"bottom",align:"start",$$slots:{default:[cp]},$$scope:{ctx:s}}}),{c(){S(e.$$.fragment),t=E(),S(n.$$.fragment)},m(a,i){k(e,a,i),_(a,t,i),k(n,a,i),r=!0},p(a,i){const o={};8195&i&&(o.$$scope={dirty:i,ctx:a}),e.$set(o);const c={};8219&i&&(c.$$scope={dirty:i,ctx:a}),n.$set(c)},i(a){r||(p(e.$$.fragment,a),p(n.$$.fragment,a),r=!0)},o(a){m(e.$$.fragment,a),m(n.$$.fragment,a),r=!1},d(a){a&&y(t),C(e,a),C(n,a)}}}function dp(s){let e;return{c(){e=U("Startup script")},m(t,n){_(t,e,n)},d(t){t&&y(e)}}}function up(s){let e;return{c(){e=U("Code to run whenever a new terminal is opened")},m(t,n){_(t,e,n)},d(t){t&&y(e)}}}function pp(s){let e,t,n,r,a,i,o,c,l,d,u,f,v,g,h,w,N,x,b,T,D,q,se;function we(re){s[9](re)}n=new ce({props:{size:1,weight:"regular",color:"secondary",$$slots:{default:[Wu]},$$scope:{ctx:s}}}),o=new ce({props:{size:1,weight:"medium",$$slots:{default:[Yu]},$$scope:{ctx:s}}});let ke={$$slots:{default:[lp]},$$scope:{ctx:s}};return s[4]!==void 0&&(ke.requestClose=s[4]),d=new Ne.Root({props:ke}),tt.push(()=>nt(d,"requestClose",we)),g=new ce({props:{size:1,weight:"medium",$$slots:{default:[dp]},$$scope:{ctx:s}}}),N=new ce({props:{size:1,color:"secondary",$$slots:{default:[up]},$$scope:{ctx:s}}}),{c(){e=Z("div"),t=Z("div"),S(n.$$.fragment),r=E(),a=Z("div"),i=Z("div"),S(o.$$.fragment),c=E(),l=Z("div"),S(d.$$.fragment),f=E(),v=Z("div"),S(g.$$.fragment),h=E(),w=Z("div"),S(N.$$.fragment),x=E(),b=Z("textarea"),$(t,"class","section-heading svelte-zio0bh"),$(l,"class","dropdown-container svelte-zio0bh"),$(i,"class","shell-selector svelte-zio0bh"),$(w,"class","startup-script-description svelte-zio0bh"),$(b,"class","startup-script-textarea svelte-zio0bh"),$(b,"placeholder","Enter shell commands to run on terminal startup"),b.value=T=s[2]||"",$(v,"class","startup-script-container svelte-zio0bh"),$(a,"class","terminal-content svelte-zio0bh"),$(e,"class","terminal-settings svelte-zio0bh")},m(re,L){_(re,e,L),M(e,t),k(n,t,null),M(e,r),M(e,a),M(a,i),k(o,i,null),M(i,c),M(i,l),k(d,l,null),M(a,f),M(a,v),k(g,v,null),M(v,h),M(v,w),k(N,w,null),M(v,x),M(v,b),D=!0,q||(se=et(b,"change",s[6]),q=!0)},p(re,[L]){const Ce={};8192&L&&(Ce.$$scope={dirty:L,ctx:re}),n.$set(Ce);const ae={};8192&L&&(ae.$$scope={dirty:L,ctx:re}),o.$set(ae);const B={};8219&L&&(B.$$scope={dirty:L,ctx:re}),!u&&16&L&&(u=!0,B.requestClose=re[4],st(()=>u=!1)),d.$set(B);const me={};8192&L&&(me.$$scope={dirty:L,ctx:re}),g.$set(me);const V={};8192&L&&(V.$$scope={dirty:L,ctx:re}),N.$set(V),(!D||4&L&&T!==(T=re[2]||""))&&(b.value=T)},i(re){D||(p(n.$$.fragment,re),p(o.$$.fragment,re),p(d.$$.fragment,re),p(g.$$.fragment,re),p(N.$$.fragment,re),D=!0)},o(re){m(n.$$.fragment,re),m(o.$$.fragment,re),m(d.$$.fragment,re),m(g.$$.fragment,re),m(N.$$.fragment,re),D=!1},d(re){re&&y(e),C(n),C(o),C(d),C(g),C(N),q=!1,se()}}}function mp(s,e,t){let n,{supportedShells:r=[]}=e,{selectedShell:a}=e,{startupScript:i}=e,{onShellSelect:o}=e,{onStartupScriptChange:c}=e;return s.$$set=l=>{"supportedShells"in l&&t(0,r=l.supportedShells),"selectedShell"in l&&t(1,a=l.selectedShell),"startupScript"in l&&t(2,i=l.startupScript),"onShellSelect"in l&&t(3,o=l.onShellSelect),"onStartupScriptChange"in l&&t(7,c=l.onStartupScriptChange)},[r,a,i,o,n,function(l){return r.find(d=>d.friendlyName===l)},function(l){const d=l.target;c(d.value)},c,l=>{o(l.friendlyName),n()},function(l){n=l,t(4,n)}]}class hp extends ue{constructor(e){super(),pe(this,e,mp,pp,de,{supportedShells:0,selectedShell:1,startupScript:2,onShellSelect:3,onStartupScriptChange:7})}}function oa(s){let e,t;return e=new Gu({props:{servers:s[1],onMCPServerAdd:s[7],onMCPServerSave:s[8],onMCPServerDelete:s[9],onMCPServerJSONImport:s[10],onCancel:s[11],isMCPImportEnabled:s[3]}}),{c(){S(e.$$.fragment)},m(n,r){k(e,n,r),t=!0},p(n,r){const a={};2&r&&(a.servers=n[1]),128&r&&(a.onMCPServerAdd=n[7]),256&r&&(a.onMCPServerSave=n[8]),512&r&&(a.onMCPServerDelete=n[9]),1024&r&&(a.onMCPServerJSONImport=n[10]),2048&r&&(a.onCancel=n[11]),8&r&&(a.isMCPImportEnabled=n[3]),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function ca(s){let e,t;return e=new hp({props:{supportedShells:s[12],selectedShell:s[13],startupScript:s[14],onShellSelect:s[15],onStartupScriptChange:s[16]}}),{c(){S(e.$$.fragment)},m(n,r){k(e,n,r),t=!0},p(n,r){const a={};4096&r&&(a.supportedShells=n[12]),8192&r&&(a.selectedShell=n[13]),16384&r&&(a.startupScript=n[14]),32768&r&&(a.onShellSelect=n[15]),65536&r&&(a.onStartupScriptChange=n[16]),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function fp(s){let e,t,n,r,a;t=new Il({props:{title:"Services",tools:s[0],onAuthenticate:s[5],onRevokeAccess:s[6]}});let i=s[2]&&oa(s),o=s[4]&&ca(s);return{c(){e=Z("div"),S(t.$$.fragment),n=E(),i&&i.c(),r=E(),o&&o.c(),$(e,"class","c-settings-tools svelte-181yusq")},m(c,l){_(c,e,l),k(t,e,null),M(e,n),i&&i.m(e,null),M(e,r),o&&o.m(e,null),a=!0},p(c,[l]){const d={};1&l&&(d.tools=c[0]),32&l&&(d.onAuthenticate=c[5]),64&l&&(d.onRevokeAccess=c[6]),t.$set(d),c[2]?i?(i.p(c,l),4&l&&p(i,1)):(i=oa(c),i.c(),p(i,1),i.m(e,r)):i&&(W(),m(i,1,1,()=>{i=null}),Y()),c[4]?o?(o.p(c,l),16&l&&p(o,1)):(o=ca(c),o.c(),p(o,1),o.m(e,null)):o&&(W(),m(o,1,1,()=>{o=null}),Y())},i(c){a||(p(t.$$.fragment,c),p(i),p(o),a=!0)},o(c){m(t.$$.fragment,c),m(i),m(o),a=!1},d(c){c&&y(e),C(t),i&&i.d(),o&&o.d()}}}function gp(s,e,t){let{tools:n=[]}=e,{servers:r=[]}=e,{isMCPEnabled:a=!0}=e,{isMCPImportEnabled:i=!0}=e,{isTerminalEnabled:o=!0}=e,{onAuthenticate:c}=e,{onRevokeAccess:l}=e,{onMCPServerAdd:d}=e,{onMCPServerSave:u}=e,{onMCPServerDelete:f}=e,{onMCPServerJSONImport:v}=e,{onCancel:g}=e,{supportedShells:h=[]}=e,{selectedShell:w}=e,{startupScript:N}=e,{onShellSelect:x=()=>{}}=e,{onStartupScriptChange:b=()=>{}}=e;return s.$$set=T=>{"tools"in T&&t(0,n=T.tools),"servers"in T&&t(1,r=T.servers),"isMCPEnabled"in T&&t(2,a=T.isMCPEnabled),"isMCPImportEnabled"in T&&t(3,i=T.isMCPImportEnabled),"isTerminalEnabled"in T&&t(4,o=T.isTerminalEnabled),"onAuthenticate"in T&&t(5,c=T.onAuthenticate),"onRevokeAccess"in T&&t(6,l=T.onRevokeAccess),"onMCPServerAdd"in T&&t(7,d=T.onMCPServerAdd),"onMCPServerSave"in T&&t(8,u=T.onMCPServerSave),"onMCPServerDelete"in T&&t(9,f=T.onMCPServerDelete),"onMCPServerJSONImport"in T&&t(10,v=T.onMCPServerJSONImport),"onCancel"in T&&t(11,g=T.onCancel),"supportedShells"in T&&t(12,h=T.supportedShells),"selectedShell"in T&&t(13,w=T.selectedShell),"startupScript"in T&&t(14,N=T.startupScript),"onShellSelect"in T&&t(15,x=T.onShellSelect),"onStartupScriptChange"in T&&t(16,b=T.onStartupScriptChange)},[n,r,a,i,o,c,l,d,u,f,v,g,h,w,N,x,b]}class vp extends ue{constructor(e){super(),pe(this,e,gp,fp,de,{tools:0,servers:1,isMCPEnabled:2,isMCPImportEnabled:3,isTerminalEnabled:4,onAuthenticate:5,onRevokeAccess:6,onMCPServerAdd:7,onMCPServerSave:8,onMCPServerDelete:9,onMCPServerJSONImport:10,onCancel:11,supportedShells:12,selectedShell:13,startupScript:14,onShellSelect:15,onStartupScriptChange:16})}}function $p(s){let e,t;return e=new Ti({}),{c(){S(e.$$.fragment)},m(n,r){k(e,n,r),t=!0},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function yp(s){let e,t;return e=new ce({props:{size:1,color:"primary",class:"c-orientation-settings-header-text",$$slots:{default:[xp]},$$scope:{ctx:s}}}),{c(){S(e.$$.fragment)},m(n,r){k(e,n,r),t=!0},p(n,r){const a={};8&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function _p(s){let e,t;return e=new ce({props:{size:1,color:"primary",class:"c-orientation-settings-header-text",$$slots:{default:[wp]},$$scope:{ctx:s}}}),{c(){S(e.$$.fragment)},m(n,r){k(e,n,r),t=!0},p(n,r){const a={};12&r&&(a.$$scope={dirty:r,ctx:n}),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function xp(s){let e;return{c(){e=U("Run Codebase Orientation")},m(t,n){_(t,e,n)},d(t){t&&y(e)}}}function wp(s){let e,t,n,r=s[2].progress+"";return{c(){e=U("Codebase Orientation in progress: "),t=U(r),n=U("%")},m(a,i){_(a,e,i),_(a,t,i),_(a,n,i)},p(a,i){4&i&&r!==(r=a[2].progress+"")&&he(t,r)},d(a){a&&(y(e),y(t),y(n))}}}function la(s){let e,t,n;return t=new ce({props:{size:1,type:"monospace",$$slots:{default:[bp]},$$scope:{ctx:s}}}),{c(){e=Z("div"),S(t.$$.fragment)},m(r,a){_(r,e,a),k(t,e,null),n=!0},p(r,a){const i={};12&a&&(i.$$scope={dirty:a,ctx:r}),t.$set(i)},i(r){n||(p(t.$$.fragment,r),n=!0)},o(r){m(t.$$.fragment,r),n=!1},d(r){r&&y(e),C(t)}}}function bp(s){let e,t=s[2].errorMessage+"";return{c(){e=U(t)},m(n,r){_(n,e,r)},p(n,r){4&r&&t!==(t=n[2].errorMessage+"")&&he(e,t)},d(n){n&&y(e)}}}function Sp(s){let e,t,n,r,a,i,o=s[2].state===Fe.succeeded?"Succeeded":s[2].state===Fe.failed?"Failed":"",c=(s[2].lastRunTimestamp?new Date(s[2].lastRunTimestamp).toLocaleTimeString():"unknown time")+"",l=s[2].state===Fe.failed&&la(s);return{c(){e=U(o),t=U(" at "),n=U(c),r=E(),l&&l.c(),a=Se()},m(d,u){_(d,e,u),_(d,t,u),_(d,n,u),_(d,r,u),l&&l.m(d,u),_(d,a,u),i=!0},p(d,u){(!i||4&u)&&o!==(o=d[2].state===Fe.succeeded?"Succeeded":d[2].state===Fe.failed?"Failed":"")&&he(e,o),(!i||4&u)&&c!==(c=(d[2].lastRunTimestamp?new Date(d[2].lastRunTimestamp).toLocaleTimeString():"unknown time")+"")&&he(n,c),d[2].state===Fe.failed?l?(l.p(d,u),4&u&&p(l,1)):(l=la(d),l.c(),p(l,1),l.m(a.parentNode,a)):l&&(W(),m(l,1,1,()=>{l=null}),Y())},i(d){i||(p(l),i=!0)},o(d){m(l),i=!1},d(d){d&&(y(e),y(t),y(n),y(r),y(a)),l&&l.d(d)}}}function kp(s){let e,t;return e=new gi({props:{slot:"icon"}}),{c(){S(e.$$.fragment)},m(n,r){k(e,n,r),t=!0},p:X,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Cp(s){let e,t,n,r,a,i,o,c,l,d;n=new zn({props:{size:1,variant:"solid",color:"accent",disabled:s[1],loading:s[1],$$slots:{default:[$p]},$$scope:{ctx:s}}}),n.$on("click",function(){wt(s[0])&&s[0].apply(this,arguments)});const u=[_p,yp],f=[];function v(g,h){return g[1]?0:1}return a=v(s),i=f[a]=u[a](s),c=new va({props:{variant:"soft",color:s[2].state===Fe.succeeded?"success":s[2].state===Fe.failed?"error":void 0,size:1,class:"c-orientation-settings-callout",$$slots:{icon:[kp],default:[Sp]},$$scope:{ctx:s}}}),{c(){e=Z("div"),t=Z("div"),S(n.$$.fragment),r=E(),i.c(),o=E(),S(c.$$.fragment),$(t,"class","c-orientation-settings-header svelte-3l3zou"),$(e,"class",l="c-orientation-settings c-orientation-settings__"+s[2].state.replace("-","_")+" svelte-3l3zou")},m(g,h){_(g,e,h),M(e,t),k(n,t,null),M(t,r),f[a].m(t,null),M(e,o),k(c,e,null),d=!0},p(g,[h]){s=g;const w={};2&h&&(w.disabled=s[1]),2&h&&(w.loading=s[1]),8&h&&(w.$$scope={dirty:h,ctx:s}),n.$set(w);let N=a;a=v(s),a===N?f[a].p(s,h):(W(),m(f[N],1,1,()=>{f[N]=null}),Y(),i=f[a],i?i.p(s,h):(i=f[a]=u[a](s),i.c()),p(i,1),i.m(t,null));const x={};4&h&&(x.color=s[2].state===Fe.succeeded?"success":s[2].state===Fe.failed?"error":void 0),12&h&&(x.$$scope={dirty:h,ctx:s}),c.$set(x),(!d||4&h&&l!==(l="c-orientation-settings c-orientation-settings__"+s[2].state.replace("-","_")+" svelte-3l3zou"))&&$(e,"class",l)},i(g){d||(p(n.$$.fragment,g),p(i),p(c.$$.fragment,g),d=!0)},o(g){m(n.$$.fragment,g),m(i),m(c.$$.fragment,g),d=!1},d(g){g&&y(e),C(n),f[a].d(),C(c)}}}function Tp(s,e,t){let{onRunInitialOrientation:n}=e,{orientationInProgress:r=!1}=e,{orientationStatus:a={state:Fe.idle}}=e;return s.$$set=i=>{"onRunInitialOrientation"in i&&t(0,n=i.onRunInitialOrientation),"orientationInProgress"in i&&t(1,r=i.orientationInProgress),"orientationStatus"in i&&t(2,a=i.orientationStatus)},[n,r,a]}class Mp extends ue{constructor(e){super(),pe(this,e,Tp,Cp,de,{onRunInitialOrientation:0,orientationInProgress:1,orientationStatus:2})}}function Zp(s){let e,t,n,r;function a(o){s[6](o)}let i={placeholder:"Add your guidelines for Augment Chat...",resize:"vertical",saveFunction:s[3]};return s[0]!==void 0&&(i.value=s[0]),t=new Zi({props:i}),tt.push(()=>nt(t,"value",a)),t.$on("focus",s[7]),{c(){e=Z("div"),S(t.$$.fragment),$(e,"class","c-user-guidelines-category__input svelte-10borzo")},m(o,c){_(o,e,c),k(t,e,null),r=!0},p(o,[c]){const l={};!n&&1&c&&(n=!0,l.value=o[0],st(()=>n=!1)),t.$set(l)},i(o){r||(p(t.$$.fragment,o),r=!0)},o(o){m(t.$$.fragment,o),r=!1},d(o){o&&y(e),C(t)}}}function Np(s,e,t){let n;const r=ei();let{userGuidelines:a=""}=e,{userGuidelinesLengthLimit:i}=e,{updateUserGuideline:o=()=>!1}=e;const c=Le(void 0);function l(){const d=a.trim();if(n!==d){if(!o(d))throw i&&d.length>i?`The user guideline must be less than ${i} character long`:"An error occurred updating the user";Us(c,n=d,n)}}return rt(s,c,d=>t(8,n=d)),ti(()=>{Us(c,n=a.trim(),n)}),ga(()=>{l()}),s.$$set=d=>{"userGuidelines"in d&&t(0,a=d.userGuidelines),"userGuidelinesLengthLimit"in d&&t(4,i=d.userGuidelinesLengthLimit),"updateUserGuideline"in d&&t(5,o=d.updateUserGuideline)},[a,r,c,l,i,o,function(d){a=d,t(0,a)},d=>{r("focus",d)}]}class Ap extends ue{constructor(e){super(),pe(this,e,Np,Zp,de,{userGuidelines:0,userGuidelinesLengthLimit:4,updateUserGuideline:5})}}function Op(s){let e,t;return e=new vp({props:{tools:s[8],onAuthenticate:s[23],onRevokeAccess:s[24],servers:s[9],onMCPServerAdd:s[30],onMCPServerSave:s[31],onMCPServerDelete:s[32],onMCPServerJSONImport:s[33],isMCPEnabled:s[10]&&s[0].mcpServerList,isMCPImportEnabled:s[10]&&s[0].mcpServerImport,supportedShells:s[11].supportedShells,selectedShell:s[11].selectedShell,startupScript:s[11].startupScript,onShellSelect:s[25],onStartupScriptChange:s[26],isTerminalEnabled:s[0].terminal}}),{c(){S(e.$$.fragment)},m(n,r){k(e,n,r),t=!0},p(n,r){const a={};256&r[0]&&(a.tools=n[8]),512&r[0]&&(a.servers=n[9]),1025&r[0]&&(a.isMCPEnabled=n[10]&&n[0].mcpServerList),1025&r[0]&&(a.isMCPImportEnabled=n[10]&&n[0].mcpServerImport),2048&r[0]&&(a.supportedShells=n[11].supportedShells),2048&r[0]&&(a.selectedShell=n[11].selectedShell),2048&r[0]&&(a.startupScript=n[11].startupScript),1&r[0]&&(a.isTerminalEnabled=n[0].terminal),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Ep(s){let e,t;return e=new Ap({props:{userGuidelines:s[5],userGuidelinesLengthLimit:s[4],updateUserGuideline:s[22]}}),{c(){S(e.$$.fragment)},m(n,r){k(e,n,r),t=!0},p(n,r){const a={};32&r[0]&&(a.userGuidelines=n[5]),16&r[0]&&(a.userGuidelinesLengthLimit=n[4]),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Ip(s){let e,t;return e=new Mp({props:{onRunInitialOrientation:s[27],orientationInProgress:s[6],orientationStatus:s[7]}}),{c(){S(e.$$.fragment)},m(n,r){k(e,n,r),t=!0},p(n,r){const a={};64&r[0]&&(a.orientationInProgress=n[6]),128&r[0]&&(a.orientationStatus=n[7]),e.$set(a)},i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function Pp(s){let e,t;return e=new vc({}),{c(){S(e.$$.fragment)},m(n,r){k(e,n,r),t=!0},p:X,i(n){t||(p(e.$$.fragment,n),t=!0)},o(n){m(e.$$.fragment,n),t=!1},d(n){C(e,n)}}}function jp(s){return{c:X,m:X,p:X,i:X,o:X,d:X}}function Rp(s){let e,t,n,r,a;const i=[jp,Pp,Ip,Ep,Op],o=[];function c(l,d){var u,f,v;return 128&d[1]&&(t=null),t==null&&(t=!Ra(l[38])),t?0:((u=l[38])==null?void 0:u.name)==="Context"?1:((f=l[38])==null?void 0:f.name)==="Orientation"?2:((v=l[38])==null?void 0:v.name)==="User Guidelines"?3:4}return n=c(s,[-1,-1]),r=o[n]=i[n](s),{c(){e=Z("span"),r.c(),$(e,"slot","content")},m(l,d){_(l,e,d),o[n].m(e,null),a=!0},p(l,d){let u=n;n=c(l,d),n===u?o[n].p(l,d):(W(),m(o[u],1,1,()=>{o[u]=null}),Y(),r=o[n],r?r.p(l,d):(r=o[n]=i[n](l),r.c()),p(r,1),r.m(e,null))},i(l){a||(p(r),a=!0)},o(l){m(r),a=!1},d(l){l&&y(e),o[n].d()}}}function Lp(s){let e,t,n;function r(i){s[34](i)}let a={items:s[3],mode:"tree",class:"c-settings-navigation",$$slots:{content:[Rp,({item:i})=>({38:i}),({item:i})=>[0,i?128:0]]},$$scope:{ctx:s}};return s[1]!==void 0&&(a.selectedId=s[1]),e=new qc({props:a}),tt.push(()=>nt(e,"selectedId",r)),{c(){S(e.$$.fragment)},m(i,o){k(e,i,o),n=!0},p(i,o){const c={};8&o[0]&&(c.items=i[3]),4081&o[0]|384&o[1]&&(c.$$scope={dirty:o,ctx:i}),!t&&2&o[0]&&(t=!0,c.selectedId=i[1],st(()=>t=!1)),e.$set(c)},i(i){n||(p(e.$$.fragment,i),n=!0)},o(i){m(e.$$.fragment,i),n=!1},d(i){C(e,i)}}}function Fp(s){let e,t,n,r;return e=new Mi.Root({props:{$$slots:{default:[Lp]},$$scope:{ctx:s}}}),{c(){S(e.$$.fragment)},m(a,i){k(e,a,i),t=!0,n||(r=et(window,"message",s[13].onMessageFromExtension),n=!0)},p(a,i){const o={};4091&i[0]|256&i[1]&&(o.$$scope={dirty:i,ctx:a}),e.$set(o)},i(a){t||(p(e.$$.fragment,a),t=!0)},o(a){m(e.$$.fragment,a),t=!1},d(a){C(e,a),n=!1,r()}}}function zp(s,e,t){let n,r,a,i,o,c,l,d,u,f,v,g=X,h=()=>(g(),g=ma(B,V=>t(9,u=V)),B);s.$$.on_destroy.push(()=>g());const w=new Ai(Ve),N=new Fo(Ve),x=new zo(Ve),b=new ni(Ve),T=w.getSettingsComponentSupported();rt(s,T,V=>t(0,a=V));const D=w.getEnableAgentMode();rt(s,D,V=>t(10,f=V));const q=w.getEnableInitialOrientation();rt(s,q,V=>t(28,i=V)),b.registerConsumer(w),b.registerConsumer(N),b.registerConsumer(x);const se=x.getTerminalSettings();let we;rt(s,se,V=>t(11,v=V));const ke=Le({state:Fe.idle});rt(s,ke,V=>t(7,l=V));const re=pa(ke,V=>V.state===Fe.inProgress);rt(s,re,V=>t(6,c=V));const L={handleMessageFromExtension:V=>V.data&&V.data.type===le.orientationStatusUpdate?(ke.set(V.data.data),!0):!(!V.data||V.data.type!==le.navigateToSettingsSection)&&(V.data.data&&typeof V.data.data=="string"&&t(1,we=V.data.data),!0)};b.registerConsumer(L);const Ce=w.getDisplayableTools();rt(s,Ce,V=>t(8,d=V));const ae=w.getGuidelines();rt(s,ae,V=>t(29,o=V)),ga(()=>{w.dispose()}),w.notifyLoaded(),Ve.postMessage({type:le.getOrientationStatus});let B=N.getServers();h();let me=[];return Ve.postMessage({type:le.settingsPanelLoaded}),s.$$.update=()=>{var V,ut;536870912&s.$$.dirty[0]&&t(5,n=(V=o.userGuidelines)==null?void 0:V.contents),536870912&s.$$.dirty[0]&&t(4,r=(ut=o.userGuidelines)==null?void 0:ut.lengthLimit),268435457&s.$$.dirty[0]&&t(3,me=[a.remoteTools?$s("Tools","",Jc,"section-tools"):void 0,i&&a.orientation?$s("Orientation",`Codebase Orientation helps Augment understand your project structure, technologies, and
      patterns. This process takes a few minutes to run but enables more accurate and contextual
      assistance with your code.`,Kc,"orientation"):void 0,a.userGuidelines?$s("User Guidelines","Guidelines for Augment Chat to follow.",$i,"userGuidelines"):void 0,a.workspaceContext?{name:"Context",description:"",icon:Wc,id:"context"}:void 0].filter(Un=>Un!==void 0))},h(t(2,B=N.getServers())),[a,we,B,me,r,n,c,l,d,u,f,v,N,b,T,D,q,se,ke,re,Ce,ae,function(V){const ut=V.trim();return!(r&&ut.length>r)&&(w.updateLocalUserGuidelines(ut),Ve.postMessage({type:le.updateUserGuidelines,data:V}),!0)},function(V){Ve.postMessage({type:le.toolConfigStartOAuth,data:{authUrl:V}}),w.startPolling()},function(V){Ve.postMessage({type:le.toolConfigRevokeAccess,data:{toolId:V.identifier}})},function(V){x.updateSelectedShell(V)},function(V){x.updateStartupScript(V)},function(){Ve.postMessage({type:le.executeInitialOrientation})},i,o,V=>N.addServer(V),V=>N.updateServer(V),V=>N.deleteServer(V),V=>N.importServersFromJSON(V),function(V){we=V,t(1,we)}]}new class extends ue{constructor(s){super(),pe(this,s,zp,Fp,de,{},null,[-1,-1])}}({target:document.getElementById("app")});
