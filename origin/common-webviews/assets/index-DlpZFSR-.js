import{B as F,C as h}from"./SpinnerAugment-BUJasFTo.js";function x(r){const o=r-1;return o*o*o+1}function C(r,{delay:o=0,duration:d=400,easing:p=F}={}){const i=+getComputedStyle(r).opacity;return{delay:o,duration:d,easing:p,css:a=>"opacity: "+a*i}}function w(r,{delay:o=0,duration:d=400,easing:p=x,x:i=0,y:a=0,opacity:y=0}={}){const s=getComputedStyle(r),$=+s.opacity,e=s.transform==="none"?"":s.transform,n=$*(1-y),[c,g]=h(i),[u,m]=h(a);return{delay:o,duration:d,easing:p,css:(l,f)=>`
			transform: ${e} translate(${(1-l)*c}${g}, ${(1-l)*u}${m});
			opacity: ${$-n*f}`}}function S(r,{delay:o=0,duration:d=400,easing:p=x,axis:i="y"}={}){const a=getComputedStyle(r),y=+a.opacity,s=i==="y"?"height":"width",$=parseFloat(a[s]),e=i==="y"?["top","bottom"]:["left","right"],n=e.map(t=>`${t[0].toUpperCase()}${t.slice(1)}`),c=parseFloat(a[`padding${n[0]}`]),g=parseFloat(a[`padding${n[1]}`]),u=parseFloat(a[`margin${n[0]}`]),m=parseFloat(a[`margin${n[1]}`]),l=parseFloat(a[`border${n[0]}Width`]),f=parseFloat(a[`border${n[1]}Width`]);return{delay:o,duration:d,easing:p,css:t=>`overflow: hidden;opacity: ${Math.min(20*t,1)*y};${s}: ${t*$}px;padding-${e[0]}: ${t*c}px;padding-${e[1]}: ${t*g}px;margin-${e[0]}: ${t*u}px;margin-${e[1]}: ${t*m}px;border-${e[0]}-width: ${t*l}px;border-${e[1]}-width: ${t*f}px;`}}export{C as a,w as f,S as s};
