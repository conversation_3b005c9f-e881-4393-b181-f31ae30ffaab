import{S as H,i as N,s as O,a as F,b as j,H as q,w as J,x as Q,y as Y,h as g,d as G,z as Z,g as _,n as w,j as S,Q as z,R as E,V as v,W as x,Y as B,u as $,t as h,Z as y,J as M,T as I,c as C,e as b,f as L,a0 as V,$ as K,P as X,r as ee,ad as P,a2 as te,_ as se,q as ne,ah as R}from"./SpinnerAugment-BUJasFTo.js";import"./design-system-init-BKdwvVur.js";import{h as T,W as A}from"./BaseButton-ci_067e0.js";import{S as U,O as oe}from"./OpenFileButton-Cgk3MGWq.js";import{C as ae,E as ce}from"./chat-flags-model-pSBfdnEi.js";import{M as D}from"./TextTooltipAugment-UDQF2J4S.js";import{M as ie}from"./MarkdownEditor-CL85cpd_.js";import"./open-in-new-window-eiueNVFd.js";import"./types-CF53Ux0u.js";import"./file-base64-RhZyEMB8.js";import"./file-paths-BcSg4gks.js";import"./types-e72Yl75f.js";import"./Content-CSmc2GUv.js";import"./globals-D0QH3NT1.js";import"./ButtonAugment-DbAwCSeR.js";import"./IconButtonAugment-DFy7vWkh.js";import"./BaseTextInput-BYcZ2XaJ.js";import"./TextAreaAugment-FdvYFnJr.js";import"./lodash-BHrlUNHT.js";function re(n){let e,s,t=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},n[0]],o={};for(let c=0;c<t.length;c+=1)o=F(o,t[c]);return{c(){e=j("svg"),s=new q(!0),this.h()},l(c){e=J(c,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var l=Q(e);s=Y(l,!0),l.forEach(g),this.h()},h(){s.a=null,G(e,o)},m(c,l){Z(c,e,l),s.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M304 128a80 80 0 1 0-160 0 80 80 0 1 0 160 0m-208 0a128 128 0 1 1 256 0 128 128 0 1 1-256 0M49.3 464h349.5c-8.9-63.3-63.3-112-129-112h-91.4c-65.7 0-120.1 48.7-129 112zM0 482.3C0 383.8 79.8 304 178.3 304h91.4c98.5 0 178.3 79.8 178.3 178.3 0 16.4-13.3 29.7-29.7 29.7H29.7C13.3 512 0 498.7 0 482.3"/>',e)},p(c,[l]){G(e,o=_(t,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},1&l&&c[0]]))},i:w,o:w,d(c){c&&g(e)}}}function le(n,e,s){return n.$$set=t=>{s(0,e=F(F({},e),S(t)))},[e=S(e)]}class ue extends H{constructor(e){super(),N(this,e,le,re,O,{})}}function me(n){let e,s,t=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},n[0]],o={};for(let c=0;c<t.length;c+=1)o=F(o,t[c]);return{c(){e=j("svg"),s=new q(!0),this.h()},l(c){e=J(c,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var l=Q(e);s=Y(l,!0),l.forEach(g),this.h()},h(){s.a=null,G(e,o)},m(c,l){Z(c,e,l),s.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M0 96c0-35.3 28.7-64 64-64h132.1c19.1 0 37.4 7.6 50.9 21.1L289.9 96H448c35.3 0 64 28.7 64 64v256c0 35.3-28.7 64-64 64H64c-35.3 0-64-28.7-64-64zm64-16c-8.8 0-16 7.2-16 16v320c0 8.8 7.2 16 16 16h384c8.8 0 16-7.2 16-16V160c0-8.8-7.2-16-16-16H286.6c-10.6 0-20.8-4.2-28.3-11.7L213.1 87c-4.5-4.5-10.6-7-17-7z"/>',e)},p(c,[l]){G(e,o=_(t,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&l&&c[0]]))},i:w,o:w,d(c){c&&g(e)}}}function pe(n,e,s){return n.$$set=t=>{s(0,e=F(F({},e),S(t)))},[e=S(e)]}class de extends H{constructor(e){super(),N(this,e,pe,me,O,{})}}function fe(n){let e;return{c(){e=V("Move Selection to User Guidelines")},m(s,t){b(s,e,t)},d(s){s&&g(e)}}}function $e(n){let e,s;return e=new ue({props:{slot:"iconLeft"}}),{c(){v(e.$$.fragment)},m(t,o){x(e,t,o),s=!0},p:w,i(t){s||($(e.$$.fragment,t),s=!0)},o(t){h(e.$$.fragment,t),s=!1},d(t){y(e,t)}}}function he(n){let e;return{c(){e=V("Move Selection to Workspace Guidelines")},m(s,t){b(s,e,t)},d(s){s&&g(e)}}}function ge(n){let e,s;return e=new de({props:{slot:"iconLeft"}}),{c(){v(e.$$.fragment)},m(t,o){x(e,t,o),s=!0},p:w,i(t){s||($(e.$$.fragment,t),s=!0)},o(t){h(e.$$.fragment,t),s=!1},d(t){y(e,t)}}}function we(n){let e;return{c(){e=V("Open Memories")},m(s,t){b(s,e,t)},d(s){s&&g(e)}}}function ve(n){let e,s;return e=new K({props:{slot:"text",size:1,$$slots:{default:[we]},$$scope:{ctx:n}}}),{c(){v(e.$$.fragment)},m(t,o){x(e,t,o),s=!0},p(t,o){const c={};131072&o&&(c.$$scope={dirty:o,ctx:t}),e.$set(c)},i(t){s||($(e.$$.fragment,t),s=!0)},o(t){h(e.$$.fragment,t),s=!1},d(t){y(e,t)}}}function xe(n){let e,s,t,o,c,l,u,m,d,r,p;return o=new U({props:{tooltip:{neutral:"Move highlighted text to user guidelines",success:"Text moved to workspace guidelines"},stateVariant:{success:"solid",neutral:"soft"},defaultColor:"neutral",onClick:n[8],disabled:!n[2],stickyColor:!1,size:1,$$slots:{iconLeft:[$e],default:[fe]},$$scope:{ctx:n}}}),u=new U({props:{tooltip:{neutral:"Move highlighted text to workspace guidelines",success:"Text moved to workspace guidelines"},defaultColor:"neutral",stickyColor:!1,stateVariant:{success:"solid",neutral:"soft"},onClick:n[9],disabled:!n[2],size:1,$$slots:{iconLeft:[ge],default:[he]},$$scope:{ctx:n}}}),r=new oe({props:{size:1,path:n[1],variant:"soft",onOpenLocalFile:n[10],$$slots:{text:[ve]},$$scope:{ctx:n}}}),{c(){e=M("div"),s=M("div"),t=M("div"),v(o.$$.fragment),c=I(),l=M("div"),v(u.$$.fragment),m=I(),d=M("div"),v(r.$$.fragment),C(t,"class","c-move-text-btn svelte-1av2xw6"),C(l,"class","c-move-text-btn svelte-1av2xw6"),C(s,"class","l-file-controls-left svelte-1av2xw6"),C(d,"class","l-file-controls-right svelte-1av2xw6"),C(e,"class","l-file-controls svelte-1av2xw6"),C(e,"slot","header")},m(a,i){b(a,e,i),L(e,s),L(s,t),x(o,t,null),L(s,c),L(s,l),x(u,l,null),L(e,m),L(e,d),x(r,d,null),p=!0},p(a,i){const f={};4&i&&(f.disabled=!a[2]),131072&i&&(f.$$scope={dirty:i,ctx:a}),o.$set(f);const W={};4&i&&(W.disabled=!a[2]),131072&i&&(W.$$scope={dirty:i,ctx:a}),u.$set(W);const k={};2&i&&(k.path=a[1]),2&i&&(k.onOpenLocalFile=a[10]),131072&i&&(k.$$scope={dirty:i,ctx:a}),r.$set(k)},i(a){p||($(o.$$.fragment,a),$(u.$$.fragment,a),$(r.$$.fragment,a),p=!0)},o(a){h(o.$$.fragment,a),h(u.$$.fragment,a),h(r.$$.fragment,a),p=!1},d(a){a&&g(e),y(o),y(u),y(r)}}}function ye(n){let e,s,t,o,c,l;function u(a){n[11](a)}function m(a){n[12](a)}function d(a){n[13](a)}function r(a){n[14](a)}let p={saveFunction:n[6],variant:"surface",size:2,resize:"vertical",class:"markdown-editor",$$slots:{header:[xe]},$$scope:{ctx:n}};return n[2]!==void 0&&(p.selectedText=n[2]),n[3]!==void 0&&(p.selectionStart=n[3]),n[4]!==void 0&&(p.selectionEnd=n[4]),n[0]!==void 0&&(p.value=n[0]),e=new ie({props:p}),z.push(()=>E(e,"selectedText",u)),z.push(()=>E(e,"selectionStart",m)),z.push(()=>E(e,"selectionEnd",d)),z.push(()=>E(e,"value",r)),{c(){v(e.$$.fragment)},m(a,i){x(e,a,i),l=!0},p(a,[i]){const f={};131078&i&&(f.$$scope={dirty:i,ctx:a}),!s&&4&i&&(s=!0,f.selectedText=a[2],B(()=>s=!1)),!t&&8&i&&(t=!0,f.selectionStart=a[3],B(()=>t=!1)),!o&&16&i&&(o=!0,f.selectionEnd=a[4],B(()=>o=!1)),!c&&1&i&&(c=!0,f.value=a[0],B(()=>c=!1)),e.$set(f)},i(a){l||($(e.$$.fragment,a),l=!0)},o(a){h(e.$$.fragment,a),l=!1},d(a){y(e,a)}}}function Ce(n,e,s){let{text:t}=e,{path:o}=e;const c=new D(T),l=new ae,u=new ce(T,c,l);let m="",d=0,r=0;const p=async()=>{o&&u.saveFile({repoRoot:"",pathName:o,content:t})};async function a(i){if(m){const f=t.substring(0,d)+t.substring(r);return s(0,t=f),i==="userGuidelines"?u.updateUserGuidelines(m):u.updateWorkspaceGuidelines(m),await p(),"success"}}return n.$$set=i=>{"text"in i&&s(0,t=i.text),"path"in i&&s(1,o=i.path)},[t,o,m,d,r,u,p,a,()=>a("userGuidelines"),()=>a("augmentGuidelines"),async()=>(u.openFile({repoRoot:"",pathName:o}),"success"),function(i){m=i,s(2,m)},function(i){d=i,s(3,d)},function(i){r=i,s(4,r)},function(i){t=i,s(0,t)}]}class Le extends H{constructor(e){super(),N(this,e,Ce,ye,O,{text:0,path:1})}}function Me(n){let e;return{c(){e=V("Loading memories...")},m(s,t){b(s,e,t)},p:w,i:w,o:w,d(s){s&&g(e)}}}function Fe(n){let e,s;return e=new Le({props:{text:n[0],path:n[1]}}),{c(){v(e.$$.fragment)},m(t,o){x(e,t,o),s=!0},p(t,o){const c={};1&o&&(c.text=t[0]),2&o&&(c.path=t[1]),e.$set(c)},i(t){s||($(e.$$.fragment,t),s=!0)},o(t){h(e.$$.fragment,t),s=!1},d(t){y(e,t)}}}function be(n){let e,s,t,o,c,l;const u=[Fe,Me],m=[];function d(r,p){return r[0]!==null&&r[1]!==null?0:1}return s=d(n),t=m[s]=u[s](n),{c(){e=M("div"),t.c(),C(e,"class","c-memories-container svelte-1vchs21")},m(r,p){b(r,e,p),m[s].m(e,null),o=!0,c||(l=X(window,"message",n[2].onMessageFromExtension),c=!0)},p(r,[p]){let a=s;s=d(r),s===a?m[s].p(r,p):(ne(),h(m[a],1,1,()=>{m[a]=null}),ee(),t=m[s],t?t.p(r,p):(t=m[s]=u[s](r),t.c()),$(t,1),t.m(e,null))},i(r){o||($(t),o=!0)},o(r){h(t),o=!1},d(r){r&&g(e),m[s].d(),c=!1,l()}}}function ke(n,e,s){let t,o;const c=new D(T),l=R(null);P(n,l,d=>s(0,t=d));const u=R(null);P(n,u,d=>s(1,o=d));const m={handleMessageFromExtension(d){const r=d.data;if(r&&r.type===A.loadFile){if(r.data.content!==void 0){const p=r.data.content.replace(/^\n+/,"");l.set(p)}r.data.pathName&&u.set(r.data.pathName)}return!0}};return te(()=>{c.registerConsumer(m),T.postMessage({type:A.memoriesLoaded})}),se(()=>{c.dispose()}),[t,o,c,l,u]}new class extends H{constructor(n){super(),N(this,n,ke,be,O,{})}}({target:document.getElementById("app")});
