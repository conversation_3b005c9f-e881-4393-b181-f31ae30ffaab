var Ot=Object.defineProperty;var Dt=(r,e,n)=>e in r?Ot(r,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):r[e]=n;var Y=(r,e,n)=>Dt(r,typeof e!="symbol"?e+"":e,n);import{S as D,i as U,s as V,D as K,e as v,u as $,q as Z,t as p,r as G,h,ac as te,ad as se,a as ge,F as Ve,J as A,V as C,T as N,a0 as M,c as x,K as Je,L as H,W as k,f as q,P as Q,g as ze,X as Ut,a1 as W,Z as b,A as Te,I as fe,a8 as Ze,M as he,N as ve,O as xe,at as Vt,n as P,af as $e,ak as Et,G as Jt,Q as ee,_ as Zt,R as Ee,Y as Re,aE as Le,a7 as Ge,a4 as Rt,a9 as we,a2 as Me,al as Lt,$ as ye,b as ce,au as Gt,ae as Mt,H as jt,w as Wt,x as Kt,y as Yt,d as je,z as Qt,j as We}from"./SpinnerAugment-BUJasFTo.js";import{M as Xt}from"./MaterialIcon-d9y4vLnQ.js";import{o as qt}from"./keypress-DD1aQVr0.js";import{A as Ke,a as J}from"./autofix-state-d-ymFdyn.js";import{b5 as en,b6 as tn,b7 as He,b8 as nn,aq as sn,b9 as on,ba as At,bb as rn,bc as an,bd as cn,be as ln,bf as Ye,bg as un}from"./AugmentMessage-DqNyWKeW.js";import{R as qe,E as ie,j as Pe,H as Qe,m as Ft,S as dn,I as be,g as Xe,a as $n,h as pn,b as mn,c as gn,d as fn,e as hn,f as vn,k as et,A as _e,T as tt}from"./open-in-new-window-eiueNVFd.js";import{T as Oe}from"./Content-CSmc2GUv.js";import{E as xn,a as wn}from"./diff-operations-DcwKj7d6.js";import{e as Ae,g as yn,h as Cn,f as kn,E as bn,i as _n,C as In,T as zt}from"./Keybindings-CJ37aOb-.js";import{c as Pt,I as Sn,R as Tn,D as En,d as Rn,e as Nt,f as Ln,h as Ie,i as Mn}from"./main-panel-C8Qm-2QZ.js";import{B as De}from"./ButtonAugment-DbAwCSeR.js";import{E as qn}from"./expand-CE2AcHxk.js";import{P as An}from"./pen-to-square-DxHNIIBu.js";import{T as Ue}from"./TextTooltipAugment-UDQF2J4S.js";import{I as Fe}from"./IconButtonAugment-DFy7vWkh.js";import{C as Fn,d as zn,M as Pn,e as Nn,f as Bn}from"./diff-utils-BYhHYFY1.js";import{B as Hn}from"./layer-group-DiHphAz9.js";import"./BaseButton-ci_067e0.js";import{C as On}from"./CardAugment-DvO45c5p.js";import{e as Se}from"./each-DUdYBCJG.js";import{R as nt,a as me}from"./types-CF53Ux0u.js";function st(r){let e,n,t,s,o,i,a,c;const l=[r[4][r[1]]];let u={};for(let f=0;f<l.length;f+=1)u=ge(u,l[f]);n=new Xt({props:u});let d=[{class:"stage-container"},r[1]?Ve(r[3][r[1]]):{},{role:"button"},{tabindex:"0"}],m={};for(let f=0;f<d.length;f+=1)m=ge(m,d[f]);return{c(){e=A("div"),C(n.$$.fragment),t=N(),s=A("div"),o=M(r[1]),x(s,"class","message svelte-1etsput"),Je(e,m),H(e,"active",r[0]),H(e,"svelte-1etsput",!0)},m(f,w){v(f,e,w),k(n,e,null),q(e,t),q(e,s),q(s,o),i=!0,a||(c=[Q(e,"click",r[5]),Q(e,"keydown",qt("Enter",r[5]))],a=!0)},p(f,w){const R=18&w?ze(l,[Ut(f[4][f[1]])]):{};n.$set(R),(!i||2&w)&&W(o,f[1]),Je(e,m=ze(d,[{class:"stage-container"},2&w&&(f[1]?Ve(f[3][f[1]]):{}),{role:"button"},{tabindex:"0"}])),H(e,"active",f[0]),H(e,"svelte-1etsput",!0)},i(f){i||($(n.$$.fragment,f),i=!0)},o(f){p(n.$$.fragment,f),i=!1},d(f){f&&h(e),b(n),a=!1,Te(c)}}}function Dn(r){let e,n,t=r[1]&&st(r);return{c(){t&&t.c(),e=K()},m(s,o){t&&t.m(s,o),v(s,e,o),n=!0},p(s,[o]){s[1]?t?(t.p(s,o),2&o&&$(t,1)):(t=st(s),t.c(),$(t,1),t.m(e.parentNode,e)):t&&(Z(),p(t,1,1,()=>{t=null}),G())},i(s){n||($(t),n=!0)},o(s){p(t),n=!1},d(s){s&&h(e),t&&t.d(s)}}}function Un(r,e,n){let t,s,o,i,{stage:a}=e,{iterationId:c}=e,{stageCount:l}=e;const u=te("autofixConversationModel");se(r,u,f=>n(10,i=f));const d={[J.retesting]:"info",[J.testRunning]:"info",[J.testFailed]:"error",[J.testPassed]:"success",[J.generatingSolutions]:"info",[J.suggestedSolutions]:"warning",[J.selectedSolutions]:"success"},m={[J.retesting]:{iconName:"cached",color:"#FFFFFF"},[J.testRunning]:{iconName:"cached",color:"#FFFFFF"},[J.testFailed]:{iconName:"error",color:"#DB3B4B"},[J.testPassed]:{iconName:"check_circle",color:"#388A34"},[J.generatingSolutions]:{iconName:"cached",color:"#FFFFFF"},[J.suggestedSolutions]:{iconName:"edit",color:"#FFFFFF"},[J.selectedSolutions]:{iconName:"edit",color:"#FFFFFF"}};return r.$$set=f=>{"stage"in f&&n(6,a=f.stage),"iterationId"in f&&n(7,c=f.iterationId),"stageCount"in f&&n(8,l=f.stageCount)},r.$$.update=()=>{var f,w,R;1152&r.$$.dirty&&n(9,t=i==null?void 0:i.getAutofixIteration(c)),1600&r.$$.dirty&&n(0,s=t&&((R=(w=(f=i.extraData)==null?void 0:f.autofixIterations)==null?void 0:w.at(-1))==null?void 0:R.id)===t.id&&t.currentStage===a),833&r.$$.dirty&&n(1,o=function(g,y,E,F){var L;return g?y===Ke.runTest?g.commandFailed===void 0&&F?g.isFirstIteration?J.testRunning:J.retesting:g.commandFailed===!0?J.testFailed:J.testPassed:y===Ke.applyFix?E===(((L=g.suggestedSolutions)==null?void 0:L.length)||0)?g.selectedSolutions?J.selectedSolutions:J.generatingSolutions:J.suggestedSolutions:null:null}(t,a,l,s))},[s,o,u,d,m,()=>{o!==J.generatingSolutions&&u.launchAutofixPanel(c,a)},a,c,l,t,i]}class lr extends D{constructor(e){super(),U(this,e,Un,Dn,V,{stage:6,iterationId:7,stageCount:8})}}function ur(r,e){const n=Math.abs(r);let t=200,s=500;typeof e=="number"?t=e:e&&(t=e.baseThreshold??200,s=e.predictTime??500);const o=n*s/1e3;return Math.max(t,o)}function dr(r,e=10){const n=Math.abs(r);return n>1e3?2*e:n>500?1.5*e:n>200?e:.5*e}function ot(r){const{scrollTop:e,clientHeight:n,scrollHeight:t}=r;return t-e-n}function $r(r,e={}){let n=e,t={scrollTop:0,scrollBottom:0,scrollHeight:0,scrolledIntoBottom:!0,scrolledAwayFromBottom:!0};const s=()=>{var g,y,E;const{scrollTop:o,scrollHeight:i,offsetHeight:a}=r,c=ot(r),l=o>t.scrollTop+1,u=i-t.scrollHeight,d=!(u<0&&t.scrollBottom<-u)&&o<t.scrollTop-1&&c>t.scrollBottom+1,m=i>a,f=((F,L=40)=>ot(F)<=L)(r),w=f&&m&&l,R=d||!m;w&&!t.scrolledIntoBottom?(g=n.onScrollIntoBottom)==null||g.call(n):R&&!t.scrolledAwayFromBottom&&((y=n.onScrollAwayFromBottom)==null||y.call(n)),t={scrollTop:o,scrollBottom:c,scrolledIntoBottom:w,scrolledAwayFromBottom:R,scrollHeight:i},(E=n.onScroll)==null||E.call(n,o)};return r.addEventListener("scroll",s),{update(o){n=o},destroy(){r.removeEventListener("scroll",s)}}}function Vn(r){let e,n,t;const s=r[4].default,o=fe(s,r,r[3],null);return{c(){e=A("div"),o&&o.c(),x(e,"class",n="c-gradient-mask "+r[2]+" svelte-say8yn"),Ze(e,"--fade-size",r[1]+"px"),H(e,"is-horizontal",r[0]==="horizontal")},m(i,a){v(i,e,a),o&&o.m(e,null),t=!0},p(i,[a]){o&&o.p&&(!t||8&a)&&he(o,s,i,i[3],t?xe(s,i[3],a,null):ve(i[3]),null),(!t||4&a&&n!==(n="c-gradient-mask "+i[2]+" svelte-say8yn"))&&x(e,"class",n),(!t||2&a)&&Ze(e,"--fade-size",i[1]+"px"),(!t||5&a)&&H(e,"is-horizontal",i[0]==="horizontal")},i(i){t||($(o,i),t=!0)},o(i){p(o,i),t=!1},d(i){i&&h(e),o&&o.d(i)}}}function Jn(r,e,n){let{$$slots:t={},$$scope:s}=e,{direction:o="vertical"}=e,{fadeSize:i=en}=e,{class:a=""}=e;return r.$$set=c=>{"direction"in c&&n(0,o=c.direction),"fadeSize"in c&&n(1,i=c.fadeSize),"class"in c&&n(2,a=c.class),"$$scope"in c&&n(3,s=c.$$scope)},[o,i,a,s,t]}class pr extends D{constructor(e){super(),U(this,e,Jn,Vn,V,{direction:0,fadeSize:1,class:2})}}function mr(r,e){var o;let n=r.offsetHeight,t=e;const s=new ResizeObserver(i=>{var c;const a=i[0].contentRect.height;i.length===1?a!==n&&((c=t.onHeightChange)==null||c.call(t,a),n=a):console.warn("Unexpected number of resize entries: ",i)});return s.observe(r),(o=t==null?void 0:t.onHeightChange)==null||o.call(t,n),{update(i){t=i},destroy:()=>s.disconnect()}}function gr(r,e){let n=e;const t=tn(()=>{n.onSeen()},1e3,{leading:!0,trailing:!0}),s=new IntersectionObserver(i=>{i.length===1?i[0].isIntersecting&&t():console.warn("Unexpected number of intersection entries: ",i)},{threshold:.5}),o=()=>{s.disconnect(),n.track&&s.observe(r)};return o(),{update(i){const a=n;n=i,n.track!==a.track&&o()},destroy:()=>{s.disconnect(),n.onSeen()}}}function rt(r){let e,n,t,s=r[6]&&it();return n=new Tn({props:{changeImageMode:r[32],saveImage:r[9].saveImage,deleteImage:r[9].deleteImage,renderImage:r[9].renderImage,isEditable:r[33]}}),{c(){s&&s.c(),e=N(),C(n.$$.fragment)},m(o,i){s&&s.m(o,i),v(o,e,i),k(n,o,i),t=!0},p(o,i){o[6]?s?64&i[0]&&$(s,1):(s=it(),s.c(),$(s,1),s.m(e.parentNode,e)):s&&(Z(),p(s,1,1,()=>{s=null}),G());const a={};258&i[0]&&(a.changeImageMode=o[32]),512&i[0]&&(a.saveImage=o[9].saveImage),512&i[0]&&(a.deleteImage=o[9].deleteImage),512&i[0]&&(a.renderImage=o[9].renderImage),64&i[0]&&(a.isEditable=o[33]),n.$set(a)},i(o){t||($(s),$(n.$$.fragment,o),t=!0)},o(o){p(s),p(n.$$.fragment,o),t=!1},d(o){o&&h(e),s&&s.d(o),b(n,o)}}}function it(r){let e,n;return e=new En({}),{c(){C(e.$$.fragment)},m(t,s){k(e,t,s),n=!0},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){b(e,t)}}}function Zn(r){var R;let e,n,t,s,o,i,a,c,l,u,d=r[9].flags.enableChatMultimodal&&rt(r);n=new yn({props:{shortcuts:r[13]}});let m={requestEditorFocus:r[22],onMentionItemsUpdated:r[21]};s=new Pt({props:m}),r[34](s),i=new Cn({props:{placeholder:r[2]}}),c=new Ae.Content({props:{content:((R=r[7])==null?void 0:R.richTextJsonRepr)??r[3],onContentChanged:r[20]}});const f=r[29].default,w=fe(f,r,r[37],null);return{c(){d&&d.c(),e=N(),C(n.$$.fragment),t=N(),C(s.$$.fragment),o=N(),C(i.$$.fragment),a=N(),C(c.$$.fragment),l=N(),w&&w.c()},m(g,y){d&&d.m(g,y),v(g,e,y),k(n,g,y),v(g,t,y),k(s,g,y),v(g,o,y),k(i,g,y),v(g,a,y),k(c,g,y),v(g,l,y),w&&w.m(g,y),u=!0},p(g,y){var O;g[9].flags.enableChatMultimodal?d?(d.p(g,y),512&y[0]&&$(d,1)):(d=rt(g),d.c(),$(d,1),d.m(e.parentNode,e)):d&&(Z(),p(d,1,1,()=>{d=null}),G());const E={};8192&y[0]&&(E.shortcuts=g[13]),n.$set(E),s.$set({});const F={};4&y[0]&&(F.placeholder=g[2]),i.$set(F);const L={};136&y[0]&&(L.content=((O=g[7])==null?void 0:O.richTextJsonRepr)??g[3]),c.$set(L),w&&w.p&&(!u||64&y[1])&&he(w,f,g,g[37],u?xe(f,g[37],y,null):ve(g[37]),null)},i(g){u||($(d),$(n.$$.fragment,g),$(s.$$.fragment,g),$(i.$$.fragment,g),$(c.$$.fragment,g),$(w,g),u=!0)},o(g){p(d),p(n.$$.fragment,g),p(s.$$.fragment,g),p(i.$$.fragment,g),p(c.$$.fragment,g),p(w,g),u=!1},d(g){g&&(h(e),h(t),h(o),h(a),h(l)),d&&d.d(g),b(n,g),r[34](null),b(s,g),b(i,g),b(c,g),w&&w.d(g)}}}function at(r){let e,n;return e=new Rn({props:{chatModel:r[16]}}),{c(){C(e.$$.fragment)},m(t,s){k(e,t,s),n=!0},p:P,i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){b(e,t)}}}function Gn(r){let e,n,t=r[6]&&at(r);return{c(){t&&t.c(),e=K()},m(s,o){t&&t.m(s,o),v(s,e,o),n=!0},p(s,o){s[6]?t?(t.p(s,o),64&o[0]&&$(t,1)):(t=at(s),t.c(),$(t,1),t.m(e.parentNode,e)):t&&(Z(),p(t,1,1,()=>{t=null}),G())},i(s){n||($(t),n=!0)},o(s){p(t),n=!1},d(s){s&&h(e),t&&t.d(s)}}}function ct(r){let e,n;return e=new Nt.Root({props:{$$slots:{rightAlign:[Kn],leftAlign:[jn]},$$scope:{ctx:r}}}),{c(){C(e.$$.fragment)},m(t,s){k(e,t,s),n=!0},p(t,s){const o={};17408&s[0]|64&s[1]&&(o.$$scope={dirty:s,ctx:t}),e.$set(o)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){b(e,t)}}}function jn(r){var t;let e,n;return e=new Nt.ContextMenu({props:{slot:"leftAlign",onCloseDropdown:r[22],onInsertMentionable:(t=r[10])==null?void 0:t.insertMentionNode}}),{c(){C(e.$$.fragment)},m(s,o){k(e,s,o),n=!0},p(s,o){var a;const i={};1024&o[0]&&(i.onInsertMentionable=(a=s[10])==null?void 0:a.insertMentionNode),e.$set(i)},i(s){n||($(e.$$.fragment,s),n=!0)},o(s){p(e.$$.fragment,s),n=!1},d(s){b(e,s)}}}function Wn(r){let e,n;return e=new kn({}),{c(){C(e.$$.fragment)},m(t,s){k(e,t,s),n=!0},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){b(e,t)}}}function Kn(r){let e,n;return e=new De({props:{size:1,variant:"solid",disabled:!r[14],$$slots:{default:[Wn]},$$scope:{ctx:r}}}),e.$on("click",r[18]),{c(){C(e.$$.fragment)},m(t,s){k(e,t,s),n=!0},p(t,s){const o={};16384&s[0]&&(o.disabled=!t[14]),64&s[1]&&(o.$$scope={dirty:s,ctx:t}),e.$set(o)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){b(e,t)}}}function lt(r){let e,n,t;return n=new De({props:{variant:"solid",color:"neutral",size:1,$$slots:{iconLeft:[Qn],default:[Yn]},$$scope:{ctx:r}}}),n.$on("click",r[31]),{c(){e=A("div"),C(n.$$.fragment),x(e,"class","c-user-msg__collapse-button svelte-9vyoe0")},m(s,o){v(s,e,o),k(n,e,null),t=!0},p(s,o){const i={};64&o[1]&&(i.$$scope={dirty:o,ctx:s}),n.$set(i)},i(s){t||($(n.$$.fragment,s),t=!0)},o(s){p(n.$$.fragment,s),t=!1},d(s){s&&h(e),b(n)}}}function Yn(r){let e;return{c(){e=A("span"),e.textContent="Expand"},m(n,t){v(n,e,t)},p:P,d(n){n&&h(e)}}}function Qn(r){let e,n;return e=new qn({props:{slot:"iconLeft"}}),{c(){C(e.$$.fragment)},m(t,s){k(e,t,s),n=!0},p:P,i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){b(e,t)}}}function Xn(r){let e,n,t,s=r[6]&&ct(r),o=r[4]&&lt(r);return{c(){s&&s.c(),e=N(),o&&o.c(),n=K()},m(i,a){s&&s.m(i,a),v(i,e,a),o&&o.m(i,a),v(i,n,a),t=!0},p(i,a){i[6]?s?(s.p(i,a),64&a[0]&&$(s,1)):(s=ct(i),s.c(),$(s,1),s.m(e.parentNode,e)):s&&(Z(),p(s,1,1,()=>{s=null}),G()),i[4]?o?(o.p(i,a),16&a[0]&&$(o,1)):(o=lt(i),o.c(),$(o,1),o.m(n.parentNode,n)):o&&(Z(),p(o,1,1,()=>{o=null}),G())},i(i){t||($(s),$(o),t=!0)},o(i){p(s),p(o),t=!1},d(i){i&&(h(e),h(n)),s&&s.d(i),o&&o.d(i)}}}function es(r){let e,n,t,s,o,i={editable:r[6],$$slots:{footer:[Xn],header:[Gn],default:[Zn]},$$scope:{ctx:r}};return n=new Ae.Root({props:i}),r[35](n),n.$on("click",ts),n.$on("dblclick",r[17]),{c(){e=A("div"),C(n.$$.fragment),x(e,"class","c-chat-input svelte-9vyoe0"),x(e,"role","button"),x(e,"tabindex","-1"),H(e,"is-collapsed",r[4]),H(e,"is-editing",r[6])},m(a,c){v(a,e,c),k(n,e,null),r[36](e),t=!0,s||(o=[Q(window,"mousedown",r[19]),Q(e,"mousedown",Vt(r[30]))],s=!0)},p(a,c){const l={};64&c[0]&&(l.editable=a[6]),26623&c[0]|64&c[1]&&(l.$$scope={dirty:c,ctx:a}),n.$set(l),(!t||16&c[0])&&H(e,"is-collapsed",a[4]),(!t||64&c[0])&&H(e,"is-editing",a[6])},i(a){t||($(n.$$.fragment,a),t=!0)},o(a){p(n.$$.fragment,a),t=!1},d(a){a&&h(e),r[35](null),b(n),r[36](null),s=!1,Te(o)}}}const ts=r=>r.stopPropagation();function ns(r,e,n){let t,s,o,i,a,c,l,u=P;r.$$.on_destroy.push(()=>u());let{$$slots:d={},$$scope:m}=e;const f=te("chatModel");se(r,f,_=>n(9,l=_));const w=te(qe.key);let{requestId:R}=e,{placeholder:g="Edit your message..."}=e,{content:y}=e,{collapsed:E=!1}=e,{onSubmitEdit:F}=e,{onCancelEdit:L}=e,{setIsCollapsed:O}=e,{userExpanded:T}=e,z,j,I,S=!1,X=[];async function ae(){o&&(n(0,T=!0),E&&O(!1),n(6,S=!0),await Et(),Ce())}function de(){return!(!i||!z)&&(F(z,X),!0)}function B(){return n(0,T=!1),n(6,S=!1),n(7,z=void 0),L(),!0}const Ce=()=>I==null?void 0:I.forceFocus();let le;return r.$$set=_=>{"requestId"in _&&n(1,R=_.requestId),"placeholder"in _&&n(2,g=_.placeholder),"content"in _&&n(3,y=_.content),"collapsed"in _&&n(4,E=_.collapsed),"onSubmitEdit"in _&&n(23,F=_.onSubmitEdit),"onCancelEdit"in _&&n(24,L=_.onCancelEdit),"setIsCollapsed"in _&&n(5,O=_.setIsCollapsed),"userExpanded"in _&&n(0,T=_.userExpanded),"$$scope"in _&&n(37,m=_.$$scope)},r.$$.update=()=>{512&r.$$.dirty[0]&&(n(15,t=l.currentConversationModel),u(),u=$e(t,_=>n(8,c=_))),268435968&r.$$.dirty[0]&&n(27,o=l.flags.enableEditableHistory&&!s),134218184&r.$$.dirty[0]&&n(14,i=S&&o&&z!==void 0&&z.rawText.trim()!==""&&z.rawText!==y&&z.richTextJsonRepr!==y&&!c.awaitingReply&&!Sn.hasLoadingImages(z.richTextJsonRepr))},n(28,s=!!(w!=null&&w.isActive)),n(13,a={Enter:de,Escape:B}),[T,R,g,y,E,O,S,z,c,l,j,I,le,a,i,t,f,ae,de,B,function(_){_!==z&&n(7,z=_)},function(_){X=_.current},()=>I==null?void 0:I.requestFocus(),F,L,()=>{ae()},function(){return le},o,s,d,function(_){Jt.call(this,r,_)},()=>{n(0,T=!0),O(!1)},_=>{R&&_&&c.updateChatItem(R,{rich_text_json_repr:_})},()=>S,function(_){ee[_?"unshift":"push"](()=>{j=_,n(10,j)})},function(_){ee[_?"unshift":"push"](()=>{I=_,n(11,I)})},function(_){ee[_?"unshift":"push"](()=>{le=_,n(12,le)})},m]}class ss extends D{constructor(e){super(),U(this,e,ns,es,V,{requestId:1,placeholder:2,content:3,collapsed:4,onSubmitEdit:23,onCancelEdit:24,setIsCollapsed:5,userExpanded:0,requestStartEdit:25,getEditorContainer:26},null,[-1,-1])}get requestStartEdit(){return this.$$.ctx[25]}get getEditorContainer(){return this.$$.ctx[26]}}const ne=class ne{constructor(e){Y(this,"_tipTapExtension");Y(this,"_resizeObserver");Y(this,"_checkHeight",e=>{var t,s;const n=e.getBoundingClientRect().height;(s=(t=this._options).onResize)==null||s.call(t,n)});Y(this,"_setResizeObserver",e=>{var t;const n=(t=e.view)==null?void 0:t.dom;n&&(this._resizeObserver=new ResizeObserver(s=>{for(const o of s)this._checkHeight(o.target)}),this._resizeObserver.observe(n),this._checkHeight(n))});Y(this,"_clearResizeObserver",()=>{var e;(e=this._resizeObserver)==null||e.disconnect(),this._resizeObserver=void 0});Y(this,"updateOptions",e=>{this._options={...this._options,...e}});this._options=e;const n=ne._getNextPluginId(),t=this._setResizeObserver,s=this._clearResizeObserver,o=this._checkHeight;this._tipTapExtension=bn.create({name:n,onCreate:function(){var a;((a=this.editor.view)==null?void 0:a.dom)&&(t(this.editor),this.editor.on("destroy",s))},onUpdate:function(){var a;const i=(a=this.editor.view)==null?void 0:a.dom;i&&o(i)},onDestroy:()=>{var i;(i=this._resizeObserver)==null||i.disconnect(),this._resizeObserver=void 0}})}get tipTapExtension(){return this._tipTapExtension}};Y(ne,"_sequenceId",0),Y(ne,"RESIZE_OBSERVER_PLUGIN_KEY_BASE","augment-resize-observer-plugin-{}"),Y(ne,"_getSequenceId",()=>ne._sequenceId++),Y(ne,"_getNextPluginId",()=>{const e=ne._getSequenceId().toString();return ne.RESIZE_OBSERVER_PLUGIN_KEY_BASE.replace("{}",e)});let Ne=ne;function os(r,e,n){let{height:t=0}=e;const s=c=>{n(0,t=c)},o=te(_n.CONTEXT_KEY),i=new Ne({onResize:s}),a=o.pluginManager.registerPlugin(i);return Zt(a),r.$$set=c=>{"height"in c&&n(0,t=c.height)},i.updateOptions({onResize:s}),[t]}let rs=class extends D{constructor(r){super(),U(this,r,os,null,V,{height:0})}};function is(r){let e,n,t;function s(i){r[21](i)}let o={};return r[6]!==void 0&&(o.height=r[6]),e=new rs({props:o}),ee.push(()=>Ee(e,"height",s)),{c(){C(e.$$.fragment)},m(i,a){k(e,i,a),t=!0},p(i,a){const c={};!n&&64&a&&(n=!0,c.height=i[6],Re(()=>n=!1)),e.$set(c)},i(i){t||($(e.$$.fragment,i),t=!0)},o(i){p(e.$$.fragment,i),t=!1},d(i){b(e,i)}}}function as(r){let e,n,t;function s(i){r[23](i)}let o={collapsed:r[7],content:r[3]??r[1],requestId:r[2],onSubmitEdit:r[13],onCancelEdit:r[5],setIsCollapsed:r[11],$$slots:{default:[is]},$$scope:{ctx:r}};return r[8]!==void 0&&(o.userExpanded=r[8]),e=new ss({props:o}),r[22](e),ee.push(()=>Ee(e,"userExpanded",s)),{c(){C(e.$$.fragment)},m(i,a){k(e,i,a),t=!0},p(i,a){const c={};128&a&&(c.collapsed=i[7]),10&a&&(c.content=i[3]??i[1]),4&a&&(c.requestId=i[2]),32&a&&(c.onCancelEdit=i[5]),134217792&a&&(c.$$scope={dirty:a,ctx:i}),!n&&256&a&&(n=!0,c.userExpanded=i[8],Re(()=>n=!1)),e.$set(c)},i(i){t||($(e.$$.fragment,i),t=!0)},o(i){p(e.$$.fragment,i),t=!1},d(i){r[22](null),b(e,i)}}}function cs(r){let e,n,t;return n=new nn({props:{items:r[10]}}),{c(){e=A("div"),C(n.$$.fragment),x(e,"slot","edit"),x(e,"class","c-user-msg__actions svelte-ln0veu")},m(s,o){v(s,e,o),k(n,e,null),t=!0},p(s,o){const i={};1024&o&&(i.items=s[10]),n.$set(i)},i(s){t||($(n.$$.fragment,s),t=!0)},o(s){p(n.$$.fragment,s),t=!1},d(s){s&&h(e),b(n)}}}function ls(r){let e,n,t,s;return e=new He({props:{timestamp:r[4],$$slots:{edit:[cs],content:[as]},$$scope:{ctx:r}}}),{c(){C(e.$$.fragment)},m(o,i){k(e,o,i),n=!0,t||(s=[Q(window,"keydown",qt("Escape",r[12])),Q(window,"mousedown",r[12])],t=!0)},p(o,[i]){const a={};16&i&&(a.timestamp=o[4]),134219758&i&&(a.$$scope={dirty:i,ctx:o}),e.$set(a)},i(o){n||($(e.$$.fragment,o),n=!0)},o(o){p(e.$$.fragment,o),n=!1},d(o){b(e,o),t=!1,Te(s)}}}function us(r,e,n){let t,s,o,i,a,c,l,u=P,d=()=>(u(),u=$e(m,S=>n(20,l=S)),m);r.$$.on_destroy.push(()=>u());let{chatModel:m}=e;d();let{msg:f}=e,{requestId:w}=e,{richTextJsonRepr:R}=e,{timestamp:g}=e,{onStartEdit:y=()=>{}}=e,{onAcceptEdit:E=()=>{}}=e,{onCancelEdit:F=()=>{}}=e;const L=te(qe.key);let O=!1,T=!1;async function z(S){await Et(),n(7,O=S&&o&&!T)}const j=S=>{a&&w&&(z(!1),I==null||I.requestStartEdit(),y(),S.stopPropagation())};let I;return r.$$set=S=>{"chatModel"in S&&d(n(0,m=S.chatModel)),"msg"in S&&n(1,f=S.msg),"requestId"in S&&n(2,w=S.requestId),"richTextJsonRepr"in S&&n(3,R=S.richTextJsonRepr),"timestamp"in S&&n(4,g=S.timestamp),"onStartEdit"in S&&n(14,y=S.onStartEdit),"onAcceptEdit"in S&&n(15,E=S.onAcceptEdit),"onCancelEdit"in S&&n(5,F=S.onCancelEdit)},r.$$.update=()=>{var S,X;1048580&r.$$.dirty&&n(19,s=w===void 0||w===((X=(S=l==null?void 0:l.currentConversationModel)==null?void 0:S.lastExchange)==null?void 0:X.request_id)),524288&r.$$.dirty&&n(16,o=!s&&!0),1310724&r.$$.dirty&&n(17,a=w!==void 0&&l.flags.fullFeatured&&l.flags.enableEditableHistory&&!t),131072&r.$$.dirty&&n(10,c=[...a?[{label:"Edit message",action:j,id:"edit-message",disabled:!1,icon:An}]:[]]),65600&r.$$.dirty&&i&&o&&(I!=null&&I.getEditorContainer())&&i&&o&&z(!(O&&i<120)&&i>120)},n(18,t=!!(L!=null&&L.isActive)),n(6,i=0),[m,f,w,R,g,F,i,O,T,I,c,z,()=>{},function(S,X){if(!w)return;m.currentConversationModel.clearHistoryFrom(w);const ae=l.flags.enableChatMultimodal&&S.richTextJsonRepr?m.currentConversationModel.createStructuredRequestNodes(S.richTextJsonRepr):void 0;m.currentConversationModel.sendExchange({request_message:S.rawText,rich_text_json_repr:S.richTextJsonRepr,status:ie.draft,mentioned_items:X,structured_request_nodes:ae}),E()},y,E,o,a,t,s,l,function(S){i=S,n(6,i)},function(S){ee[S?"unshift":"push"](()=>{I=S,n(9,I)})},function(S){T=S,n(8,T)}]}class ds extends D{constructor(e){super(),U(this,e,us,ls,V,{chatModel:0,msg:1,requestId:2,richTextJsonRepr:3,timestamp:4,onStartEdit:14,onAcceptEdit:15,onCancelEdit:5})}}function ut(r){let e,n;return e=new ds({props:{msg:r[1].request_message??"",richTextJsonRepr:r[10],chatModel:r[0],requestId:r[8],timestamp:r[1].timestamp}}),{c(){C(e.$$.fragment)},m(t,s){k(e,t,s),n=!0},p(t,s){const o={};2&s&&(o.msg=t[1].request_message??""),1024&s&&(o.richTextJsonRepr=t[10]),1&s&&(o.chatModel=t[0]),256&s&&(o.requestId=t[8]),2&s&&(o.timestamp=t[1].timestamp),e.$set(o)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){b(e,t)}}}function dt(r){let e,n,t;function s(c,l){return c[1].display_error_message?ms:c[1].response_text&&c[1].response_text.length>0?ps:$s}let o=s(r),i=o(r),a=r[8]&&$t(r);return{c(){e=A("div"),i.c(),n=N(),a&&a.c(),x(e,"class","c-msg-list__turn-response-failure svelte-1d1manc")},m(c,l){v(c,e,l),i.m(e,null),q(e,n),a&&a.m(e,null),t=!0},p(c,l){o===(o=s(c))&&i?i.p(c,l):(i.d(1),i=o(c),i&&(i.c(),i.m(e,n))),c[8]?a?(a.p(c,l),256&l&&$(a,1)):(a=$t(c),a.c(),$(a,1),a.m(e,null)):a&&(Z(),p(a,1,1,()=>{a=null}),G())},i(c){t||($(a),t=!0)},o(c){p(a),t=!1},d(c){c&&h(e),i.d(),a&&a.d()}}}function $s(r){let e,n,t,s;return{c(){e=M(`We encountered an issue sending your message. Please
        `),n=A("button"),n.textContent="try again",x(n,"class","svelte-1d1manc")},m(o,i){v(o,e,i),v(o,n,i),t||(s=Q(n,"click",Le(r[14])),t=!0)},p:P,d(o){o&&(h(e),h(n)),t=!1,s()}}}function ps(r){let e,n,t,s,o;return{c(){e=M(`Connection lost. Please
        `),n=A("button"),n.textContent="try again",t=M(`
        to restart the conversation!`),x(n,"class","svelte-1d1manc")},m(i,a){v(i,e,a),v(i,n,a),v(i,t,a),s||(o=Q(n,"click",Le(r[14])),s=!0)},p:P,d(i){i&&(h(e),h(n),h(t)),s=!1,o()}}}function ms(r){let e,n=r[1].display_error_message+"";return{c(){e=M(n)},m(t,s){v(t,e,s)},p(t,s){2&s&&n!==(n=t[1].display_error_message+"")&&W(e,n)},d(t){t&&h(e)}}}function $t(r){let e,n,t,s,o,i,a,c;function l(d){r[20](d)}let u={onOpenChange:r[15],content:r[6],triggerOn:[Oe.Hover],$$slots:{default:[fs]},$$scope:{ctx:r}};return r[7]!==void 0&&(u.requestClose=r[7]),i=new Ue({props:u}),ee.push(()=>Ee(i,"requestClose",l)),{c(){e=A("div"),n=A("span"),t=M("Request ID: "),s=M(r[8]),o=N(),C(i.$$.fragment),x(e,"class","c-msg-list__request-id svelte-1d1manc")},m(d,m){v(d,e,m),q(e,n),q(n,t),q(n,s),q(e,o),k(i,e,null),c=!0},p(d,m){(!c||256&m)&&W(s,d[8]);const f={};64&m&&(f.content=d[6]),8388608&m&&(f.$$scope={dirty:m,ctx:d}),!a&&128&m&&(a=!0,f.requestClose=d[7],Re(()=>a=!1)),i.$set(f)},i(d){c||($(i.$$.fragment,d),c=!0)},o(d){p(i.$$.fragment,d),c=!1},d(d){d&&h(e),b(i)}}}function gs(r){let e,n;return e=new In({}),{c(){C(e.$$.fragment)},m(t,s){k(e,t,s),n=!0},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){b(e,t)}}}function fs(r){let e,n;return e=new Fe({props:{variant:"ghost",color:"neutral",size:1,$$slots:{default:[gs]},$$scope:{ctx:r}}}),e.$on("click",r[16]),{c(){C(e.$$.fragment)},m(t,s){k(e,t,s),n=!0},p(t,s){const o={};8388608&s&&(o.$$scope={dirty:s,ctx:t}),e.$set(o)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){b(e,t)}}}function hs(r){let e,n,t,s,o,i=!r[9]&&!Pe(r[1])&&!r[11],a=i&&ut(r);t=new sn({props:{chatModel:r[5],turn:r[1],turnIndex:r[3],requestId:r[8],isLastTurn:r[2],showName:!r[9],showFooter:!Qe(r[1]),markdown:r[1].response_text??"",timestamp:r[1].timestamp,messageListContainer:r[4]}});let c=r[1].status===ie.failed&&dt(r);return{c(){e=A("div"),a&&a.c(),n=N(),C(t.$$.fragment),s=N(),c&&c.c(),x(e,"class","c-msg-list__turn svelte-1d1manc")},m(l,u){v(l,e,u),a&&a.m(e,null),q(e,n),k(t,e,null),q(e,s),c&&c.m(e,null),o=!0},p(l,[u]){2562&u&&(i=!l[9]&&!Pe(l[1])&&!l[11]),i?a?(a.p(l,u),2562&u&&$(a,1)):(a=ut(l),a.c(),$(a,1),a.m(e,n)):a&&(Z(),p(a,1,1,()=>{a=null}),G());const d={};32&u&&(d.chatModel=l[5]),2&u&&(d.turn=l[1]),8&u&&(d.turnIndex=l[3]),256&u&&(d.requestId=l[8]),4&u&&(d.isLastTurn=l[2]),512&u&&(d.showName=!l[9]),2&u&&(d.showFooter=!Qe(l[1])),2&u&&(d.markdown=l[1].response_text??""),2&u&&(d.timestamp=l[1].timestamp),16&u&&(d.messageListContainer=l[4]),t.$set(d),l[1].status===ie.failed?c?(c.p(l,u),2&u&&$(c,1)):(c=dt(l),c.c(),$(c,1),c.m(e,null)):c&&(Z(),p(c,1,1,()=>{c=null}),G())},i(l){o||($(a),$(t.$$.fragment,l),$(c),o=!0)},o(l){p(a),p(t.$$.fragment,l),p(c),o=!1},d(l){l&&h(e),a&&a.d(),b(t),c&&c.d()}}}function vs(r,e,n){let t,s,o,i,a,c,l,u,d,m,f=P,w=()=>(f(),f=$e(g,I=>n(5,u=I)),g),R=P;r.$$.on_destroy.push(()=>f()),r.$$.on_destroy.push(()=>R());let{chatModel:g}=e;w();let{turn:y}=e,{isLastTurn:E=!1}=e,{turnIndex:F=0}=e,{messageListContainer:L}=e;const O=te(qe.key);se(r,O,I=>n(19,m=I));let T,z="Copy request ID",j=()=>{};return r.$$set=I=>{"chatModel"in I&&w(n(0,g=I.chatModel)),"turn"in I&&n(1,y=I.turn),"isLastTurn"in I&&n(2,E=I.isLastTurn),"turnIndex"in I&&n(3,F=I.turnIndex),"messageListContainer"in I&&n(4,L=I.messageListContainer)},r.$$.update=()=>{var I;2&r.$$.dirty&&n(8,t=y.request_id),32&r.$$.dirty&&(n(12,s=u==null?void 0:u.currentConversationModel),R(),R=$e(s,S=>n(22,d=S))),524288&r.$$.dirty&&n(18,o=(m==null?void 0:m.isActive)&&((I=m==null?void 0:m.currentAgent)==null?void 0:I.is_setup_script_agent)===!0),8&r.$$.dirty&&n(17,i=F===0),393216&r.$$.dirty&&n(11,a=o&&i),34&r.$$.dirty&&n(10,c=u.flags.enableRichTextHistory?y.rich_text_json_repr:void 0),2&r.$$.dirty&&n(9,l=Ft(y))},[g,y,E,F,L,u,z,j,t,l,c,a,s,O,()=>{d.resendTurn(y)},function(I){I||(clearTimeout(T),T=void 0,n(6,z="Copy request ID"))},async function(){t&&(await navigator.clipboard.writeText(t),n(6,z="Copied!"),clearTimeout(T),T=setTimeout(j,1500))},i,o,m,function(I){j=I,n(7,j)}]}class fr extends D{constructor(e){super(),U(this,e,vs,hs,V,{chatModel:0,turn:1,isLastTurn:2,turnIndex:3,messageListContainer:4})}}function xs(r){let e,n,t,s,o,i,a;const c=r[15].default,l=fe(c,r,r[14],null);return{c(){e=A("div"),l&&l.c(),x(e,"class",n=Ge(`c-msg-list__item ${r[5]}`)+" svelte-1s0uz2w"),x(e,"style",t=`min-height: calc(${r[4]}px - (var(--msg-list-item-spacing) * 2));`),x(e,"data-request-id",r[6])},m(u,d){v(u,e,d),l&&l.m(e,null),r[16](e),o=!0,i||(a=Rt(s=on.call(null,e,{follow:!r[2]&&r[1],scrollContainer:r[3],disableScrollUp:!0,smooth:!0,bottom:!0})),i=!0)},p(u,[d]){l&&l.p&&(!o||16384&d)&&he(l,c,u,u[14],o?xe(c,u[14],d,null):ve(u[14]),null),(!o||32&d&&n!==(n=Ge(`c-msg-list__item ${u[5]}`)+" svelte-1s0uz2w"))&&x(e,"class",n),(!o||16&d&&t!==(t=`min-height: calc(${u[4]}px - (var(--msg-list-item-spacing) * 2));`))&&x(e,"style",t),(!o||64&d)&&x(e,"data-request-id",u[6]),s&&we(s.update)&&14&d&&s.update.call(null,{follow:!u[2]&&u[1],scrollContainer:u[3],disableScrollUp:!0,smooth:!0,bottom:!0})},i(u){o||($(l,u),o=!0)},o(u){p(l,u),o=!1},d(u){u&&h(e),l&&l.d(u),r[16](null),i=!1,a()}}}function ws(r,e,n){let t,s,o,i,a=P,c=P,l=()=>(c(),c=$e(f,T=>n(13,i=T)),f);r.$$.on_destroy.push(()=>a()),r.$$.on_destroy.push(()=>c());let{$$slots:u={},$$scope:d}=e,{requestId:m}=e,{chatModel:f}=e;l();let w,{isLastItem:R=!1}=e,{userControlsScroll:g=!1}=e,{releaseScroll:y=()=>{}}=e,{messageListContainer:E}=e,{minHeight:F}=e,{class:L=""}=e,{dataRequestId:O}=e;return Me(()=>{E&&R&&At(E,{smooth:!0,onScrollFinish:y})}),r.$$set=T=>{"requestId"in T&&n(9,m=T.requestId),"chatModel"in T&&l(n(0,f=T.chatModel)),"isLastItem"in T&&n(1,R=T.isLastItem),"userControlsScroll"in T&&n(2,g=T.userControlsScroll),"releaseScroll"in T&&n(10,y=T.releaseScroll),"messageListContainer"in T&&n(3,E=T.messageListContainer),"minHeight"in T&&n(4,F=T.minHeight),"class"in T&&n(5,L=T.class),"dataRequestId"in T&&n(6,O=T.dataRequestId),"$$scope"in T&&n(14,d=T.$$scope)},r.$$.update=()=>{var T,z;8192&r.$$.dirty&&(n(8,t=(T=i==null?void 0:i.currentConversationModel)==null?void 0:T.focusModel),a(),a=$e(t,j=>n(12,o=j))),4608&r.$$.dirty&&n(11,s=((z=o.focusedItem)==null?void 0:z.request_id)===m),2048&r.$$.dirty&&s&&E&&w&&rn(E,w,{topBuffer:0,smooth:!0,scrollDuration:100,onScrollFinish:y})},[f,R,g,E,F,L,O,w,t,m,y,s,o,i,d,u,function(T){ee[T?"unshift":"push"](()=>{w=T,n(7,w)})}]}class hr extends D{constructor(e){super(),U(this,e,ws,xs,V,{requestId:9,chatModel:0,isLastItem:1,userControlsScroll:2,releaseScroll:10,messageListContainer:3,minHeight:4,class:5,dataRequestId:6})}}function ys(r){let e;return{c(){e=M("Generating response...")},m(n,t){v(n,e,t)},d(n){n&&h(e)}}}function pt(r){let e,n;return e=new Hn.Root({props:{color:"neutral",size:1,$$slots:{default:[ks]},$$scope:{ctx:r}}}),{c(){C(e.$$.fragment)},m(t,s){k(e,t,s),n=!0},p(t,s){const o={};1029&s&&(o.$$scope={dirty:s,ctx:t}),e.$set(o)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){b(e,t)}}}function Cs(r){let e,n;return{c(){e=A("span"),n=M(r[2]),x(e,"class","c-gen-response__timer svelte-148snxl"),H(e,"is_minutes",r[0]>=60)},m(t,s){v(t,e,s),q(e,n)},p(t,s){4&s&&W(n,t[2]),1&s&&H(e,"is_minutes",t[0]>=60)},d(t){t&&h(e)}}}function ks(r){let e,n;return e=new ye({props:{type:"monospace",size:1,weight:"light",color:"secondary",$$slots:{default:[Cs]},$$scope:{ctx:r}}}),{c(){C(e.$$.fragment)},m(t,s){k(e,t,s),n=!0},p(t,s){const o={};1029&s&&(o.$$scope={dirty:s,ctx:t}),e.$set(o)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){b(e,t)}}}function bs(r){let e,n,t,s,o,i,a;t=new Lt({props:{size:1}}),o=new ye({props:{size:1,weight:"light",color:"secondary",$$slots:{default:[ys]},$$scope:{ctx:r}}});let c=r[1]&&pt(r);return{c(){e=A("div"),n=A("span"),C(t.$$.fragment),s=N(),C(o.$$.fragment),i=N(),c&&c.c(),x(n,"class","c-gen-response__text svelte-148snxl"),x(e,"class","c-gen-response svelte-148snxl")},m(l,u){v(l,e,u),q(e,n),k(t,n,null),q(n,s),k(o,n,null),q(e,i),c&&c.m(e,null),a=!0},p(l,[u]){const d={};1024&u&&(d.$$scope={dirty:u,ctx:l}),o.$set(d),l[1]?c?(c.p(l,u),2&u&&$(c,1)):(c=pt(l),c.c(),$(c,1),c.m(e,null)):c&&(Z(),p(c,1,1,()=>{c=null}),G())},i(l){a||($(t.$$.fragment,l),$(o.$$.fragment,l),$(c),a=!0)},o(l){p(t.$$.fragment,l),p(o.$$.fragment,l),p(c),a=!1},d(l){l&&h(e),b(t),b(o),c&&c.d()}}}function _s(r,e,n){let t,s,o,{timeToTimerMs:i=5e3}=e,a=0,c=Date.now(),l=!1;function u(){n(0,a=Math.floor((Date.now()-c)/1e3))}function d(){n(1,l=!0),u(),o=setInterval(u,1e3)}return Me(function(){return s=setTimeout(d,i),c=Date.now(),()=>{n(0,a=0),n(1,l=!1),clearTimeout(s),clearInterval(o)}}),r.$$set=m=>{"timeToTimerMs"in m&&n(3,i=m.timeToTimerMs)},r.$$.update=()=>{1&r.$$.dirty&&n(2,t=function(m){return m>=60?`${Math.floor(m/60)}:${String(m%60).padStart(2,"0")}`:`0:${String(m).padStart(2,"0")}`}(a))},[a,l,t,i]}class Is extends D{constructor(e){super(),U(this,e,_s,bs,V,{timeToTimerMs:3})}}class re{constructor(e){Y(this,"type","plainText");this.text=e}}class ke{constructor(e){Y(this,"type","specialBlock");this.text=e}}function Be(r){return r.map(e=>e.text).join("")}function Ss(r){let e,n,t,s,o=(!r[0].status||r[0].status===ie.success)&&r[4]===Be(r[3]);e=new Pn({props:{renderers:r[5],markdown:r[1]+r[4]}});let i=o&&mt(r);return{c(){C(e.$$.fragment),n=N(),i&&i.c(),t=K()},m(a,c){k(e,a,c),v(a,n,c),i&&i.m(a,c),v(a,t,c),s=!0},p(a,c){const l={};18&c&&(l.markdown=a[1]+a[4]),e.$set(l),25&c&&(o=(!a[0].status||a[0].status===ie.success)&&a[4]===Be(a[3])),o?i?(i.p(a,c),25&c&&$(i,1)):(i=mt(a),i.c(),$(i,1),i.m(t.parentNode,t)):i&&(Z(),p(i,1,1,()=>{i=null}),G())},i(a){s||($(e.$$.fragment,a),$(i),s=!0)},o(a){p(e.$$.fragment,a),p(i),s=!1},d(a){a&&(h(n),h(t)),b(e,a),i&&i.d(a)}}}function Ts(r){let e;function n(o,i){return o[0].display_error_message?Ms:o[0].response_text&&o[0].response_text.length>0?Ls:Rs}let t=n(r),s=t(r);return{c(){e=A("div"),s.c(),x(e,"class","c-msg-failure svelte-9a9fi8")},m(o,i){v(o,e,i),s.m(e,null)},p(o,i){t===(t=n(o))&&s?s.p(o,i):(s.d(1),s=t(o),s&&(s.c(),s.m(e,null)))},i:P,o:P,d(o){o&&h(e),s.d()}}}function Es(r){let e,n;return e=new Is({}),{c(){C(e.$$.fragment)},m(t,s){k(e,t,s),n=!0},p:P,i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){b(e,t)}}}function mt(r){let e;const n=r[7].default,t=fe(n,r,r[8],null);return{c(){t&&t.c()},m(s,o){t&&t.m(s,o),e=!0},p(s,o){t&&t.p&&(!e||256&o)&&he(t,n,s,s[8],e?xe(n,s[8],o,null):ve(s[8]),null)},i(s){e||($(t,s),e=!0)},o(s){p(t,s),e=!1},d(s){t&&t.d(s)}}}function Rs(r){let e,n,t=r[2]&&gt(r);return{c(){e=M("We encountered an issue sending your message."),t&&t.c(),n=M(".")},m(s,o){v(s,e,o),t&&t.m(s,o),v(s,n,o)},p(s,o){s[2]?t?t.p(s,o):(t=gt(s),t.c(),t.m(n.parentNode,n)):t&&(t.d(1),t=null)},d(s){s&&(h(e),h(n)),t&&t.d(s)}}}function Ls(r){let e,n,t=r[2]&&ft(r);return{c(){e=M("Connection lost."),t&&t.c(),n=K()},m(s,o){v(s,e,o),t&&t.m(s,o),v(s,n,o)},p(s,o){s[2]?t?t.p(s,o):(t=ft(s),t.c(),t.m(n.parentNode,n)):t&&(t.d(1),t=null)},d(s){s&&(h(e),h(n)),t&&t.d(s)}}}function Ms(r){let e,n=r[0].display_error_message+"";return{c(){e=M(n)},m(t,s){v(t,e,s)},p(t,s){1&s&&n!==(n=t[0].display_error_message+"")&&W(e,n)},d(t){t&&h(e)}}}function gt(r){let e,n,t,s;return{c(){e=M(`Please
            `),n=A("button"),n.textContent="try again",x(n,"class","svelte-9a9fi8")},m(o,i){v(o,e,i),v(o,n,i),t||(s=Q(n,"click",Le(function(){we(r[2])&&r[2].apply(this,arguments)})),t=!0)},p(o,i){r=o},d(o){o&&(h(e),h(n)),t=!1,s()}}}function ft(r){let e,n,t,s,o;return{c(){e=M(`Please
            `),n=A("button"),n.textContent="try again",t=M("."),x(n,"class","svelte-9a9fi8")},m(i,a){v(i,e,a),v(i,n,a),v(i,t,a),s||(o=Q(n,"click",Le(function(){we(r[2])&&r[2].apply(this,arguments)})),s=!0)},p(i,a){r=i},d(i){i&&(h(e),h(n),h(t)),s=!1,o()}}}function qs(r){let e,n,t,s;const o=[Es,Ts,Ss],i=[];function a(c,l){return(!c[0].status||c[0].status===ie.sent)&&c[4].length<=0?0:c[0].status===ie.failed?1:2}return e=a(r),n=i[e]=o[e](r),{c(){n.c(),t=K()},m(c,l){i[e].m(c,l),v(c,t,l),s=!0},p(c,l){let u=e;e=a(c),e===u?i[e].p(c,l):(Z(),p(i[u],1,1,()=>{i[u]=null}),G(),n=i[e],n?n.p(c,l):(n=i[e]=o[e](c),n.c()),$(n,1),n.m(t.parentNode,t))},i(c){s||($(n),s=!0)},o(c){p(n),s=!1},d(c){c&&h(t),i[e].d(c)}}}function As(r){let e,n;return e=new He({props:{isAugment:!0,$$slots:{content:[qs]},$$scope:{ctx:r}}}),{c(){C(e.$$.fragment)},m(t,s){k(e,t,s),n=!0},p(t,[s]){const o={};287&s&&(o.$$scope={dirty:s,ctx:t}),e.$set(o)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){b(e,t)}}}function Fs(r,e,n){let t,s,{$$slots:o={},$$scope:i}=e,{turn:a}=e,{preamble:c=""}=e,{resendTurn:l}=e,{markdownBlocks:u=[]}=e,d={code:Fn,codespan:zn,link:an};return Me(()=>{if(a.seen_state===dn.seen)return void n(0,a.response_text=Be(t),a);let m=Date.now();const f=function*(g){for(const y of g)if(y.type==="specialBlock")yield y.text;else for(const E of y.text)yield E}(t);let w=f.next();const R=()=>{let g=Date.now();const y=Math.round((g-m)/8);let E="";for(let F=0;F<y&&!w.done;F++)E+=w.value,w=f.next();n(0,a.response_text+=E,a),m=g,w.done||requestAnimationFrame(R)};R()}),r.$$set=m=>{"turn"in m&&n(0,a=m.turn),"preamble"in m&&n(1,c=m.preamble),"resendTurn"in m&&n(2,l=m.resendTurn),"markdownBlocks"in m&&n(6,u=m.markdownBlocks),"$$scope"in m&&n(8,i=m.$$scope)},r.$$.update=()=>{1&r.$$.dirty&&n(0,a.response_text=a.response_text??"",a),65&r.$$.dirty&&n(3,t=a.response_text?[new re(a.response_text)]:u),1&r.$$.dirty&&n(4,s=a.response_text??"")},[a,c,l,t,s,d,u,o,i]}class zs extends D{constructor(e){super(),U(this,e,Fs,As,V,{turn:0,preamble:1,resendTurn:2,markdownBlocks:6})}}function Ps(r){let e,n,t,s;return e=new Ae.Content({props:{content:r[2]}}),t=new Pt({props:{requestEditorFocus:r[4]}}),{c(){C(e.$$.fragment),n=N(),C(t.$$.fragment)},m(o,i){k(e,o,i),v(o,n,i),k(t,o,i),s=!0},p:P,i(o){s||($(e.$$.fragment,o),$(t.$$.fragment,o),s=!0)},o(o){p(e.$$.fragment,o),p(t.$$.fragment,o),s=!1},d(o){o&&h(n),b(e,o),b(t,o)}}}function Ns(r){let e,n,t={slot:"content",$$slots:{default:[Ps]},$$scope:{ctx:r}};return e=new Ae.Root({props:t}),r[7](e),{c(){C(e.$$.fragment)},m(s,o){k(e,s,o),n=!0},p(s,o){const i={};512&o&&(i.$$scope={dirty:o,ctx:s}),e.$set(i)},i(s){n||($(e.$$.fragment,s),n=!0)},o(s){p(e.$$.fragment,s),n=!1},d(s){r[7](null),b(e,s)}}}function Bs(r){let e,n,t,s;return e=new He({props:{$$slots:{content:[Ns]},$$scope:{ctx:r}}}),t=new zs({props:{turn:r[1],markdownBlocks:r[3]}}),{c(){C(e.$$.fragment),n=N(),C(t.$$.fragment)},m(o,i){k(e,o,i),v(o,n,i),k(t,o,i),s=!0},p(o,[i]){const a={};513&i&&(a.$$scope={dirty:i,ctx:o}),e.$set(a)},i(o){s||($(e.$$.fragment,o),$(t.$$.fragment,o),s=!0)},o(o){p(e.$$.fragment,o),p(t.$$.fragment,o),s=!1},d(o){o&&h(n),b(e,o),b(t,o)}}}function Hs(r,e,n){let{flagsModel:t}=e,{turn:s}=e;const o={seen_state:s.seen_state,status:ie.success},i=[[new ke("[**Chat**](https://docs.augmentcode.com/using-augment/chat)"),new re(": Explore your codebase, get up to speed on unfamiliar code, and work through technical problems using natural language.")],[new ke("[**Code Completions**](https://docs.augmentcode.com/using-augment/completions)"),new re(": Receive intelligent code suggestions that take your entire codebase into account as you type.")],[new ke("[**Instructions**](https://docs.augmentcode.com/using-augment/instructions)"),new re(": Use natural language prompts to write or modify code, applied as a diff for your review.")]];t.suggestedEditsAvailable&&i.push([new ke("[**Suggested Edits**](https://docs.augmentcode.com/using-augment/suggested-edits)"),new re(": Take your completions beyond the cursor and across your workspace.")]);let a,c=[new re(`Welcome to Augment!

Augment can help you understand code, debug issues, and ship faster with its deep understanding of your codebase. Here is what Augment can do for you:

`),...i.flatMap((l,u)=>[new re(`${u+1}. `),...l,new re(`

`)]),new re("Ask questions to learn more! Just remember to tag **@Augment** when asking about Augment's capabilities.")];return r.$$set=l=>{"flagsModel"in l&&n(5,t=l.flagsModel),"turn"in l&&n(6,s=l.turn)},[a,o,{type:"doc",content:[{type:"paragraph",content:[{type:"text",text:"What can "},{type:"mention",attrs:{id:"Augment",label:"Augment",data:{id:"Augment",label:"Augment"}}},{type:"text",text:" do?"}]}]},c,function(){a==null||a.requestFocus()},t,s,function(l){ee[l?"unshift":"push"](()=>{a=l,n(0,a)})}]}class vr extends D{constructor(e){super(),U(this,e,Hs,Bs,V,{flagsModel:5,turn:6})}}function Os(r){let e,n;return{c(){e=ce("svg"),n=ce("path"),x(n,"d","M6.04995 2.74998C6.04995 2.44623 5.80371 2.19998 5.49995 2.19998C5.19619 2.19998 4.94995 2.44623 4.94995 2.74998V12.25C4.94995 12.5537 5.19619 12.8 5.49995 12.8C5.80371 12.8 6.04995 12.5537 6.04995 12.25V2.74998ZM10.05 2.74998C10.05 2.44623 9.80371 2.19998 9.49995 2.19998C9.19619 2.19998 8.94995 2.44623 8.94995 2.74998V12.25C8.94995 12.5537 9.19619 12.8 9.49995 12.8C9.80371 12.8 10.05 12.5537 10.05 12.25V2.74998Z"),x(n,"fill","currentColor"),x(n,"fill-rule","evenodd"),x(n,"clip-rule","evenodd"),x(e,"width","15"),x(e,"height","15"),x(e,"viewBox","0 0 15 15"),x(e,"fill","none"),x(e,"xmlns","http://www.w3.org/2000/svg")},m(t,s){v(t,e,s),q(e,n)},p:P,i:P,o:P,d(t){t&&h(e)}}}class Ds extends D{constructor(e){super(),U(this,e,null,Os,V,{})}}function Us(r){let e,n,t,s,o;return n=new Ds({}),{c(){e=A("span"),C(n.$$.fragment),t=M(`
  Waiting for user input`),s=M(r[0]),x(e,"class","c-gen-response svelte-5is5us")},m(i,a){v(i,e,a),k(n,e,null),q(e,t),q(e,s),o=!0},p(i,[a]){(!o||1&a)&&W(s,i[0])},i(i){o||($(n.$$.fragment,i),o=!0)},o(i){p(n.$$.fragment,i),o=!1},d(i){i&&h(e),b(n)}}}function Vs(r,e,n){let t=".";return Me(()=>{const s=setInterval(()=>{n(0,t=t.length>=3?".":t+".")},500);return()=>clearInterval(s)}),[t]}class xr extends D{constructor(e){super(),U(this,e,Vs,Us,V,{})}}function Js(r){let e,n,t,s,o;return n=new Lt({props:{size:1}}),{c(){e=A("span"),C(n.$$.fragment),t=N(),s=M(r[0]),x(e,"class","c-retry-response svelte-1lxm8qk")},m(i,a){v(i,e,a),k(n,e,null),q(e,t),q(e,s),o=!0},p(i,[a]){(!o||1&a)&&W(s,i[0])},i(i){o||($(n.$$.fragment,i),o=!0)},o(i){p(n.$$.fragment,i),o=!1},d(i){i&&h(e),b(n)}}}function Zs(r,e,n){let{message:t="Retrying..."}=e;return r.$$set=s=>{"message"in s&&n(0,t=s.message)},[t]}class wr extends D{constructor(e){super(),U(this,e,Zs,Js,V,{message:0})}}function Gs(r){let e,n,t;return e=new Ln({}),{c(){C(e.$$.fragment),n=M(`
    Stopped`)},m(s,o){k(e,s,o),v(s,n,o),t=!0},i(s){t||($(e.$$.fragment,s),t=!0)},o(s){p(e.$$.fragment,s),t=!1},d(s){s&&h(n),b(e,s)}}}function js(r){let e,n,t;return n=new ye({props:{size:1,$$slots:{default:[Gs]},$$scope:{ctx:r}}}),{c(){e=A("span"),C(n.$$.fragment),x(e,"class","c-stopped svelte-lv19x6")},m(s,o){v(s,e,o),k(n,e,null),t=!0},p(s,[o]){const i={};1&o&&(i.$$scope={dirty:o,ctx:s}),n.$set(i)},i(s){t||($(n.$$.fragment,s),t=!0)},o(s){p(n.$$.fragment,s),t=!1},d(s){s&&h(e),b(n)}}}class yr extends D{constructor(e){super(),U(this,e,null,js,V,{})}}function Ws(r){let e,n;return{c(){e=ce("svg"),n=ce("path"),x(n,"fill-rule","evenodd"),x(n,"clip-rule","evenodd"),x(n,"d","M4.85355 2.14645C5.04882 2.34171 5.04882 2.65829 4.85355 2.85355L3.70711 4H9C11.4853 4 13.5 6.01472 13.5 8.5C13.5 10.9853 11.4853 13 9 13H5C4.72386 13 4.5 12.7761 4.5 12.5C4.5 12.2239 4.72386 12 5 12H9C10.933 12 12.5 10.433 12.5 8.5C12.5 6.567 10.933 5 9 5H3.70711L4.85355 6.14645C5.04882 6.34171 5.04882 6.65829 4.85355 6.85355C4.65829 7.04882 4.34171 7.04882 4.14645 6.85355L2.14645 4.85355C1.95118 4.65829 1.95118 4.34171 2.14645 4.14645L4.14645 2.14645C4.34171 1.95118 4.65829 1.95118 4.85355 2.14645Z"),x(n,"fill","currentColor"),x(e,"width","15"),x(e,"height","15"),x(e,"viewBox","0 0 15 15"),x(e,"fill","none"),x(e,"xmlns","http://www.w3.org/2000/svg")},m(t,s){v(t,e,s),q(e,n)},p:P,i:P,o:P,d(t){t&&h(e)}}}class Bt extends D{constructor(e){super(),U(this,e,null,Ws,V,{})}}function Ks(r){let e,n,t;return{c(){e=ce("svg"),n=ce("path"),t=ce("path"),x(n,"fill-rule","evenodd"),x(n,"clip-rule","evenodd"),x(n,"d","M3.1784 5.56111C3.17842 5.85569 3.41722 6.09449 3.71173 6.09444L9.92275 6.09447C10.0585 6.09447 10.1929 6.06857 10.3189 6.01818L13.9947 4.54786C14.1973 4.46681 14.33 4.27071 14.3301 4.05261C14.33 3.83458 14.1973 3.63846 13.9948 3.55744L10.3189 2.08711C10.1929 2.0367 10.0584 2.01083 9.92278 2.01079L3.71173 2.01079C3.41722 2.01084 3.17844 2.24962 3.1784 2.54412L3.1784 5.56111ZM9.92275 5.0278L4.2451 5.02781L4.24509 3.07749L9.92278 3.07745L11.5339 3.72196L12.2527 4.05263C12.2527 4.05263 11.8167 4.25864 11.534 4.38331C10.9139 4.65675 9.92275 5.0278 9.92275 5.0278Z"),x(n,"fill","currentColor"),x(t,"fill-rule","evenodd"),x(t,"clip-rule","evenodd"),x(t,"d","M8.53346 1.59998C8.53346 1.30543 8.29468 1.06665 8.00013 1.06665C7.70558 1.06665 7.4668 1.30543 7.4668 1.59998V3.07746L8.53346 3.07745V1.59998ZM8.53346 5.0278L7.4668 5.0278V14.4C7.4668 14.6945 7.70558 14.9333 8.00013 14.9333C8.29468 14.9333 8.53346 14.6945 8.53346 14.4V5.0278Z"),x(t,"fill","currentColor"),x(e,"width","15"),x(e,"height","15"),x(e,"viewBox","0 0 15 15"),x(e,"fill","none"),x(e,"xmlns","http://www.w3.org/2000/svg")},m(s,o){v(s,e,o),q(e,n),q(e,t)},p:P,i:P,o:P,d(s){s&&h(e)}}}class Ht extends D{constructor(e){super(),U(this,e,null,Ks,V,{})}}function ht(r){let e,n,t,s,o;return n=new On({props:{size:1,insetContent:!0,variant:"ghost",class:"c-checkpoint-tag","data-testid":"checkpoint-version-tag",$$slots:{default:[no]},$$scope:{ctx:r}}}),n.$on("click",r[17]),s=new Fe({props:{variant:r[6]?"soft":"ghost-block",color:"neutral",size:1,disabled:r[6]||r[4],class:"c-revert-button","data-testid":"revert-button",$$slots:{default:[oo]},$$scope:{ctx:r}}}),s.$on("click",r[12]),{c(){e=A("div"),C(n.$$.fragment),t=N(),C(s.$$.fragment),x(e,"class","c-checkpoint-container svelte-q20gs5"),x(e,"data-checkpoint-number",r[0]),H(e,"c-checkpoint-container--target-checkpoint",r[6]),H(e,"c-checkpoint-container--dimmed-marker",r[5])},m(i,a){v(i,e,a),k(n,e,null),q(e,t),k(s,e,null),o=!0},p(i,a){const c={};1048778&a&&(c.$$scope={dirty:a,ctx:i}),n.$set(c);const l={};64&a&&(l.variant=i[6]?"soft":"ghost-block"),80&a&&(l.disabled=i[6]||i[4]),1048656&a&&(l.$$scope={dirty:a,ctx:i}),s.$set(l),(!o||1&a)&&x(e,"data-checkpoint-number",i[0]),(!o||64&a)&&H(e,"c-checkpoint-container--target-checkpoint",i[6]),(!o||32&a)&&H(e,"c-checkpoint-container--dimmed-marker",i[5])},i(i){o||($(n.$$.fragment,i),$(s.$$.fragment,i),o=!0)},o(i){p(n.$$.fragment,i),p(s.$$.fragment,i),o=!1},d(i){i&&h(e),b(n),b(s)}}}function Ys(r){let e,n;return e=new Ht({props:{slot:"leftIcon"}}),{c(){C(e.$$.fragment)},m(t,s){k(e,t,s),n=!0},p:P,i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){b(e,t)}}}function Qs(r){let e,n;return{c(){e=M("Checkpoint "),n=M(r[3])},m(t,s){v(t,e,s),v(t,n,s)},p(t,s){8&s&&W(n,t[3])},d(t){t&&(h(e),h(n))}}}function Xs(r){let e,n=Ie(r[7])+"";return{c(){e=M(n)},m(t,s){v(t,e,s)},p(t,s){128&s&&n!==(n=Ie(t[7])+"")&&W(e,n)},d(t){t&&h(e)}}}function eo(r){let e;return{c(){e=M(r[1])},m(n,t){v(n,e,t)},p(n,t){2&t&&W(e,n[1])},d(n){n&&h(e)}}}function to(r){let e;function n(o,i){return o[1]?eo:o[6]?Xs:void 0}let t=n(r),s=t&&t(r);return{c(){s&&s.c(),e=K()},m(o,i){s&&s.m(o,i),v(o,e,i)},p(o,i){t===(t=n(o))&&s?s.p(o,i):(s&&s.d(1),s=t&&t(o),s&&(s.c(),s.m(e.parentNode,e)))},d(o){o&&h(e),s&&s.d(o)}}}function no(r){let e,n;return e=new zt({props:{size:1,shrink:!0,align:"left",$$slots:{grayText:[to],text:[Qs],leftIcon:[Ys]},$$scope:{ctx:r}}}),{c(){C(e.$$.fragment)},m(t,s){k(e,t,s),n=!0},p(t,s){const o={};1048778&s&&(o.$$scope={dirty:s,ctx:t}),e.$set(o)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){b(e,t)}}}function so(r){let e,n;return e=new Bt({}),{c(){C(e.$$.fragment)},m(t,s){k(e,t,s),n=!0},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){b(e,t)}}}function oo(r){let e,n;return e=new Ue({props:{triggerOn:[Oe.Hover],content:r[6]||r[4]?"Cannot revert to current version":"Revert to this version",$$slots:{default:[so]},$$scope:{ctx:r}}}),{c(){C(e.$$.fragment)},m(t,s){k(e,t,s),n=!0},p(t,s){const o={};80&s&&(o.content=t[6]||t[4]?"Cannot revert to current version":"Revert to this version"),1048576&s&&(o.$$scope={dirty:s,ctx:t}),e.$set(o)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){b(e,t)}}}function ro(r){let e,n,t=(!r[4]||r[2])&&ht(r);return{c(){t&&t.c(),e=K()},m(s,o){t&&t.m(s,o),v(s,e,o),n=!0},p(s,[o]){!s[4]||s[2]?t?(t.p(s,o),20&o&&$(t,1)):(t=ht(s),t.c(),$(t,1),t.m(e.parentNode,e)):t&&(Z(),p(t,1,1,()=>{t=null}),G())},i(s){n||($(t),n=!0)},o(s){p(t),n=!1},d(s){s&&h(e),t&&t.d(s)}}}function io(r,e,n){let t,s,o,i,a,c,l,u,d,m,f,{turn:w}=e;const R=te("checkpointStore"),{targetCheckpointIdx:g,totalCheckpointCount:y,uuidToIdx:E}=R;function F(L){Gt(g,m=L,m)}return se(r,g,L=>n(15,m=L)),se(r,y,L=>n(14,d=L)),se(r,E,L=>n(16,f=L)),r.$$set=L=>{"turn"in L&&n(13,w=L.turn)},r.$$.update=()=>{var L,O,T;73728&r.$$.dirty&&n(0,t=f.get(w.uuid)??-1),8192&r.$$.dirty&&n(7,s=w.toTimestamp),49153&r.$$.dirty&&n(6,(O=d,o=(L=t)===(T=m)||T===void 0&&L===O-1)),49153&r.$$.dirty&&n(5,i=function(z,j,I){return z===I&&I!==void 0&&I<j-1}(t,d,m)),16385&r.$$.dirty&&n(4,a=t===d-1),1&r.$$.dirty&&n(3,c=t+1),8192&r.$$.dirty&&n(2,l=be(w)),8192&r.$$.dirty&&n(1,u=be(w)?function(z){var j,I;if((j=z.revertTarget)!=null&&j.uuid){const S=f.get(z.revertTarget.uuid);return S===void 0?void 0:`Reverted to Checkpoint ${S+1}`}return(I=z.revertTarget)!=null&&I.filePath?`Undid changes to ${z.revertTarget.filePath.relPath}`:void 0}(w):void 0)},[t,u,l,c,a,i,o,s,g,y,E,F,async function(){await R.revertToCheckpoint(w.uuid)},w,d,m,f,()=>F(t)]}class Cr extends D{constructor(e){super(),U(this,e,io,ro,V,{turn:13})}}function ao(r){let e,n;return e=new Ht({props:{slot:"leftIcon"}}),{c(){C(e.$$.fragment)},m(t,s){k(e,t,s),n=!0},p:P,i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){b(e,t)}}}function vt(r){let e,n,t,s,o,i,a=r[1]===1?"":"s";return{c(){e=A("span"),n=M("("),t=M(r[1]),s=M(" file"),o=M(a),i=M(")"),x(e,"class","c-checkpoint-files-count")},m(c,l){v(c,e,l),q(e,n),q(e,t),q(e,s),q(e,o),q(e,i)},p(c,l){2&l&&W(t,c[1]),2&l&&a!==(a=c[1]===1?"":"s")&&W(o,a)},d(c){c&&h(e)}}}function co(r){let e,n,t,s,o=r[1]>0&&vt(r);return{c(){e=M("Checkpoint "),n=M(r[0]),t=N(),o&&o.c(),s=K()},m(i,a){v(i,e,a),v(i,n,a),v(i,t,a),o&&o.m(i,a),v(i,s,a)},p(i,a){1&a&&W(n,i[0]),i[1]>0?o?o.p(i,a):(o=vt(i),o.c(),o.m(s.parentNode,s)):o&&(o.d(1),o=null)},d(i){i&&(h(e),h(n),h(t),h(s)),o&&o.d(i)}}}function lo(r){let e;return{c(){e=M(r[2])},m(n,t){v(n,e,t)},p(n,t){4&t&&W(e,n[2])},d(n){n&&h(e)}}}function uo(r){let e;return{c(){e=M(r[3])},m(n,t){v(n,e,t)},p(n,t){8&t&&W(e,n[3])},d(n){n&&h(e)}}}function $o(r){let e;function n(o,i){return o[3]?uo:o[2]?lo:void 0}let t=n(r),s=t&&t(r);return{c(){s&&s.c(),e=K()},m(o,i){s&&s.m(o,i),v(o,e,i)},p(o,i){t===(t=n(o))&&s?s.p(o,i):(s&&s.d(1),s=t&&t(o),s&&(s.c(),s.m(e.parentNode,e)))},d(o){o&&h(e),s&&s.d(o)}}}function xt(r){let e,n,t;return n=new xn({props:{totalAddedLines:r[4].totalAddedLines,totalRemovedLines:r[4].totalRemovedLines}}),{c(){e=A("div"),C(n.$$.fragment),x(e,"class","c-checkpoint-summary")},m(s,o){v(s,e,o),k(n,e,null),t=!0},p(s,o){const i={};16&o&&(i.totalAddedLines=s[4].totalAddedLines),16&o&&(i.totalRemovedLines=s[4].totalRemovedLines),n.$set(i)},i(s){t||($(n.$$.fragment,s),t=!0)},o(s){p(n.$$.fragment,s),t=!1},d(s){s&&h(e),b(n)}}}function wt(r){let e,n;return e=new Fe({props:{variant:"ghost-block",color:"neutral",size:1,class:"c-revert-button","data-testid":"revert-button",$$slots:{default:[mo]},$$scope:{ctx:r}}}),e.$on("click",function(){we(r[7])&&r[7].apply(this,arguments)}),{c(){C(e.$$.fragment)},m(t,s){k(e,t,s),n=!0},p(t,s){r=t;const o={};256&s&&(o.$$scope={dirty:s,ctx:r}),e.$set(o)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){b(e,t)}}}function po(r){let e,n;return e=new Bt({}),{c(){C(e.$$.fragment)},m(t,s){k(e,t,s),n=!0},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){b(e,t)}}}function mo(r){let e,n;return e=new Ue({props:{triggerOn:[Oe.Hover],content:"Revert to this Checkpoint",$$slots:{default:[po]},$$scope:{ctx:r}}}),{c(){C(e.$$.fragment)},m(t,s){k(e,t,s),n=!0},p(t,s){const o={};256&s&&(o.$$scope={dirty:s,ctx:t}),e.$set(o)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){b(e,t)}}}function go(r){let e,n,t,s,o,i,a,c;t=new Nn({}),o=new zt({props:{size:1,shrink:!0,align:"left",$$slots:{grayText:[$o],text:[co],leftIcon:[ao]},$$scope:{ctx:r}}});let l=r[5]&&xt(r),u=!r[6]&&wt(r);return{c(){e=A("div"),n=A("div"),C(t.$$.fragment),s=N(),C(o.$$.fragment),i=N(),l&&l.c(),a=N(),u&&u.c(),x(n,"class","c-checkpoint-tag"),x(e,"class","c-checkpoint-header svelte-htx8xt")},m(d,m){v(d,e,m),q(e,n),k(t,n,null),q(n,s),k(o,n,null),q(e,i),l&&l.m(e,null),q(e,a),u&&u.m(e,null),c=!0},p(d,[m]){const f={};271&m&&(f.$$scope={dirty:m,ctx:d}),o.$set(f),d[5]?l?(l.p(d,m),32&m&&$(l,1)):(l=xt(d),l.c(),$(l,1),l.m(e,a)):l&&(Z(),p(l,1,1,()=>{l=null}),G()),d[6]?u&&(Z(),p(u,1,1,()=>{u=null}),G()):u?(u.p(d,m),64&m&&$(u,1)):(u=wt(d),u.c(),$(u,1),u.m(e,null))},i(d){c||($(t.$$.fragment,d),$(o.$$.fragment,d),$(l),$(u),c=!0)},o(d){p(t.$$.fragment,d),p(o.$$.fragment,d),p(l),p(u),c=!1},d(d){d&&h(e),b(t),b(o),l&&l.d(),u&&u.d()}}}function fo(r,e,n){let{displayCheckpointIdx:t}=e,{filesCount:s=0}=e,{timestamp:o=""}=e,{revertMessage:i}=e,{diffSummary:a={totalAddedLines:0,totalRemovedLines:0}}=e,{hasChanges:c=!1}=e,{isTarget:l=!1}=e,{onRevertClick:u}=e;return r.$$set=d=>{"displayCheckpointIdx"in d&&n(0,t=d.displayCheckpointIdx),"filesCount"in d&&n(1,s=d.filesCount),"timestamp"in d&&n(2,o=d.timestamp),"revertMessage"in d&&n(3,i=d.revertMessage),"diffSummary"in d&&n(4,a=d.diffSummary),"hasChanges"in d&&n(5,c=d.hasChanges),"isTarget"in d&&n(6,l=d.isTarget),"onRevertClick"in d&&n(7,u=d.onRevertClick)},[t,s,o,i,a,c,l,u]}class ho extends D{constructor(e){super(),U(this,e,fo,go,V,{displayCheckpointIdx:0,filesCount:1,timestamp:2,revertMessage:3,diffSummary:4,hasChanges:5,isTarget:6,onRevertClick:7})}}function yt(r,e,n){const t=r.slice();return t[33]=e[n],t}function Ct(r){let e,n,t,s,o,i,a;function c(u){r[27](u)}let l={class:"c-checkpoint-collapsible",stickyHeader:!0,$$slots:{header:[Co],default:[yo]},$$scope:{ctx:r}};return r[3]!==void 0&&(l.collapsed=r[3]),n=new Bn({props:l}),ee.push(()=>Ee(n,"collapsed",c)),{c(){e=A("div"),C(n.$$.fragment),x(e,"class","c-checkpoint-container svelte-mxd32u"),x(e,"data-checkpoint-number",r[2]),H(e,"c-checkpoint-container--target-checkpoint",r[11]),H(e,"c-checkpoint-container--dimmed-marker",r[10])},m(u,d){v(u,e,d),k(n,e,null),o=!0,i||(a=Rt(s=cn.call(null,e,{onVisible:r[28],scrollTarget:document.body})),i=!0)},p(u,d){const m={};6522&d[0]|32&d[1]&&(m.$$scope={dirty:d,ctx:u}),!t&&8&d[0]&&(t=!0,m.collapsed=u[3],Re(()=>t=!1)),n.$set(m),(!o||4&d[0])&&x(e,"data-checkpoint-number",u[2]),s&&we(s.update)&&1&d[0]&&s.update.call(null,{onVisible:u[28],scrollTarget:document.body}),(!o||2048&d[0])&&H(e,"c-checkpoint-container--target-checkpoint",u[11]),(!o||1024&d[0])&&H(e,"c-checkpoint-container--dimmed-marker",u[10])},i(u){o||($(n.$$.fragment,u),o=!0)},o(u){p(n.$$.fragment,u),o=!1},d(u){u&&h(e),b(n),i=!1,a()}}}function vo(r){let e,n,t;return n=new ye({props:{size:1,color:"neutral",$$slots:{default:[wo]},$$scope:{ctx:r}}}),{c(){e=A("div"),C(n.$$.fragment),x(e,"class","c-edits-list c-edits-list--empty svelte-mxd32u")},m(s,o){v(s,e,o),k(n,e,null),t=!0},p(s,o){const i={};32&o[1]&&(i.$$scope={dirty:o,ctx:s}),n.$set(i)},i(s){t||($(n.$$.fragment,s),t=!0)},o(s){p(n.$$.fragment,s),t=!1},d(s){s&&h(e),b(n)}}}function xo(r){let e,n,t=Se(r[1]),s=[];for(let i=0;i<t.length;i+=1)s[i]=kt(yt(r,t,i));const o=i=>p(s[i],1,1,()=>{s[i]=null});return{c(){e=A("div");for(let i=0;i<s.length;i+=1)s[i].c();x(e,"class","c-edits-list svelte-mxd32u")},m(i,a){v(i,e,a);for(let c=0;c<s.length;c+=1)s[c]&&s[c].m(e,null);n=!0},p(i,a){if(196610&a[0]){let c;for(t=Se(i[1]),c=0;c<t.length;c+=1){const l=yt(i,t,c);s[c]?(s[c].p(l,a),$(s[c],1)):(s[c]=kt(l),s[c].c(),$(s[c],1),s[c].m(e,null))}for(Z(),c=t.length;c<s.length;c+=1)o(c);G()}},i(i){if(!n){for(let a=0;a<t.length;a+=1)$(s[a]);n=!0}},o(i){s=s.filter(Boolean);for(let a=0;a<s.length;a+=1)p(s[a]);n=!1},d(i){i&&h(e),Mt(s,i)}}}function wo(r){let e;return{c(){e=M("No changes to show")},m(n,t){v(n,e,t)},d(n){n&&h(e)}}}function kt(r){let e,n;function t(){return r[25](r[33])}function s(){return r[26](r[33])}return e=new Mn({props:{qualifiedPathName:r[33].qualifiedPathName,lineChanges:r[33].changesSummary,onClickFile:t,onClickReview:s}}),{c(){C(e.$$.fragment)},m(o,i){k(e,o,i),n=!0},p(o,i){r=o;const a={};2&i[0]&&(a.qualifiedPathName=r[33].qualifiedPathName),2&i[0]&&(a.lineChanges=r[33].changesSummary),2&i[0]&&(a.onClickFile=t),2&i[0]&&(a.onClickReview=s),e.$set(a)},i(o){n||($(e.$$.fragment,o),n=!0)},o(o){p(e.$$.fragment,o),n=!1},d(o){b(e,o)}}}function yo(r){let e,n,t,s;const o=[xo,vo],i=[];function a(c,l){return c[4]?0:c[3]?-1:1}return~(e=a(r))&&(n=i[e]=o[e](r)),{c(){n&&n.c(),t=K()},m(c,l){~e&&i[e].m(c,l),v(c,t,l),s=!0},p(c,l){let u=e;e=a(c),e===u?~e&&i[e].p(c,l):(n&&(Z(),p(i[u],1,1,()=>{i[u]=null}),G()),~e?(n=i[e],n?n.p(c,l):(n=i[e]=o[e](c),n.c()),$(n,1),n.m(t.parentNode,t)):n=null)},i(c){s||($(n),s=!0)},o(c){p(n),s=!1},d(c){c&&h(t),~e&&i[e].d(c)}}}function Co(r){let e,n;return e=new ho({props:{slot:"header",displayCheckpointIdx:r[8],filesCount:r[1].length,timestamp:Ie(r[12]),revertMessage:r[6],diffSummary:r[5],hasChanges:r[4],isTarget:r[11],onRevertClick:r[18]}}),{c(){C(e.$$.fragment)},m(t,s){k(e,t,s),n=!0},p(t,s){const o={};256&s[0]&&(o.displayCheckpointIdx=t[8]),2&s[0]&&(o.filesCount=t[1].length),4096&s[0]&&(o.timestamp=Ie(t[12])),64&s[0]&&(o.revertMessage=t[6]),32&s[0]&&(o.diffSummary=t[5]),16&s[0]&&(o.hasChanges=t[4]),2048&s[0]&&(o.isTarget=t[11]),e.$set(o)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){b(e,t)}}}function ko(r){let e,n,t=(!r[9]||r[7])&&Ct(r);return{c(){t&&t.c(),e=K()},m(s,o){t&&t.m(s,o),v(s,e,o),n=!0},p(s,o){!s[9]||s[7]?t?(t.p(s,o),640&o[0]&&$(t,1)):(t=Ct(s),t.c(),$(t,1),t.m(e.parentNode,e)):t&&(Z(),p(t,1,1,()=>{t=null}),G())},i(s){n||($(t),n=!0)},o(s){p(t),n=!1},d(s){s&&h(e),t&&t.d(s)}}}function bo(r,e,n){let t,s,o,i,a,c,l,u,d,m,f,w,R,g,y,{turn:E}=e;const F=te("checkpointStore"),L=te("chatModel"),{targetCheckpointIdx:O,totalCheckpointCount:T,uuidToIdx:z}=F;se(r,O,B=>n(23,g=B)),se(r,T,B=>n(22,R=B)),se(r,z,B=>n(24,y=B));let j=!0;function I(B){L==null||L.extensionClient.openFile({repoRoot:B.rootPath,pathName:B.relPath,allowOutOfWorkspace:!0})}function S(B){L==null||L.extensionClient.showAgentReview(B,o,s,!1)}let X=[],ae=!1,de=!1;return r.$$set=B=>{"turn"in B&&n(19,E=B.turn)},r.$$.update=()=>{var B,Ce,le;17301504&r.$$.dirty[0]&&n(2,t=y.get(E.uuid)??-1),524288&r.$$.dirty[0]&&n(12,s=E.toTimestamp),524288&r.$$.dirty[0]&&(o=E.fromTimestamp),12582916&r.$$.dirty[0]&&n(11,(Ce=R,i=(B=t)===(le=g)||le===void 0&&B===Ce-1)),12582916&r.$$.dirty[0]&&n(10,a=function(_,ue,oe){return _===oe&&oe!==void 0&&oe<ue-1}(t,R,g)),4194308&r.$$.dirty[0]&&n(9,c=t===R-1),4&r.$$.dirty[0]&&n(8,l=t+1),524288&r.$$.dirty[0]&&n(7,u=be(E)),524288&r.$$.dirty[0]&&n(6,d=be(E)?function(_){var ue,oe;if((ue=_.revertTarget)!=null&&ue.uuid){const pe=y.get(_.revertTarget.uuid);return pe===void 0?void 0:`Reverted to Checkpoint ${pe+1}`}return(oe=_.revertTarget)!=null&&oe.filePath?`Undid changes to ${_.revertTarget.filePath.relPath}`:void 0}(E):void 0),2621441&r.$$.dirty[0]&&ae&&E&&!de&&F.getCheckpointSummary(E).then(_=>{n(20,X=_),n(21,de=!0)}),1048576&r.$$.dirty[0]&&n(1,m=X.filter(_=>_.changesSummary&&(_.changesSummary.totalAddedLines>0||_.changesSummary.totalRemovedLines>0))),2&r.$$.dirty[0]&&n(5,f=m.reduce((_,ue)=>{var oe,pe;return _.totalAddedLines+=((oe=ue.changesSummary)==null?void 0:oe.totalAddedLines)??0,_.totalRemovedLines+=((pe=ue.changesSummary)==null?void 0:pe.totalRemovedLines)??0,_},{totalAddedLines:0,totalRemovedLines:0})),2&r.$$.dirty[0]&&n(4,w=m.length>0)},[ae,m,t,j,w,f,d,u,l,c,a,i,s,O,T,z,I,S,function(){F.revertToCheckpoint(E.uuid)},E,X,de,R,g,y,B=>I(B.qualifiedPathName),B=>S(B.qualifiedPathName),function(B){j=B,n(3,j)},()=>n(0,ae=!0)]}class kr extends D{constructor(e){super(),U(this,e,bo,ko,V,{turn:19},null,[-1,-1])}}const _o=(r,e,n,t)=>{const s={retryMessage:void 0,showGeneratingResponse:!1,showAwaitingUserInput:!1,showRunningSpacer:!1,showStopped:!1};if(e===_e.running){const o=r==null?void 0:r.lastExchange;o!=null&&o.isRetriable&&(o!=null&&o.display_error_message)?s.retryMessage=o.display_error_message:n||t.isActive?(t.isActive?t.getLastToolUseState():r.getLastToolUseState()).phase!==tt.running?s.showGeneratingResponse=!0:s.showRunningSpacer=!0:s.showGeneratingResponse=!0}else e===_e.awaitingUserAction?(s.showAwaitingUserInput=!0,s.showRunningSpacer=!0):((o,i)=>{var d;const a=(d=o==null?void 0:o.lastExchange)==null?void 0:d.status,c=a===ie.cancelled,l=o==null?void 0:o.getLastToolUseState().phase,u=l===tt.cancelled;return!i.isActive&&(c||u)})(r,t)&&(s.showStopped=!0);return s},br=(r,e,n,t)=>{const s=r.currentConversationModel,o=((c,l)=>l.isActive?l.getCurrentChatHistory():c.chatHistory.filter(u=>Xe(u)||$n(u)||pn(u)||mn(u)||gn(u)||fn(u)||hn(u)||vn(u)||Pe(u)||et(u)))(s,t),i=(c=>c.reduce((l,u,d)=>(Xe(u)&&Ft(u)&&l.length>0||et(u)&&l.length>0?l[l.length-1].push({turn:u,idx:d}):l.push([{turn:u,idx:d}]),l),[]))(o),a=((c,l)=>l.isActive?l.isCurrentAgentRunning?_e.running:_e.notRunning:c)(e,t);return{chatHistory:o,groupedChatHistory:i,lastGroupConfig:_o(s,a,n,t),doShowFloatingButtons:!t.isActive,doShowAgentSetupLogs:!!t.isActive}};function bt(r,e,n){const t=r.slice();return t[17]=e[n],t}function _t(r){let e,n,t,s,o=r[2]&&It(r),i=Se(r[1].steps),a=[];for(let l=0;l<i.length;l+=1)a[l]=St(bt(r,i,l));const c=l=>p(a[l],1,1,()=>{a[l]=null});return{c(){e=A("div"),o&&o.c(),n=N(),t=A("div");for(let l=0;l<a.length;l+=1)a[l].c();x(t,"id","agent-setup-logs-details"),x(t,"class","c-agent-setup-logs svelte-3wsalz"),H(t,"is-hidden",r[2]&&!r[3]),x(e,"class","c-agent-setup-logs-container svelte-3wsalz"),H(e,"c-agent-setup-logs-container--loading",!r[2])},m(l,u){v(l,e,u),o&&o.m(e,null),q(e,n),q(e,t);for(let d=0;d<a.length;d+=1)a[d]&&a[d].m(t,null);r[15](e),s=!0},p(l,u){if(l[2]?o?(o.p(l,u),4&u&&$(o,1)):(o=It(l),o.c(),$(o,1),o.m(e,n)):o&&(Z(),p(o,1,1,()=>{o=null}),G()),130&u){let d;for(i=Se(l[1].steps),d=0;d<i.length;d+=1){const m=bt(l,i,d);a[d]?(a[d].p(m,u),$(a[d],1)):(a[d]=St(m),a[d].c(),$(a[d],1),a[d].m(t,null))}for(Z(),d=i.length;d<a.length;d+=1)c(d);G()}(!s||12&u)&&H(t,"is-hidden",l[2]&&!l[3]),(!s||4&u)&&H(e,"c-agent-setup-logs-container--loading",!l[2])},i(l){if(!s){$(o);for(let u=0;u<i.length;u+=1)$(a[u]);s=!0}},o(l){p(o),a=a.filter(Boolean);for(let u=0;u<a.length;u+=1)p(a[u]);s=!1},d(l){l&&h(e),o&&o.d(),Mt(a,l),r[15](null)}}}function It(r){let e,n,t,s,o,i,a,c,l,u,d,m;return o=new wn({}),a=new ye({props:{size:1,class:"c-agent-setup-logs-summary-text",$$slots:{default:[Io]},$$scope:{ctx:r}}}),l=new De({props:{variant:"ghost",color:"neutral",size:1,class:"c-agent-setup-logs-toggle-button",$$slots:{default:[So]},$$scope:{ctx:r}}}),l.$on("click",r[9]),{c(){e=A("div"),n=A("div"),t=A("div"),s=A("div"),C(o.$$.fragment),i=N(),C(a.$$.fragment),c=N(),C(l.$$.fragment),x(s,"class","c-agent-setup-logs-summary-icon svelte-3wsalz"),x(t,"class","c-agent-setup-logs-summary-left svelte-3wsalz"),x(n,"class","c-agent-setup-logs-summary-content svelte-3wsalz"),x(e,"class","c-agent-setup-logs-summary svelte-3wsalz"),x(e,"role","button"),x(e,"tabindex","0"),x(e,"aria-expanded",r[3]),x(e,"aria-controls","agent-setup-logs-details")},m(f,w){v(f,e,w),q(e,n),q(n,t),q(t,s),k(o,s,null),q(t,i),k(a,t,null),q(n,c),k(l,n,null),u=!0,d||(m=[Q(e,"click",r[8]),Q(e,"keydown",r[10])],d=!0)},p(f,w){const R={};1048624&w&&(R.$$scope={dirty:w,ctx:f}),a.$set(R);const g={};1048584&w&&(g.$$scope={dirty:w,ctx:f}),l.$set(g),(!u||8&w)&&x(e,"aria-expanded",f[3])},i(f){u||($(o.$$.fragment,f),$(a.$$.fragment,f),$(l.$$.fragment,f),u=!0)},o(f){p(o.$$.fragment,f),p(a.$$.fragment,f),p(l.$$.fragment,f),u=!1},d(f){f&&h(e),b(o),b(a),b(l),d=!1,Te(m)}}}function Io(r){let e,n,t,s,o;return{c(){e=M("Setup complete: "),n=M(r[5]),t=M(" of "),s=M(r[4]),o=M(" steps successful")},m(i,a){v(i,e,a),v(i,n,a),v(i,t,a),v(i,s,a),v(i,o,a)},p(i,a){32&a&&W(n,i[5]),16&a&&W(s,i[4])},d(i){i&&(h(e),h(n),h(t),h(s),h(o))}}}function So(r){let e,n=r[3]?"Hide":"Show";return{c(){e=M(n)},m(t,s){v(t,e,s)},p(t,s){8&s&&n!==(n=t[3]?"Hide":"Show")&&W(e,n)},d(t){t&&h(e)}}}function St(r){let e,n;return e=new ln({props:{title:r[17].step_description,output:r[17].logs,status:Ye(r[17].status),isLoading:r[17].status===me.running,collapsed:r[17].status!==me.running,showCollapseButton:!!r[17].logs,viewButtonTooltip:"View full output in editor",onViewOutput:r[14]}}),{c(){C(e.$$.fragment)},m(t,s){k(e,t,s),n=!0},p(t,s){const o={};2&s&&(o.title=t[17].step_description),2&s&&(o.output=t[17].logs),2&s&&(o.status=Ye(t[17].status)),2&s&&(o.isLoading=t[17].status===me.running),2&s&&(o.collapsed=t[17].status!==me.running),2&s&&(o.showCollapseButton=!!t[17].logs),e.$set(o)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){b(e,t)}}}function To(r){let e,n,t=r[1]&&r[1].steps&&r[1].steps.length>0&&_t(r);return{c(){t&&t.c(),e=K()},m(s,o){t&&t.m(s,o),v(s,e,o),n=!0},p(s,[o]){s[1]&&s[1].steps&&s[1].steps.length>0?t?(t.p(s,o),2&o&&$(t,1)):(t=_t(s),t.c(),$(t,1),t.m(e.parentNode,e)):t&&(Z(),p(t,1,1,()=>{t=null}),G())},i(s){n||($(t),n=!0)},o(s){p(t),n=!1},d(s){s&&h(e),t&&t.d(s)}}}function Eo(r,e,n){let t,s,o,i,a,c,l;const u=te(qe.key);se(r,u,g=>n(13,c=g));const d=te("chatModel");let m=!1,f=!1;function w(g){g&&d&&d.extensionClient.openScratchFile(g,"plaintext")}function R(){n(3,f=!f)}return r.$$.update=()=>{var g,y,E;if(8192&r.$$.dirty&&n(12,t=(g=c==null?void 0:c.currentAgent)==null?void 0:g.status),4096&r.$$.dirty&&n(2,s=t!==nt.agentStarting),8192&r.$$.dirty&&n(1,o=c==null?void 0:c.agentSetupLogs),4097&r.$$.dirty&&l&&t===nt.agentStarting&&setTimeout(()=>{l.scrollIntoView({behavior:"smooth",block:"start"})},500),2053&r.$$.dirty)if(s&&!m&&l){n(11,m=!0),n(3,f=!1);const F=l.closest(".c-msg-list");F&&setTimeout(()=>{F.scrollTo({top:F.scrollHeight,behavior:"smooth"})},300)}else!s&&m&&n(11,m=!1);2&r.$$.dirty&&n(5,i=((y=o==null?void 0:o.steps)==null?void 0:y.filter(F=>F.status===me.success).length)||0),2&r.$$.dirty&&n(4,a=((E=o==null?void 0:o.steps)==null?void 0:E.length)||0)},[l,o,s,f,a,i,u,w,R,function(g){g.stopPropagation(),R()},function(g){g.key!=="Enter"&&g.key!==" "||(g.preventDefault(),R())},m,t,c,(g,y)=>w(y),function(g){ee[g?"unshift":"push"](()=>{l=g,n(0,l)})}]}class _r extends D{constructor(e){super(),U(this,e,Eo,To,V,{})}}function Ro(r){let e;const n=r[3].default,t=fe(n,r,r[4],null);return{c(){t&&t.c()},m(s,o){t&&t.m(s,o),e=!0},p(s,o){t&&t.p&&(!e||16&o)&&he(t,n,s,s[4],e?xe(n,s[4],o,null):ve(s[4]),null)},i(s){e||($(t,s),e=!0)},o(s){p(t,s),e=!1},d(s){t&&t.d(s)}}}function Lo(r){let e,n;return e=new un({props:{class:"c-chat-floating-container c-chat-floating-container--"+r[0],xPos:r[1],yPos:r[2],$$slots:{default:[Ro]},$$scope:{ctx:r}}}),{c(){C(e.$$.fragment)},m(t,s){k(e,t,s),n=!0},p(t,[s]){const o={};1&s&&(o.class="c-chat-floating-container c-chat-floating-container--"+t[0]),2&s&&(o.xPos=t[1]),4&s&&(o.yPos=t[2]),16&s&&(o.$$scope={dirty:s,ctx:t}),e.$set(o)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){b(e,t)}}}function Mo(r,e,n){let{$$slots:t={},$$scope:s}=e,{position:o="bottom"}=e,{xPos:i="middle"}=e,{yPos:a=o==="top"?"top":"bottom"}=e;return r.$$set=c=>{"position"in c&&n(0,o=c.position),"xPos"in c&&n(1,i=c.xPos),"yPos"in c&&n(2,a=c.yPos),"$$scope"in c&&n(4,s=c.$$scope)},[o,i,a,t,s]}class qo extends D{constructor(e){super(),U(this,e,Mo,Lo,V,{position:0,xPos:1,yPos:2})}}function Ao(r){let e,n,t=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 384 512"},r[0]],s={};for(let o=0;o<t.length;o+=1)s=ge(s,t[o]);return{c(){e=ce("svg"),n=new jt(!0),this.h()},l(o){e=Wt(o,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var i=Kt(e);n=Yt(i,!0),i.forEach(h),this.h()},h(){n.a=null,je(e,s)},m(o,i){Qt(o,e,i),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M174.6 472.6c4.5 4.7 10.8 7.4 17.4 7.4s12.8-2.7 17.4-7.4l168-176c9.2-9.6 8.8-24.8-.8-33.9s-24.8-8.8-33.9.8L216 396.1V56c0-13.3-10.7-24-24-24s-24 10.7-24 24v340.1L41.4 263.4c-9.2-9.6-24.3-9.9-33.9-.8s-9.9 24.3-.8 33.9l168 176z"/>',e)},p(o,[i]){je(e,s=ze(t,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 384 512"},1&i&&o[0]]))},i:P,o:P,d(o){o&&h(e)}}}function Fo(r,e,n){return r.$$set=t=>{n(0,e=ge(ge({},e),We(t)))},[e=We(e)]}class zo extends D{constructor(e){super(),U(this,e,Fo,Ao,V,{})}}function Tt(r){let e,n,t;return n=new Fe({props:{class:"c-chat-floating-button",variant:"solid",color:"neutral",size:1,radius:"full",$$slots:{default:[Po]},$$scope:{ctx:r}}}),n.$on("click",r[1]),{c(){e=A("div"),C(n.$$.fragment),x(e,"class","c-msg-list-bottom-button svelte-rg7wt6")},m(s,o){v(s,e,o),k(n,e,null),t=!0},p(s,o){const i={};8&o&&(i.$$scope={dirty:o,ctx:s}),n.$set(i)},i(s){t||($(n.$$.fragment,s),t=!0)},o(s){p(n.$$.fragment,s),t=!1},d(s){s&&h(e),b(n)}}}function Po(r){let e,n;return e=new zo({}),{c(){C(e.$$.fragment)},m(t,s){k(e,t,s),n=!0},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){b(e,t)}}}function No(r){let e,n,t=r[0]&&Tt(r);return{c(){t&&t.c(),e=K()},m(s,o){t&&t.m(s,o),v(s,e,o),n=!0},p(s,o){s[0]?t?(t.p(s,o),1&o&&$(t,1)):(t=Tt(s),t.c(),$(t,1),t.m(e.parentNode,e)):t&&(Z(),p(t,1,1,()=>{t=null}),G())},i(s){n||($(t),n=!0)},o(s){p(t),n=!1},d(s){s&&h(e),t&&t.d(s)}}}function Bo(r){let e,n;return e=new qo({props:{position:"bottom",$$slots:{default:[No]},$$scope:{ctx:r}}}),{c(){C(e.$$.fragment)},m(t,s){k(e,t,s),n=!0},p(t,[s]){const o={};9&s&&(o.$$scope={dirty:s,ctx:t}),e.$set(o)},i(t){n||($(e.$$.fragment,t),n=!0)},o(t){p(e.$$.fragment,t),n=!1},d(t){b(e,t)}}}function Ho(r,e,n){let{showScrollDown:t=!1}=e,{messageListElement:s=null}=e;return r.$$set=o=>{"showScrollDown"in o&&n(0,t=o.showScrollDown),"messageListElement"in o&&n(2,s=o.messageListElement)},[t,()=>{s&&At(s,{smooth:!0})},s]}class Ir extends D{constructor(e){super(),U(this,e,Ho,Bo,V,{showScrollDown:0,messageListElement:2})}}export{_r as A,fr as C,vr as E,pr as G,Ir as M,wr as R,yr as S,ds as U,mr as a,hr as b,gr as c,xr as d,Is as e,lr as f,br as g,zs as h,Cr as i,kr as j,ot as k,dr as l,ur as m,$r as t};
