<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Remote Agents</title>
    <script nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <meta property="csp-nonce" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <script type="module" crossorigin src="./assets/remote-agent-home-DiL-Xgdz.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ=="></script>
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-BUJasFTo.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-BKdwvVur.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-BkU7QRx6.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-e2yaG07g.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/BaseButton-ci_067e0.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/IconButtonAugment-DFy7vWkh.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/globals-D0QH3NT1.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/Content-CSmc2GUv.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/TextTooltipAugment-UDQF2J4S.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/trash-mbophkQL.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/each-DUdYBCJG.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/types-CF53Ux0u.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/CardAugment-DvO45c5p.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/terminal-CwJUqtXN.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/augment-logo-CSOE_v2f.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-DnPofOlT.css" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="stylesheet" crossorigin href="./assets/design-system-init-CRmW_T8r.css" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="stylesheet" crossorigin href="./assets/index-CbECJl0o.css" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="stylesheet" crossorigin href="./assets/BaseButton-DvMdfQ3F.css" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="stylesheet" crossorigin href="./assets/TextTooltipAugment-BIMZ5dVo.css" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="stylesheet" crossorigin href="./assets/Content-D0WttAzY.css" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="stylesheet" crossorigin href="./assets/CardAugment-BAo8Ti0V.css" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="stylesheet" crossorigin href="./assets/IconButtonAugment-BTu-iglL.css" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="stylesheet" crossorigin href="./assets/remote-agent-home-BX7nIEse.css" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
