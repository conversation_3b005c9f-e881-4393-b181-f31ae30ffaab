<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Preference</title>
    <script nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <meta property="csp-nonce" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <script type="module" crossorigin src="./assets/preference-D23-5VWw.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ=="></script>
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-BUJasFTo.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/circle-check-D3m08yO6.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/augment-logo-CSOE_v2f.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/BaseButton-ci_067e0.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/IconButtonAugment-DFy7vWkh.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/globals-D0QH3NT1.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/Content-CSmc2GUv.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/TextTooltipAugment-UDQF2J4S.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/types-CF53Ux0u.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/file-base64-RhZyEMB8.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/file-paths-BcSg4gks.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/types-e72Yl75f.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/open-in-new-window-eiueNVFd.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/folder-opened-C1X7jSw2.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/each-DUdYBCJG.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/preload-helper-Dv6uf1Os.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/toggleHighContrast-CwIv4U26.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-B-fP3g4F.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/diff-utils-BYhHYFY1.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/diff-operations-DcwKj7d6.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/keypress-DD1aQVr0.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/await_block-MKx3qG42.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/ButtonAugment-DbAwCSeR.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/expand-CE2AcHxk.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/CardAugment-DvO45c5p.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/BaseTextInput-BYcZ2XaJ.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-yg8vr2DA.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/magnifying-glass--PD1Uw4z.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/ellipsis-CRdQranZ.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/LanguageIcon-DPvfnfyG.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/next-edit-types-904A5ehg.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/IconFilePath-XiFTI0mW.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/chevron-down-BPcCn3Z6.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/MaterialIcon-d9y4vLnQ.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/play-Dd7ujDDf.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/lodash-BHrlUNHT.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/terminal-CwJUqtXN.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/pen-to-square-DxHNIIBu.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="modulepreload" crossorigin href="./assets/AugmentMessage-DqNyWKeW.js" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-DnPofOlT.css" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="stylesheet" crossorigin href="./assets/circle-check-C69UsBAv.css" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="stylesheet" crossorigin href="./assets/TextTooltipAugment-BIMZ5dVo.css" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="stylesheet" crossorigin href="./assets/Content-D0WttAzY.css" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="stylesheet" crossorigin href="./assets/diff-operations-B-q93Xcb.css" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="stylesheet" crossorigin href="./assets/diff-utils-BuU6GhmE.css" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="stylesheet" crossorigin href="./assets/folder-opened-tclW2Ian.css" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="stylesheet" crossorigin href="./assets/BaseButton-DvMdfQ3F.css" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="stylesheet" crossorigin href="./assets/index-McRKs1sU.css" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="stylesheet" crossorigin href="./assets/ButtonAugment-mhLjBVAk.css" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="stylesheet" crossorigin href="./assets/index-DVlbYrD2.css" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="stylesheet" crossorigin href="./assets/IconButtonAugment-BTu-iglL.css" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="stylesheet" crossorigin href="./assets/IconFilePath-CiKel2Kp.css" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="stylesheet" crossorigin href="./assets/MaterialIcon-BO_oU5T3.css" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="stylesheet" crossorigin href="./assets/CardAugment-BAo8Ti0V.css" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="stylesheet" crossorigin href="./assets/toggleHighContrast-D4zjdeIP.css" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="stylesheet" crossorigin href="./assets/LanguageIcon-D78BqCXT.css" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="stylesheet" crossorigin href="./assets/AugmentMessage-BOCQgrQn.css" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="stylesheet" crossorigin href="./assets/BaseTextInput-DuK8hbkY.css" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
    <link rel="stylesheet" crossorigin href="./assets/preference-CiPuop_U.css" nonce="nonce-asy8XTcUmJJa3MOKEQ14nQ==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
