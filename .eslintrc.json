{"root": true, "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2020, "sourceType": "module", "project": "./tsconfig.json"}, "plugins": ["@typescript-eslint", "prettier"], "extends": ["eslint:recommended", "@typescript-eslint/recommended", "@typescript-eslint/recommended-requiring-type-checking", "prettier"], "rules": {"prettier/prettier": "error", "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "@typescript-eslint/explicit-function-return-type": "warn", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/prefer-nullish-coalescing": "error", "@typescript-eslint/prefer-optional-chain": "error", "@typescript-eslint/no-floating-promises": "error", "@typescript-eslint/await-thenable": "error", "no-console": "warn", "prefer-const": "error", "no-var": "error"}, "env": {"node": true, "es6": true}, "ignorePatterns": ["out", "dist", "**/*.d.ts", "webviews"]}