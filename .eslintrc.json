{"root": true, "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2020, "sourceType": "module"}, "plugins": ["@typescript-eslint"], "extends": ["eslint:recommended"], "rules": {"@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_"}], "no-unused-vars": ["error", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_"}], "no-console": "warn", "prefer-const": "error", "no-var": "error"}, "env": {"node": true, "es6": true}, "ignorePatterns": ["out", "dist", "**/*.d.ts", "webviews"]}